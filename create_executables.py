#!/usr/bin/env python3
"""
Create executable scripts for deployment
"""

from pathlib import Path

def create_executables():
    """Create executable scripts."""
    
    deployment_root = Path("deployment/AreTomo3-GUI-Professional-v3.0.0")
    bin_dir = deployment_root / "bin"
    
    # Unix/Linux executable
    unix_script = bin_dir / "aretomo3-gui"
    with open(unix_script, 'w') as f:
        f.write('''#!/usr/bin/env python3
"""
AreTomo3 GUI Launcher Script
"""

import sys
import os
from pathlib import Path

# Add src to path
script_dir = Path(__file__).parent
src_dir = script_dir.parent / "src"
sys.path.insert(0, str(src_dir))

try:
    from aretomo3_gui.main import main
    main()
except ImportError as e:
    print(f"Error importing AreTomo3 GUI: {e}")
    print("Please ensure the package is properly installed")
    sys.exit(1)
except Exception as e:
    print(f"Error running AreTomo3 GUI: {e}")
    sys.exit(1)
''')
    
    # Make executable
    unix_script.chmod(0o755)
    print("✅ Created Unix/Linux launcher")
    
    # Windows batch script
    windows_script = bin_dir / "aretomo3-gui.bat"
    with open(windows_script, 'w') as f:
        f.write('''@echo off
REM AreTomo3 GUI Windows Launcher

set SCRIPT_DIR=%~dp0
set SRC_DIR=%SCRIPT_DIR%..\\src

python -c "import sys; sys.path.insert(0, r'%SRC_DIR%'); from aretomo3_gui.main import main; main()"

if %ERRORLEVEL% neq 0 (
    echo Error running AreTomo3 GUI
    pause
)
''')
    
    print("✅ Created Windows launcher")

if __name__ == "__main__":
    create_executables()

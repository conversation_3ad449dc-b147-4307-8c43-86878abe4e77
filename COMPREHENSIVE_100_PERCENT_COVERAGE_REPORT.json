{"timestamp": "2025-06-08T22:21:20.272131", "test_phases": {"phase_1": {"return_code": 1, "tests_run": 0, "tests_passed": 0, "tests_failed": 0, "coverage": 8.853459972862957}, "phase_2": {"return_code": 1, "tests_run": 0, "tests_passed": 0, "tests_failed": 0, "coverage": 11.463441211724964}, "phase_3": {"return_code": 1, "tests_run": 0, "tests_passed": 0, "tests_failed": 0, "coverage": 12.059529524723956}}, "coverage_progression": [], "final_results": {"return_code": 1, "total_tests_run": 0, "total_tests_passed": 0, "total_tests_failed": 0, "output_sample": "D tests/test_real_data_management.py::TestDataManagerRealCoverage::test_import_export_all_branches\nFAILED tests/test_real_data_management.py::TestDataManagerRealCoverage::test_data_manager_init\nFAILED tests/test_real_data_management.py::TestDataManagerRealCoverage::test_metadata_management_all_branches\nFAILED tests/test_real_data_management.py::TestDataManagerRealCoverage::test_load_data_all_branches\nFAILED tests/test_real_data_management.py::TestDataManagerRealCoverage::test_dataset_management_all_branches\nFAILED tests/test_real_data_management.py::TestDataManagerRealCoverage::test_cache_management_all_branches\nFAILED tests/test_real_data_management.py::TestDataManagerRealCoverage::test_save_data_all_branches\nFAILED tests/test_real_file_utils.py::TestFileUtilsRealCoverage::test_utility_functions_all_branches\nFAILED tests/test_real_file_utils.py::TestFileUtilsRealCoverage::test_sanitize_filename_all_cases\n================== 34 failed, 16 passed, 1 warning in 10.75s ===================\n", "final_coverage": 4.326304326304326, "lines_covered": 1624, "total_lines": 32686, "missing_lines": 31062, "branch_coverage": "4"}}
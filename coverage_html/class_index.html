<!DOCTYPE html>
<html lang="en">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
    <title>Coverage report</title>
    <link rel="icon" sizes="32x32" href="favicon_32_cb_58284776.png">
    <link rel="stylesheet" href="style_cb_8e611ae1.css" type="text/css">
    <script src="coverage_html_cb_6fb7b396.js" defer></script>
</head>
<body class="indexfile">
<header>
    <div class="content">
        <h1>Coverage report:
            <span class="pc_cov">3%</span>
        </h1>
        <aside id="help_panel_wrapper">
            <input id="help_panel_state" type="checkbox">
            <label for="help_panel_state">
                <img id="keyboard_icon" src="keybd_closed_cb_ce680311.png" alt="Show/hide keyboard shortcuts">
            </label>
            <div id="help_panel">
                <p class="legend">Shortcuts on this page</p>
                <div class="keyhelp">
                    <p>
                        <kbd>f</kbd>
                        <kbd>n</kbd>
                        <kbd>s</kbd>
                        <kbd>m</kbd>
                        <kbd>x</kbd>
                        <kbd>c</kbd>
                        &nbsp; change column sorting
                    </p>
                    <p>
                        <kbd>[</kbd>
                        <kbd>]</kbd>
                        &nbsp; prev/next file
                    </p>
                    <p>
                        <kbd>?</kbd> &nbsp; show/hide this help
                    </p>
                </div>
            </div>
        </aside>
        <form id="filter_container">
            <input id="filter" type="text" value="" placeholder="filter...">
            <div>
                <input id="hide100" type="checkbox" >
                <label for="hide100">hide covered</label>
            </div>
        </form>
        <h2>
                <a class="button" href="index.html">Files</a>
                <a class="button" href="function_index.html">Functions</a>
                <a class="button current">Classes</a>
        </h2>
        <p class="text">
            <a class="nav" href="https://coverage.readthedocs.io/en/7.8.0">coverage.py v7.8.0</a>,
            created at 2025-06-08 22:09 -0400
        </p>
    </div>
</header>
<main id="index">
    <table class="index" data-sortable>
        <thead>
            <tr class="tablehead" title="Click to sort">
                <th id="file" class="name left" aria-sort="none" data-shortcut="f">File<span class="arrows"></span></th>
                <th id="region" class="name left" aria-sort="none" data-default-sort-order="ascending" data-shortcut="n">class<span class="arrows"></span></th>
                <th id="statements" aria-sort="none" data-default-sort-order="descending" data-shortcut="s">statements<span class="arrows"></span></th>
                <th id="missing" aria-sort="none" data-default-sort-order="descending" data-shortcut="m">missing<span class="arrows"></span></th>
                <th id="excluded" aria-sort="none" data-default-sort-order="descending" data-shortcut="x">excluded<span class="arrows"></span></th>
                <th id="coverage" class="right" aria-sort="none" data-shortcut="c">coverage<span class="arrows"></span></th>
            </tr>
        </thead>
        <tbody>
            <tr class="region">
                <td class="name left"><a href="z_3caff36e279812ae___init___py.html">aretomo3_gui/__init__.py</a></td>
                <td class="name left"><a href="z_3caff36e279812ae___init___py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>14</td>
                <td>7</td>
                <td>0</td>
                <td class="right" data-ratio="7 14">50%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_3caff36e279812ae___main___py.html">aretomo3_gui/__main__.py</a></td>
                <td class="name left"><a href="z_3caff36e279812ae___main___py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>0</td>
                <td>0</td>
                <td>3</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a233f4e704dc3d95___init___py.html">aretomo3_gui/analysis/__init__.py</a></td>
                <td class="name left"><a href="z_a233f4e704dc3d95___init___py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a233f4e704dc3d95_aretomo3_output_analyzer_py.html#t18">aretomo3_gui/analysis/aretomo3_output_analyzer.py</a></td>
                <td class="name left"><a href="z_a233f4e704dc3d95_aretomo3_output_analyzer_py.html#t18"><data value='AreTomo3OutputAnalyzer'>AreTomo3OutputAnalyzer</data></a></td>
                <td>133</td>
                <td>133</td>
                <td>0</td>
                <td class="right" data-ratio="0 133">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a233f4e704dc3d95_aretomo3_output_analyzer_py.html">aretomo3_gui/analysis/aretomo3_output_analyzer.py</a></td>
                <td class="name left"><a href="z_a233f4e704dc3d95_aretomo3_output_analyzer_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>26</td>
                <td>26</td>
                <td>0</td>
                <td class="right" data-ratio="0 26">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a233f4e704dc3d95_auto_plot_generator_py.html#t26">aretomo3_gui/analysis/auto_plot_generator.py</a></td>
                <td class="name left"><a href="z_a233f4e704dc3d95_auto_plot_generator_py.html#t26"><data value='AutoPlotGenerator'>AutoPlotGenerator</data></a></td>
                <td>365</td>
                <td>365</td>
                <td>0</td>
                <td class="right" data-ratio="0 365">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a233f4e704dc3d95_auto_plot_generator_py.html">aretomo3_gui/analysis/auto_plot_generator.py</a></td>
                <td class="name left"><a href="z_a233f4e704dc3d95_auto_plot_generator_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>33</td>
                <td>33</td>
                <td>0</td>
                <td class="right" data-ratio="0 33">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b8fe8d00152a262b___init___py.html">aretomo3_gui/analysis/ctf_analysis/__init__.py</a></td>
                <td class="name left"><a href="z_b8fe8d00152a262b___init___py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>6</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 6">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b8fe8d00152a262b_ctf_dashboard_py.html#t27">aretomo3_gui/analysis/ctf_analysis/ctf_dashboard.py</a></td>
                <td class="name left"><a href="z_b8fe8d00152a262b_ctf_dashboard_py.html#t27"><data value='CTFAnalysisDashboard'>CTFAnalysisDashboard</data></a></td>
                <td>165</td>
                <td>165</td>
                <td>0</td>
                <td class="right" data-ratio="0 165">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b8fe8d00152a262b_ctf_dashboard_py.html">aretomo3_gui/analysis/ctf_analysis/ctf_dashboard.py</a></td>
                <td class="name left"><a href="z_b8fe8d00152a262b_ctf_dashboard_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>44</td>
                <td>44</td>
                <td>2</td>
                <td class="right" data-ratio="0 44">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b8fe8d00152a262b_ctf_parser_py.html#t29">aretomo3_gui/analysis/ctf_analysis/ctf_parser.py</a></td>
                <td class="name left"><a href="z_b8fe8d00152a262b_ctf_parser_py.html#t29"><data value='CTFDataParser'>CTFDataParser</data></a></td>
                <td>125</td>
                <td>125</td>
                <td>0</td>
                <td class="right" data-ratio="0 125">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b8fe8d00152a262b_ctf_parser_py.html">aretomo3_gui/analysis/ctf_analysis/ctf_parser.py</a></td>
                <td class="name left"><a href="z_b8fe8d00152a262b_ctf_parser_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>97</td>
                <td>97</td>
                <td>2</td>
                <td class="right" data-ratio="0 97">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b8fe8d00152a262b_ctf_quality_py.html#t30">aretomo3_gui/analysis/ctf_analysis/ctf_quality.py</a></td>
                <td class="name left"><a href="z_b8fe8d00152a262b_ctf_quality_py.html#t30"><data value='CTFQualityAssessment'>CTFQualityAssessment</data></a></td>
                <td>136</td>
                <td>136</td>
                <td>0</td>
                <td class="right" data-ratio="0 136">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b8fe8d00152a262b_ctf_quality_py.html">aretomo3_gui/analysis/ctf_analysis/ctf_quality.py</a></td>
                <td class="name left"><a href="z_b8fe8d00152a262b_ctf_quality_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>59</td>
                <td>59</td>
                <td>2</td>
                <td class="right" data-ratio="0 59">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b8fe8d00152a262b_ctf_utils_py.html#t23">aretomo3_gui/analysis/ctf_analysis/ctf_utils.py</a></td>
                <td class="name left"><a href="z_b8fe8d00152a262b_ctf_utils_py.html#t23"><data value='CTFUtils'>CTFUtils</data></a></td>
                <td>160</td>
                <td>160</td>
                <td>0</td>
                <td class="right" data-ratio="0 160">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b8fe8d00152a262b_ctf_utils_py.html">aretomo3_gui/analysis/ctf_analysis/ctf_utils.py</a></td>
                <td class="name left"><a href="z_b8fe8d00152a262b_ctf_utils_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>52</td>
                <td>52</td>
                <td>2</td>
                <td class="right" data-ratio="0 52">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b8fe8d00152a262b_ctf_visualizer_py.html#t30">aretomo3_gui/analysis/ctf_analysis/ctf_visualizer.py</a></td>
                <td class="name left"><a href="z_b8fe8d00152a262b_ctf_visualizer_py.html#t30"><data value='CTF2DVisualizer'>CTF2DVisualizer</data></a></td>
                <td>291</td>
                <td>291</td>
                <td>0</td>
                <td class="right" data-ratio="0 291">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b8fe8d00152a262b_ctf_visualizer_py.html">aretomo3_gui/analysis/ctf_analysis/ctf_visualizer.py</a></td>
                <td class="name left"><a href="z_b8fe8d00152a262b_ctf_visualizer_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>57</td>
                <td>57</td>
                <td>2</td>
                <td class="right" data-ratio="0 57">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a233f4e704dc3d95_interactive_plots_py.html#t47">aretomo3_gui/analysis/interactive_plots.py</a></td>
                <td class="name left"><a href="z_a233f4e704dc3d95_interactive_plots_py.html#t47"><data value='InteractivePlotter'>InteractivePlotter</data></a></td>
                <td>99</td>
                <td>99</td>
                <td>0</td>
                <td class="right" data-ratio="0 99">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a233f4e704dc3d95_interactive_plots_py.html">aretomo3_gui/analysis/interactive_plots.py</a></td>
                <td class="name left"><a href="z_a233f4e704dc3d95_interactive_plots_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>33</td>
                <td>33</td>
                <td>0</td>
                <td class="right" data-ratio="0 33">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2e874a7ec04b2056___init___py.html">aretomo3_gui/analysis/motion_analysis/__init__.py</a></td>
                <td class="name left"><a href="z_2e874a7ec04b2056___init___py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>5</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="0 5">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2e874a7ec04b2056_motion_parser_py.html#t38">aretomo3_gui/analysis/motion_analysis/motion_parser.py</a></td>
                <td class="name left"><a href="z_2e874a7ec04b2056_motion_parser_py.html#t38"><data value='MotionCorrectionParser'>MotionCorrectionParser</data></a></td>
                <td>143</td>
                <td>143</td>
                <td>0</td>
                <td class="right" data-ratio="0 143">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2e874a7ec04b2056_motion_parser_py.html">aretomo3_gui/analysis/motion_analysis/motion_parser.py</a></td>
                <td class="name left"><a href="z_2e874a7ec04b2056_motion_parser_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>60</td>
                <td>60</td>
                <td>2</td>
                <td class="right" data-ratio="0 60">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2e874a7ec04b2056_motion_visualizer_py.html#t27">aretomo3_gui/analysis/motion_analysis/motion_visualizer.py</a></td>
                <td class="name left"><a href="z_2e874a7ec04b2056_motion_visualizer_py.html#t27"><data value='MotionCorrectionVisualizer'>MotionCorrectionVisualizer</data></a></td>
                <td>178</td>
                <td>178</td>
                <td>0</td>
                <td class="right" data-ratio="0 178">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2e874a7ec04b2056_motion_visualizer_py.html">aretomo3_gui/analysis/motion_analysis/motion_visualizer.py</a></td>
                <td class="name left"><a href="z_2e874a7ec04b2056_motion_visualizer_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>53</td>
                <td>53</td>
                <td>2</td>
                <td class="right" data-ratio="0 53">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a233f4e704dc3d95_realtime_monitor_py.html#t22">aretomo3_gui/analysis/realtime_monitor.py</a></td>
                <td class="name left"><a href="z_a233f4e704dc3d95_realtime_monitor_py.html#t22"><data value='AreTomo3ProcessingHandler'>AreTomo3ProcessingHandler</data></a></td>
                <td>21</td>
                <td>21</td>
                <td>0</td>
                <td class="right" data-ratio="0 21">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a233f4e704dc3d95_realtime_monitor_py.html#t66">aretomo3_gui/analysis/realtime_monitor.py</a></td>
                <td class="name left"><a href="z_a233f4e704dc3d95_realtime_monitor_py.html#t66"><data value='RealtimeProcessingMonitor'>RealtimeProcessingMonitor</data></a></td>
                <td>136</td>
                <td>136</td>
                <td>0</td>
                <td class="right" data-ratio="0 136">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a233f4e704dc3d95_realtime_monitor_py.html">aretomo3_gui/analysis/realtime_monitor.py</a></td>
                <td class="name left"><a href="z_a233f4e704dc3d95_realtime_monitor_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>37</td>
                <td>37</td>
                <td>0</td>
                <td class="right" data-ratio="0 37">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_1fe0bc49e225fa3c___init___py.html">aretomo3_gui/analytics/__init__.py</a></td>
                <td class="name left"><a href="z_1fe0bc49e225fa3c___init___py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>2</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="2 2">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_1fe0bc49e225fa3c_advanced_analytics_py.html#t47">aretomo3_gui/analytics/advanced_analytics.py</a></td>
                <td class="name left"><a href="z_1fe0bc49e225fa3c_advanced_analytics_py.html#t47"><data value='AnalyticsResult'>AnalyticsResult</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_1fe0bc49e225fa3c_advanced_analytics_py.html#t60">aretomo3_gui/analytics/advanced_analytics.py</a></td>
                <td class="name left"><a href="z_1fe0bc49e225fa3c_advanced_analytics_py.html#t60"><data value='QualityMetrics'>QualityMetrics</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_1fe0bc49e225fa3c_advanced_analytics_py.html#t71">aretomo3_gui/analytics/advanced_analytics.py</a></td>
                <td class="name left"><a href="z_1fe0bc49e225fa3c_advanced_analytics_py.html#t71"><data value='DataQualityAnalyzer'>DataQualityAnalyzer</data></a></td>
                <td>79</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="73 79">92%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_1fe0bc49e225fa3c_advanced_analytics_py.html#t275">aretomo3_gui/analytics/advanced_analytics.py</a></td>
                <td class="name left"><a href="z_1fe0bc49e225fa3c_advanced_analytics_py.html#t275"><data value='StatisticalAnalyzer'>StatisticalAnalyzer</data></a></td>
                <td>38</td>
                <td>7</td>
                <td>0</td>
                <td class="right" data-ratio="31 38">82%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_1fe0bc49e225fa3c_advanced_analytics_py.html#t357">aretomo3_gui/analytics/advanced_analytics.py</a></td>
                <td class="name left"><a href="z_1fe0bc49e225fa3c_advanced_analytics_py.html#t357"><data value='MachineLearningAnalyzer'>MachineLearningAnalyzer</data></a></td>
                <td>39</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="35 39">90%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_1fe0bc49e225fa3c_advanced_analytics_py.html#t452">aretomo3_gui/analytics/advanced_analytics.py</a></td>
                <td class="name left"><a href="z_1fe0bc49e225fa3c_advanced_analytics_py.html#t452"><data value='AdvancedAnalytics'>AdvancedAnalytics</data></a></td>
                <td>38</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="33 38">87%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_1fe0bc49e225fa3c_advanced_analytics_py.html">aretomo3_gui/analytics/advanced_analytics.py</a></td>
                <td class="name left"><a href="z_1fe0bc49e225fa3c_advanced_analytics_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>74</td>
                <td>9</td>
                <td>0</td>
                <td class="right" data-ratio="65 74">88%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_1fe0bc49e225fa3c_analyzer_py.html#t21">aretomo3_gui/analytics/analyzer.py</a></td>
                <td class="name left"><a href="z_1fe0bc49e225fa3c_analyzer_py.html#t21"><data value='AnalysisResult'>AnalysisResult</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_1fe0bc49e225fa3c_analyzer_py.html#t30">aretomo3_gui/analytics/analyzer.py</a></td>
                <td class="name left"><a href="z_1fe0bc49e225fa3c_analyzer_py.html#t30"><data value='StatisticalAnalyzer'>StatisticalAnalyzer</data></a></td>
                <td>36</td>
                <td>36</td>
                <td>0</td>
                <td class="right" data-ratio="0 36">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_1fe0bc49e225fa3c_analyzer_py.html#t172">aretomo3_gui/analytics/analyzer.py</a></td>
                <td class="name left"><a href="z_1fe0bc49e225fa3c_analyzer_py.html#t172"><data value='QualityMetrics'>QualityMetrics</data></a></td>
                <td>36</td>
                <td>36</td>
                <td>0</td>
                <td class="right" data-ratio="0 36">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_1fe0bc49e225fa3c_analyzer_py.html#t253">aretomo3_gui/analytics/analyzer.py</a></td>
                <td class="name left"><a href="z_1fe0bc49e225fa3c_analyzer_py.html#t253"><data value='AdvancedAnalytics'>AdvancedAnalytics</data></a></td>
                <td>34</td>
                <td>34</td>
                <td>0</td>
                <td class="right" data-ratio="0 34">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_1fe0bc49e225fa3c_analyzer_py.html">aretomo3_gui/analytics/analyzer.py</a></td>
                <td class="name left"><a href="z_1fe0bc49e225fa3c_analyzer_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>35</td>
                <td>35</td>
                <td>0</td>
                <td class="right" data-ratio="0 35">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_3caff36e279812ae_cli_py.html">aretomo3_gui/cli.py</a></td>
                <td class="name left"><a href="z_3caff36e279812ae_cli_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>91</td>
                <td>91</td>
                <td>2</td>
                <td class="right" data-ratio="0 91">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f984836ddf12effb___init___py.html">aretomo3_gui/core/__init__.py</a></td>
                <td class="name left"><a href="z_f984836ddf12effb___init___py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f984836ddf12effb_advanced_logging_py.html#t28">aretomo3_gui/core/advanced_logging.py</a></td>
                <td class="name left"><a href="z_f984836ddf12effb_advanced_logging_py.html#t28"><data value='LogCategory'>LogCategory</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f984836ddf12effb_advanced_logging_py.html#t42">aretomo3_gui/core/advanced_logging.py</a></td>
                <td class="name left"><a href="z_f984836ddf12effb_advanced_logging_py.html#t42"><data value='LogEntry'>LogEntry</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f984836ddf12effb_advanced_logging_py.html#t58">aretomo3_gui/core/advanced_logging.py</a></td>
                <td class="name left"><a href="z_f984836ddf12effb_advanced_logging_py.html#t58"><data value='StructuredFormatter'>StructuredFormatter</data></a></td>
                <td>10</td>
                <td>10</td>
                <td>0</td>
                <td class="right" data-ratio="0 10">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f984836ddf12effb_advanced_logging_py.html#t93">aretomo3_gui/core/advanced_logging.py</a></td>
                <td class="name left"><a href="z_f984836ddf12effb_advanced_logging_py.html#t93"><data value='AdvancedLogger'>AdvancedLogger</data></a></td>
                <td>110</td>
                <td>110</td>
                <td>0</td>
                <td class="right" data-ratio="0 110">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f984836ddf12effb_advanced_logging_py.html">aretomo3_gui/core/advanced_logging.py</a></td>
                <td class="name left"><a href="z_f984836ddf12effb_advanced_logging_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>70</td>
                <td>70</td>
                <td>0</td>
                <td class="right" data-ratio="0 70">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f99fa31c6596b435___init___py.html">aretomo3_gui/core/automation/__init__.py</a></td>
                <td class="name left"><a href="z_f99fa31c6596b435___init___py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f99fa31c6596b435_parameter_optimizer_py.html#t6">aretomo3_gui/core/automation/parameter_optimizer.py</a></td>
                <td class="name left"><a href="z_f99fa31c6596b435_parameter_optimizer_py.html#t6"><data value='ParameterOptimizer'>ParameterOptimizer</data></a></td>
                <td>17</td>
                <td>17</td>
                <td>0</td>
                <td class="right" data-ratio="0 17">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f99fa31c6596b435_parameter_optimizer_py.html">aretomo3_gui/core/automation/parameter_optimizer.py</a></td>
                <td class="name left"><a href="z_f99fa31c6596b435_parameter_optimizer_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f99fa31c6596b435_quality_predictor_py.html#t8">aretomo3_gui/core/automation/quality_predictor.py</a></td>
                <td class="name left"><a href="z_f99fa31c6596b435_quality_predictor_py.html#t8"><data value='QualityPredictor'>QualityPredictor</data></a></td>
                <td>10</td>
                <td>10</td>
                <td>0</td>
                <td class="right" data-ratio="0 10">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f99fa31c6596b435_quality_predictor_py.html">aretomo3_gui/core/automation/quality_predictor.py</a></td>
                <td class="name left"><a href="z_f99fa31c6596b435_quality_predictor_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>5</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="0 5">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f99fa31c6596b435_workflow_manager_py.html#t23">aretomo3_gui/core/automation/workflow_manager.py</a></td>
                <td class="name left"><a href="z_f99fa31c6596b435_workflow_manager_py.html#t23"><data value='WorkflowStatus'>WorkflowStatus</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f99fa31c6596b435_workflow_manager_py.html#t33">aretomo3_gui/core/automation/workflow_manager.py</a></td>
                <td class="name left"><a href="z_f99fa31c6596b435_workflow_manager_py.html#t33"><data value='DatasetType'>DatasetType</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f99fa31c6596b435_workflow_manager_py.html#t47">aretomo3_gui/core/automation/workflow_manager.py</a></td>
                <td class="name left"><a href="z_f99fa31c6596b435_workflow_manager_py.html#t47"><data value='DatasetInfo'>DatasetInfo</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f99fa31c6596b435_workflow_manager_py.html#t68">aretomo3_gui/core/automation/workflow_manager.py</a></td>
                <td class="name left"><a href="z_f99fa31c6596b435_workflow_manager_py.html#t68"><data value='WorkflowTask'>WorkflowTask</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f99fa31c6596b435_workflow_manager_py.html#t89">aretomo3_gui/core/automation/workflow_manager.py</a></td>
                <td class="name left"><a href="z_f99fa31c6596b435_workflow_manager_py.html#t89"><data value='WorkflowManager'>WorkflowManager</data></a></td>
                <td>189</td>
                <td>189</td>
                <td>0</td>
                <td class="right" data-ratio="0 189">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f99fa31c6596b435_workflow_manager_py.html">aretomo3_gui/core/automation/workflow_manager.py</a></td>
                <td class="name left"><a href="z_f99fa31c6596b435_workflow_manager_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>77</td>
                <td>77</td>
                <td>0</td>
                <td class="right" data-ratio="0 77">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f984836ddf12effb_backup_system_py.html#t25">aretomo3_gui/core/backup_system.py</a></td>
                <td class="name left"><a href="z_f984836ddf12effb_backup_system_py.html#t25"><data value='BackupMetadata'>BackupMetadata</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f984836ddf12effb_backup_system_py.html#t40">aretomo3_gui/core/backup_system.py</a></td>
                <td class="name left"><a href="z_f984836ddf12effb_backup_system_py.html#t40"><data value='BackupSystem'>BackupSystem</data></a></td>
                <td>203</td>
                <td>203</td>
                <td>0</td>
                <td class="right" data-ratio="0 203">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f984836ddf12effb_backup_system_py.html">aretomo3_gui/core/backup_system.py</a></td>
                <td class="name left"><a href="z_f984836ddf12effb_backup_system_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>49</td>
                <td>49</td>
                <td>0</td>
                <td class="right" data-ratio="0 49">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_61fb54b6db731140___init___py.html">aretomo3_gui/core/config/__init__.py</a></td>
                <td class="name left"><a href="z_61fb54b6db731140___init___py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>3</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="3 3">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_61fb54b6db731140_config_py.html">aretomo3_gui/core/config/config.py</a></td>
                <td class="name left"><a href="z_61fb54b6db731140_config_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>12</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="12 12">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_61fb54b6db731140_config_manager_py.html#t14">aretomo3_gui/core/config/config_manager.py</a></td>
                <td class="name left"><a href="z_61fb54b6db731140_config_manager_py.html#t14"><data value='MicroscopeProfile'>MicroscopeProfile</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_61fb54b6db731140_config_manager_py.html#t27">aretomo3_gui/core/config/config_manager.py</a></td>
                <td class="name left"><a href="z_61fb54b6db731140_config_manager_py.html#t27"><data value='ProcessingPreset'>ProcessingPreset</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_61fb54b6db731140_config_manager_py.html#t36">aretomo3_gui/core/config/config_manager.py</a></td>
                <td class="name left"><a href="z_61fb54b6db731140_config_manager_py.html#t36"><data value='ConfigManager'>ConfigManager</data></a></td>
                <td>102</td>
                <td>102</td>
                <td>0</td>
                <td class="right" data-ratio="0 102">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_61fb54b6db731140_config_manager_py.html">aretomo3_gui/core/config/config_manager.py</a></td>
                <td class="name left"><a href="z_61fb54b6db731140_config_manager_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>43</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="43 43">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_61fb54b6db731140_config_validation_py.html#t16">aretomo3_gui/core/config/config_validation.py</a></td>
                <td class="name left"><a href="z_61fb54b6db731140_config_validation_py.html#t16"><data value='AreTomo3Config'>AreTomo3Config</data></a></td>
                <td>19</td>
                <td>19</td>
                <td>0</td>
                <td class="right" data-ratio="0 19">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_61fb54b6db731140_config_validation_py.html">aretomo3_gui/core/config/config_validation.py</a></td>
                <td class="name left"><a href="z_61fb54b6db731140_config_validation_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>87</td>
                <td>87</td>
                <td>0</td>
                <td class="right" data-ratio="0 87">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_61fb54b6db731140_template_manager_py.html#t15">aretomo3_gui/core/config/template_manager.py</a></td>
                <td class="name left"><a href="z_61fb54b6db731140_template_manager_py.html#t15"><data value='ProcessingTemplate'>ProcessingTemplate</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_61fb54b6db731140_template_manager_py.html#t25">aretomo3_gui/core/config/template_manager.py</a></td>
                <td class="name left"><a href="z_61fb54b6db731140_template_manager_py.html#t25"><data value='ConfigManager'>ConfigManager</data></a></td>
                <td>30</td>
                <td>30</td>
                <td>0</td>
                <td class="right" data-ratio="0 30">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_61fb54b6db731140_template_manager_py.html">aretomo3_gui/core/config/template_manager.py</a></td>
                <td class="name left"><a href="z_61fb54b6db731140_template_manager_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>22</td>
                <td>22</td>
                <td>0</td>
                <td class="right" data-ratio="0 22">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f984836ddf12effb_config_manager_py.html#t22">aretomo3_gui/core/config_manager.py</a></td>
                <td class="name left"><a href="z_f984836ddf12effb_config_manager_py.html#t22"><data value='ConfigFormat'>ConfigFormat</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f984836ddf12effb_config_manager_py.html#t31">aretomo3_gui/core/config_manager.py</a></td>
                <td class="name left"><a href="z_f984836ddf12effb_config_manager_py.html#t31"><data value='ConfigProfile'>ConfigProfile</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f984836ddf12effb_config_manager_py.html#t43">aretomo3_gui/core/config_manager.py</a></td>
                <td class="name left"><a href="z_f984836ddf12effb_config_manager_py.html#t43"><data value='ConfigManager'>ConfigManager</data></a></td>
                <td>189</td>
                <td>189</td>
                <td>0</td>
                <td class="right" data-ratio="0 189">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f984836ddf12effb_config_manager_py.html">aretomo3_gui/core/config_manager.py</a></td>
                <td class="name left"><a href="z_f984836ddf12effb_config_manager_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>48</td>
                <td>48</td>
                <td>0</td>
                <td class="right" data-ratio="0 48">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f984836ddf12effb_continue_mode_manager_py.html#t20">aretomo3_gui/core/continue_mode_manager.py</a></td>
                <td class="name left"><a href="z_f984836ddf12effb_continue_mode_manager_py.html#t20"><data value='ProcessingState'>ProcessingState</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f984836ddf12effb_continue_mode_manager_py.html#t32">aretomo3_gui/core/continue_mode_manager.py</a></td>
                <td class="name left"><a href="z_f984836ddf12effb_continue_mode_manager_py.html#t32"><data value='ContinueSession'>ContinueSession</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f984836ddf12effb_continue_mode_manager_py.html#t51">aretomo3_gui/core/continue_mode_manager.py</a></td>
                <td class="name left"><a href="z_f984836ddf12effb_continue_mode_manager_py.html#t51"><data value='ContinueModeManager'>ContinueModeManager</data></a></td>
                <td>191</td>
                <td>191</td>
                <td>0</td>
                <td class="right" data-ratio="0 191">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f984836ddf12effb_continue_mode_manager_py.html">aretomo3_gui/core/continue_mode_manager.py</a></td>
                <td class="name left"><a href="z_f984836ddf12effb_continue_mode_manager_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>52</td>
                <td>52</td>
                <td>0</td>
                <td class="right" data-ratio="0 52">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f984836ddf12effb_data_validation_py.html#t22">aretomo3_gui/core/data_validation.py</a></td>
                <td class="name left"><a href="z_f984836ddf12effb_data_validation_py.html#t22"><data value='ValidationSeverity'>ValidationSeverity</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f984836ddf12effb_data_validation_py.html#t32">aretomo3_gui/core/data_validation.py</a></td>
                <td class="name left"><a href="z_f984836ddf12effb_data_validation_py.html#t32"><data value='ValidationResult'>ValidationResult</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f984836ddf12effb_data_validation_py.html#t45">aretomo3_gui/core/data_validation.py</a></td>
                <td class="name left"><a href="z_f984836ddf12effb_data_validation_py.html#t45"><data value='ValidationReport'>ValidationReport</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f984836ddf12effb_data_validation_py.html#t75">aretomo3_gui/core/data_validation.py</a></td>
                <td class="name left"><a href="z_f984836ddf12effb_data_validation_py.html#t75"><data value='DataValidator'>DataValidator</data></a></td>
                <td>152</td>
                <td>152</td>
                <td>0</td>
                <td class="right" data-ratio="0 152">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f984836ddf12effb_data_validation_py.html">aretomo3_gui/core/data_validation.py</a></td>
                <td class="name left"><a href="z_f984836ddf12effb_data_validation_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>52</td>
                <td>52</td>
                <td>0</td>
                <td class="right" data-ratio="0 52">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f984836ddf12effb_database_manager_py.html#t21">aretomo3_gui/core/database_manager.py</a></td>
                <td class="name left"><a href="z_f984836ddf12effb_database_manager_py.html#t21"><data value='ProcessingRecord'>ProcessingRecord</data></a></td>
                <td>8</td>
                <td>8</td>
                <td>0</td>
                <td class="right" data-ratio="0 8">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f984836ddf12effb_database_manager_py.html#t50">aretomo3_gui/core/database_manager.py</a></td>
                <td class="name left"><a href="z_f984836ddf12effb_database_manager_py.html#t50"><data value='AnalysisRecord'>AnalysisRecord</data></a></td>
                <td>10</td>
                <td>10</td>
                <td>0</td>
                <td class="right" data-ratio="0 10">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f984836ddf12effb_database_manager_py.html#t76">aretomo3_gui/core/database_manager.py</a></td>
                <td class="name left"><a href="z_f984836ddf12effb_database_manager_py.html#t76"><data value='DatabaseManager'>DatabaseManager</data></a></td>
                <td>164</td>
                <td>164</td>
                <td>0</td>
                <td class="right" data-ratio="0 164">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f984836ddf12effb_database_manager_py.html">aretomo3_gui/core/database_manager.py</a></td>
                <td class="name left"><a href="z_f984836ddf12effb_database_manager_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>55</td>
                <td>55</td>
                <td>0</td>
                <td class="right" data-ratio="0 55">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f984836ddf12effb_dependency_check_py.html">aretomo3_gui/core/dependency_check.py</a></td>
                <td class="name left"><a href="z_f984836ddf12effb_dependency_check_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>78</td>
                <td>78</td>
                <td>3</td>
                <td class="right" data-ratio="0 78">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f984836ddf12effb_enhanced_database_manager_py.html#t23">aretomo3_gui/core/enhanced_database_manager.py</a></td>
                <td class="name left"><a href="z_f984836ddf12effb_enhanced_database_manager_py.html#t23"><data value='ConnectionInfo'>ConnectionInfo</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f984836ddf12effb_enhanced_database_manager_py.html#t33">aretomo3_gui/core/enhanced_database_manager.py</a></td>
                <td class="name left"><a href="z_f984836ddf12effb_enhanced_database_manager_py.html#t33"><data value='TransactionManager'>TransactionManager</data></a></td>
                <td>19</td>
                <td>19</td>
                <td>0</td>
                <td class="right" data-ratio="0 19">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f984836ddf12effb_enhanced_database_manager_py.html#t67">aretomo3_gui/core/enhanced_database_manager.py</a></td>
                <td class="name left"><a href="z_f984836ddf12effb_enhanced_database_manager_py.html#t67"><data value='ConnectionPool'>ConnectionPool</data></a></td>
                <td>97</td>
                <td>97</td>
                <td>0</td>
                <td class="right" data-ratio="0 97">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f984836ddf12effb_enhanced_database_manager_py.html#t250">aretomo3_gui/core/enhanced_database_manager.py</a></td>
                <td class="name left"><a href="z_f984836ddf12effb_enhanced_database_manager_py.html#t250"><data value='EnhancedDatabaseManager'>EnhancedDatabaseManager</data></a></td>
                <td>37</td>
                <td>37</td>
                <td>0</td>
                <td class="right" data-ratio="0 37">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f984836ddf12effb_enhanced_database_manager_py.html#t423">aretomo3_gui/core/enhanced_database_manager.py</a></td>
                <td class="name left"><a href="z_f984836ddf12effb_enhanced_database_manager_py.html#t423"><data value='DatabaseConnectionContext'>DatabaseConnectionContext</data></a></td>
                <td>8</td>
                <td>8</td>
                <td>0</td>
                <td class="right" data-ratio="0 8">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f984836ddf12effb_enhanced_database_manager_py.html">aretomo3_gui/core/enhanced_database_manager.py</a></td>
                <td class="name left"><a href="z_f984836ddf12effb_enhanced_database_manager_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>46</td>
                <td>46</td>
                <td>0</td>
                <td class="right" data-ratio="0 46">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f984836ddf12effb_enhanced_parameters_py.html#t15">aretomo3_gui/core/enhanced_parameters.py</a></td>
                <td class="name left"><a href="z_f984836ddf12effb_enhanced_parameters_py.html#t15"><data value='ParameterCategory'>ParameterCategory</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f984836ddf12effb_enhanced_parameters_py.html#t26">aretomo3_gui/core/enhanced_parameters.py</a></td>
                <td class="name left"><a href="z_f984836ddf12effb_enhanced_parameters_py.html#t26"><data value='ParameterType'>ParameterType</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f984836ddf12effb_enhanced_parameters_py.html#t40">aretomo3_gui/core/enhanced_parameters.py</a></td>
                <td class="name left"><a href="z_f984836ddf12effb_enhanced_parameters_py.html#t40"><data value='ParameterDefinition'>ParameterDefinition</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f984836ddf12effb_enhanced_parameters_py.html#t62">aretomo3_gui/core/enhanced_parameters.py</a></td>
                <td class="name left"><a href="z_f984836ddf12effb_enhanced_parameters_py.html#t62"><data value='EnhancedParameterManager'>EnhancedParameterManager</data></a></td>
                <td>106</td>
                <td>106</td>
                <td>0</td>
                <td class="right" data-ratio="0 106">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f984836ddf12effb_enhanced_parameters_py.html">aretomo3_gui/core/enhanced_parameters.py</a></td>
                <td class="name left"><a href="z_f984836ddf12effb_enhanced_parameters_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>48</td>
                <td>48</td>
                <td>0</td>
                <td class="right" data-ratio="0 48">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f984836ddf12effb_enhanced_realtime_processor_py.html#t19">aretomo3_gui/core/enhanced_realtime_processor.py</a></td>
                <td class="name left"><a href="z_f984836ddf12effb_enhanced_realtime_processor_py.html#t19"><data value='EnhancedRealTimeProcessor'>EnhancedRealTimeProcessor</data></a></td>
                <td>72</td>
                <td>72</td>
                <td>0</td>
                <td class="right" data-ratio="0 72">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f984836ddf12effb_enhanced_realtime_processor_py.html#t179">aretomo3_gui/core/enhanced_realtime_processor.py</a></td>
                <td class="name left"><a href="z_f984836ddf12effb_enhanced_realtime_processor_py.html#t179"><data value='EnhancedTiltSeriesEventHandler'>EnhancedTiltSeriesEventHandler</data></a></td>
                <td>19</td>
                <td>19</td>
                <td>0</td>
                <td class="right" data-ratio="0 19">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f984836ddf12effb_enhanced_realtime_processor_py.html">aretomo3_gui/core/enhanced_realtime_processor.py</a></td>
                <td class="name left"><a href="z_f984836ddf12effb_enhanced_realtime_processor_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>27</td>
                <td>27</td>
                <td>0</td>
                <td class="right" data-ratio="0 27">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f984836ddf12effb_error_handling_py.html#t88">aretomo3_gui/core/error_handling.py</a></td>
                <td class="name left"><a href="z_f984836ddf12effb_error_handling_py.html#t88"><data value='ErrorSeverity'>ErrorSeverity</data></a></td>
                <td>12</td>
                <td>12</td>
                <td>0</td>
                <td class="right" data-ratio="0 12">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f984836ddf12effb_error_handling_py.html#t134">aretomo3_gui/core/error_handling.py</a></td>
                <td class="name left"><a href="z_f984836ddf12effb_error_handling_py.html#t134"><data value='AreTomo3Error'>AreTomo3Error</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f984836ddf12effb_error_handling_py.html#t172">aretomo3_gui/core/error_handling.py</a></td>
                <td class="name left"><a href="z_f984836ddf12effb_error_handling_py.html#t172"><data value='ProcessingError'>ProcessingError</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f984836ddf12effb_error_handling_py.html#t186">aretomo3_gui/core/error_handling.py</a></td>
                <td class="name left"><a href="z_f984836ddf12effb_error_handling_py.html#t186"><data value='FileSystemError'>FileSystemError</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f984836ddf12effb_error_handling_py.html#t200">aretomo3_gui/core/error_handling.py</a></td>
                <td class="name left"><a href="z_f984836ddf12effb_error_handling_py.html#t200"><data value='GPUError'>GPUError</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f984836ddf12effb_error_handling_py.html">aretomo3_gui/core/error_handling.py</a></td>
                <td class="name left"><a href="z_f984836ddf12effb_error_handling_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>90</td>
                <td>54</td>
                <td>0</td>
                <td class="right" data-ratio="36 90">40%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f984836ddf12effb_error_recovery_py.html#t21">aretomo3_gui/core/error_recovery.py</a></td>
                <td class="name left"><a href="z_f984836ddf12effb_error_recovery_py.html#t21"><data value='ErrorSeverity'>ErrorSeverity</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f984836ddf12effb_error_recovery_py.html#t31">aretomo3_gui/core/error_recovery.py</a></td>
                <td class="name left"><a href="z_f984836ddf12effb_error_recovery_py.html#t31"><data value='ErrorRecord'>ErrorRecord</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f984836ddf12effb_error_recovery_py.html#t47">aretomo3_gui/core/error_recovery.py</a></td>
                <td class="name left"><a href="z_f984836ddf12effb_error_recovery_py.html#t47"><data value='ErrorRecoverySystem'>ErrorRecoverySystem</data></a></td>
                <td>161</td>
                <td>161</td>
                <td>0</td>
                <td class="right" data-ratio="0 161">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f984836ddf12effb_error_recovery_py.html">aretomo3_gui/core/error_recovery.py</a></td>
                <td class="name left"><a href="z_f984836ddf12effb_error_recovery_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>64</td>
                <td>64</td>
                <td>0</td>
                <td class="right" data-ratio="0 64">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f984836ddf12effb_file_organization_py.html#t22">aretomo3_gui/core/file_organization.py</a></td>
                <td class="name left"><a href="z_f984836ddf12effb_file_organization_py.html#t22"><data value='FileMetadata'>FileMetadata</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f984836ddf12effb_file_organization_py.html#t43">aretomo3_gui/core/file_organization.py</a></td>
                <td class="name left"><a href="z_f984836ddf12effb_file_organization_py.html#t43"><data value='FileOrganizer'>FileOrganizer</data></a></td>
                <td>181</td>
                <td>181</td>
                <td>0</td>
                <td class="right" data-ratio="0 181">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f984836ddf12effb_file_organization_py.html">aretomo3_gui/core/file_organization.py</a></td>
                <td class="name left"><a href="z_f984836ddf12effb_file_organization_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>43</td>
                <td>43</td>
                <td>0</td>
                <td class="right" data-ratio="0 43">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f984836ddf12effb_file_watcher_py.html#t17">aretomo3_gui/core/file_watcher.py</a></td>
                <td class="name left"><a href="z_f984836ddf12effb_file_watcher_py.html#t17"><data value='TiltSeriesStatus'>TiltSeriesStatus</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f984836ddf12effb_file_watcher_py.html#t38">aretomo3_gui/core/file_watcher.py</a></td>
                <td class="name left"><a href="z_f984836ddf12effb_file_watcher_py.html#t38"><data value='FileEvent'>FileEvent</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f984836ddf12effb_file_watcher_py.html#t46">aretomo3_gui/core/file_watcher.py</a></td>
                <td class="name left"><a href="z_f984836ddf12effb_file_watcher_py.html#t46"><data value='FileWatcher'>FileWatcher</data></a></td>
                <td>65</td>
                <td>65</td>
                <td>0</td>
                <td class="right" data-ratio="0 65">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f984836ddf12effb_file_watcher_py.html">aretomo3_gui/core/file_watcher.py</a></td>
                <td class="name left"><a href="z_f984836ddf12effb_file_watcher_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>33</td>
                <td>33</td>
                <td>0</td>
                <td class="right" data-ratio="0 33">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f984836ddf12effb_logging_config_py.html#t74">aretomo3_gui/core/logging_config.py</a></td>
                <td class="name left"><a href="z_f984836ddf12effb_logging_config_py.html#t74"><data value='LogFilter'>LogFilter</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f984836ddf12effb_logging_config_py.html">aretomo3_gui/core/logging_config.py</a></td>
                <td class="name left"><a href="z_f984836ddf12effb_logging_config_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>103</td>
                <td>103</td>
                <td>3</td>
                <td class="right" data-ratio="0 103">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f984836ddf12effb_memory_manager_py.html#t25">aretomo3_gui/core/memory_manager.py</a></td>
                <td class="name left"><a href="z_f984836ddf12effb_memory_manager_py.html#t25"><data value='MemorySnapshot'>MemorySnapshot</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f984836ddf12effb_memory_manager_py.html#t38">aretomo3_gui/core/memory_manager.py</a></td>
                <td class="name left"><a href="z_f984836ddf12effb_memory_manager_py.html#t38"><data value='ObjectTracker'>ObjectTracker</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f984836ddf12effb_memory_manager_py.html#t48">aretomo3_gui/core/memory_manager.py</a></td>
                <td class="name left"><a href="z_f984836ddf12effb_memory_manager_py.html#t48"><data value='MemoryAwareCache'>MemoryAwareCache</data></a></td>
                <td>30</td>
                <td>30</td>
                <td>0</td>
                <td class="right" data-ratio="0 30">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f984836ddf12effb_memory_manager_py.html#t123">aretomo3_gui/core/memory_manager.py</a></td>
                <td class="name left"><a href="z_f984836ddf12effb_memory_manager_py.html#t123"><data value='MemoryLeakDetector'>MemoryLeakDetector</data></a></td>
                <td>67</td>
                <td>67</td>
                <td>0</td>
                <td class="right" data-ratio="0 67">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f984836ddf12effb_memory_manager_py.html#t265">aretomo3_gui/core/memory_manager.py</a></td>
                <td class="name left"><a href="z_f984836ddf12effb_memory_manager_py.html#t265"><data value='MemoryManager'>MemoryManager</data></a></td>
                <td>78</td>
                <td>78</td>
                <td>0</td>
                <td class="right" data-ratio="0 78">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f984836ddf12effb_memory_manager_py.html">aretomo3_gui/core/memory_manager.py</a></td>
                <td class="name left"><a href="z_f984836ddf12effb_memory_manager_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>66</td>
                <td>66</td>
                <td>0</td>
                <td class="right" data-ratio="0 66">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f984836ddf12effb_multi_format_handler_py.html#t17">aretomo3_gui/core/multi_format_handler.py</a></td>
                <td class="name left"><a href="z_f984836ddf12effb_multi_format_handler_py.html#t17"><data value='InputFormat'>InputFormat</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f984836ddf12effb_multi_format_handler_py.html#t30">aretomo3_gui/core/multi_format_handler.py</a></td>
                <td class="name left"><a href="z_f984836ddf12effb_multi_format_handler_py.html#t30"><data value='TiltSeriesInfo'>TiltSeriesInfo</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f984836ddf12effb_multi_format_handler_py.html#t45">aretomo3_gui/core/multi_format_handler.py</a></td>
                <td class="name left"><a href="z_f984836ddf12effb_multi_format_handler_py.html#t45"><data value='MultiFormatHandler'>MultiFormatHandler</data></a></td>
                <td>149</td>
                <td>149</td>
                <td>0</td>
                <td class="right" data-ratio="0 149">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f984836ddf12effb_multi_format_handler_py.html">aretomo3_gui/core/multi_format_handler.py</a></td>
                <td class="name left"><a href="z_f984836ddf12effb_multi_format_handler_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>43</td>
                <td>43</td>
                <td>0</td>
                <td class="right" data-ratio="0 43">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f984836ddf12effb_performance_monitor_py.html#t22">aretomo3_gui/core/performance_monitor.py</a></td>
                <td class="name left"><a href="z_f984836ddf12effb_performance_monitor_py.html#t22"><data value='PerformanceMetrics'>PerformanceMetrics</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f984836ddf12effb_performance_monitor_py.html#t39">aretomo3_gui/core/performance_monitor.py</a></td>
                <td class="name left"><a href="z_f984836ddf12effb_performance_monitor_py.html#t39"><data value='OptimizationSettings'>OptimizationSettings</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f984836ddf12effb_performance_monitor_py.html#t53">aretomo3_gui/core/performance_monitor.py</a></td>
                <td class="name left"><a href="z_f984836ddf12effb_performance_monitor_py.html#t53"><data value='PerformanceMonitor'>PerformanceMonitor</data></a></td>
                <td>122</td>
                <td>122</td>
                <td>0</td>
                <td class="right" data-ratio="0 122">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f984836ddf12effb_performance_monitor_py.html">aretomo3_gui/core/performance_monitor.py</a></td>
                <td class="name left"><a href="z_f984836ddf12effb_performance_monitor_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>53</td>
                <td>53</td>
                <td>0</td>
                <td class="right" data-ratio="0 53">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f984836ddf12effb_performance_optimizer_py.html#t26">aretomo3_gui/core/performance_optimizer.py</a></td>
                <td class="name left"><a href="z_f984836ddf12effb_performance_optimizer_py.html#t26"><data value='PerformanceMetrics'>PerformanceMetrics</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f984836ddf12effb_performance_optimizer_py.html#t44">aretomo3_gui/core/performance_optimizer.py</a></td>
                <td class="name left"><a href="z_f984836ddf12effb_performance_optimizer_py.html#t44"><data value='OptimizationRule'>OptimizationRule</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f984836ddf12effb_performance_optimizer_py.html#t56">aretomo3_gui/core/performance_optimizer.py</a></td>
                <td class="name left"><a href="z_f984836ddf12effb_performance_optimizer_py.html#t56"><data value='PerformanceOptimizer'>PerformanceOptimizer</data></a></td>
                <td>178</td>
                <td>178</td>
                <td>0</td>
                <td class="right" data-ratio="0 178">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f984836ddf12effb_performance_optimizer_py.html">aretomo3_gui/core/performance_optimizer.py</a></td>
                <td class="name left"><a href="z_f984836ddf12effb_performance_optimizer_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>83</td>
                <td>83</td>
                <td>0</td>
                <td class="right" data-ratio="0 83">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f984836ddf12effb_plugin_system_py.html#t22">aretomo3_gui/core/plugin_system.py</a></td>
                <td class="name left"><a href="z_f984836ddf12effb_plugin_system_py.html#t22"><data value='PluginType'>PluginType</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f984836ddf12effb_plugin_system_py.html#t36">aretomo3_gui/core/plugin_system.py</a></td>
                <td class="name left"><a href="z_f984836ddf12effb_plugin_system_py.html#t36"><data value='PluginMetadata'>PluginMetadata</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f984836ddf12effb_plugin_system_py.html#t51">aretomo3_gui/core/plugin_system.py</a></td>
                <td class="name left"><a href="z_f984836ddf12effb_plugin_system_py.html#t51"><data value='PluginInterface'>PluginInterface</data></a></td>
                <td>8</td>
                <td>8</td>
                <td>31</td>
                <td class="right" data-ratio="0 8">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f984836ddf12effb_plugin_system_py.html#t133">aretomo3_gui/core/plugin_system.py</a></td>
                <td class="name left"><a href="z_f984836ddf12effb_plugin_system_py.html#t133"><data value='AnalysisPlugin'>AnalysisPlugin</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>11</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f984836ddf12effb_plugin_system_py.html#t151">aretomo3_gui/core/plugin_system.py</a></td>
                <td class="name left"><a href="z_f984836ddf12effb_plugin_system_py.html#t151"><data value='VisualizationPlugin'>VisualizationPlugin</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>11</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f984836ddf12effb_plugin_system_py.html#t169">aretomo3_gui/core/plugin_system.py</a></td>
                <td class="name left"><a href="z_f984836ddf12effb_plugin_system_py.html#t169"><data value='ProcessingPlugin'>ProcessingPlugin</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>11</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f984836ddf12effb_plugin_system_py.html#t187">aretomo3_gui/core/plugin_system.py</a></td>
                <td class="name left"><a href="z_f984836ddf12effb_plugin_system_py.html#t187"><data value='PluginManager'>PluginManager</data></a></td>
                <td>161</td>
                <td>161</td>
                <td>0</td>
                <td class="right" data-ratio="0 161">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f984836ddf12effb_plugin_system_py.html">aretomo3_gui/core/plugin_system.py</a></td>
                <td class="name left"><a href="z_f984836ddf12effb_plugin_system_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>59</td>
                <td>59</td>
                <td>14</td>
                <td class="right" data-ratio="0 59">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f984836ddf12effb_realtime_processor_py.html#t35">aretomo3_gui/core/realtime_processor.py</a></td>
                <td class="name left"><a href="z_f984836ddf12effb_realtime_processor_py.html#t35"><data value='ProcessingJob'>ProcessingJob</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f984836ddf12effb_realtime_processor_py.html#t50">aretomo3_gui/core/realtime_processor.py</a></td>
                <td class="name left"><a href="z_f984836ddf12effb_realtime_processor_py.html#t50"><data value='ProcessingStats'>ProcessingStats</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f984836ddf12effb_realtime_processor_py.html#t62">aretomo3_gui/core/realtime_processor.py</a></td>
                <td class="name left"><a href="z_f984836ddf12effb_realtime_processor_py.html#t62"><data value='RealTimeFileHandler'>RealTimeFileHandler</data></a></td>
                <td>37</td>
                <td>37</td>
                <td>0</td>
                <td class="right" data-ratio="0 37">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f984836ddf12effb_realtime_processor_py.html#t130">aretomo3_gui/core/realtime_processor.py</a></td>
                <td class="name left"><a href="z_f984836ddf12effb_realtime_processor_py.html#t130"><data value='RealTimeProcessor'>RealTimeProcessor</data></a></td>
                <td>101</td>
                <td>101</td>
                <td>0</td>
                <td class="right" data-ratio="0 101">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f984836ddf12effb_realtime_processor_py.html">aretomo3_gui/core/realtime_processor.py</a></td>
                <td class="name left"><a href="z_f984836ddf12effb_realtime_processor_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>57</td>
                <td>57</td>
                <td>0</td>
                <td class="right" data-ratio="0 57">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f984836ddf12effb_resource_manager_py.html#t56">aretomo3_gui/core/resource_manager.py</a></td>
                <td class="name left"><a href="z_f984836ddf12effb_resource_manager_py.html#t56"><data value='ResourceMonitor'>ResourceMonitor</data></a></td>
                <td>30</td>
                <td>30</td>
                <td>0</td>
                <td class="right" data-ratio="0 30">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f984836ddf12effb_resource_manager_py.html#t180">aretomo3_gui/core/resource_manager.py</a></td>
                <td class="name left"><a href="z_f984836ddf12effb_resource_manager_py.html#t180"><data value='FileManager'>FileManager</data></a></td>
                <td>62</td>
                <td>62</td>
                <td>0</td>
                <td class="right" data-ratio="0 62">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f984836ddf12effb_resource_manager_py.html#t412">aretomo3_gui/core/resource_manager.py</a></td>
                <td class="name left"><a href="z_f984836ddf12effb_resource_manager_py.html#t412"><data value='ResourceManager'>ResourceManager</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f984836ddf12effb_resource_manager_py.html">aretomo3_gui/core/resource_manager.py</a></td>
                <td class="name left"><a href="z_f984836ddf12effb_resource_manager_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>44</td>
                <td>8</td>
                <td>0</td>
                <td class="right" data-ratio="36 44">82%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f984836ddf12effb_results_tracker_py.html#t20">aretomo3_gui/core/results_tracker.py</a></td>
                <td class="name left"><a href="z_f984836ddf12effb_results_tracker_py.html#t20"><data value='ProcessingResult'>ProcessingResult</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f984836ddf12effb_results_tracker_py.html#t56">aretomo3_gui/core/results_tracker.py</a></td>
                <td class="name left"><a href="z_f984836ddf12effb_results_tracker_py.html#t56"><data value='ResultsTracker'>ResultsTracker</data></a></td>
                <td>138</td>
                <td>138</td>
                <td>0</td>
                <td class="right" data-ratio="0 138">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f984836ddf12effb_results_tracker_py.html">aretomo3_gui/core/results_tracker.py</a></td>
                <td class="name left"><a href="z_f984836ddf12effb_results_tracker_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>43</td>
                <td>43</td>
                <td>0</td>
                <td class="right" data-ratio="0 43">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f984836ddf12effb_secure_web_api_py.html#t28">aretomo3_gui/core/secure_web_api.py</a></td>
                <td class="name left"><a href="z_f984836ddf12effb_secure_web_api_py.html#t28"><data value='SecurityConfig'>SecurityConfig</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f984836ddf12effb_secure_web_api_py.html#t46">aretomo3_gui/core/secure_web_api.py</a></td>
                <td class="name left"><a href="z_f984836ddf12effb_secure_web_api_py.html#t46"><data value='RateLimitInfo'>RateLimitInfo</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f984836ddf12effb_secure_web_api_py.html#t53">aretomo3_gui/core/secure_web_api.py</a></td>
                <td class="name left"><a href="z_f984836ddf12effb_secure_web_api_py.html#t53"><data value='SecurityManager'>SecurityManager</data></a></td>
                <td>56</td>
                <td>56</td>
                <td>0</td>
                <td class="right" data-ratio="0 56">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f984836ddf12effb_secure_web_api_py.html#t179">aretomo3_gui/core/secure_web_api.py</a></td>
                <td class="name left"><a href="z_f984836ddf12effb_secure_web_api_py.html#t179"><data value='SecureWebAPI'>SecureWebAPI</data></a></td>
                <td>99</td>
                <td>99</td>
                <td>0</td>
                <td class="right" data-ratio="0 99">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f984836ddf12effb_secure_web_api_py.html">aretomo3_gui/core/secure_web_api.py</a></td>
                <td class="name left"><a href="z_f984836ddf12effb_secure_web_api_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>49</td>
                <td>49</td>
                <td>5</td>
                <td class="right" data-ratio="0 49">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f984836ddf12effb_security_framework_py.html#t27">aretomo3_gui/core/security_framework.py</a></td>
                <td class="name left"><a href="z_f984836ddf12effb_security_framework_py.html#t27"><data value='SecurityConfig'>SecurityConfig</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f984836ddf12effb_security_framework_py.html#t43">aretomo3_gui/core/security_framework.py</a></td>
                <td class="name left"><a href="z_f984836ddf12effb_security_framework_py.html#t43"><data value='UserSession'>UserSession</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f984836ddf12effb_security_framework_py.html#t56">aretomo3_gui/core/security_framework.py</a></td>
                <td class="name left"><a href="z_f984836ddf12effb_security_framework_py.html#t56"><data value='AuditEvent'>AuditEvent</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f984836ddf12effb_security_framework_py.html#t68">aretomo3_gui/core/security_framework.py</a></td>
                <td class="name left"><a href="z_f984836ddf12effb_security_framework_py.html#t68"><data value='SecurityFramework'>SecurityFramework</data></a></td>
                <td>164</td>
                <td>164</td>
                <td>0</td>
                <td class="right" data-ratio="0 164">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f984836ddf12effb_security_framework_py.html">aretomo3_gui/core/security_framework.py</a></td>
                <td class="name left"><a href="z_f984836ddf12effb_security_framework_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>82</td>
                <td>82</td>
                <td>0</td>
                <td class="right" data-ratio="0 82">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f984836ddf12effb_session_manager_py.html#t20">aretomo3_gui/core/session_manager.py</a></td>
                <td class="name left"><a href="z_f984836ddf12effb_session_manager_py.html#t20"><data value='ProcessingSession'>ProcessingSession</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f984836ddf12effb_session_manager_py.html#t42">aretomo3_gui/core/session_manager.py</a></td>
                <td class="name left"><a href="z_f984836ddf12effb_session_manager_py.html#t42"><data value='SessionManager'>SessionManager</data></a></td>
                <td>129</td>
                <td>129</td>
                <td>0</td>
                <td class="right" data-ratio="0 129">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f984836ddf12effb_session_manager_py.html">aretomo3_gui/core/session_manager.py</a></td>
                <td class="name left"><a href="z_f984836ddf12effb_session_manager_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>45</td>
                <td>45</td>
                <td>0</td>
                <td class="right" data-ratio="0 45">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f984836ddf12effb_system_integration_py.html#t19">aretomo3_gui/core/system_integration.py</a></td>
                <td class="name left"><a href="z_f984836ddf12effb_system_integration_py.html#t19"><data value='SystemComponent'>SystemComponent</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f984836ddf12effb_system_integration_py.html#t30">aretomo3_gui/core/system_integration.py</a></td>
                <td class="name left"><a href="z_f984836ddf12effb_system_integration_py.html#t30"><data value='SystemIntegrationManager'>SystemIntegrationManager</data></a></td>
                <td>107</td>
                <td>107</td>
                <td>0</td>
                <td class="right" data-ratio="0 107">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f984836ddf12effb_system_integration_py.html">aretomo3_gui/core/system_integration.py</a></td>
                <td class="name left"><a href="z_f984836ddf12effb_system_integration_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>29</td>
                <td>29</td>
                <td>0</td>
                <td class="right" data-ratio="0 29">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f984836ddf12effb_system_monitor_py.html#t16">aretomo3_gui/core/system_monitor.py</a></td>
                <td class="name left"><a href="z_f984836ddf12effb_system_monitor_py.html#t16"><data value='GPUMonitor'>GPUMonitor</data></a></td>
                <td>57</td>
                <td>57</td>
                <td>0</td>
                <td class="right" data-ratio="0 57">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f984836ddf12effb_system_monitor_py.html#t159">aretomo3_gui/core/system_monitor.py</a></td>
                <td class="name left"><a href="z_f984836ddf12effb_system_monitor_py.html#t159"><data value='SystemMonitor'>SystemMonitor</data></a></td>
                <td>34</td>
                <td>34</td>
                <td>0</td>
                <td class="right" data-ratio="0 34">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f984836ddf12effb_system_monitor_py.html">aretomo3_gui/core/system_monitor.py</a></td>
                <td class="name left"><a href="z_f984836ddf12effb_system_monitor_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>30</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="30 30">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f984836ddf12effb_thread_manager_py.html#t67">aretomo3_gui/core/thread_manager.py</a></td>
                <td class="name left"><a href="z_f984836ddf12effb_thread_manager_py.html#t67"><data value='TaskPriority'>TaskPriority</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f984836ddf12effb_thread_manager_py.html#t75">aretomo3_gui/core/thread_manager.py</a></td>
                <td class="name left"><a href="z_f984836ddf12effb_thread_manager_py.html#t75"><data value='TaskStatus'>TaskStatus</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f984836ddf12effb_thread_manager_py.html#t86">aretomo3_gui/core/thread_manager.py</a></td>
                <td class="name left"><a href="z_f984836ddf12effb_thread_manager_py.html#t86"><data value='Task'>Task</data></a></td>
                <td>39</td>
                <td>39</td>
                <td>0</td>
                <td class="right" data-ratio="0 39">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f984836ddf12effb_thread_manager_py.html#t210">aretomo3_gui/core/thread_manager.py</a></td>
                <td class="name left"><a href="z_f984836ddf12effb_thread_manager_py.html#t210"><data value='WorkerSignals'>WorkerSignals</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f984836ddf12effb_thread_manager_py.html#t219">aretomo3_gui/core/thread_manager.py</a></td>
                <td class="name left"><a href="z_f984836ddf12effb_thread_manager_py.html#t219"><data value='WorkerThread'>WorkerThread</data></a></td>
                <td>35</td>
                <td>35</td>
                <td>0</td>
                <td class="right" data-ratio="0 35">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f984836ddf12effb_thread_manager_py.html#t293">aretomo3_gui/core/thread_manager.py</a></td>
                <td class="name left"><a href="z_f984836ddf12effb_thread_manager_py.html#t293"><data value='ThreadManager'>ThreadManager</data></a></td>
                <td>51</td>
                <td>51</td>
                <td>0</td>
                <td class="right" data-ratio="0 51">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f984836ddf12effb_thread_manager_py.html">aretomo3_gui/core/thread_manager.py</a></td>
                <td class="name left"><a href="z_f984836ddf12effb_thread_manager_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>59</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="56 59">95%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f984836ddf12effb_tilt_series_py.html#t1">aretomo3_gui/core/tilt_series.py</a></td>
                <td class="name left"><a href="z_f984836ddf12effb_tilt_series_py.html#t1"><data value='TiltSeries'>TiltSeries</data></a></td>
                <td>8</td>
                <td>8</td>
                <td>0</td>
                <td class="right" data-ratio="0 8">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f984836ddf12effb_tilt_series_py.html">aretomo3_gui/core/tilt_series.py</a></td>
                <td class="name left"><a href="z_f984836ddf12effb_tilt_series_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e475032cb85aca8e___init___py.html">aretomo3_gui/data_management/__init__.py</a></td>
                <td class="name left"><a href="z_e475032cb85aca8e___init___py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>2</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="2 2">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e475032cb85aca8e_data_manager_py.html#t21">aretomo3_gui/data_management/data_manager.py</a></td>
                <td class="name left"><a href="z_e475032cb85aca8e_data_manager_py.html#t21"><data value='DatasetMetadata'>DatasetMetadata</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e475032cb85aca8e_data_manager_py.html#t41">aretomo3_gui/data_management/data_manager.py</a></td>
                <td class="name left"><a href="z_e475032cb85aca8e_data_manager_py.html#t41"><data value='ProcessingRecord'>ProcessingRecord</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e475032cb85aca8e_data_manager_py.html#t55">aretomo3_gui/data_management/data_manager.py</a></td>
                <td class="name left"><a href="z_e475032cb85aca8e_data_manager_py.html#t55"><data value='DataManager'>DataManager</data></a></td>
                <td>152</td>
                <td>152</td>
                <td>0</td>
                <td class="right" data-ratio="0 152">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e475032cb85aca8e_data_manager_py.html">aretomo3_gui/data_management/data_manager.py</a></td>
                <td class="name left"><a href="z_e475032cb85aca8e_data_manager_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>57</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="57 57">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e475032cb85aca8e_manager_py.html#t17">aretomo3_gui/data_management/manager.py</a></td>
                <td class="name left"><a href="z_e475032cb85aca8e_manager_py.html#t17"><data value='DatasetMetadata'>DatasetMetadata</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e475032cb85aca8e_manager_py.html#t33">aretomo3_gui/data_management/manager.py</a></td>
                <td class="name left"><a href="z_e475032cb85aca8e_manager_py.html#t33"><data value='DataManager'>DataManager</data></a></td>
                <td>60</td>
                <td>60</td>
                <td>0</td>
                <td class="right" data-ratio="0 60">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e475032cb85aca8e_manager_py.html">aretomo3_gui/data_management/manager.py</a></td>
                <td class="name left"><a href="z_e475032cb85aca8e_manager_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>30</td>
                <td>30</td>
                <td>0</td>
                <td class="right" data-ratio="0 30">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e29991547583ad4f___init___py.html">aretomo3_gui/formats/__init__.py</a></td>
                <td class="name left"><a href="z_e29991547583ad4f___init___py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e29991547583ad4f_format_manager_py.html#t48">aretomo3_gui/formats/format_manager.py</a></td>
                <td class="name left"><a href="z_e29991547583ad4f_format_manager_py.html#t48"><data value='FormatInfo'>FormatInfo</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e29991547583ad4f_format_manager_py.html#t61">aretomo3_gui/formats/format_manager.py</a></td>
                <td class="name left"><a href="z_e29991547583ad4f_format_manager_py.html#t61"><data value='FileMetadata'>FileMetadata</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e29991547583ad4f_format_manager_py.html#t74">aretomo3_gui/formats/format_manager.py</a></td>
                <td class="name left"><a href="z_e29991547583ad4f_format_manager_py.html#t74"><data value='FormatHandler'>FormatHandler</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>6</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e29991547583ad4f_format_manager_py.html#t93">aretomo3_gui/formats/format_manager.py</a></td>
                <td class="name left"><a href="z_e29991547583ad4f_format_manager_py.html#t93"><data value='MRCHandler'>MRCHandler</data></a></td>
                <td>25</td>
                <td>25</td>
                <td>0</td>
                <td class="right" data-ratio="0 25">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e29991547583ad4f_format_manager_py.html#t168">aretomo3_gui/formats/format_manager.py</a></td>
                <td class="name left"><a href="z_e29991547583ad4f_format_manager_py.html#t168"><data value='TIFFHandler'>TIFFHandler</data></a></td>
                <td>29</td>
                <td>29</td>
                <td>0</td>
                <td class="right" data-ratio="0 29">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e29991547583ad4f_format_manager_py.html#t241">aretomo3_gui/formats/format_manager.py</a></td>
                <td class="name left"><a href="z_e29991547583ad4f_format_manager_py.html#t241"><data value='HDF5Handler'>HDF5Handler</data></a></td>
                <td>34</td>
                <td>34</td>
                <td>0</td>
                <td class="right" data-ratio="0 34">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e29991547583ad4f_format_manager_py.html#t321">aretomo3_gui/formats/format_manager.py</a></td>
                <td class="name left"><a href="z_e29991547583ad4f_format_manager_py.html#t321"><data value='FormatManager'>FormatManager</data></a></td>
                <td>76</td>
                <td>76</td>
                <td>0</td>
                <td class="right" data-ratio="0 76">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e29991547583ad4f_format_manager_py.html">aretomo3_gui/formats/format_manager.py</a></td>
                <td class="name left"><a href="z_e29991547583ad4f_format_manager_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>78</td>
                <td>78</td>
                <td>6</td>
                <td class="right" data-ratio="0 78">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e29991547583ad4f_manager_py.html#t16">aretomo3_gui/formats/manager.py</a></td>
                <td class="name left"><a href="z_e29991547583ad4f_manager_py.html#t16"><data value='DataFormat'>DataFormat</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>6</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e29991547583ad4f_manager_py.html#t35">aretomo3_gui/formats/manager.py</a></td>
                <td class="name left"><a href="z_e29991547583ad4f_manager_py.html#t35"><data value='MRCFormat'>MRCFormat</data></a></td>
                <td>11</td>
                <td>11</td>
                <td>0</td>
                <td class="right" data-ratio="0 11">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e29991547583ad4f_manager_py.html#t73">aretomo3_gui/formats/manager.py</a></td>
                <td class="name left"><a href="z_e29991547583ad4f_manager_py.html#t73"><data value='HDF5Format'>HDF5Format</data></a></td>
                <td>18</td>
                <td>18</td>
                <td>0</td>
                <td class="right" data-ratio="0 18">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e29991547583ad4f_manager_py.html#t112">aretomo3_gui/formats/manager.py</a></td>
                <td class="name left"><a href="z_e29991547583ad4f_manager_py.html#t112"><data value='TIFFFormat'>TIFFFormat</data></a></td>
                <td>14</td>
                <td>14</td>
                <td>0</td>
                <td class="right" data-ratio="0 14">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e29991547583ad4f_manager_py.html#t150">aretomo3_gui/formats/manager.py</a></td>
                <td class="name left"><a href="z_e29991547583ad4f_manager_py.html#t150"><data value='FormatManager'>FormatManager</data></a></td>
                <td>18</td>
                <td>18</td>
                <td>0</td>
                <td class="right" data-ratio="0 18">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e29991547583ad4f_manager_py.html">aretomo3_gui/formats/manager.py</a></td>
                <td class="name left"><a href="z_e29991547583ad4f_manager_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>28</td>
                <td>28</td>
                <td>6</td>
                <td class="right" data-ratio="0 28">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2f7edb285fa49a79___init___py.html">aretomo3_gui/gui/__init__.py</a></td>
                <td class="name left"><a href="z_2f7edb285fa49a79___init___py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2f7edb285fa49a79_advanced_settings_tab_py.html#t34">aretomo3_gui/gui/advanced_settings_tab.py</a></td>
                <td class="name left"><a href="z_2f7edb285fa49a79_advanced_settings_tab_py.html#t34"><data value='AdvancedSettingsTab'>AdvancedSettingsTab</data></a></td>
                <td>298</td>
                <td>298</td>
                <td>0</td>
                <td class="right" data-ratio="0 298">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2f7edb285fa49a79_advanced_settings_tab_py.html">aretomo3_gui/gui/advanced_settings_tab.py</a></td>
                <td class="name left"><a href="z_2f7edb285fa49a79_advanced_settings_tab_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>15</td>
                <td>15</td>
                <td>0</td>
                <td class="right" data-ratio="0 15">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_005df56b57153476___init___py.html">aretomo3_gui/gui/analysis/__init__.py</a></td>
                <td class="name left"><a href="z_005df56b57153476___init___py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_005df56b57153476_real_time_analyzer_py.html#t32">aretomo3_gui/gui/analysis/real_time_analyzer.py</a></td>
                <td class="name left"><a href="z_005df56b57153476_real_time_analyzer_py.html#t32"><data value='AreTomo3ResultsParser'>AreTomo3ResultsParser</data></a></td>
                <td>28</td>
                <td>28</td>
                <td>0</td>
                <td class="right" data-ratio="0 28">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_005df56b57153476_real_time_analyzer_py.html#t117">aretomo3_gui/gui/analysis/real_time_analyzer.py</a></td>
                <td class="name left"><a href="z_005df56b57153476_real_time_analyzer_py.html#t117"><data value='RealTimePlotGenerator'>RealTimePlotGenerator</data></a></td>
                <td>103</td>
                <td>103</td>
                <td>0</td>
                <td class="right" data-ratio="0 103">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_005df56b57153476_real_time_analyzer_py.html#t331">aretomo3_gui/gui/analysis/real_time_analyzer.py</a></td>
                <td class="name left"><a href="z_005df56b57153476_real_time_analyzer_py.html#t331"><data value='RealTimeAnalysisMonitor'>RealTimeAnalysisMonitor</data></a></td>
                <td>62</td>
                <td>62</td>
                <td>0</td>
                <td class="right" data-ratio="0 62">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_005df56b57153476_real_time_analyzer_py.html#t465">aretomo3_gui/gui/analysis/real_time_analyzer.py</a></td>
                <td class="name left"><a href="z_005df56b57153476_real_time_analyzer_py.html#t465"><data value='RealTimeAnalysisWidget'>RealTimeAnalysisWidget</data></a></td>
                <td>38</td>
                <td>38</td>
                <td>0</td>
                <td class="right" data-ratio="0 38">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_005df56b57153476_real_time_analyzer_py.html">aretomo3_gui/gui/analysis/real_time_analyzer.py</a></td>
                <td class="name left"><a href="z_005df56b57153476_real_time_analyzer_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>48</td>
                <td>48</td>
                <td>0</td>
                <td class="right" data-ratio="0 48">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ce4f5229c74c4b5a___init___py.html">aretomo3_gui/gui/components/__init__.py</a></td>
                <td class="name left"><a href="z_ce4f5229c74c4b5a___init___py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ce4f5229c74c4b5a_menu_manager_py.html#t15">aretomo3_gui/gui/components/menu_manager.py</a></td>
                <td class="name left"><a href="z_ce4f5229c74c4b5a_menu_manager_py.html#t15"><data value='MenuManager'>MenuManager</data></a></td>
                <td>132</td>
                <td>132</td>
                <td>0</td>
                <td class="right" data-ratio="0 132">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ce4f5229c74c4b5a_menu_manager_py.html">aretomo3_gui/gui/components/menu_manager.py</a></td>
                <td class="name left"><a href="z_ce4f5229c74c4b5a_menu_manager_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>27</td>
                <td>27</td>
                <td>0</td>
                <td class="right" data-ratio="0 27">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ce4f5229c74c4b5a_napari_viewer_py.html#t23">aretomo3_gui/gui/components/napari_viewer.py</a></td>
                <td class="name left"><a href="z_ce4f5229c74c4b5a_napari_viewer_py.html#t23"><data value='NapariViewerWidget'>NapariViewerWidget</data></a></td>
                <td>133</td>
                <td>133</td>
                <td>0</td>
                <td class="right" data-ratio="0 133">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ce4f5229c74c4b5a_napari_viewer_py.html">aretomo3_gui/gui/components/napari_viewer.py</a></td>
                <td class="name left"><a href="z_ce4f5229c74c4b5a_napari_viewer_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>25</td>
                <td>25</td>
                <td>0</td>
                <td class="right" data-ratio="0 25">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ce4f5229c74c4b5a_parameter_manager_py.html#t17">aretomo3_gui/gui/components/parameter_manager.py</a></td>
                <td class="name left"><a href="z_ce4f5229c74c4b5a_parameter_manager_py.html#t17"><data value='ParameterManager'>ParameterManager</data></a></td>
                <td>276</td>
                <td>276</td>
                <td>0</td>
                <td class="right" data-ratio="0 276">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ce4f5229c74c4b5a_parameter_manager_py.html">aretomo3_gui/gui/components/parameter_manager.py</a></td>
                <td class="name left"><a href="z_ce4f5229c74c4b5a_parameter_manager_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>20</td>
                <td>20</td>
                <td>0</td>
                <td class="right" data-ratio="0 20">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ce4f5229c74c4b5a_session_manager_py.html#t17">aretomo3_gui/gui/components/session_manager.py</a></td>
                <td class="name left"><a href="z_ce4f5229c74c4b5a_session_manager_py.html#t17"><data value='SessionManager'>SessionManager</data></a></td>
                <td>137</td>
                <td>137</td>
                <td>0</td>
                <td class="right" data-ratio="0 137">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ce4f5229c74c4b5a_session_manager_py.html">aretomo3_gui/gui/components/session_manager.py</a></td>
                <td class="name left"><a href="z_ce4f5229c74c4b5a_session_manager_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>18</td>
                <td>18</td>
                <td>0</td>
                <td class="right" data-ratio="0 18">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_3a511341f990a247___init___py.html">aretomo3_gui/gui/embedded_viewers/__init__.py</a></td>
                <td class="name left"><a href="z_3a511341f990a247___init___py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_3a511341f990a247_ctf_viewer_py.html#t27">aretomo3_gui/gui/embedded_viewers/ctf_viewer.py</a></td>
                <td class="name left"><a href="z_3a511341f990a247_ctf_viewer_py.html#t27"><data value='EmbeddedCTFViewer'>EmbeddedCTFViewer</data></a></td>
                <td>255</td>
                <td>255</td>
                <td>0</td>
                <td class="right" data-ratio="0 255">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_3a511341f990a247_ctf_viewer_py.html">aretomo3_gui/gui/embedded_viewers/ctf_viewer.py</a></td>
                <td class="name left"><a href="z_3a511341f990a247_ctf_viewer_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>38</td>
                <td>38</td>
                <td>0</td>
                <td class="right" data-ratio="0 38">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_3a511341f990a247_motion_viewer_py.html#t27">aretomo3_gui/gui/embedded_viewers/motion_viewer.py</a></td>
                <td class="name left"><a href="z_3a511341f990a247_motion_viewer_py.html#t27"><data value='EmbeddedMotionViewer'>EmbeddedMotionViewer</data></a></td>
                <td>318</td>
                <td>318</td>
                <td>0</td>
                <td class="right" data-ratio="0 318">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_3a511341f990a247_motion_viewer_py.html">aretomo3_gui/gui/embedded_viewers/motion_viewer.py</a></td>
                <td class="name left"><a href="z_3a511341f990a247_motion_viewer_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>41</td>
                <td>41</td>
                <td>0</td>
                <td class="right" data-ratio="0 41">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2f7edb285fa49a79_main_window_py.html#t31">aretomo3_gui/gui/main_window.py</a></td>
                <td class="name left"><a href="z_2f7edb285fa49a79_main_window_py.html#t31"><data value='AreTomoGUI'>AreTomoGUI</data></a></td>
                <td>388</td>
                <td>388</td>
                <td>0</td>
                <td class="right" data-ratio="0 388">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2f7edb285fa49a79_main_window_py.html">aretomo3_gui/gui/main_window.py</a></td>
                <td class="name left"><a href="z_2f7edb285fa49a79_main_window_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>45</td>
                <td>45</td>
                <td>0</td>
                <td class="right" data-ratio="0 45">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2f7edb285fa49a79_minimal_gui_py.html#t17">aretomo3_gui/gui/minimal_gui.py</a></td>
                <td class="name left"><a href="z_2f7edb285fa49a79_minimal_gui_py.html#t17"><data value='MinimalAreTomoGUI'>MinimalAreTomoGUI</data></a></td>
                <td>141</td>
                <td>141</td>
                <td>0</td>
                <td class="right" data-ratio="0 141">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2f7edb285fa49a79_minimal_gui_py.html">aretomo3_gui/gui/minimal_gui.py</a></td>
                <td class="name left"><a href="z_2f7edb285fa49a79_minimal_gui_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>16</td>
                <td>16</td>
                <td>0</td>
                <td class="right" data-ratio="0 16">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2f7edb285fa49a79_plot_theme_manager_py.html#t14">aretomo3_gui/gui/plot_theme_manager.py</a></td>
                <td class="name left"><a href="z_2f7edb285fa49a79_plot_theme_manager_py.html#t14"><data value='PlotThemeManager'>PlotThemeManager</data></a></td>
                <td>42</td>
                <td>42</td>
                <td>0</td>
                <td class="right" data-ratio="0 42">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2f7edb285fa49a79_plot_theme_manager_py.html">aretomo3_gui/gui/plot_theme_manager.py</a></td>
                <td class="name left"><a href="z_2f7edb285fa49a79_plot_theme_manager_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>26</td>
                <td>26</td>
                <td>0</td>
                <td class="right" data-ratio="0 26">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2f7edb285fa49a79_rich_main_window_py.html#t32">aretomo3_gui/gui/rich_main_window.py</a></td>
                <td class="name left"><a href="z_2f7edb285fa49a79_rich_main_window_py.html#t32"><data value='RichAreTomoGUI'>RichAreTomoGUI</data></a></td>
                <td>1526</td>
                <td>1526</td>
                <td>0</td>
                <td class="right" data-ratio="0 1526">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2f7edb285fa49a79_rich_main_window_py.html#t497">aretomo3_gui/gui/rich_main_window.py</a></td>
                <td class="name left"><a href="z_2f7edb285fa49a79_rich_main_window_py.html#t497"><data value='GuiLogHandler'>RichAreTomoGUI._setup_log_handler.GuiLogHandler</data></a></td>
                <td>13</td>
                <td>13</td>
                <td>0</td>
                <td class="right" data-ratio="0 13">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2f7edb285fa49a79_rich_main_window_py.html#t1828">aretomo3_gui/gui/rich_main_window.py</a></td>
                <td class="name left"><a href="z_2f7edb285fa49a79_rich_main_window_py.html#t1828"><data value='TiltSeries'>RichAreTomoGUI.TiltSeries</data></a></td>
                <td>10</td>
                <td>10</td>
                <td>0</td>
                <td class="right" data-ratio="0 10">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2f7edb285fa49a79_rich_main_window_py.html">aretomo3_gui/gui/rich_main_window.py</a></td>
                <td class="name left"><a href="z_2f7edb285fa49a79_rich_main_window_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>106</td>
                <td>106</td>
                <td>0</td>
                <td class="right" data-ratio="0 106">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8b5a9328199a4f25___init___py.html">aretomo3_gui/gui/tabs/__init__.py</a></td>
                <td class="name left"><a href="z_8b5a9328199a4f25___init___py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>8</td>
                <td>8</td>
                <td>0</td>
                <td class="right" data-ratio="0 8">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8b5a9328199a4f25_analysis_tab_py.html#t27">aretomo3_gui/gui/tabs/analysis_tab.py</a></td>
                <td class="name left"><a href="z_8b5a9328199a4f25_analysis_tab_py.html#t27"><data value='AnalysisTabManager'>AnalysisTabManager</data></a></td>
                <td>40</td>
                <td>40</td>
                <td>0</td>
                <td class="right" data-ratio="0 40">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8b5a9328199a4f25_analysis_tab_py.html">aretomo3_gui/gui/tabs/analysis_tab.py</a></td>
                <td class="name left"><a href="z_8b5a9328199a4f25_analysis_tab_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>12</td>
                <td>12</td>
                <td>0</td>
                <td class="right" data-ratio="0 12">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8b5a9328199a4f25_batch_tab_py.html#t16">aretomo3_gui/gui/tabs/batch_tab.py</a></td>
                <td class="name left"><a href="z_8b5a9328199a4f25_batch_tab_py.html#t16"><data value='BatchTabManager'>BatchTabManager</data></a></td>
                <td>11</td>
                <td>11</td>
                <td>0</td>
                <td class="right" data-ratio="0 11">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8b5a9328199a4f25_batch_tab_py.html">aretomo3_gui/gui/tabs/batch_tab.py</a></td>
                <td class="name left"><a href="z_8b5a9328199a4f25_batch_tab_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>9</td>
                <td>9</td>
                <td>0</td>
                <td class="right" data-ratio="0 9">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8b5a9328199a4f25_ctf_tab_py.html#t30">aretomo3_gui/gui/tabs/ctf_tab.py</a></td>
                <td class="name left"><a href="z_8b5a9328199a4f25_ctf_tab_py.html#t30"><data value='CTFTab'>CTFTab</data></a></td>
                <td>82</td>
                <td>82</td>
                <td>0</td>
                <td class="right" data-ratio="0 82">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8b5a9328199a4f25_ctf_tab_py.html">aretomo3_gui/gui/tabs/ctf_tab.py</a></td>
                <td class="name left"><a href="z_8b5a9328199a4f25_ctf_tab_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>15</td>
                <td>15</td>
                <td>0</td>
                <td class="right" data-ratio="0 15">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8b5a9328199a4f25_enhanced_analysis_tab_py.html#t60">aretomo3_gui/gui/tabs/enhanced_analysis_tab.py</a></td>
                <td class="name left"><a href="z_8b5a9328199a4f25_enhanced_analysis_tab_py.html#t60"><data value='EnhancedAnalysisTab'>EnhancedAnalysisTab</data></a></td>
                <td>1088</td>
                <td>1088</td>
                <td>0</td>
                <td class="right" data-ratio="0 1088">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8b5a9328199a4f25_enhanced_analysis_tab_py.html">aretomo3_gui/gui/tabs/enhanced_analysis_tab.py</a></td>
                <td class="name left"><a href="z_8b5a9328199a4f25_enhanced_analysis_tab_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>82</td>
                <td>82</td>
                <td>0</td>
                <td class="right" data-ratio="0 82">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8b5a9328199a4f25_enhanced_monitor_tab_py.html#t32">aretomo3_gui/gui/tabs/enhanced_monitor_tab.py</a></td>
                <td class="name left"><a href="z_8b5a9328199a4f25_enhanced_monitor_tab_py.html#t32"><data value='EnhancedMonitorTab'>EnhancedMonitorTab</data></a></td>
                <td>133</td>
                <td>133</td>
                <td>0</td>
                <td class="right" data-ratio="0 133">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8b5a9328199a4f25_enhanced_monitor_tab_py.html">aretomo3_gui/gui/tabs/enhanced_monitor_tab.py</a></td>
                <td class="name left"><a href="z_8b5a9328199a4f25_enhanced_monitor_tab_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>27</td>
                <td>27</td>
                <td>0</td>
                <td class="right" data-ratio="0 27">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8b5a9328199a4f25_enhanced_parameters_tab_py.html#t224">aretomo3_gui/gui/tabs/enhanced_parameters_tab.py</a></td>
                <td class="name left"><a href="z_8b5a9328199a4f25_enhanced_parameters_tab_py.html#t224"><data value='EnhancedParametersTab'>EnhancedParametersTab</data></a></td>
                <td>1027</td>
                <td>1027</td>
                <td>0</td>
                <td class="right" data-ratio="0 1027">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8b5a9328199a4f25_enhanced_parameters_tab_py.html">aretomo3_gui/gui/tabs/enhanced_parameters_tab.py</a></td>
                <td class="name left"><a href="z_8b5a9328199a4f25_enhanced_parameters_tab_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>46</td>
                <td>46</td>
                <td>0</td>
                <td class="right" data-ratio="0 46">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8b5a9328199a4f25_export_tab_py.html#t21">aretomo3_gui/gui/tabs/export_tab.py</a></td>
                <td class="name left"><a href="z_8b5a9328199a4f25_export_tab_py.html#t21"><data value='ExportTabManager'>ExportTabManager</data></a></td>
                <td>28</td>
                <td>28</td>
                <td>0</td>
                <td class="right" data-ratio="0 28">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8b5a9328199a4f25_export_tab_py.html">aretomo3_gui/gui/tabs/export_tab.py</a></td>
                <td class="name left"><a href="z_8b5a9328199a4f25_export_tab_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>9</td>
                <td>9</td>
                <td>0</td>
                <td class="right" data-ratio="0 9">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8b5a9328199a4f25_live_processing_tab_py.html#t51">aretomo3_gui/gui/tabs/live_processing_tab.py</a></td>
                <td class="name left"><a href="z_8b5a9328199a4f25_live_processing_tab_py.html#t51"><data value='LiveFileMonitor'>LiveFileMonitor</data></a></td>
                <td>44</td>
                <td>44</td>
                <td>0</td>
                <td class="right" data-ratio="0 44">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8b5a9328199a4f25_live_processing_tab_py.html#t133">aretomo3_gui/gui/tabs/live_processing_tab.py</a></td>
                <td class="name left"><a href="z_8b5a9328199a4f25_live_processing_tab_py.html#t133"><data value='LiveProcessor'>LiveProcessor</data></a></td>
                <td>33</td>
                <td>33</td>
                <td>0</td>
                <td class="right" data-ratio="0 33">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8b5a9328199a4f25_live_processing_tab_py.html#t205">aretomo3_gui/gui/tabs/live_processing_tab.py</a></td>
                <td class="name left"><a href="z_8b5a9328199a4f25_live_processing_tab_py.html#t205"><data value='LiveProcessingTab'>LiveProcessingTab</data></a></td>
                <td>500</td>
                <td>500</td>
                <td>0</td>
                <td class="right" data-ratio="0 500">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8b5a9328199a4f25_live_processing_tab_py.html">aretomo3_gui/gui/tabs/live_processing_tab.py</a></td>
                <td class="name left"><a href="z_8b5a9328199a4f25_live_processing_tab_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>67</td>
                <td>67</td>
                <td>0</td>
                <td class="right" data-ratio="0 67">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8b5a9328199a4f25_log_tab_py.html#t27">aretomo3_gui/gui/tabs/log_tab.py</a></td>
                <td class="name left"><a href="z_8b5a9328199a4f25_log_tab_py.html#t27"><data value='LogTabManager'>LogTabManager</data></a></td>
                <td>112</td>
                <td>112</td>
                <td>0</td>
                <td class="right" data-ratio="0 112">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8b5a9328199a4f25_log_tab_py.html">aretomo3_gui/gui/tabs/log_tab.py</a></td>
                <td class="name left"><a href="z_8b5a9328199a4f25_log_tab_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>20</td>
                <td>20</td>
                <td>0</td>
                <td class="right" data-ratio="0 20">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8b5a9328199a4f25_main_tab_py.html#t33">aretomo3_gui/gui/tabs/main_tab.py</a></td>
                <td class="name left"><a href="z_8b5a9328199a4f25_main_tab_py.html#t33"><data value='MainTabManager'>MainTabManager</data></a></td>
                <td>286</td>
                <td>286</td>
                <td>0</td>
                <td class="right" data-ratio="0 286">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8b5a9328199a4f25_main_tab_py.html">aretomo3_gui/gui/tabs/main_tab.py</a></td>
                <td class="name left"><a href="z_8b5a9328199a4f25_main_tab_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>19</td>
                <td>19</td>
                <td>0</td>
                <td class="right" data-ratio="0 19">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8b5a9328199a4f25_monitor_tab_py.html#t14">aretomo3_gui/gui/tabs/monitor_tab.py</a></td>
                <td class="name left"><a href="z_8b5a9328199a4f25_monitor_tab_py.html#t14"><data value='MonitorTabManager'>MonitorTabManager</data></a></td>
                <td>19</td>
                <td>19</td>
                <td>0</td>
                <td class="right" data-ratio="0 19">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8b5a9328199a4f25_monitor_tab_py.html">aretomo3_gui/gui/tabs/monitor_tab.py</a></td>
                <td class="name left"><a href="z_8b5a9328199a4f25_monitor_tab_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>8</td>
                <td>8</td>
                <td>0</td>
                <td class="right" data-ratio="0 8">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8b5a9328199a4f25_napari_viewer_tab_py.html#t41">aretomo3_gui/gui/tabs/napari_viewer_tab.py</a></td>
                <td class="name left"><a href="z_8b5a9328199a4f25_napari_viewer_tab_py.html#t41"><data value='NapariViewerTab'>NapariViewerTab</data></a></td>
                <td>152</td>
                <td>152</td>
                <td>0</td>
                <td class="right" data-ratio="0 152">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8b5a9328199a4f25_napari_viewer_tab_py.html">aretomo3_gui/gui/tabs/napari_viewer_tab.py</a></td>
                <td class="name left"><a href="z_8b5a9328199a4f25_napari_viewer_tab_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>31</td>
                <td>31</td>
                <td>0</td>
                <td class="right" data-ratio="0 31">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8b5a9328199a4f25_realtime_analysis_tab_py.html#t61">aretomo3_gui/gui/tabs/realtime_analysis_tab.py</a></td>
                <td class="name left"><a href="z_8b5a9328199a4f25_realtime_analysis_tab_py.html#t61"><data value='RealTimeAnalysisTab'>RealTimeAnalysisTab</data></a></td>
                <td>1236</td>
                <td>1236</td>
                <td>0</td>
                <td class="right" data-ratio="0 1236">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8b5a9328199a4f25_realtime_analysis_tab_py.html">aretomo3_gui/gui/tabs/realtime_analysis_tab.py</a></td>
                <td class="name left"><a href="z_8b5a9328199a4f25_realtime_analysis_tab_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>81</td>
                <td>81</td>
                <td>0</td>
                <td class="right" data-ratio="0 81">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8b5a9328199a4f25_reorganized_main_tab_py.html#t43">aretomo3_gui/gui/tabs/reorganized_main_tab.py</a></td>
                <td class="name left"><a href="z_8b5a9328199a4f25_reorganized_main_tab_py.html#t43"><data value='ProjectManager'>ProjectManager</data></a></td>
                <td>30</td>
                <td>30</td>
                <td>0</td>
                <td class="right" data-ratio="0 30">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8b5a9328199a4f25_reorganized_main_tab_py.html#t99">aretomo3_gui/gui/tabs/reorganized_main_tab.py</a></td>
                <td class="name left"><a href="z_8b5a9328199a4f25_reorganized_main_tab_py.html#t99"><data value='ReorganizedMainTab'>ReorganizedMainTab</data></a></td>
                <td>502</td>
                <td>502</td>
                <td>0</td>
                <td class="right" data-ratio="0 502">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8b5a9328199a4f25_reorganized_main_tab_py.html">aretomo3_gui/gui/tabs/reorganized_main_tab.py</a></td>
                <td class="name left"><a href="z_8b5a9328199a4f25_reorganized_main_tab_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>61</td>
                <td>61</td>
                <td>0</td>
                <td class="right" data-ratio="0 61">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8b5a9328199a4f25_reorganized_main_tab_backup_py.html#t41">aretomo3_gui/gui/tabs/reorganized_main_tab_backup.py</a></td>
                <td class="name left"><a href="z_8b5a9328199a4f25_reorganized_main_tab_backup_py.html#t41"><data value='ProjectManager'>ProjectManager</data></a></td>
                <td>30</td>
                <td>30</td>
                <td>0</td>
                <td class="right" data-ratio="0 30">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8b5a9328199a4f25_reorganized_main_tab_backup_py.html#t100">aretomo3_gui/gui/tabs/reorganized_main_tab_backup.py</a></td>
                <td class="name left"><a href="z_8b5a9328199a4f25_reorganized_main_tab_backup_py.html#t100"><data value='ReorganizedMainTab'>ReorganizedMainTab</data></a></td>
                <td>595</td>
                <td>595</td>
                <td>0</td>
                <td class="right" data-ratio="0 595">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8b5a9328199a4f25_reorganized_main_tab_backup_py.html">aretomo3_gui/gui/tabs/reorganized_main_tab_backup.py</a></td>
                <td class="name left"><a href="z_8b5a9328199a4f25_reorganized_main_tab_backup_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>63</td>
                <td>63</td>
                <td>0</td>
                <td class="right" data-ratio="0 63">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8b5a9328199a4f25_unified_analysis_tab_py.html#t33">aretomo3_gui/gui/tabs/unified_analysis_tab.py</a></td>
                <td class="name left"><a href="z_8b5a9328199a4f25_unified_analysis_tab_py.html#t33"><data value='UnifiedAnalysisTab'>UnifiedAnalysisTab</data></a></td>
                <td>519</td>
                <td>519</td>
                <td>0</td>
                <td class="right" data-ratio="0 519">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8b5a9328199a4f25_unified_analysis_tab_py.html">aretomo3_gui/gui/tabs/unified_analysis_tab.py</a></td>
                <td class="name left"><a href="z_8b5a9328199a4f25_unified_analysis_tab_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>59</td>
                <td>59</td>
                <td>0</td>
                <td class="right" data-ratio="0 59">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8b5a9328199a4f25_unified_live_processing_tab_py.html#t39">aretomo3_gui/gui/tabs/unified_live_processing_tab.py</a></td>
                <td class="name left"><a href="z_8b5a9328199a4f25_unified_live_processing_tab_py.html#t39"><data value='UnifiedLiveProcessingTab'>UnifiedLiveProcessingTab</data></a></td>
                <td>167</td>
                <td>167</td>
                <td>0</td>
                <td class="right" data-ratio="0 167">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8b5a9328199a4f25_unified_live_processing_tab_py.html">aretomo3_gui/gui/tabs/unified_live_processing_tab.py</a></td>
                <td class="name left"><a href="z_8b5a9328199a4f25_unified_live_processing_tab_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>28</td>
                <td>28</td>
                <td>0</td>
                <td class="right" data-ratio="0 28">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8b5a9328199a4f25_viewer_tab_py.html#t16">aretomo3_gui/gui/tabs/viewer_tab.py</a></td>
                <td class="name left"><a href="z_8b5a9328199a4f25_viewer_tab_py.html#t16"><data value='ViewerTabManager'>ViewerTabManager</data></a></td>
                <td>10</td>
                <td>10</td>
                <td>0</td>
                <td class="right" data-ratio="0 10">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8b5a9328199a4f25_viewer_tab_py.html">aretomo3_gui/gui/tabs/viewer_tab.py</a></td>
                <td class="name left"><a href="z_8b5a9328199a4f25_viewer_tab_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>9</td>
                <td>9</td>
                <td>0</td>
                <td class="right" data-ratio="0 9">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8b5a9328199a4f25_web_dashboard_tab_py.html#t42">aretomo3_gui/gui/tabs/web_dashboard_tab.py</a></td>
                <td class="name left"><a href="z_8b5a9328199a4f25_web_dashboard_tab_py.html#t42"><data value='WebServerManager'>WebServerManager</data></a></td>
                <td>31</td>
                <td>31</td>
                <td>0</td>
                <td class="right" data-ratio="0 31">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8b5a9328199a4f25_web_dashboard_tab_py.html#t103">aretomo3_gui/gui/tabs/web_dashboard_tab.py</a></td>
                <td class="name left"><a href="z_8b5a9328199a4f25_web_dashboard_tab_py.html#t103"><data value='WebDashboardTab'>WebDashboardTab</data></a></td>
                <td>162</td>
                <td>162</td>
                <td>0</td>
                <td class="right" data-ratio="0 162">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8b5a9328199a4f25_web_dashboard_tab_py.html">aretomo3_gui/gui/tabs/web_dashboard_tab.py</a></td>
                <td class="name left"><a href="z_8b5a9328199a4f25_web_dashboard_tab_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>46</td>
                <td>46</td>
                <td>0</td>
                <td class="right" data-ratio="0 46">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2f7edb285fa49a79_theme_manager_py.html#t15">aretomo3_gui/gui/theme_manager.py</a></td>
                <td class="name left"><a href="z_2f7edb285fa49a79_theme_manager_py.html#t15"><data value='ThemeManager'>ThemeManager</data></a></td>
                <td>34</td>
                <td>34</td>
                <td>0</td>
                <td class="right" data-ratio="0 34">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2f7edb285fa49a79_theme_manager_py.html">aretomo3_gui/gui/theme_manager.py</a></td>
                <td class="name left"><a href="z_2f7edb285fa49a79_theme_manager_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>14</td>
                <td>14</td>
                <td>0</td>
                <td class="right" data-ratio="0 14">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2f7edb285fa49a79_themes_py.html#t10">aretomo3_gui/gui/themes.py</a></td>
                <td class="name left"><a href="z_2f7edb285fa49a79_themes_py.html#t10"><data value='ThemeManager'>ThemeManager</data></a></td>
                <td>13</td>
                <td>13</td>
                <td>0</td>
                <td class="right" data-ratio="0 13">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2f7edb285fa49a79_themes_py.html">aretomo3_gui/gui/themes.py</a></td>
                <td class="name left"><a href="z_2f7edb285fa49a79_themes_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>9</td>
                <td>9</td>
                <td>0</td>
                <td class="right" data-ratio="0 9">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_140fbbd1834799a1___init___py.html">aretomo3_gui/gui/viewers/__init__.py</a></td>
                <td class="name left"><a href="z_140fbbd1834799a1___init___py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_140fbbd1834799a1_analysis_viewer_py.html#t42">aretomo3_gui/gui/viewers/analysis_viewer.py</a></td>
                <td class="name left"><a href="z_140fbbd1834799a1_analysis_viewer_py.html#t42"><data value='MotionCorrectionTab'>MotionCorrectionTab</data></a></td>
                <td>71</td>
                <td>71</td>
                <td>0</td>
                <td class="right" data-ratio="0 71">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_140fbbd1834799a1_analysis_viewer_py.html#t180">aretomo3_gui/gui/viewers/analysis_viewer.py</a></td>
                <td class="name left"><a href="z_140fbbd1834799a1_analysis_viewer_py.html#t180"><data value='TiltSeriesTab'>TiltSeriesTab</data></a></td>
                <td>40</td>
                <td>40</td>
                <td>0</td>
                <td class="right" data-ratio="0 40">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_140fbbd1834799a1_analysis_viewer_py.html#t255">aretomo3_gui/gui/viewers/analysis_viewer.py</a></td>
                <td class="name left"><a href="z_140fbbd1834799a1_analysis_viewer_py.html#t255"><data value='CTFTab'>CTFTab</data></a></td>
                <td>79</td>
                <td>79</td>
                <td>0</td>
                <td class="right" data-ratio="0 79">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_140fbbd1834799a1_analysis_viewer_py.html#t391">aretomo3_gui/gui/viewers/analysis_viewer.py</a></td>
                <td class="name left"><a href="z_140fbbd1834799a1_analysis_viewer_py.html#t391"><data value='AnalysisViewer'>AnalysisViewer</data></a></td>
                <td>65</td>
                <td>65</td>
                <td>0</td>
                <td class="right" data-ratio="0 65">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_140fbbd1834799a1_analysis_viewer_py.html">aretomo3_gui/gui/viewers/analysis_viewer.py</a></td>
                <td class="name left"><a href="z_140fbbd1834799a1_analysis_viewer_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>43</td>
                <td>43</td>
                <td>0</td>
                <td class="right" data-ratio="0 43">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_140fbbd1834799a1_mock_napari_viewer_py.html#t14">aretomo3_gui/gui/viewers/mock_napari_viewer.py</a></td>
                <td class="name left"><a href="z_140fbbd1834799a1_mock_napari_viewer_py.html#t14"><data value='MockNapariViewer'>MockNapariViewer</data></a></td>
                <td>12</td>
                <td>12</td>
                <td>0</td>
                <td class="right" data-ratio="0 12">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_140fbbd1834799a1_mock_napari_viewer_py.html">aretomo3_gui/gui/viewers/mock_napari_viewer.py</a></td>
                <td class="name left"><a href="z_140fbbd1834799a1_mock_napari_viewer_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>10</td>
                <td>10</td>
                <td>0</td>
                <td class="right" data-ratio="0 10">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_140fbbd1834799a1_mrc_viewer_py.html#t59">aretomo3_gui/gui/viewers/mrc_viewer.py</a></td>
                <td class="name left"><a href="z_140fbbd1834799a1_mrc_viewer_py.html#t59"><data value='ProcessingMonitor'>ProcessingMonitor</data></a></td>
                <td>64</td>
                <td>64</td>
                <td>0</td>
                <td class="right" data-ratio="0 64">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_140fbbd1834799a1_mrc_viewer_py.html#t169">aretomo3_gui/gui/viewers/mrc_viewer.py</a></td>
                <td class="name left"><a href="z_140fbbd1834799a1_mrc_viewer_py.html#t169"><data value='ElegantSlider'>ElegantSlider</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_140fbbd1834799a1_mrc_viewer_py.html#t210">aretomo3_gui/gui/viewers/mrc_viewer.py</a></td>
                <td class="name left"><a href="z_140fbbd1834799a1_mrc_viewer_py.html#t210"><data value='HistogramWidget'>HistogramWidget</data></a></td>
                <td>26</td>
                <td>26</td>
                <td>0</td>
                <td class="right" data-ratio="0 26">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_140fbbd1834799a1_mrc_viewer_py.html#t262">aretomo3_gui/gui/viewers/mrc_viewer.py</a></td>
                <td class="name left"><a href="z_140fbbd1834799a1_mrc_viewer_py.html#t262"><data value='MeasurementTool'>MeasurementTool</data></a></td>
                <td>19</td>
                <td>19</td>
                <td>0</td>
                <td class="right" data-ratio="0 19">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_140fbbd1834799a1_mrc_viewer_py.html#t308">aretomo3_gui/gui/viewers/mrc_viewer.py</a></td>
                <td class="name left"><a href="z_140fbbd1834799a1_mrc_viewer_py.html#t308"><data value='IntegratedViewer'>IntegratedViewer</data></a></td>
                <td>355</td>
                <td>355</td>
                <td>0</td>
                <td class="right" data-ratio="0 355">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_140fbbd1834799a1_mrc_viewer_py.html#t1011">aretomo3_gui/gui/viewers/mrc_viewer.py</a></td>
                <td class="name left"><a href="z_140fbbd1834799a1_mrc_viewer_py.html#t1011"><data value='MRCViewer'>MRCViewer</data></a></td>
                <td>466</td>
                <td>466</td>
                <td>0</td>
                <td class="right" data-ratio="0 466">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_140fbbd1834799a1_mrc_viewer_py.html">aretomo3_gui/gui/viewers/mrc_viewer.py</a></td>
                <td class="name left"><a href="z_140fbbd1834799a1_mrc_viewer_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>91</td>
                <td>91</td>
                <td>0</td>
                <td class="right" data-ratio="0 91">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_140fbbd1834799a1_napari_mrc_viewer_py.html#t55">aretomo3_gui/gui/viewers/napari_mrc_viewer.py</a></td>
                <td class="name left"><a href="z_140fbbd1834799a1_napari_mrc_viewer_py.html#t55"><data value='NapariMRCViewer'>NapariMRCViewer</data></a></td>
                <td>75</td>
                <td>75</td>
                <td>0</td>
                <td class="right" data-ratio="0 75">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_140fbbd1834799a1_napari_mrc_viewer_py.html">aretomo3_gui/gui/viewers/napari_mrc_viewer.py</a></td>
                <td class="name left"><a href="z_140fbbd1834799a1_napari_mrc_viewer_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>46</td>
                <td>46</td>
                <td>0</td>
                <td class="right" data-ratio="0 46">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_140fbbd1834799a1_preview_grid_py.html#t19">aretomo3_gui/gui/viewers/preview_grid.py</a></td>
                <td class="name left"><a href="z_140fbbd1834799a1_preview_grid_py.html#t19"><data value='ThumbnailWidget'>ThumbnailWidget</data></a></td>
                <td>19</td>
                <td>19</td>
                <td>0</td>
                <td class="right" data-ratio="0 19">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_140fbbd1834799a1_preview_grid_py.html#t62">aretomo3_gui/gui/viewers/preview_grid.py</a></td>
                <td class="name left"><a href="z_140fbbd1834799a1_preview_grid_py.html#t62"><data value='PreviewGridView'>PreviewGridView</data></a></td>
                <td>27</td>
                <td>27</td>
                <td>0</td>
                <td class="right" data-ratio="0 27">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_140fbbd1834799a1_preview_grid_py.html">aretomo3_gui/gui/viewers/preview_grid.py</a></td>
                <td class="name left"><a href="z_140fbbd1834799a1_preview_grid_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>18</td>
                <td>18</td>
                <td>0</td>
                <td class="right" data-ratio="0 18">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_140fbbd1834799a1_visualization_py.html#t34">aretomo3_gui/gui/viewers/visualization.py</a></td>
                <td class="name left"><a href="z_140fbbd1834799a1_visualization_py.html#t34"><data value='TomogramViewer'>TomogramViewer</data></a></td>
                <td>147</td>
                <td>147</td>
                <td>0</td>
                <td class="right" data-ratio="0 147">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_140fbbd1834799a1_visualization_py.html#t293">aretomo3_gui/gui/viewers/visualization.py</a></td>
                <td class="name left"><a href="z_140fbbd1834799a1_visualization_py.html#t293"><data value='CTFViewer'>CTFViewer</data></a></td>
                <td>83</td>
                <td>83</td>
                <td>0</td>
                <td class="right" data-ratio="0 83">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_140fbbd1834799a1_visualization_py.html#t461">aretomo3_gui/gui/viewers/visualization.py</a></td>
                <td class="name left"><a href="z_140fbbd1834799a1_visualization_py.html#t461"><data value='AlignmentViewer'>AlignmentViewer</data></a></td>
                <td>34</td>
                <td>34</td>
                <td>0</td>
                <td class="right" data-ratio="0 34">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_140fbbd1834799a1_visualization_py.html#t525">aretomo3_gui/gui/viewers/visualization.py</a></td>
                <td class="name left"><a href="z_140fbbd1834799a1_visualization_py.html#t525"><data value='MetricsDashboard'>MetricsDashboard</data></a></td>
                <td>104</td>
                <td>104</td>
                <td>0</td>
                <td class="right" data-ratio="0 104">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_140fbbd1834799a1_visualization_py.html">aretomo3_gui/gui/viewers/visualization.py</a></td>
                <td class="name left"><a href="z_140fbbd1834799a1_visualization_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>43</td>
                <td>43</td>
                <td>0</td>
                <td class="right" data-ratio="0 43">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_cba28bd5643ea358___init___py.html">aretomo3_gui/gui/widgets/__init__.py</a></td>
                <td class="name left"><a href="z_cba28bd5643ea358___init___py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>5</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="5 5">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_cba28bd5643ea358_advanced_file_browser_py.html#t54">aretomo3_gui/gui/widgets/advanced_file_browser.py</a></td>
                <td class="name left"><a href="z_cba28bd5643ea358_advanced_file_browser_py.html#t54"><data value='ThumbnailGenerator'>ThumbnailGenerator</data></a></td>
                <td>42</td>
                <td>42</td>
                <td>0</td>
                <td class="right" data-ratio="0 42">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_cba28bd5643ea358_advanced_file_browser_py.html#t132">aretomo3_gui/gui/widgets/advanced_file_browser.py</a></td>
                <td class="name left"><a href="z_cba28bd5643ea358_advanced_file_browser_py.html#t132"><data value='FileInfoWidget'>FileInfoWidget</data></a></td>
                <td>150</td>
                <td>150</td>
                <td>0</td>
                <td class="right" data-ratio="0 150">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_cba28bd5643ea358_advanced_file_browser_py.html#t417">aretomo3_gui/gui/widgets/advanced_file_browser.py</a></td>
                <td class="name left"><a href="z_cba28bd5643ea358_advanced_file_browser_py.html#t417"><data value='FileListWidget'>FileListWidget</data></a></td>
                <td>23</td>
                <td>23</td>
                <td>0</td>
                <td class="right" data-ratio="0 23">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_cba28bd5643ea358_advanced_file_browser_py.html#t460">aretomo3_gui/gui/widgets/advanced_file_browser.py</a></td>
                <td class="name left"><a href="z_cba28bd5643ea358_advanced_file_browser_py.html#t460"><data value='AdvancedFileBrowser'>AdvancedFileBrowser</data></a></td>
                <td>109</td>
                <td>109</td>
                <td>0</td>
                <td class="right" data-ratio="0 109">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_cba28bd5643ea358_advanced_file_browser_py.html">aretomo3_gui/gui/widgets/advanced_file_browser.py</a></td>
                <td class="name left"><a href="z_cba28bd5643ea358_advanced_file_browser_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>57</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="57 57">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_cba28bd5643ea358_advanced_progress_widget_py.html#t40">aretomo3_gui/gui/widgets/advanced_progress_widget.py</a></td>
                <td class="name left"><a href="z_cba28bd5643ea358_advanced_progress_widget_py.html#t40"><data value='CircularProgressWidget'>CircularProgressWidget</data></a></td>
                <td>28</td>
                <td>28</td>
                <td>0</td>
                <td class="right" data-ratio="0 28">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_cba28bd5643ea358_advanced_progress_widget_py.html#t110">aretomo3_gui/gui/widgets/advanced_progress_widget.py</a></td>
                <td class="name left"><a href="z_cba28bd5643ea358_advanced_progress_widget_py.html#t110"><data value='TimelineProgressWidget'>TimelineProgressWidget</data></a></td>
                <td>40</td>
                <td>40</td>
                <td>0</td>
                <td class="right" data-ratio="0 40">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_cba28bd5643ea358_advanced_progress_widget_py.html#t212">aretomo3_gui/gui/widgets/advanced_progress_widget.py</a></td>
                <td class="name left"><a href="z_cba28bd5643ea358_advanced_progress_widget_py.html#t212"><data value='AdvancedProgressWidget'>AdvancedProgressWidget</data></a></td>
                <td>175</td>
                <td>175</td>
                <td>0</td>
                <td class="right" data-ratio="0 175">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_cba28bd5643ea358_advanced_progress_widget_py.html">aretomo3_gui/gui/widgets/advanced_progress_widget.py</a></td>
                <td class="name left"><a href="z_cba28bd5643ea358_advanced_progress_widget_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>43</td>
                <td>43</td>
                <td>0</td>
                <td class="right" data-ratio="0 43">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_cba28bd5643ea358_batch_processing_py.html#t33">aretomo3_gui/gui/widgets/batch_processing.py</a></td>
                <td class="name left"><a href="z_cba28bd5643ea358_batch_processing_py.html#t33"><data value='BatchProcessingWidget'>BatchProcessingWidget</data></a></td>
                <td>520</td>
                <td>520</td>
                <td>0</td>
                <td class="right" data-ratio="0 520">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_cba28bd5643ea358_batch_processing_py.html">aretomo3_gui/gui/widgets/batch_processing.py</a></td>
                <td class="name left"><a href="z_cba28bd5643ea358_batch_processing_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>41</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="41 41">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_cba28bd5643ea358_enhanced_progress_visualization_py.html#t39">aretomo3_gui/gui/widgets/enhanced_progress_visualization.py</a></td>
                <td class="name left"><a href="z_cba28bd5643ea358_enhanced_progress_visualization_py.html#t39"><data value='ProcessingTimePredictor'>ProcessingTimePredictor</data></a></td>
                <td>23</td>
                <td>23</td>
                <td>0</td>
                <td class="right" data-ratio="0 23">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_cba28bd5643ea358_enhanced_progress_visualization_py.html#t93">aretomo3_gui/gui/widgets/enhanced_progress_visualization.py</a></td>
                <td class="name left"><a href="z_cba28bd5643ea358_enhanced_progress_visualization_py.html#t93"><data value='ResourceUtilizationGraph'>ResourceUtilizationGraph</data></a></td>
                <td>47</td>
                <td>47</td>
                <td>0</td>
                <td class="right" data-ratio="0 47">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_cba28bd5643ea358_enhanced_progress_visualization_py.html#t190">aretomo3_gui/gui/widgets/enhanced_progress_visualization.py</a></td>
                <td class="name left"><a href="z_cba28bd5643ea358_enhanced_progress_visualization_py.html#t190"><data value='Enhanced3DProgressIndicator'>Enhanced3DProgressIndicator</data></a></td>
                <td>36</td>
                <td>36</td>
                <td>0</td>
                <td class="right" data-ratio="0 36">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_cba28bd5643ea358_enhanced_progress_visualization_py.html#t277">aretomo3_gui/gui/widgets/enhanced_progress_visualization.py</a></td>
                <td class="name left"><a href="z_cba28bd5643ea358_enhanced_progress_visualization_py.html#t277"><data value='EnhancedProgressVisualization'>EnhancedProgressVisualization</data></a></td>
                <td>86</td>
                <td>86</td>
                <td>0</td>
                <td class="right" data-ratio="0 86">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_cba28bd5643ea358_enhanced_progress_visualization_py.html#t470">aretomo3_gui/gui/widgets/enhanced_progress_visualization.py</a></td>
                <td class="name left"><a href="z_cba28bd5643ea358_enhanced_progress_visualization_py.html#t470"><data value='TestWindow'>TestWindow</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>36</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_cba28bd5643ea358_enhanced_progress_visualization_py.html">aretomo3_gui/gui/widgets/enhanced_progress_visualization.py</a></td>
                <td class="name left"><a href="z_cba28bd5643ea358_enhanced_progress_visualization_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>46</td>
                <td>46</td>
                <td>15</td>
                <td class="right" data-ratio="0 46">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_cba28bd5643ea358_enhanced_spinbox_py.html#t28">aretomo3_gui/gui/widgets/enhanced_spinbox.py</a></td>
                <td class="name left"><a href="z_cba28bd5643ea358_enhanced_spinbox_py.html#t28"><data value='EnhancedSpinBox'>EnhancedSpinBox</data></a></td>
                <td>13</td>
                <td>13</td>
                <td>0</td>
                <td class="right" data-ratio="0 13">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_cba28bd5643ea358_enhanced_spinbox_py.html#t146">aretomo3_gui/gui/widgets/enhanced_spinbox.py</a></td>
                <td class="name left"><a href="z_cba28bd5643ea358_enhanced_spinbox_py.html#t146"><data value='EnhancedDoubleSpinBox'>EnhancedDoubleSpinBox</data></a></td>
                <td>13</td>
                <td>13</td>
                <td>0</td>
                <td class="right" data-ratio="0 13">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_cba28bd5643ea358_enhanced_spinbox_py.html#t264">aretomo3_gui/gui/widgets/enhanced_spinbox.py</a></td>
                <td class="name left"><a href="z_cba28bd5643ea358_enhanced_spinbox_py.html#t264"><data value='SpinBoxWithSlider'>SpinBoxWithSlider</data></a></td>
                <td>28</td>
                <td>28</td>
                <td>0</td>
                <td class="right" data-ratio="0 28">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_cba28bd5643ea358_enhanced_spinbox_py.html#t360">aretomo3_gui/gui/widgets/enhanced_spinbox.py</a></td>
                <td class="name left"><a href="z_cba28bd5643ea358_enhanced_spinbox_py.html#t360"><data value='DoubleSpinBoxWithSlider'>DoubleSpinBoxWithSlider</data></a></td>
                <td>32</td>
                <td>32</td>
                <td>0</td>
                <td class="right" data-ratio="0 32">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_cba28bd5643ea358_enhanced_spinbox_py.html#t467">aretomo3_gui/gui/widgets/enhanced_spinbox.py</a></td>
                <td class="name left"><a href="z_cba28bd5643ea358_enhanced_spinbox_py.html#t467"><data value='ParameterSpinBoxWidget'>ParameterSpinBoxWidget</data></a></td>
                <td>53</td>
                <td>53</td>
                <td>0</td>
                <td class="right" data-ratio="0 53">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_cba28bd5643ea358_enhanced_spinbox_py.html">aretomo3_gui/gui/widgets/enhanced_spinbox.py</a></td>
                <td class="name left"><a href="z_cba28bd5643ea358_enhanced_spinbox_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>56</td>
                <td>56</td>
                <td>0</td>
                <td class="right" data-ratio="0 56">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_cba28bd5643ea358_gpu_manager_widget_py.html#t17">aretomo3_gui/gui/widgets/gpu_manager_widget.py</a></td>
                <td class="name left"><a href="z_cba28bd5643ea358_gpu_manager_widget_py.html#t17"><data value='GPUManagerWidget'>GPUManagerWidget</data></a></td>
                <td>36</td>
                <td>36</td>
                <td>0</td>
                <td class="right" data-ratio="0 36">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_cba28bd5643ea358_gpu_manager_widget_py.html">aretomo3_gui/gui/widgets/gpu_manager_widget.py</a></td>
                <td class="name left"><a href="z_cba28bd5643ea358_gpu_manager_widget_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>16</td>
                <td>16</td>
                <td>0</td>
                <td class="right" data-ratio="0 16">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_cba28bd5643ea358_live_tilt_series_monitor_py.html#t37">aretomo3_gui/gui/widgets/live_tilt_series_monitor.py</a></td>
                <td class="name left"><a href="z_cba28bd5643ea358_live_tilt_series_monitor_py.html#t37"><data value='TiltSeriesToggleWidget'>TiltSeriesToggleWidget</data></a></td>
                <td>49</td>
                <td>49</td>
                <td>0</td>
                <td class="right" data-ratio="0 49">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_cba28bd5643ea358_live_tilt_series_monitor_py.html#t179">aretomo3_gui/gui/widgets/live_tilt_series_monitor.py</a></td>
                <td class="name left"><a href="z_cba28bd5643ea358_live_tilt_series_monitor_py.html#t179"><data value='LivePlotCanvas'>LivePlotCanvas</data></a></td>
                <td>108</td>
                <td>108</td>
                <td>0</td>
                <td class="right" data-ratio="0 108">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_cba28bd5643ea358_live_tilt_series_monitor_py.html#t389">aretomo3_gui/gui/widgets/live_tilt_series_monitor.py</a></td>
                <td class="name left"><a href="z_cba28bd5643ea358_live_tilt_series_monitor_py.html#t389"><data value='LiveTiltSeriesMonitor'>LiveTiltSeriesMonitor</data></a></td>
                <td>27</td>
                <td>27</td>
                <td>0</td>
                <td class="right" data-ratio="0 27">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_cba28bd5643ea358_live_tilt_series_monitor_py.html">aretomo3_gui/gui/widgets/live_tilt_series_monitor.py</a></td>
                <td class="name left"><a href="z_cba28bd5643ea358_live_tilt_series_monitor_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>37</td>
                <td>37</td>
                <td>0</td>
                <td class="right" data-ratio="0 37">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_cba28bd5643ea358_multigpu_manager_py.html#t15">aretomo3_gui/gui/widgets/multigpu_manager.py</a></td>
                <td class="name left"><a href="z_cba28bd5643ea358_multigpu_manager_py.html#t15"><data value='MultiGPUManager'>MultiGPUManager</data></a></td>
                <td>57</td>
                <td>57</td>
                <td>0</td>
                <td class="right" data-ratio="0 57">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_cba28bd5643ea358_multigpu_manager_py.html">aretomo3_gui/gui/widgets/multigpu_manager.py</a></td>
                <td class="name left"><a href="z_cba28bd5643ea358_multigpu_manager_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>14</td>
                <td>14</td>
                <td>0</td>
                <td class="right" data-ratio="0 14">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_cba28bd5643ea358_processing_dashboard_py.html#t37">aretomo3_gui/gui/widgets/processing_dashboard.py</a></td>
                <td class="name left"><a href="z_cba28bd5643ea358_processing_dashboard_py.html#t37"><data value='SystemMonitorThread'>SystemMonitorThread</data></a></td>
                <td>23</td>
                <td>23</td>
                <td>0</td>
                <td class="right" data-ratio="0 23">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_cba28bd5643ea358_processing_dashboard_py.html#t107">aretomo3_gui/gui/widgets/processing_dashboard.py</a></td>
                <td class="name left"><a href="z_cba28bd5643ea358_processing_dashboard_py.html#t107"><data value='LiveGraphWidget'>LiveGraphWidget</data></a></td>
                <td>35</td>
                <td>35</td>
                <td>0</td>
                <td class="right" data-ratio="0 35">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_cba28bd5643ea358_processing_dashboard_py.html#t176">aretomo3_gui/gui/widgets/processing_dashboard.py</a></td>
                <td class="name left"><a href="z_cba28bd5643ea358_processing_dashboard_py.html#t176"><data value='ProcessingStatsWidget'>ProcessingStatsWidget</data></a></td>
                <td>64</td>
                <td>64</td>
                <td>0</td>
                <td class="right" data-ratio="0 64">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_cba28bd5643ea358_processing_dashboard_py.html#t296">aretomo3_gui/gui/widgets/processing_dashboard.py</a></td>
                <td class="name left"><a href="z_cba28bd5643ea358_processing_dashboard_py.html#t296"><data value='ProcessingDashboard'>ProcessingDashboard</data></a></td>
                <td>37</td>
                <td>37</td>
                <td>0</td>
                <td class="right" data-ratio="0 37">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_cba28bd5643ea358_processing_dashboard_py.html">aretomo3_gui/gui/widgets/processing_dashboard.py</a></td>
                <td class="name left"><a href="z_cba28bd5643ea358_processing_dashboard_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>40</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="40 40">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_cba28bd5643ea358_project_management_py.html#t50">aretomo3_gui/gui/widgets/project_management.py</a></td>
                <td class="name left"><a href="z_cba28bd5643ea358_project_management_py.html#t50"><data value='ProjectLoadWorker'>ProjectLoadWorker</data></a></td>
                <td>26</td>
                <td>26</td>
                <td>0</td>
                <td class="right" data-ratio="0 26">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_cba28bd5643ea358_project_management_py.html#t107">aretomo3_gui/gui/widgets/project_management.py</a></td>
                <td class="name left"><a href="z_cba28bd5643ea358_project_management_py.html#t107"><data value='ProjectManagementWidget'>ProjectManagementWidget</data></a></td>
                <td>556</td>
                <td>556</td>
                <td>0</td>
                <td class="right" data-ratio="0 556">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_cba28bd5643ea358_project_management_py.html">aretomo3_gui/gui/widgets/project_management.py</a></td>
                <td class="name left"><a href="z_cba28bd5643ea358_project_management_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>62</td>
                <td>62</td>
                <td>0</td>
                <td class="right" data-ratio="0 62">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_cba28bd5643ea358_realtime_widget_py.html#t40">aretomo3_gui/gui/widgets/realtime_widget.py</a></td>
                <td class="name left"><a href="z_cba28bd5643ea358_realtime_widget_py.html#t40"><data value='RealTimeStatsWidget'>RealTimeStatsWidget</data></a></td>
                <td>38</td>
                <td>38</td>
                <td>0</td>
                <td class="right" data-ratio="0 38">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_cba28bd5643ea358_realtime_widget_py.html#t138">aretomo3_gui/gui/widgets/realtime_widget.py</a></td>
                <td class="name left"><a href="z_cba28bd5643ea358_realtime_widget_py.html#t138"><data value='JobListWidget'>JobListWidget</data></a></td>
                <td>51</td>
                <td>51</td>
                <td>0</td>
                <td class="right" data-ratio="0 51">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_cba28bd5643ea358_realtime_widget_py.html#t239">aretomo3_gui/gui/widgets/realtime_widget.py</a></td>
                <td class="name left"><a href="z_cba28bd5643ea358_realtime_widget_py.html#t239"><data value='RealTimeControlWidget'>RealTimeControlWidget</data></a></td>
                <td>50</td>
                <td>50</td>
                <td>0</td>
                <td class="right" data-ratio="0 50">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_cba28bd5643ea358_realtime_widget_py.html#t362">aretomo3_gui/gui/widgets/realtime_widget.py</a></td>
                <td class="name left"><a href="z_cba28bd5643ea358_realtime_widget_py.html#t362"><data value='RealTimeProcessingWidget'>RealTimeProcessingWidget</data></a></td>
                <td>71</td>
                <td>71</td>
                <td>0</td>
                <td class="right" data-ratio="0 71">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_cba28bd5643ea358_realtime_widget_py.html">aretomo3_gui/gui/widgets/realtime_widget.py</a></td>
                <td class="name left"><a href="z_cba28bd5643ea358_realtime_widget_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>43</td>
                <td>43</td>
                <td>0</td>
                <td class="right" data-ratio="0 43">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_cba28bd5643ea358_resource_monitor_py.html#t29">aretomo3_gui/gui/widgets/resource_monitor.py</a></td>
                <td class="name left"><a href="z_cba28bd5643ea358_resource_monitor_py.html#t29"><data value='ResourceMonitor'>ResourceMonitor</data></a></td>
                <td>97</td>
                <td>97</td>
                <td>0</td>
                <td class="right" data-ratio="0 97">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_cba28bd5643ea358_resource_monitor_py.html">aretomo3_gui/gui/widgets/resource_monitor.py</a></td>
                <td class="name left"><a href="z_cba28bd5643ea358_resource_monitor_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>18</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="15 18">83%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_cba28bd5643ea358_smart_file_organizer_py.html#t37">aretomo3_gui/gui/widgets/smart_file_organizer.py</a></td>
                <td class="name left"><a href="z_cba28bd5643ea358_smart_file_organizer_py.html#t37"><data value='FileMetadata'>FileMetadata</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_cba28bd5643ea358_smart_file_organizer_py.html#t64">aretomo3_gui/gui/widgets/smart_file_organizer.py</a></td>
                <td class="name left"><a href="z_cba28bd5643ea358_smart_file_organizer_py.html#t64"><data value='ProjectStructure'>ProjectStructure</data></a></td>
                <td>5</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="0 5">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_cba28bd5643ea358_smart_file_organizer_py.html#t109">aretomo3_gui/gui/widgets/smart_file_organizer.py</a></td>
                <td class="name left"><a href="z_cba28bd5643ea358_smart_file_organizer_py.html#t109"><data value='MetadataExtractor'>MetadataExtractor</data></a></td>
                <td>98</td>
                <td>98</td>
                <td>0</td>
                <td class="right" data-ratio="0 98">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_cba28bd5643ea358_smart_file_organizer_py.html#t380">aretomo3_gui/gui/widgets/smart_file_organizer.py</a></td>
                <td class="name left"><a href="z_cba28bd5643ea358_smart_file_organizer_py.html#t380"><data value='IntelligentFileNamer'>IntelligentFileNamer</data></a></td>
                <td>28</td>
                <td>28</td>
                <td>0</td>
                <td class="right" data-ratio="0 28">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_cba28bd5643ea358_smart_file_organizer_py.html#t469">aretomo3_gui/gui/widgets/smart_file_organizer.py</a></td>
                <td class="name left"><a href="z_cba28bd5643ea358_smart_file_organizer_py.html#t469"><data value='SmartFileOrganizer'>SmartFileOrganizer</data></a></td>
                <td>105</td>
                <td>105</td>
                <td>0</td>
                <td class="right" data-ratio="0 105">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_cba28bd5643ea358_smart_file_organizer_py.html">aretomo3_gui/gui/widgets/smart_file_organizer.py</a></td>
                <td class="name left"><a href="z_cba28bd5643ea358_smart_file_organizer_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>76</td>
                <td>76</td>
                <td>7</td>
                <td class="right" data-ratio="0 76">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_cba28bd5643ea358_unified_processing_monitor_py.html#t48">aretomo3_gui/gui/widgets/unified_processing_monitor.py</a></td>
                <td class="name left"><a href="z_cba28bd5643ea358_unified_processing_monitor_py.html#t48"><data value='SystemMonitorThread'>SystemMonitorThread</data></a></td>
                <td>23</td>
                <td>23</td>
                <td>0</td>
                <td class="right" data-ratio="0 23">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_cba28bd5643ea358_unified_processing_monitor_py.html#t114">aretomo3_gui/gui/widgets/unified_processing_monitor.py</a></td>
                <td class="name left"><a href="z_cba28bd5643ea358_unified_processing_monitor_py.html#t114"><data value='LiveGraphWidget'>LiveGraphWidget</data></a></td>
                <td>35</td>
                <td>35</td>
                <td>0</td>
                <td class="right" data-ratio="0 35">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_cba28bd5643ea358_unified_processing_monitor_py.html#t179">aretomo3_gui/gui/widgets/unified_processing_monitor.py</a></td>
                <td class="name left"><a href="z_cba28bd5643ea358_unified_processing_monitor_py.html#t179"><data value='ProcessingQueueWidget'>ProcessingQueueWidget</data></a></td>
                <td>99</td>
                <td>99</td>
                <td>0</td>
                <td class="right" data-ratio="0 99">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_cba28bd5643ea358_unified_processing_monitor_py.html#t348">aretomo3_gui/gui/widgets/unified_processing_monitor.py</a></td>
                <td class="name left"><a href="z_cba28bd5643ea358_unified_processing_monitor_py.html#t348"><data value='UnifiedProcessingMonitor'>UnifiedProcessingMonitor</data></a></td>
                <td>357</td>
                <td>357</td>
                <td>0</td>
                <td class="right" data-ratio="0 357">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_cba28bd5643ea358_unified_processing_monitor_py.html">aretomo3_gui/gui/widgets/unified_processing_monitor.py</a></td>
                <td class="name left"><a href="z_cba28bd5643ea358_unified_processing_monitor_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>63</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="63 63">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_cba28bd5643ea358_web_server_widget_py.html#t40">aretomo3_gui/gui/widgets/web_server_widget.py</a></td>
                <td class="name left"><a href="z_cba28bd5643ea358_web_server_widget_py.html#t40"><data value='WebServerThread'>WebServerThread</data></a></td>
                <td>16</td>
                <td>16</td>
                <td>0</td>
                <td class="right" data-ratio="0 16">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_cba28bd5643ea358_web_server_widget_py.html#t74">aretomo3_gui/gui/widgets/web_server_widget.py</a></td>
                <td class="name left"><a href="z_cba28bd5643ea358_web_server_widget_py.html#t74"><data value='QRCodeWidget'>QRCodeWidget</data></a></td>
                <td>20</td>
                <td>20</td>
                <td>0</td>
                <td class="right" data-ratio="0 20">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_cba28bd5643ea358_web_server_widget_py.html#t133">aretomo3_gui/gui/widgets/web_server_widget.py</a></td>
                <td class="name left"><a href="z_cba28bd5643ea358_web_server_widget_py.html#t133"><data value='WebServerControlWidget'>WebServerControlWidget</data></a></td>
                <td>213</td>
                <td>213</td>
                <td>0</td>
                <td class="right" data-ratio="0 213">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_cba28bd5643ea358_web_server_widget_py.html">aretomo3_gui/gui/widgets/web_server_widget.py</a></td>
                <td class="name left"><a href="z_cba28bd5643ea358_web_server_widget_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>47</td>
                <td>47</td>
                <td>0</td>
                <td class="right" data-ratio="0 47">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6ffcbd6373365cc1___init___py.html">aretomo3_gui/integration/__init__.py</a></td>
                <td class="name left"><a href="z_6ffcbd6373365cc1___init___py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6ffcbd6373365cc1_external_tools_py.html#t21">aretomo3_gui/integration/external_tools.py</a></td>
                <td class="name left"><a href="z_6ffcbd6373365cc1_external_tools_py.html#t21"><data value='ExternalTool'>ExternalTool</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6ffcbd6373365cc1_external_tools_py.html#t33">aretomo3_gui/integration/external_tools.py</a></td>
                <td class="name left"><a href="z_6ffcbd6373365cc1_external_tools_py.html#t33"><data value='ToolCommand'>ToolCommand</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6ffcbd6373365cc1_external_tools_py.html#t44">aretomo3_gui/integration/external_tools.py</a></td>
                <td class="name left"><a href="z_6ffcbd6373365cc1_external_tools_py.html#t44"><data value='ExternalToolInterface'>ExternalToolInterface</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>6</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6ffcbd6373365cc1_external_tools_py.html#t63">aretomo3_gui/integration/external_tools.py</a></td>
                <td class="name left"><a href="z_6ffcbd6373365cc1_external_tools_py.html#t63"><data value='RELIONInterface'>RELIONInterface</data></a></td>
                <td>30</td>
                <td>30</td>
                <td>0</td>
                <td class="right" data-ratio="0 30">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6ffcbd6373365cc1_external_tools_py.html#t145">aretomo3_gui/integration/external_tools.py</a></td>
                <td class="name left"><a href="z_6ffcbd6373365cc1_external_tools_py.html#t145"><data value='IMODInterface'>IMODInterface</data></a></td>
                <td>40</td>
                <td>40</td>
                <td>0</td>
                <td class="right" data-ratio="0 40">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6ffcbd6373365cc1_external_tools_py.html#t240">aretomo3_gui/integration/external_tools.py</a></td>
                <td class="name left"><a href="z_6ffcbd6373365cc1_external_tools_py.html#t240"><data value='EMAN2Interface'>EMAN2Interface</data></a></td>
                <td>30</td>
                <td>30</td>
                <td>0</td>
                <td class="right" data-ratio="0 30">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6ffcbd6373365cc1_external_tools_py.html#t322">aretomo3_gui/integration/external_tools.py</a></td>
                <td class="name left"><a href="z_6ffcbd6373365cc1_external_tools_py.html#t322"><data value='ExternalToolsManager'>ExternalToolsManager</data></a></td>
                <td>66</td>
                <td>66</td>
                <td>0</td>
                <td class="right" data-ratio="0 66">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6ffcbd6373365cc1_external_tools_py.html">aretomo3_gui/integration/external_tools.py</a></td>
                <td class="name left"><a href="z_6ffcbd6373365cc1_external_tools_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>59</td>
                <td>59</td>
                <td>6</td>
                <td class="right" data-ratio="0 59">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6ffcbd6373365cc1_manager_py.html#t15">aretomo3_gui/integration/manager.py</a></td>
                <td class="name left"><a href="z_6ffcbd6373365cc1_manager_py.html#t15"><data value='ExternalSoftware'>ExternalSoftware</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>6</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6ffcbd6373365cc1_manager_py.html#t34">aretomo3_gui/integration/manager.py</a></td>
                <td class="name left"><a href="z_6ffcbd6373365cc1_manager_py.html#t34"><data value='RELIONIntegration'>RELIONIntegration</data></a></td>
                <td>39</td>
                <td>39</td>
                <td>0</td>
                <td class="right" data-ratio="0 39">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6ffcbd6373365cc1_manager_py.html#t123">aretomo3_gui/integration/manager.py</a></td>
                <td class="name left"><a href="z_6ffcbd6373365cc1_manager_py.html#t123"><data value='IMODIntegration'>IMODIntegration</data></a></td>
                <td>26</td>
                <td>26</td>
                <td>0</td>
                <td class="right" data-ratio="0 26">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6ffcbd6373365cc1_manager_py.html#t189">aretomo3_gui/integration/manager.py</a></td>
                <td class="name left"><a href="z_6ffcbd6373365cc1_manager_py.html#t189"><data value='WarpIntegration'>WarpIntegration</data></a></td>
                <td>17</td>
                <td>17</td>
                <td>0</td>
                <td class="right" data-ratio="0 17">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6ffcbd6373365cc1_manager_py.html#t236">aretomo3_gui/integration/manager.py</a></td>
                <td class="name left"><a href="z_6ffcbd6373365cc1_manager_py.html#t236"><data value='SoftwareManager'>SoftwareManager</data></a></td>
                <td>22</td>
                <td>22</td>
                <td>0</td>
                <td class="right" data-ratio="0 22">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6ffcbd6373365cc1_manager_py.html">aretomo3_gui/integration/manager.py</a></td>
                <td class="name left"><a href="z_6ffcbd6373365cc1_manager_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>36</td>
                <td>36</td>
                <td>6</td>
                <td class="right" data-ratio="0 36">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_3caff36e279812ae_main_py.html">aretomo3_gui/main.py</a></td>
                <td class="name left"><a href="z_3caff36e279812ae_main_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>108</td>
                <td>108</td>
                <td>2</td>
                <td class="right" data-ratio="0 108">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_578c64bf13b5f5c5___init___py.html">aretomo3_gui/particle_picking/__init__.py</a></td>
                <td class="name left"><a href="z_578c64bf13b5f5c5___init___py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_578c64bf13b5f5c5_picker_py.html#t16">aretomo3_gui/particle_picking/picker.py</a></td>
                <td class="name left"><a href="z_578c64bf13b5f5c5_picker_py.html#t16"><data value='Particle'>Particle</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_578c64bf13b5f5c5_picker_py.html#t28">aretomo3_gui/particle_picking/picker.py</a></td>
                <td class="name left"><a href="z_578c64bf13b5f5c5_picker_py.html#t28"><data value='TemplateBasedPicker'>TemplateBasedPicker</data></a></td>
                <td>15</td>
                <td>15</td>
                <td>0</td>
                <td class="right" data-ratio="0 15">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_578c64bf13b5f5c5_picker_py.html#t71">aretomo3_gui/particle_picking/picker.py</a></td>
                <td class="name left"><a href="z_578c64bf13b5f5c5_picker_py.html#t71"><data value='BlobDetector'>BlobDetector</data></a></td>
                <td>11</td>
                <td>11</td>
                <td>0</td>
                <td class="right" data-ratio="0 11">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_578c64bf13b5f5c5_picker_py.html#t108">aretomo3_gui/particle_picking/picker.py</a></td>
                <td class="name left"><a href="z_578c64bf13b5f5c5_picker_py.html#t108"><data value='MLParticlePicker'>MLParticlePicker</data></a></td>
                <td>9</td>
                <td>9</td>
                <td>0</td>
                <td class="right" data-ratio="0 9">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_578c64bf13b5f5c5_picker_py.html#t134">aretomo3_gui/particle_picking/picker.py</a></td>
                <td class="name left"><a href="z_578c64bf13b5f5c5_picker_py.html#t134"><data value='ParticleClassifier'>ParticleClassifier</data></a></td>
                <td>23</td>
                <td>23</td>
                <td>0</td>
                <td class="right" data-ratio="0 23">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_578c64bf13b5f5c5_picker_py.html#t194">aretomo3_gui/particle_picking/picker.py</a></td>
                <td class="name left"><a href="z_578c64bf13b5f5c5_picker_py.html#t194"><data value='ParticlePickingManager'>ParticlePickingManager</data></a></td>
                <td>19</td>
                <td>19</td>
                <td>0</td>
                <td class="right" data-ratio="0 19">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_578c64bf13b5f5c5_picker_py.html">aretomo3_gui/particle_picking/picker.py</a></td>
                <td class="name left"><a href="z_578c64bf13b5f5c5_picker_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>41</td>
                <td>41</td>
                <td>0</td>
                <td class="right" data-ratio="0 41">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_3caff36e279812ae_qt_backend_init_py.html">aretomo3_gui/qt_backend_init.py</a></td>
                <td class="name left"><a href="z_3caff36e279812ae_qt_backend_init_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>50</td>
                <td>50</td>
                <td>0</td>
                <td class="right" data-ratio="0 50">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2ed433c7d3dc6b1b___init___py.html">aretomo3_gui/realtime/__init__.py</a></td>
                <td class="name left"><a href="z_2ed433c7d3dc6b1b___init___py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2ed433c7d3dc6b1b_processor_py.html#t17">aretomo3_gui/realtime/processor.py</a></td>
                <td class="name left"><a href="z_2ed433c7d3dc6b1b_processor_py.html#t17"><data value='ProcessingPipeline'>ProcessingPipeline</data></a></td>
                <td>12</td>
                <td>12</td>
                <td>0</td>
                <td class="right" data-ratio="0 12">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2ed433c7d3dc6b1b_processor_py.html#t44">aretomo3_gui/realtime/processor.py</a></td>
                <td class="name left"><a href="z_2ed433c7d3dc6b1b_processor_py.html#t44"><data value='TiltSeriesHandler'>TiltSeriesHandler</data></a></td>
                <td>6</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 6">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2ed433c7d3dc6b1b_processor_py.html#t65">aretomo3_gui/realtime/processor.py</a></td>
                <td class="name left"><a href="z_2ed433c7d3dc6b1b_processor_py.html#t65"><data value='RealTimeProcessor'>RealTimeProcessor</data></a></td>
                <td>42</td>
                <td>42</td>
                <td>0</td>
                <td class="right" data-ratio="0 42">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2ed433c7d3dc6b1b_processor_py.html">aretomo3_gui/realtime/processor.py</a></td>
                <td class="name left"><a href="z_2ed433c7d3dc6b1b_processor_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>25</td>
                <td>25</td>
                <td>0</td>
                <td class="right" data-ratio="0 25">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_07f42b498c11b456___init___py.html">aretomo3_gui/subtomogram/__init__.py</a></td>
                <td class="name left"><a href="z_07f42b498c11b456___init___py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_07f42b498c11b456_averaging_py.html#t15">aretomo3_gui/subtomogram/averaging.py</a></td>
                <td class="name left"><a href="z_07f42b498c11b456_averaging_py.html#t15"><data value='Subtomogram'>Subtomogram</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_07f42b498c11b456_averaging_py.html#t26">aretomo3_gui/subtomogram/averaging.py</a></td>
                <td class="name left"><a href="z_07f42b498c11b456_averaging_py.html#t26"><data value='SubtomogramExtractor'>SubtomogramExtractor</data></a></td>
                <td>11</td>
                <td>11</td>
                <td>0</td>
                <td class="right" data-ratio="0 11">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_07f42b498c11b456_averaging_py.html#t66">aretomo3_gui/subtomogram/averaging.py</a></td>
                <td class="name left"><a href="z_07f42b498c11b456_averaging_py.html#t66"><data value='SubtomogramAligner'>SubtomogramAligner</data></a></td>
                <td>29</td>
                <td>29</td>
                <td>0</td>
                <td class="right" data-ratio="0 29">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_07f42b498c11b456_averaging_py.html#t140">aretomo3_gui/subtomogram/averaging.py</a></td>
                <td class="name left"><a href="z_07f42b498c11b456_averaging_py.html#t140"><data value='SubtomogramClassifier'>SubtomogramClassifier</data></a></td>
                <td>24</td>
                <td>24</td>
                <td>0</td>
                <td class="right" data-ratio="0 24">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_07f42b498c11b456_averaging_py.html#t195">aretomo3_gui/subtomogram/averaging.py</a></td>
                <td class="name left"><a href="z_07f42b498c11b456_averaging_py.html#t195"><data value='SubtomogramAveraging'>SubtomogramAveraging</data></a></td>
                <td>43</td>
                <td>43</td>
                <td>0</td>
                <td class="right" data-ratio="0 43">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_07f42b498c11b456_averaging_py.html">aretomo3_gui/subtomogram/averaging.py</a></td>
                <td class="name left"><a href="z_07f42b498c11b456_averaging_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>34</td>
                <td>34</td>
                <td>0</td>
                <td class="right" data-ratio="0 34">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6f4929cd9ce15cf8___init___py.html">aretomo3_gui/tools/__init__.py</a></td>
                <td class="name left"><a href="z_6f4929cd9ce15cf8___init___py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6f4929cd9ce15cf8_kmeans_integration_py.html#t38">aretomo3_gui/tools/kmeans_integration.py</a></td>
                <td class="name left"><a href="z_6f4929cd9ce15cf8_kmeans_integration_py.html#t38"><data value='KmeansWorker'>KmeansWorker</data></a></td>
                <td>44</td>
                <td>44</td>
                <td>0</td>
                <td class="right" data-ratio="0 44">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6f4929cd9ce15cf8_kmeans_integration_py.html#t151">aretomo3_gui/tools/kmeans_integration.py</a></td>
                <td class="name left"><a href="z_6f4929cd9ce15cf8_kmeans_integration_py.html#t151"><data value='KmeansMetricsIntegration'>KmeansMetricsIntegration</data></a></td>
                <td>125</td>
                <td>125</td>
                <td>0</td>
                <td class="right" data-ratio="0 125">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6f4929cd9ce15cf8_kmeans_integration_py.html">aretomo3_gui/tools/kmeans_integration.py</a></td>
                <td class="name left"><a href="z_6f4929cd9ce15cf8_kmeans_integration_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>32</td>
                <td>32</td>
                <td>0</td>
                <td class="right" data-ratio="0 32">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_0bdf6ef230ab75fb___init___py.html">aretomo3_gui/utils/__init__.py</a></td>
                <td class="name left"><a href="z_0bdf6ef230ab75fb___init___py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>15</td>
                <td>8</td>
                <td>0</td>
                <td class="right" data-ratio="7 15">47%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_0bdf6ef230ab75fb_aretomo3_parser_py.html#t21">aretomo3_gui/utils/aretomo3_parser.py</a></td>
                <td class="name left"><a href="z_0bdf6ef230ab75fb_aretomo3_parser_py.html#t21"><data value='AreTomo3ResultsParser'>AreTomo3ResultsParser</data></a></td>
                <td>630</td>
                <td>630</td>
                <td>0</td>
                <td class="right" data-ratio="0 630">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_0bdf6ef230ab75fb_aretomo3_parser_py.html">aretomo3_gui/utils/aretomo3_parser.py</a></td>
                <td class="name left"><a href="z_0bdf6ef230ab75fb_aretomo3_parser_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>35</td>
                <td>35</td>
                <td>0</td>
                <td class="right" data-ratio="0 35">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_0bdf6ef230ab75fb_documentation_generator_py.html#t21">aretomo3_gui/utils/documentation_generator.py</a></td>
                <td class="name left"><a href="z_0bdf6ef230ab75fb_documentation_generator_py.html#t21"><data value='DocumentationSection'>DocumentationSection</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_0bdf6ef230ab75fb_documentation_generator_py.html#t35">aretomo3_gui/utils/documentation_generator.py</a></td>
                <td class="name left"><a href="z_0bdf6ef230ab75fb_documentation_generator_py.html#t35"><data value='DocumentationGenerator'>DocumentationGenerator</data></a></td>
                <td>177</td>
                <td>177</td>
                <td>0</td>
                <td class="right" data-ratio="0 177">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_0bdf6ef230ab75fb_documentation_generator_py.html">aretomo3_gui/utils/documentation_generator.py</a></td>
                <td class="name left"><a href="z_0bdf6ef230ab75fb_documentation_generator_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>42</td>
                <td>42</td>
                <td>3</td>
                <td class="right" data-ratio="0 42">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_0bdf6ef230ab75fb_eer_reader_py.html#t34">aretomo3_gui/utils/eer_reader.py</a></td>
                <td class="name left"><a href="z_0bdf6ef230ab75fb_eer_reader_py.html#t34"><data value='EERMetadata'>EERMetadata</data></a></td>
                <td>16</td>
                <td>16</td>
                <td>0</td>
                <td class="right" data-ratio="0 16">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_0bdf6ef230ab75fb_eer_reader_py.html">aretomo3_gui/utils/eer_reader.py</a></td>
                <td class="name left"><a href="z_0bdf6ef230ab75fb_eer_reader_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>201</td>
                <td>201</td>
                <td>20</td>
                <td class="right" data-ratio="0 201">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_0bdf6ef230ab75fb_eer_reader_new_py.html#t27">aretomo3_gui/utils/eer_reader_new.py</a></td>
                <td class="name left"><a href="z_0bdf6ef230ab75fb_eer_reader_new_py.html#t27"><data value='EERHeader'>EERHeader</data></a></td>
                <td>19</td>
                <td>19</td>
                <td>0</td>
                <td class="right" data-ratio="0 19">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_0bdf6ef230ab75fb_eer_reader_new_py.html#t74">aretomo3_gui/utils/eer_reader_new.py</a></td>
                <td class="name left"><a href="z_0bdf6ef230ab75fb_eer_reader_new_py.html#t74"><data value='EERReaderError'>EERReaderError</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_0bdf6ef230ab75fb_eer_reader_new_py.html#t80">aretomo3_gui/utils/eer_reader_new.py</a></td>
                <td class="name left"><a href="z_0bdf6ef230ab75fb_eer_reader_new_py.html#t80"><data value='EERReader'>EERReader</data></a></td>
                <td>140</td>
                <td>140</td>
                <td>0</td>
                <td class="right" data-ratio="0 140">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_0bdf6ef230ab75fb_eer_reader_new_py.html">aretomo3_gui/utils/eer_reader_new.py</a></td>
                <td class="name left"><a href="z_0bdf6ef230ab75fb_eer_reader_new_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>41</td>
                <td>41</td>
                <td>9</td>
                <td class="right" data-ratio="0 41">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_0bdf6ef230ab75fb_export_functions_py.html">aretomo3_gui/utils/export_functions.py</a></td>
                <td class="name left"><a href="z_0bdf6ef230ab75fb_export_functions_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>277</td>
                <td>256</td>
                <td>0</td>
                <td class="right" data-ratio="21 277">8%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_0bdf6ef230ab75fb_file_utils_py.html">aretomo3_gui/utils/file_utils.py</a></td>
                <td class="name left"><a href="z_0bdf6ef230ab75fb_file_utils_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>132</td>
                <td>14</td>
                <td>0</td>
                <td class="right" data-ratio="118 132">89%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_0bdf6ef230ab75fb_mdoc_parser_py.html#t16">aretomo3_gui/utils/mdoc_parser.py</a></td>
                <td class="name left"><a href="z_0bdf6ef230ab75fb_mdoc_parser_py.html#t16"><data value='MDOCParser'>MDOCParser</data></a></td>
                <td>79</td>
                <td>79</td>
                <td>0</td>
                <td class="right" data-ratio="0 79">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_0bdf6ef230ab75fb_mdoc_parser_py.html">aretomo3_gui/utils/mdoc_parser.py</a></td>
                <td class="name left"><a href="z_0bdf6ef230ab75fb_mdoc_parser_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>28</td>
                <td>10</td>
                <td>0</td>
                <td class="right" data-ratio="18 28">64%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_0bdf6ef230ab75fb_pdf_report_generator_py.html#t48">aretomo3_gui/utils/pdf_report_generator.py</a></td>
                <td class="name left"><a href="z_0bdf6ef230ab75fb_pdf_report_generator_py.html#t48"><data value='TomogramPDFReportGenerator'>TomogramPDFReportGenerator</data></a></td>
                <td>147</td>
                <td>147</td>
                <td>0</td>
                <td class="right" data-ratio="0 147">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_0bdf6ef230ab75fb_pdf_report_generator_py.html">aretomo3_gui/utils/pdf_report_generator.py</a></td>
                <td class="name left"><a href="z_0bdf6ef230ab75fb_pdf_report_generator_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>36</td>
                <td>36</td>
                <td>0</td>
                <td class="right" data-ratio="0 36">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_0bdf6ef230ab75fb_utils_py.html#t18">aretomo3_gui/utils/utils.py</a></td>
                <td class="name left"><a href="z_0bdf6ef230ab75fb_utils_py.html#t18"><data value='ProgressDialog'>ProgressDialog</data></a></td>
                <td>9</td>
                <td>9</td>
                <td>0</td>
                <td class="right" data-ratio="0 9">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_0bdf6ef230ab75fb_utils_py.html">aretomo3_gui/utils/utils.py</a></td>
                <td class="name left"><a href="z_0bdf6ef230ab75fb_utils_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>76</td>
                <td>56</td>
                <td>0</td>
                <td class="right" data-ratio="20 76">26%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_0bdf6ef230ab75fb_warning_suppression_py.html">aretomo3_gui/utils/warning_suppression.py</a></td>
                <td class="name left"><a href="z_0bdf6ef230ab75fb_warning_suppression_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>29</td>
                <td>29</td>
                <td>0</td>
                <td class="right" data-ratio="0 29">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_d0cb65951d5615b5___init___py.html">aretomo3_gui/visualization/__init__.py</a></td>
                <td class="name left"><a href="z_d0cb65951d5615b5___init___py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_d0cb65951d5615b5_engine_py.html#t17">aretomo3_gui/visualization/engine.py</a></td>
                <td class="name left"><a href="z_d0cb65951d5615b5_engine_py.html#t17"><data value='Volume3D'>Volume3D</data></a></td>
                <td>5</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="0 5">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_d0cb65951d5615b5_engine_py.html#t34">aretomo3_gui/visualization/engine.py</a></td>
                <td class="name left"><a href="z_d0cb65951d5615b5_engine_py.html#t34"><data value='VolumeRenderer'>VolumeRenderer</data></a></td>
                <td>47</td>
                <td>47</td>
                <td>0</td>
                <td class="right" data-ratio="0 47">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_d0cb65951d5615b5_engine_py.html#t129">aretomo3_gui/visualization/engine.py</a></td>
                <td class="name left"><a href="z_d0cb65951d5615b5_engine_py.html#t129"><data value='NapariViewer'>NapariViewer</data></a></td>
                <td>14</td>
                <td>14</td>
                <td>0</td>
                <td class="right" data-ratio="0 14">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_d0cb65951d5615b5_engine_py.html#t165">aretomo3_gui/visualization/engine.py</a></td>
                <td class="name left"><a href="z_d0cb65951d5615b5_engine_py.html#t165"><data value='VisPyRenderer'>VisPyRenderer</data></a></td>
                <td>12</td>
                <td>12</td>
                <td>0</td>
                <td class="right" data-ratio="0 12">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_d0cb65951d5615b5_engine_py.html#t196">aretomo3_gui/visualization/engine.py</a></td>
                <td class="name left"><a href="z_d0cb65951d5615b5_engine_py.html#t196"><data value='Visualization3D'>Visualization3D</data></a></td>
                <td>33</td>
                <td>33</td>
                <td>0</td>
                <td class="right" data-ratio="0 33">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_d0cb65951d5615b5_engine_py.html">aretomo3_gui/visualization/engine.py</a></td>
                <td class="name left"><a href="z_d0cb65951d5615b5_engine_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>37</td>
                <td>37</td>
                <td>0</td>
                <td class="right" data-ratio="0 37">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_0878bee3da37af71___init___py.html">aretomo3_gui/web/__init__.py</a></td>
                <td class="name left"><a href="z_0878bee3da37af71___init___py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_0878bee3da37af71_api_server_py.html#t73">aretomo3_gui/web/api_server.py</a></td>
                <td class="name left"><a href="z_0878bee3da37af71_api_server_py.html#t73"><data value='ProcessingRequest'>ProcessingRequest</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_0878bee3da37af71_api_server_py.html#t84">aretomo3_gui/web/api_server.py</a></td>
                <td class="name left"><a href="z_0878bee3da37af71_api_server_py.html#t84"><data value='JobResponse'>JobResponse</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_0878bee3da37af71_api_server_py.html#t97">aretomo3_gui/web/api_server.py</a></td>
                <td class="name left"><a href="z_0878bee3da37af71_api_server_py.html#t97"><data value='StatsResponse'>StatsResponse</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_0878bee3da37af71_api_server_py.html#t109">aretomo3_gui/web/api_server.py</a></td>
                <td class="name left"><a href="z_0878bee3da37af71_api_server_py.html#t109"><data value='SystemStatus'>SystemStatus</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_0878bee3da37af71_api_server_py.html#t120">aretomo3_gui/web/api_server.py</a></td>
                <td class="name left"><a href="z_0878bee3da37af71_api_server_py.html#t120"><data value='WebSocketManager'>WebSocketManager</data></a></td>
                <td>21</td>
                <td>21</td>
                <td>0</td>
                <td class="right" data-ratio="0 21">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_0878bee3da37af71_api_server_py.html#t166">aretomo3_gui/web/api_server.py</a></td>
                <td class="name left"><a href="z_0878bee3da37af71_api_server_py.html#t166"><data value='AreTomo3WebAPI'>AreTomo3WebAPI</data></a></td>
                <td>735</td>
                <td>735</td>
                <td>0</td>
                <td class="right" data-ratio="0 735">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_0878bee3da37af71_api_server_py.html#t523">aretomo3_gui/web/api_server.py</a></td>
                <td class="name left"><a href="z_0878bee3da37af71_api_server_py.html#t523"><data value='WebLogHandler'>AreTomo3WebAPI.setup_python_log_handler.WebLogHandler</data></a></td>
                <td>8</td>
                <td>8</td>
                <td>0</td>
                <td class="right" data-ratio="0 8">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_0878bee3da37af71_api_server_py.html">aretomo3_gui/web/api_server.py</a></td>
                <td class="name left"><a href="z_0878bee3da37af71_api_server_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>115</td>
                <td>115</td>
                <td>0</td>
                <td class="right" data-ratio="0 115">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_0878bee3da37af71_plot_server_py.html#t18">aretomo3_gui/web/plot_server.py</a></td>
                <td class="name left"><a href="z_0878bee3da37af71_plot_server_py.html#t18"><data value='AreTomo3PlotServer'>AreTomo3PlotServer</data></a></td>
                <td>88</td>
                <td>88</td>
                <td>0</td>
                <td class="right" data-ratio="0 88">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_0878bee3da37af71_plot_server_py.html">aretomo3_gui/web/plot_server.py</a></td>
                <td class="name left"><a href="z_0878bee3da37af71_plot_server_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>32</td>
                <td>32</td>
                <td>0</td>
                <td class="right" data-ratio="0 32">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_0878bee3da37af71_server_py.html#t45">aretomo3_gui/web/server.py</a></td>
                <td class="name left"><a href="z_0878bee3da37af71_server_py.html#t45"><data value='WebServer'>WebServer</data></a></td>
                <td>87</td>
                <td>87</td>
                <td>0</td>
                <td class="right" data-ratio="0 87">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_0878bee3da37af71_server_py.html#t238">aretomo3_gui/web/server.py</a></td>
                <td class="name left"><a href="z_0878bee3da37af71_server_py.html#t238"><data value='WebSocketServer'>WebSocketServer</data></a></td>
                <td>53</td>
                <td>53</td>
                <td>0</td>
                <td class="right" data-ratio="0 53">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_0878bee3da37af71_server_py.html#t340">aretomo3_gui/web/server.py</a></td>
                <td class="name left"><a href="z_0878bee3da37af71_server_py.html#t340"><data value='WebInterface'>WebInterface</data></a></td>
                <td>15</td>
                <td>15</td>
                <td>0</td>
                <td class="right" data-ratio="0 15">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_0878bee3da37af71_server_py.html">aretomo3_gui/web/server.py</a></td>
                <td class="name left"><a href="z_0878bee3da37af71_server_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>81</td>
                <td>81</td>
                <td>2</td>
                <td class="right" data-ratio="0 81">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_48936c27d675ff69___init___py.html">aretomo3_gui/web_interface/__init__.py</a></td>
                <td class="name left"><a href="z_48936c27d675ff69___init___py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_48936c27d675ff69_server_py.html#t19">aretomo3_gui/web_interface/server.py</a></td>
                <td class="name left"><a href="z_48936c27d675ff69_server_py.html#t19"><data value='WebInterface'>WebInterface</data></a></td>
                <td>25</td>
                <td>25</td>
                <td>0</td>
                <td class="right" data-ratio="0 25">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_48936c27d675ff69_server_py.html">aretomo3_gui/web_interface/server.py</a></td>
                <td class="name left"><a href="z_48936c27d675ff69_server_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>16</td>
                <td>16</td>
                <td>0</td>
                <td class="right" data-ratio="0 16">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_063c36b57d90849f___init___py.html">aretomo3_gui/workflow/__init__.py</a></td>
                <td class="name left"><a href="z_063c36b57d90849f___init___py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_063c36b57d90849f_engine_py.html#t15">aretomo3_gui/workflow/engine.py</a></td>
                <td class="name left"><a href="z_063c36b57d90849f_engine_py.html#t15"><data value='NodeType'>NodeType</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_063c36b57d90849f_engine_py.html#t24">aretomo3_gui/workflow/engine.py</a></td>
                <td class="name left"><a href="z_063c36b57d90849f_engine_py.html#t24"><data value='NodeStatus'>NodeStatus</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_063c36b57d90849f_engine_py.html#t34">aretomo3_gui/workflow/engine.py</a></td>
                <td class="name left"><a href="z_063c36b57d90849f_engine_py.html#t34"><data value='WorkflowNode'>WorkflowNode</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_063c36b57d90849f_engine_py.html#t50">aretomo3_gui/workflow/engine.py</a></td>
                <td class="name left"><a href="z_063c36b57d90849f_engine_py.html#t50"><data value='Workflow'>Workflow</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_063c36b57d90849f_engine_py.html#t62">aretomo3_gui/workflow/engine.py</a></td>
                <td class="name left"><a href="z_063c36b57d90849f_engine_py.html#t62"><data value='WorkflowEngine'>WorkflowEngine</data></a></td>
                <td>46</td>
                <td>46</td>
                <td>0</td>
                <td class="right" data-ratio="0 46">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_063c36b57d90849f_engine_py.html">aretomo3_gui/workflow/engine.py</a></td>
                <td class="name left"><a href="z_063c36b57d90849f_engine_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>52</td>
                <td>52</td>
                <td>0</td>
                <td class="right" data-ratio="0 52">0%</td>
            </tr>
        </tbody>
        <tfoot>
            <tr class="total">
                <td class="name left">Total</td>
                <td class="name left">&nbsp;</td>
                <td>32686</td>
                <td>31760</td>
                <td>250</td>
                <td class="right" data-ratio="926 32686">3%</td>
            </tr>
        </tfoot>
    </table>
    <p id="no_rows">
        No items found using the specified filter.
    </p>
</main>
<footer>
    <div class="content">
        <p>
            <a class="nav" href="https://coverage.readthedocs.io/en/7.8.0">coverage.py v7.8.0</a>,
            created at 2025-06-08 22:09 -0400
        </p>
    </div>
    <aside class="hidden">
        <a id="prevFileLink" class="nav" href=""></a>
        <a id="nextFileLink" class="nav" href=""></a>
        <button type="button" class="button_prev_file" data-shortcut="["></button>
        <button type="button" class="button_next_file" data-shortcut="]"></button>
        <button type="button" class="button_show_hide_help" data-shortcut="?"></button>
    </aside>
</footer>
</body>
</html>

"""
Comprehensive Workflow Management System
Visual workflow designer and execution engine
"""

import json
import uuid
from typing import Dict, List, Any, Optional
from dataclasses import dataclass, asdict
from enum import Enum
import networkx as nx
from datetime import datetime


class NodeType(Enum):
    """Types of workflow nodes."""
    INPUT = "input"
    PROCESSING = "processing"
    OUTPUT = "output"
    DECISION = "decision"
    PARALLEL = "parallel"


class NodeStatus(Enum):
    """Status of workflow nodes."""
    PENDING = "pending"
    RUNNING = "running"
    COMPLETED = "completed"
    FAILED = "failed"
    SKIPPED = "skipped"


@dataclass
class WorkflowNode:
    """Individual workflow node."""
    id: str
    name: str
    node_type: NodeType
    parameters: Dict[str, Any]
    inputs: List[str]
    outputs: List[str]
    status: NodeStatus = NodeStatus.PENDING
    progress: float = 0.0
    start_time: Optional[datetime] = None
    end_time: Optional[datetime] = None
    error_message: Optional[str] = None


@dataclass
class Workflow:
    """Complete workflow definition."""
    id: str
    name: str
    description: str
    nodes: List[WorkflowNode]
    connections: List[Dict[str, str]]
    metadata: Dict[str, Any]
    created_time: datetime
    modified_time: datetime


class WorkflowEngine:
    """Workflow execution engine."""
    
    def __init__(self):
        self.workflows = {}
        self.active_executions = {}
        self.templates = {}
        self.load_default_templates()
        
    def create_workflow(self, name: str, description: str = "") -> Workflow:
        """Create a new workflow."""
        workflow_id = str(uuid.uuid4())
        workflow = Workflow(
            id=workflow_id,
            name=name,
            description=description,
            nodes=[],
            connections=[],
            metadata={},
            created_time=datetime.now(),
            modified_time=datetime.now()
        )
        self.workflows[workflow_id] = workflow
        return workflow
        
    def add_node(self, workflow_id: str, node: WorkflowNode):
        """Add a node to workflow."""
        if workflow_id in self.workflows:
            self.workflows[workflow_id].nodes.append(node)
            self.workflows[workflow_id].modified_time = datetime.now()
            
    def connect_nodes(self, workflow_id: str, from_node: str, to_node: str):
        """Connect two nodes in workflow."""
        if workflow_id in self.workflows:
            connection = {"from": from_node, "to": to_node}
            self.workflows[workflow_id].connections.append(connection)
            
    def validate_workflow(self, workflow_id: str) -> List[str]:
        """Validate workflow for execution."""
        errors = []
        workflow = self.workflows.get(workflow_id)
        
        if not workflow:
            return ["Workflow not found"]
            
        # Build graph for validation
        graph = nx.DiGraph()
        for node in workflow.nodes:
            graph.add_node(node.id)
            
        for connection in workflow.connections:
            graph.add_edge(connection["from"], connection["to"])
            
        # Check for cycles
        if not nx.is_directed_acyclic_graph(graph):
            errors.append("Workflow contains cycles")
            
        # Check for disconnected nodes
        if not nx.is_weakly_connected(graph):
            errors.append("Workflow has disconnected components")
            
        return errors
        
    def execute_workflow(self, workflow_id: str, parameters: Dict[str, Any] = None):
        """Execute a workflow."""
        errors = self.validate_workflow(workflow_id)
        if errors:
            raise ValueError(f"Workflow validation failed: {errors}")
            
        workflow = self.workflows[workflow_id]
        execution_id = str(uuid.uuid4())
        
        self.active_executions[execution_id] = {
            "workflow_id": workflow_id,
            "status": "running",
            "start_time": datetime.now(),
            "parameters": parameters or {}
        }
        
        # Execute workflow (simplified)
        self._execute_workflow_async(execution_id)
        
        return execution_id
        
    def _execute_workflow_async(self, execution_id: str):
        """Execute workflow asynchronously."""
        # This would be implemented with proper async execution
        # For now, just mark as completed
        import threading
        
        def execute():
            import time
            time.sleep(2)  # Simulate execution
            self.active_executions[execution_id]["status"] = "completed"
            self.active_executions[execution_id]["end_time"] = datetime.now()
            
        thread = threading.Thread(target=execute, daemon=True)
        thread.start()
        
    def load_default_templates(self):
        """Load default workflow templates."""
        # Standard tomography workflow
        tomo_template = {
            "name": "Standard Tomography Pipeline",
            "description": "Complete tomographic reconstruction workflow",
            "nodes": [
                {
                    "name": "Import Data",
                    "type": "input",
                    "parameters": {"data_path": "", "file_pattern": "*.mrc"}
                },
                {
                    "name": "Motion Correction", 
                    "type": "processing",
                    "parameters": {"patch_size": 5, "iterations": 10}
                },
                {
                    "name": "CTF Estimation",
                    "type": "processing", 
                    "parameters": {"voltage": 300, "cs": 2.7}
                },
                {
                    "name": "Alignment",
                    "type": "processing",
                    "parameters": {"alignment_method": "cross_correlation"}
                },
                {
                    "name": "Reconstruction",
                    "type": "processing",
                    "parameters": {"algorithm": "SIRT", "iterations": 15}
                },
                {
                    "name": "Export Results",
                    "type": "output",
                    "parameters": {"output_format": "mrc"}
                }
            ]
        }
        
        self.templates["standard_tomography"] = tomo_template


# Global workflow engine
workflow_engine = WorkflowEngine()

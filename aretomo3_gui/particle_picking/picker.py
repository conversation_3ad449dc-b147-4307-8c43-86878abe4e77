"""
Advanced Particle Picking System
Intelligent particle detection and classification
"""

import numpy as np
import cv2
from scipy import ndimage
from sklearn.cluster import DBSCAN
from typing import List, Tuple, Dict, Any, Optional
import matplotlib.pyplot as plt
from dataclasses import dataclass


@dataclass
class Particle:
    """Individual particle representation."""
    x: float
    y: float
    z: float
    confidence: float
    size: float
    angle: float
    class_id: int = 0
    metadata: Dict[str, Any] = None


class TemplateBasedPicker:
    """Template-based particle picking."""
    
    def __init__(self):
        self.templates = {}
        self.threshold = 0.7
        
    def add_template(self, name: str, template: np.ndarray):
        """Add template for matching."""
        self.templates[name] = template
        
    def pick_particles(self, image: np.ndarray, template_name: str) -> List[Particle]:
        """Pick particles using template matching."""
        if template_name not in self.templates:
            raise ValueError(f"Template {template_name} not found")
            
        template = self.templates[template_name]
        
        # Normalize image and template
        image_norm = cv2.normalize(image.astype(np.float32), None, 0, 1, cv2.NORM_MINMAX)
        template_norm = cv2.normalize(template.astype(np.float32), None, 0, 1, cv2.NORM_MINMAX)
        
        # Template matching
        result = cv2.matchTemplate(image_norm, template_norm, cv2.TM_CCOEFF_NORMED)
        
        # Find peaks
        locations = np.where(result >= self.threshold)
        
        particles = []
        for y, x in zip(locations[0], locations[1]):
            confidence = result[y, x]
            particles.append(Particle(
                x=float(x + template.shape[1]//2),
                y=float(y + template.shape[0]//2),
                z=0.0,
                confidence=float(confidence),
                size=float(np.mean(template.shape)),
                angle=0.0
            ))
            
        return particles


class BlobDetector:
    """Blob-based particle detection."""
    
    def __init__(self):
        self.min_sigma = 1.0
        self.max_sigma = 30.0
        self.num_sigma = 10
        self.threshold = 0.1
        
    def detect_particles(self, image: np.ndarray) -> List[Particle]:
        """Detect particles using blob detection."""
        from skimage.feature import blob_log
        
        # Detect blobs
        blobs = blob_log(
            image,
            min_sigma=self.min_sigma,
            max_sigma=self.max_sigma,
            num_sigma=self.num_sigma,
            threshold=self.threshold
        )
        
        particles = []
        for blob in blobs:
            y, x, sigma = blob
            particles.append(Particle(
                x=float(x),
                y=float(y),
                z=0.0,
                confidence=1.0,
                size=float(sigma * np.sqrt(2)),
                angle=0.0
            ))
            
        return particles


class MLParticlePicker:
    """Machine learning-based particle picker."""
    
    def __init__(self):
        self.model = None
        self.trained = False
        
    def train(self, positive_examples: List[np.ndarray], negative_examples: List[np.ndarray]):
        """Train ML model for particle picking."""
        # Simplified training - in practice would use CNN
        print("Training ML model...")
        self.trained = True
        
    def predict(self, image: np.ndarray) -> List[Particle]:
        """Predict particle locations using ML."""
        if not self.trained:
            raise ValueError("Model not trained")
            
        # Simplified prediction - would use actual ML model
        particles = []
        
        # Use blob detection as placeholder
        detector = BlobDetector()
        return detector.detect_particles(image)


class ParticleClassifier:
    """Classify picked particles."""
    
    def __init__(self):
        self.classes = {}
        
    def add_class(self, class_id: int, name: str, examples: List[np.ndarray]):
        """Add particle class."""
        self.classes[class_id] = {
            'name': name,
            'examples': examples,
            'template': np.mean(examples, axis=0) if examples else None
        }
        
    def classify_particles(self, particles: List[Particle], image: np.ndarray) -> List[Particle]:
        """Classify particles into classes."""
        for particle in particles:
            # Extract particle region
            x, y = int(particle.x), int(particle.y)
            size = int(particle.size)
            
            x1, y1 = max(0, x - size//2), max(0, y - size//2)
            x2, y2 = min(image.shape[1], x + size//2), min(image.shape[0], y + size//2)
            
            if x2 > x1 and y2 > y1:
                particle_region = image[y1:y2, x1:x2]
                
                # Simple classification based on correlation
                best_class = 0
                best_score = 0
                
                for class_id, class_info in self.classes.items():
                    if class_info['template'] is not None:
                        template = class_info['template']
                        
                        # Resize to match
                        if particle_region.shape != template.shape:
                            particle_region_resized = cv2.resize(
                                particle_region, 
                                (template.shape[1], template.shape[0])
                            )
                        else:
                            particle_region_resized = particle_region
                            
                        # Calculate correlation
                        correlation = cv2.matchTemplate(
                            particle_region_resized.astype(np.float32),
                            template.astype(np.float32),
                            cv2.TM_CCOEFF_NORMED
                        )[0, 0]
                        
                        if correlation > best_score:
                            best_score = correlation
                            best_class = class_id
                            
                particle.class_id = best_class
                
        return particles


class ParticlePickingManager:
    """Main particle picking coordinator."""
    
    def __init__(self):
        self.template_picker = TemplateBasedPicker()
        self.blob_detector = BlobDetector()
        self.ml_picker = MLParticlePicker()
        self.classifier = ParticleClassifier()
        self.picked_particles = {}
        
    def pick_particles_template(self, image: np.ndarray, template_name: str) -> List[Particle]:
        """Pick particles using template matching."""
        return self.template_picker.pick_particles(image, template_name)
        
    def pick_particles_blob(self, image: np.ndarray) -> List[Particle]:
        """Pick particles using blob detection."""
        return self.blob_detector.detect_particles(image)
        
    def pick_particles_ml(self, image: np.ndarray) -> List[Particle]:
        """Pick particles using machine learning."""
        return self.ml_picker.predict(image)
        
    def filter_particles(self, particles: List[Particle], min_confidence: float = 0.5) -> List[Particle]:
        """Filter particles by confidence."""
        return [p for p in particles if p.confidence >= min_confidence]
        
    def cluster_particles(self, particles: List[Particle], eps: float = 10.0) -> List[Particle]:
        """Remove duplicate particles using clustering."""
        if not particles:
            return particles
            
        # Extract coordinates
        coords = np.array([[p.x, p.y] for p in particles])
        
        # Cluster nearby particles
        clustering = DBSCAN(eps=eps, min_samples=1).fit(coords)
        
        # Keep highest confidence particle from each cluster
        unique_particles = []
        for cluster_id in set(clustering.labels_):
            cluster_particles = [p for i, p in enumerate(particles) 
                               if clustering.labels_[i] == cluster_id]
            best_particle = max(cluster_particles, key=lambda x: x.confidence)
            unique_particles.append(best_particle)
            
        return unique_particles


# Alias for compatibility
ParticlePicker = ParticlePickingManager

# Global particle picking manager
particle_picker = ParticlePickingManager()

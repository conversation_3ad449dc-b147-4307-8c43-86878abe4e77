#!/usr/bin/env python3
"""
CTF Analysis Utilities

This module provides utility functions for CTF analysis:
- CTF theoretical calculations
- File format conversions
- Data validation
- Export utilities
"""

import json
import logging
from pathlib import Path
from typing import Dict, List, Optional, Tuple, Union

import numpy as np
import pandas as pd

logger = logging.getLogger(__name__)


class CTFUtils:
    """Utility functions for CTF analysis."""

    @staticmethod
    # TODO: Refactor function - Function 'calculate_theoretical_ctf' too long
    # (66 lines)
    def calculate_theoretical_ctf(
        defocus_um: float,
        voltage_kv: float = 300,
        cs_mm: float = 2.7,
        amplitude_contrast: float = 0.1,
        pixel_size_a: float = 1.0,
        image_size: int = 512,
    ) -> <PERSON>ple[np.ndarray, np.ndarray]:
        """
        Calculate theoretical CTF for given parameters.

        Args:
            defocus_um: Defocus in micrometers (positive = underfocus)
            voltage_kv: Acceleration voltage in kV
            cs_mm: Spherical aberration in mm
            amplitude_contrast: Amplitude contrast (0-1)
            pixel_size_a: Pixel size in Angstroms
            image_size: Image size in pixels

        Returns:
            Tuple of (frequency_array, ctf_array)
        """
        # Physical constants
        h = 6.626e-34  # Planck constant
        c = 2.998e8  # Speed of light
        e = 1.602e-19  # Electron charge
        m0 = 9.109e-31  # Electron rest mass

        # Calculate electron wavelength
        voltage_v = voltage_kv * 1000
        wavelength_m = h / np.sqrt(
            2 * m0 * e * voltage_v * (1 + e * voltage_v / (2 * m0 * c**2))
        )
        wavelength_a = wavelength_m * 1e10  # Convert to Angstroms

        # Create frequency grid
        freq_max = 1.0 / (2.0 * pixel_size_a)  # Nyquist frequency
        freq_step = freq_max / (image_size // 2)

        # Create 2D frequency grid
        x = np.arange(-image_size // 2, image_size // 2) * freq_step
        y = np.arange(-image_size // 2, image_size // 2) * freq_step
        fx, fy = np.meshgrid(x, y)
        freq_2d = np.sqrt(fx**2 + fy**2)

        # Convert units
        defocus_a = defocus_um * 1e4  # Convert to Angstroms
        cs_a = cs_mm * 1e7  # Convert to Angstroms

        # Calculate CTF
        freq_2d_safe = np.maximum(freq_2d, 1e-10)  # Avoid division by zero

        # Phase contrast term
        chi = (
            np.pi
            * wavelength_a
            * freq_2d_safe**2
            * (defocus_a - 0.5 * cs_a * wavelength_a**2 * freq_2d_safe**2)
        )

        # CTF with amplitude contrast
        ctf = -np.sqrt(1 - amplitude_contrast**2) * np.sin(
            chi
        ) - amplitude_contrast * np.cos(chi)

        return freq_2d, ctf

    @staticmethod
    def estimate_resolution_from_ctf(
        ctf_params: Dict, cross_correlation_threshold: float = 0.5
    ) -> float:
        """
        Estimate resolution limit from CTF parameters.

        Args:
            ctf_params: Dictionary with CTF parameters
            cross_correlation_threshold: Threshold for resolution estimation

        Returns:
            Estimated resolution in Angstroms
        """
        # Simple resolution estimation based on cross-correlation
        # In practice, this would be more sophisticated

        cross_corr = ctf_params.get("cross_correlation", 0.0)
        base_resolution = ctf_params.get("resolution_limit_A", 10.0)

        if cross_corr >= cross_correlation_threshold:
            # Good CTF fit - use reported resolution
            return base_resolution
        else:
            # Poor CTF fit - penalize resolution
            penalty_factor = 1.0 + (cross_correlation_threshold - cross_corr) * 2
            return base_resolution * penalty_factor

    @staticmethod
    # TODO: Refactor validate_ctf_data - complexity: 17 (target: <10)
    # TODO: Refactor function - Function 'validate_ctf_data' too long (70
    # lines)
    def validate_ctf_data(ctf_data: Dict) -> Dict[str, List[str]]:
        """
        Validate CTF data for consistency and quality.

        Args:
            ctf_data: Dictionary containing CTF data

        Returns:
            Dictionary with validation results
        """
        issues = {"errors": [], "warnings": [], "info": []}

        # Check required fields
        required_fields = ["series_name", "parameters"]
        for field in required_fields:
            if field not in ctf_data:
                issues["errors"].append(f"Missing required field: {field}")

        if "parameters" in ctf_data and not ctf_data["parameters"].empty:
            params = ctf_data["parameters"]

            # Check parameter ranges
            if "defocus1_A" in params.columns:
                defocus_range = params["defocus1_A"].max() - params["defocus1_A"].min()
                if defocus_range > 50000:  # >5 μm range
                    issues["warnings"].append(
                        f"Large defocus range: {defocus_range / 10000:.1f} μm"
                    )

                # Check for unrealistic defocus values
                if params["defocus1_A"].min() < 5000:  # <0.5 μm
                    issues["warnings"].append("Very low defocus values detected")
                if params["defocus1_A"].max() > 100000:  # >10 μm
                    issues["warnings"].append("Very high defocus values detected")

            # Check cross-correlation values
            if "cross_correlation" in params.columns:
                low_cc_count = (params["cross_correlation"] < 0.1).sum()
                if low_cc_count > len(params) * 0.2:  # >20% low CC
                    issues["warnings"].append(
                        f"Many tilts with low cross-correlation: {low_cc_count}"
                    )

            # Check resolution values
            if "resolution_limit_A" in params.columns:
                poor_res_count = (params["resolution_limit_A"] > 15).sum()
                if poor_res_count > len(params) * 0.3:  # >30% poor resolution
                    issues["warnings"].append(
                        f"Many tilts with poor resolution: {poor_res_count}"
                    )

        # Check tilt angles
        if "tilt_angles" in ctf_data and ctf_data["tilt_angles"]:
            tilt_range = max(ctf_data["tilt_angles"]) - min(ctf_data["tilt_angles"])
            if tilt_range < 60:  # <60° range
                issues["warnings"].append(f"Limited tilt range: { tilt_range:.1f}°")

            issues["info"].append(
                f"Tilt range: {tilt_range:.1f}° ({len(ctf_data['tilt_angles'])} tilts)"
            )

        # Check power spectra
        if ctf_data.get("has_power_spectra"):
            issues["info"].append("Power spectra available for visualization")
        else:
            issues["warnings"].append(
                "No power spectra available - limited visualization"
            )

        return issues

    # TODO: Refactor export_ctf_data - complexity: 13 (target: <10)
    @staticmethod
    # TODO: Refactor function - Function 'export_ctf_data' too long (75 lines)
    def export_ctf_data(
        ctf_data: Dict, output_path: Union[str, Path], format: str = "json"
    ) -> bool:
        """
        Export CTF data to various formats.

        Args:
            ctf_data: Dictionary containing CTF data
            output_path: Output file path
            format: Export format ('json', 'csv', 'excel')

        Returns:
            Success status
        """
        output_path = Path(output_path)

        try:
            if format.lower() == "json":
                # Convert to JSON-serializable format
                export_data = {
                    "series_name": ctf_data.get("series_name"),
                    "n_tilts": ctf_data.get("n_tilts", 0),
                    "has_power_spectra": ctf_data.get("has_power_spectra", False),
                    "tilt_angles": ctf_data.get("tilt_angles", []),
                }

                # Add parameters if available
                if "parameters" in ctf_data and not ctf_data["parameters"].empty:
                    export_data["parameters"] = ctf_data["parameters"].to_dict(
                        "records"
                    )

                with open(output_path, "w") as f:
                    json.dump(export_data, f, indent=2)

            elif format.lower() == "csv":
                if "parameters" in ctf_data and not ctf_data["parameters"].empty:
                    ctf_data["parameters"].to_csv(output_path, index=False)
                else:
                    raise ValueError("No parameters available for CSV export")

            elif format.lower() == "excel":
                if "parameters" in ctf_data and not ctf_data["parameters"].empty:
                    with pd.ExcelWriter(output_path) as writer:
                        ctf_data["parameters"].to_excel(
                            writer, sheet_name="CTF_Parameters", index=False
                        )

                        # Add summary sheet
                        summary_data = {
                            "Metric": [
                                "Series Name",
                                "Number of Tilts",
                                "Has Power Spectra",
                            ],
                            "Value": [
                                ctf_data.get("series_name", "Unknown"),
                                ctf_data.get("n_tilts", 0),
                                ctf_data.get("has_power_spectra", False),
                            ],
                        }
                        pd.DataFrame(summary_data).to_excel(
                            writer, sheet_name="Summary", index=False
                        )
                else:
                    raise ValueError("No parameters available for Excel export")
            else:
                raise ValueError(f"Unsupported export format: {format}")

            logger.info(f"Exported CTF data to {output_path}")
            return True

        except Exception as e:
            logger.error(f"Error exporting CTF data: {e}")
            return False

    # TODO: Refactor create_ctf_report - complexity: 13 (target: <10)

    @staticmethod
    # TODO: Refactor function - Function 'create_ctf_report' too long (135
    # lines)
    def create_ctf_report(
        ctf_data: Dict,
        quality_summary: Dict = None,
        output_path: Union[str, Path] = None,
    ) -> str:
        """
        Create a comprehensive CTF analysis report.

        Args:
            ctf_data: Dictionary containing CTF data
            quality_summary: Quality assessment summary
            output_path: Output file path (optional)

        Returns:
            Report text
        """
        report_lines = []

        # Header
        report_lines.append("=" * 60)
        report_lines.append("CTF ANALYSIS REPORT")
        report_lines.append("=" * 60)
        report_lines.append("")

        # Basic information
        report_lines.append(f"Series Name: { ctf_data.get( 'series_name', 'Unknown')}")
        report_lines.append(f"Number of Tilts: {ctf_data.get('n_tilts', 0)}")
        report_lines.append(
            f"Has Power Spectra: {ctf_data.get('has_power_spectra', False)}"
        )

        if ctf_data.get("tilt_angles"):
            tilt_range = max(ctf_data["tilt_angles"]) - min(ctf_data["tilt_angles"])
            report_lines.append(
                f"Tilt Range: { tilt_range:.1f}° ({ min( ctf_data['tilt_angles']):.1f}° to { max( ctf_data['tilt_angles']):.1f}°)"
            )

        report_lines.append("")

        # CTF parameters summary
        if "parameters" in ctf_data and not ctf_data["parameters"].empty:
            params = ctf_data["parameters"]

            report_lines.append("CTF PARAMETERS SUMMARY:")
            report_lines.append("-" * 30)

            # Defocus statistics
            defocus1_mean = params["defocus1_A"].mean() / 10000
            defocus1_std = params["defocus1_A"].std() / 10000
            defocus1_range = (
                params["defocus1_A"].min() / 10000,
                params["defocus1_A"].max() / 10000,
            )

            report_lines.append(
                f"Defocus 1: { defocus1_mean:.2f} ± { defocus1_std:.2f} μm (range: { defocus1_range[0]:.2f} - { defocus1_range[1]:.2f} μm)"
            )

            # Resolution statistics
            res_mean = params["resolution_limit_A"].mean()
            res_std = params["resolution_limit_A"].std()
            res_range = (
                params["resolution_limit_A"].min(),
                params["resolution_limit_A"].max(),
            )

            report_lines.append(
                f"Resolution: { res_mean:.1f} ± { res_std:.1f} Å (range: { res_range[0]:.1f} - { res_range[1]:.1f} Å)"
            )

            # Cross-correlation statistics
            cc_mean = params["cross_correlation"].mean()
            cc_std = params["cross_correlation"].std()
            cc_range = (
                params["cross_correlation"].min(),
                params["cross_correlation"].max(),
            )

            report_lines.append(
                f"Cross-correlation: {cc_mean:.3f} ± {cc_std:.3f} (range: {cc_range[0]:.3f} - {cc_range[1]:.3f})"
            )

            report_lines.append("")

        # Quality assessment
        if quality_summary:
            report_lines.append("QUALITY ASSESSMENT:")
            report_lines.append("-" * 20)

            overall = quality_summary.get("overall_quality", {})
            report_lines.append(f"Overall Grade: { overall.get( 'grade', 'Unknown')}")
            report_lines.append(f"Overall Score: { overall.get( 'score', 0):.1f}/100")
            report_lines.append(
                f"Number of Outliers: {len(quality_summary.get('outliers', []))}"
            )

            # Recommendations
            recommendations = quality_summary.get("recommendations", [])
            if recommendations:
                report_lines.append("")
                report_lines.append("RECOMMENDATIONS:")
                report_lines.append("-" * 15)
                for i, rec in enumerate(recommendations, 1):
                    report_lines.append(f"{i}. {rec}")

            report_lines.append("")

        # Data validation
        validation = CTFUtils.validate_ctf_data(ctf_data)
        if any(validation.values()):
            report_lines.append("DATA VALIDATION:")
            report_lines.append("-" * 17)

            for level, messages in validation.items():
                if messages:
                    report_lines.append(f"{level.upper()}:")
                    for msg in messages:
                        report_lines.append(f"  • {msg}")
                    report_lines.append("")

        # Footer
        report_lines.append("=" * 60)
        report_lines.append("End of Report")
        report_lines.append("=" * 60)

        report_text = "\n".join(report_lines)

        # Save to file if path provided
        if output_path:
            output_path = Path(output_path)
            with open(output_path, "w") as f:
                f.write(report_text)
            logger.info(f"CTF report saved to {output_path}")

        return report_text

    @staticmethod
    def find_ctf_files(directory: Union[str, Path]) -> List[Dict[str, Path]]:
        """
        Find all CTF-related files in a directory.

        Args:
            directory: Directory to search

        Returns:
            List of dictionaries with CTF file information
        """
        directory = Path(directory)
        ctf_files = []

        # Find all *_CTF.txt files
        for ctf_txt in directory.glob("*_CTF.txt"):
            series_name = ctf_txt.stem.replace("_CTF", "")

            file_info = {
                "series_name": series_name,
                "ctf_txt": ctf_txt,
                "ctf_mrc": None,
                "ctf_imod": None,
                "base_path": directory,
            }

            # Look for corresponding files
            ctf_mrc = directory / f"{series_name}_CTF.mrc"
            if ctf_mrc.exists():
                file_info["ctf_mrc"] = ctf_mrc

            ctf_imod = directory / f"{series_name}_CTF_Imod.txt"
            if ctf_imod.exists():
                file_info["ctf_imod"] = ctf_imod

            ctf_files.append(file_info)

        logger.info(f"Found {len(ctf_files)} CTF file sets in {directory}")
        return ctf_files


def test_ctf_utils():
    """Test function for CTF utilities."""
    import sys

    if len(sys.argv) > 1:
        test_path = sys.argv[1]
    else:
        test_path = "sample_data/test_batch/aretomo_output"

    try:
        # Test file finding
        ctf_files = CTFUtils.find_ctf_files(test_path)
        logger.info(f"Found {len(ctf_files)} CTF file sets:")
        for file_info in ctf_files:
            logger.info(f"  {file_info['series_name']}: {file_info}")

        if ctf_files:
            # Test with first file set
            from .ctf_parser import CTFDataParser
            from .ctf_quality import CTFQualityAssessment

            parser = CTFDataParser(ctf_files[0]["base_path"])
            ctf_data = parser.parse_all()

            # Test validation
            validation = CTFUtils.validate_ctf_data(ctf_data)
            logger.info(f"\nValidation results:")
            for level, messages in validation.items():
                if messages:
                    logger.info(f"  {level}: {messages}")

            # Test quality assessment and report
            quality_assessor = CTFQualityAssessment(ctf_data)
            quality_summary = quality_assessor.assess_all_quality()

            report = CTFUtils.create_ctf_report(ctf_data, quality_summary)
            logger.info(f"\nGenerated report ({len(report)} characters)")

            # Test export
            export_success = CTFUtils.export_ctf_data(ctf_data, "test_ctf_export.json")
            logger.info(f"Export success: {export_success}")

        return True

    except Exception as e:
        logger.info(f"Error testing CTF utilities: {e}")
        import traceback

        traceback.print_exc()
        return False


if __name__ == "__main__":
    test_ctf_utils()

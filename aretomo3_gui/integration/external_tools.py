#!/usr/bin/env python3
"""
External Software Integration for AreTomo3 GUI
Seamless integration with RELION, IMOD, EMAN2, and other tools.
"""

import os
import subprocess
import logging
import shutil
from pathlib import Path
from typing import Dict, List, Optional, Any, Union
from dataclasses import dataclass
from abc import ABC, abstractmethod
import json

logger = logging.getLogger(__name__)


@dataclass
class ExternalTool:
    """Information about an external tool."""
    name: str
    executable: str
    version: Optional[str] = None
    is_available: bool = False
    installation_path: Optional[str] = None
    environment_vars: Dict[str, str] = None
    description: str = ""


@dataclass
class ToolCommand:
    """Command to execute with an external tool."""
    tool_name: str
    command: str
    arguments: List[str]
    input_files: List[str]
    output_files: List[str]
    working_directory: Optional[str] = None
    environment: Optional[Dict[str, str]] = None


class ExternalToolInterface(ABC):
    """Abstract interface for external tool integration."""
    
    @abstractmethod
    def get_tool_info(self) -> ExternalTool:
        """Get information about this tool."""
        pass
    
    @abstractmethod
    def check_availability(self) -> bool:
        """Check if the tool is available."""
        pass
    
    @abstractmethod
    def execute_command(self, command: ToolCommand) -> Dict[str, Any]:
        """Execute a command with this tool."""
        pass


class RELIONInterface(ExternalToolInterface):
    """Interface for RELION integration."""
    
    def __init__(self):
        self.tool_info = None
        self._check_installation()
    
    def get_tool_info(self) -> ExternalTool:
        if self.tool_info is None:
            self._check_installation()
        return self.tool_info
    
    def check_availability(self) -> bool:
        """Check if RELION is available."""
        try:
            result = subprocess.run(['relion_refine', '--version'], 
                                  capture_output=True, text=True, timeout=10)
            return result.returncode == 0
        except (subprocess.TimeoutExpired, FileNotFoundError):
            return False
    
    def _check_installation(self):
        """Check RELION installation."""
        is_available = self.check_availability()
        version = None
        installation_path = None
        
        if is_available:
            try:
                # Get version
                result = subprocess.run(['relion_refine', '--version'], 
                                      capture_output=True, text=True, timeout=10)
                if result.returncode == 0:
                    version = result.stdout.strip()
                
                # Get installation path
                relion_path = shutil.which('relion_refine')
                if relion_path:
                    installation_path = str(Path(relion_path).parent)
                    
            except Exception as e:
                logger.warning(f"Failed to get RELION details: {e}")
        
        self.tool_info = ExternalTool(
            name="RELION",
            executable="relion_refine",
            version=version,
            is_available=is_available,
            installation_path=installation_path,
            description="REgularised LIkelihood OptimisatioN for cryo-EM"
        )
    
    def execute_command(self, command: ToolCommand) -> Dict[str, Any]:
        """Execute RELION command."""
        try:
            cmd = [command.command] + command.arguments
            
            result = subprocess.run(
                cmd,
                cwd=command.working_directory,
                env=command.environment,
                capture_output=True,
                text=True,
                timeout=3600  # 1 hour timeout
            )
            
            return {
                "success": result.returncode == 0,
                "returncode": result.returncode,
                "stdout": result.stdout,
                "stderr": result.stderr,
                "command": " ".join(cmd)
            }
            
        except Exception as e:
            return {
                "success": False,
                "error": str(e),
                "command": " ".join([command.command] + command.arguments)
            }


class IMODInterface(ExternalToolInterface):
    """Interface for IMOD integration."""
    
    def __init__(self):
        self.tool_info = None
        self._check_installation()
    
    def get_tool_info(self) -> ExternalTool:
        if self.tool_info is None:
            self._check_installation()
        return self.tool_info
    
    def check_availability(self) -> bool:
        """Check if IMOD is available."""
        try:
            result = subprocess.run(['imodinfo'], 
                                  capture_output=True, text=True, timeout=10)
            return result.returncode == 0
        except (subprocess.TimeoutExpired, FileNotFoundError):
            return False
    
    def _check_installation(self):
        """Check IMOD installation."""
        is_available = self.check_availability()
        version = None
        installation_path = None
        
        if is_available:
            try:
                # Get version
                result = subprocess.run(['imodinfo'], 
                                      capture_output=True, text=True, timeout=10)
                if result.returncode == 0:
                    for line in result.stdout.split('\n'):
                        if 'IMOD' in line and 'version' in line.lower():
                            version = line.strip()
                            break
                
                # Get installation path from environment
                installation_path = os.environ.get('IMOD_DIR')
                if not installation_path:
                    imod_path = shutil.which('imodinfo')
                    if imod_path:
                        installation_path = str(Path(imod_path).parent)
                        
            except Exception as e:
                logger.warning(f"Failed to get IMOD details: {e}")
        
        self.tool_info = ExternalTool(
            name="IMOD",
            executable="imodinfo",
            version=version,
            is_available=is_available,
            installation_path=installation_path,
            environment_vars={"IMOD_DIR": installation_path} if installation_path else None,
            description="Image MODeling package for tomographic reconstruction"
        )
    
    def execute_command(self, command: ToolCommand) -> Dict[str, Any]:
        """Execute IMOD command."""
        try:
            cmd = [command.command] + command.arguments
            
            # Set up environment
            env = os.environ.copy()
            if command.environment:
                env.update(command.environment)
            if self.tool_info.environment_vars:
                env.update(self.tool_info.environment_vars)
            
            result = subprocess.run(
                cmd,
                cwd=command.working_directory,
                env=env,
                capture_output=True,
                text=True,
                timeout=3600
            )
            
            return {
                "success": result.returncode == 0,
                "returncode": result.returncode,
                "stdout": result.stdout,
                "stderr": result.stderr,
                "command": " ".join(cmd)
            }
            
        except Exception as e:
            return {
                "success": False,
                "error": str(e),
                "command": " ".join([command.command] + command.arguments)
            }


class EMAN2Interface(ExternalToolInterface):
    """Interface for EMAN2 integration."""
    
    def __init__(self):
        self.tool_info = None
        self._check_installation()
    
    def get_tool_info(self) -> ExternalTool:
        if self.tool_info is None:
            self._check_installation()
        return self.tool_info
    
    def check_availability(self) -> bool:
        """Check if EMAN2 is available."""
        try:
            result = subprocess.run(['e2version.py'], 
                                  capture_output=True, text=True, timeout=10)
            return result.returncode == 0
        except (subprocess.TimeoutExpired, FileNotFoundError):
            return False
    
    def _check_installation(self):
        """Check EMAN2 installation."""
        is_available = self.check_availability()
        version = None
        installation_path = None
        
        if is_available:
            try:
                # Get version
                result = subprocess.run(['e2version.py'], 
                                      capture_output=True, text=True, timeout=10)
                if result.returncode == 0:
                    version = result.stdout.strip()
                
                # Get installation path
                eman_path = shutil.which('e2version.py')
                if eman_path:
                    installation_path = str(Path(eman_path).parent)
                    
            except Exception as e:
                logger.warning(f"Failed to get EMAN2 details: {e}")
        
        self.tool_info = ExternalTool(
            name="EMAN2",
            executable="e2version.py",
            version=version,
            is_available=is_available,
            installation_path=installation_path,
            description="Electron Microscopy ANalysis software suite"
        )
    
    def execute_command(self, command: ToolCommand) -> Dict[str, Any]:
        """Execute EMAN2 command."""
        try:
            cmd = [command.command] + command.arguments
            
            result = subprocess.run(
                cmd,
                cwd=command.working_directory,
                env=command.environment,
                capture_output=True,
                text=True,
                timeout=3600
            )
            
            return {
                "success": result.returncode == 0,
                "returncode": result.returncode,
                "stdout": result.stdout,
                "stderr": result.stderr,
                "command": " ".join(cmd)
            }
            
        except Exception as e:
            return {
                "success": False,
                "error": str(e),
                "command": " ".join([command.command] + command.arguments)
            }


class ExternalToolsManager:
    """Manager for all external tool integrations."""
    
    def __init__(self):
        self.tools: Dict[str, ExternalToolInterface] = {}
        self.tool_configs = {}
        
        # Register available tools
        self._register_tools()
        
        logger.info(f"External tools manager initialized with {len(self.tools)} tools")
    
    def _register_tools(self):
        """Register all available external tools."""
        # Register RELION
        relion = RELIONInterface()
        self.tools["RELION"] = relion
        
        # Register IMOD
        imod = IMODInterface()
        self.tools["IMOD"] = imod
        
        # Register EMAN2
        eman2 = EMAN2Interface()
        self.tools["EMAN2"] = eman2
    
    def get_available_tools(self) -> List[ExternalTool]:
        """Get list of all available tools."""
        available_tools = []
        for tool_interface in self.tools.values():
            tool_info = tool_interface.get_tool_info()
            if tool_info.is_available:
                available_tools.append(tool_info)
        return available_tools
    
    def get_all_tools(self) -> List[ExternalTool]:
        """Get list of all tools (available and unavailable)."""
        return [tool_interface.get_tool_info() for tool_interface in self.tools.values()]
    
    def is_tool_available(self, tool_name: str) -> bool:
        """Check if a specific tool is available."""
        if tool_name in self.tools:
            return self.tools[tool_name].get_tool_info().is_available
        return False
    
    def execute_tool_command(self, tool_name: str, command: ToolCommand) -> Dict[str, Any]:
        """Execute a command with a specific tool."""
        if tool_name not in self.tools:
            return {
                "success": False,
                "error": f"Tool {tool_name} not registered"
            }
        
        tool_interface = self.tools[tool_name]
        if not tool_interface.get_tool_info().is_available:
            return {
                "success": False,
                "error": f"Tool {tool_name} not available"
            }
        
        return tool_interface.execute_command(command)
    
    def create_relion_job(self, job_type: str, input_files: List[str], 
                         output_dir: str, parameters: Dict[str, Any]) -> ToolCommand:
        """Create a RELION job command."""
        if job_type == "motion_correction":
            command = "relion_run_motioncorr"
            arguments = [
                "--i", " ".join(input_files),
                "--o", output_dir,
                "--dose_per_frame", str(parameters.get("dose_per_frame", 1.0)),
                "--voltage", str(parameters.get("voltage", 300)),
                "--pixel_size", str(parameters.get("pixel_size", 1.0))
            ]
        elif job_type == "ctf_estimation":
            command = "relion_run_ctffind"
            arguments = [
                "--i", " ".join(input_files),
                "--o", output_dir,
                "--voltage", str(parameters.get("voltage", 300)),
                "--cs", str(parameters.get("cs", 2.7)),
                "--pixel_size", str(parameters.get("pixel_size", 1.0))
            ]
        else:
            raise ValueError(f"Unknown RELION job type: {job_type}")
        
        return ToolCommand(
            tool_name="RELION",
            command=command,
            arguments=arguments,
            input_files=input_files,
            output_files=[],
            working_directory=output_dir
        )
    
    def create_imod_job(self, job_type: str, input_files: List[str], 
                       output_dir: str, parameters: Dict[str, Any]) -> ToolCommand:
        """Create an IMOD job command."""
        if job_type == "alignment":
            command = "tiltalign"
            arguments = [
                "-input", input_files[0],
                "-output", os.path.join(output_dir, "aligned.ali"),
                "-tiltfile", parameters.get("tilt_file", ""),
                "-rotation", str(parameters.get("rotation", 0))
            ]
        elif job_type == "reconstruction":
            command = "tilt"
            arguments = [
                "-input", input_files[0],
                "-output", os.path.join(output_dir, "reconstruction.mrc"),
                "-tiltfile", parameters.get("tilt_file", ""),
                "-thickness", str(parameters.get("thickness", 100))
            ]
        else:
            raise ValueError(f"Unknown IMOD job type: {job_type}")
        
        return ToolCommand(
            tool_name="IMOD",
            command=command,
            arguments=arguments,
            input_files=input_files,
            output_files=[],
            working_directory=output_dir
        )
    
    def save_tool_config(self, config_path: Union[str, Path]):
        """Save tool configuration to file."""
        config_path = Path(config_path)
        
        config_data = {
            "tools": {}
        }
        
        for tool_name, tool_interface in self.tools.items():
            tool_info = tool_interface.get_tool_info()
            config_data["tools"][tool_name] = {
                "name": tool_info.name,
                "executable": tool_info.executable,
                "version": tool_info.version,
                "is_available": tool_info.is_available,
                "installation_path": tool_info.installation_path,
                "environment_vars": tool_info.environment_vars,
                "description": tool_info.description
            }
        
        with open(config_path, 'w') as f:
            json.dump(config_data, f, indent=2)
        
        logger.info(f"Saved tool configuration to {config_path}")
    
    def load_tool_config(self, config_path: Union[str, Path]):
        """Load tool configuration from file."""
        config_path = Path(config_path)
        
        if not config_path.exists():
            logger.warning(f"Tool config file not found: {config_path}")
            return
        
        try:
            with open(config_path, 'r') as f:
                config_data = json.load(f)
            
            self.tool_configs = config_data.get("tools", {})
            logger.info(f"Loaded tool configuration from {config_path}")
            
        except Exception as e:
            logger.error(f"Failed to load tool configuration: {e}")
    
    def get_integration_status(self) -> Dict[str, Any]:
        """Get comprehensive integration status."""
        status = {
            "total_tools": len(self.tools),
            "available_tools": len(self.get_available_tools()),
            "tools": {}
        }
        
        for tool_name, tool_interface in self.tools.items():
            tool_info = tool_interface.get_tool_info()
            status["tools"][tool_name] = {
                "available": tool_info.is_available,
                "version": tool_info.version,
                "path": tool_info.installation_path
            }
        
        return status

#!/usr/bin/env python3
"""
Data Management System for AreTomo3 GUI
Comprehensive data organization, validation, and lifecycle management.
"""

import os
import json
import shutil
import logging
from pathlib import Path
from datetime import datetime
from typing import Dict, List, Optional, Any, Union
from dataclasses import dataclass, asdict
import hashlib

logger = logging.getLogger(__name__)


@dataclass
class DatasetMetadata:
    """Metadata for a tomography dataset."""
    name: str
    path: str
    created_at: datetime
    modified_at: datetime
    size_bytes: int
    file_count: int
    pixel_size: float
    voltage: float
    cs: float
    dose_rate: float
    tilt_range: tuple
    processing_status: str
    quality_score: float
    tags: List[str]
    checksum: str


@dataclass
class ProcessingRecord:
    """Record of processing operations."""
    operation_id: str
    dataset_name: str
    operation_type: str
    parameters: Dict[str, Any]
    started_at: datetime
    completed_at: Optional[datetime]
    status: str
    output_files: List[str]
    log_file: str
    error_message: Optional[str]


class DataManager:
    """Comprehensive data management system."""
    
    def __init__(self, base_path: Union[str, Path]):
        self.base_path = Path(base_path)
        self.metadata_file = self.base_path / "data_registry.json"
        self.processing_log = self.base_path / "processing_history.json"
        
        # Create directory structure
        self.datasets_path = self.base_path / "datasets"
        self.processed_path = self.base_path / "processed"
        self.exports_path = self.base_path / "exports"
        self.temp_path = self.base_path / "temp"
        
        for path in [self.datasets_path, self.processed_path, self.exports_path, self.temp_path]:
            path.mkdir(parents=True, exist_ok=True)
        
        # Load existing metadata
        self.datasets: Dict[str, DatasetMetadata] = {}
        self.processing_records: List[ProcessingRecord] = []
        self.load_metadata()
        
        logger.info(f"Data manager initialized at {self.base_path}")
    
    def register_dataset(self, dataset_path: Union[str, Path], 
                        pixel_size: float = 1.0, voltage: float = 300.0,
                        cs: float = 2.7, dose_rate: float = 1.0,
                        tilt_range: tuple = (-60, 60)) -> str:
        """Register a new dataset."""
        dataset_path = Path(dataset_path)
        dataset_name = dataset_path.name
        
        # Calculate dataset statistics
        total_size = 0
        file_count = 0
        for file_path in dataset_path.rglob("*"):
            if file_path.is_file():
                total_size += file_path.stat().st_size
                file_count += 1
        
        # Calculate checksum
        checksum = self._calculate_dataset_checksum(dataset_path)
        
        # Create metadata
        metadata = DatasetMetadata(
            name=dataset_name,
            path=str(dataset_path),
            created_at=datetime.now(),
            modified_at=datetime.now(),
            size_bytes=total_size,
            file_count=file_count,
            pixel_size=pixel_size,
            voltage=voltage,
            cs=cs,
            dose_rate=dose_rate,
            tilt_range=tilt_range,
            processing_status="registered",
            quality_score=0.0,
            tags=[],
            checksum=checksum
        )
        
        self.datasets[dataset_name] = metadata
        self.save_metadata()
        
        logger.info(f"Registered dataset: {dataset_name}")
        return dataset_name
    
    def get_dataset(self, name: str) -> Optional[DatasetMetadata]:
        """Get dataset metadata by name."""
        return self.datasets.get(name)
    
    def list_datasets(self, status_filter: Optional[str] = None,
                     tag_filter: Optional[str] = None) -> List[DatasetMetadata]:
        """List datasets with optional filtering."""
        datasets = list(self.datasets.values())
        
        if status_filter:
            datasets = [d for d in datasets if d.processing_status == status_filter]
        
        if tag_filter:
            datasets = [d for d in datasets if tag_filter in d.tags]
        
        return datasets
    
    def update_dataset_status(self, name: str, status: str, quality_score: float = None):
        """Update dataset processing status."""
        if name in self.datasets:
            self.datasets[name].processing_status = status
            self.datasets[name].modified_at = datetime.now()
            if quality_score is not None:
                self.datasets[name].quality_score = quality_score
            self.save_metadata()
            logger.info(f"Updated dataset {name} status to {status}")
    
    def add_dataset_tag(self, name: str, tag: str):
        """Add a tag to a dataset."""
        if name in self.datasets and tag not in self.datasets[name].tags:
            self.datasets[name].tags.append(tag)
            self.save_metadata()
    
    def record_processing_operation(self, dataset_name: str, operation_type: str,
                                  parameters: Dict[str, Any]) -> str:
        """Record a new processing operation."""
        operation_id = f"{operation_type}_{dataset_name}_{int(datetime.now().timestamp())}"
        
        record = ProcessingRecord(
            operation_id=operation_id,
            dataset_name=dataset_name,
            operation_type=operation_type,
            parameters=parameters,
            started_at=datetime.now(),
            completed_at=None,
            status="running",
            output_files=[],
            log_file="",
            error_message=None
        )
        
        self.processing_records.append(record)
        self.save_processing_log()
        
        logger.info(f"Recorded processing operation: {operation_id}")
        return operation_id
    
    def complete_processing_operation(self, operation_id: str, 
                                    output_files: List[str],
                                    log_file: str = "",
                                    error_message: str = None):
        """Mark a processing operation as complete."""
        for record in self.processing_records:
            if record.operation_id == operation_id:
                record.completed_at = datetime.now()
                record.status = "failed" if error_message else "completed"
                record.output_files = output_files
                record.log_file = log_file
                record.error_message = error_message
                break
        
        self.save_processing_log()
        logger.info(f"Completed processing operation: {operation_id}")
    
    def get_processing_history(self, dataset_name: str = None) -> List[ProcessingRecord]:
        """Get processing history, optionally filtered by dataset."""
        if dataset_name:
            return [r for r in self.processing_records if r.dataset_name == dataset_name]
        return self.processing_records
    
    def cleanup_temp_files(self, older_than_hours: int = 24):
        """Clean up temporary files older than specified hours."""
        cutoff_time = datetime.now().timestamp() - (older_than_hours * 3600)
        cleaned_count = 0
        
        for file_path in self.temp_path.rglob("*"):
            if file_path.is_file() and file_path.stat().st_mtime < cutoff_time:
                try:
                    file_path.unlink()
                    cleaned_count += 1
                except Exception as e:
                    logger.warning(f"Failed to delete temp file {file_path}: {e}")
        
        logger.info(f"Cleaned up {cleaned_count} temporary files")
        return cleaned_count
    
    def export_dataset(self, dataset_name: str, export_path: Union[str, Path],
                      include_processed: bool = True) -> bool:
        """Export a dataset to a specified location."""
        if dataset_name not in self.datasets:
            logger.error(f"Dataset {dataset_name} not found")
            return False
        
        dataset = self.datasets[dataset_name]
        export_path = Path(export_path)
        export_path.mkdir(parents=True, exist_ok=True)
        
        try:
            # Copy original data
            source_path = Path(dataset.path)
            dest_path = export_path / dataset_name
            shutil.copytree(source_path, dest_path, dirs_exist_ok=True)
            
            # Copy processed data if requested
            if include_processed:
                processed_source = self.processed_path / dataset_name
                if processed_source.exists():
                    processed_dest = dest_path / "processed"
                    shutil.copytree(processed_source, processed_dest, dirs_exist_ok=True)
            
            # Export metadata
            metadata_file = export_path / f"{dataset_name}_metadata.json"
            with open(metadata_file, 'w') as f:
                json.dump(asdict(dataset), f, indent=2, default=str)
            
            logger.info(f"Exported dataset {dataset_name} to {export_path}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to export dataset {dataset_name}: {e}")
            return False
    
    def validate_dataset_integrity(self, dataset_name: str) -> Dict[str, Any]:
        """Validate dataset integrity using checksums."""
        if dataset_name not in self.datasets:
            return {"valid": False, "error": "Dataset not found"}
        
        dataset = self.datasets[dataset_name]
        current_checksum = self._calculate_dataset_checksum(Path(dataset.path))
        
        return {
            "valid": current_checksum == dataset.checksum,
            "original_checksum": dataset.checksum,
            "current_checksum": current_checksum,
            "dataset_path": dataset.path
        }
    
    def get_storage_statistics(self) -> Dict[str, Any]:
        """Get storage usage statistics."""
        stats = {
            "total_datasets": len(self.datasets),
            "total_size_gb": sum(d.size_bytes for d in self.datasets.values()) / (1024**3),
            "processing_operations": len(self.processing_records),
            "status_breakdown": {},
            "storage_paths": {
                "datasets": str(self.datasets_path),
                "processed": str(self.processed_path),
                "exports": str(self.exports_path),
                "temp": str(self.temp_path)
            }
        }
        
        # Calculate status breakdown
        for dataset in self.datasets.values():
            status = dataset.processing_status
            stats["status_breakdown"][status] = stats["status_breakdown"].get(status, 0) + 1
        
        return stats
    
    def _calculate_dataset_checksum(self, dataset_path: Path) -> str:
        """Calculate MD5 checksum for a dataset."""
        hasher = hashlib.md5()
        
        for file_path in sorted(dataset_path.rglob("*")):
            if file_path.is_file():
                try:
                    with open(file_path, 'rb') as f:
                        for chunk in iter(lambda: f.read(4096), b""):
                            hasher.update(chunk)
                except Exception as e:
                    logger.warning(f"Failed to read file for checksum: {file_path}: {e}")
        
        return hasher.hexdigest()
    
    def load_metadata(self):
        """Load metadata from disk."""
        if self.metadata_file.exists():
            try:
                with open(self.metadata_file, 'r') as f:
                    data = json.load(f)
                    for name, metadata_dict in data.items():
                        # Convert datetime strings back to datetime objects
                        metadata_dict['created_at'] = datetime.fromisoformat(metadata_dict['created_at'])
                        metadata_dict['modified_at'] = datetime.fromisoformat(metadata_dict['modified_at'])
                        self.datasets[name] = DatasetMetadata(**metadata_dict)
            except Exception as e:
                logger.error(f"Failed to load metadata: {e}")
        
        if self.processing_log.exists():
            try:
                with open(self.processing_log, 'r') as f:
                    data = json.load(f)
                    for record_dict in data:
                        # Convert datetime strings back to datetime objects
                        record_dict['started_at'] = datetime.fromisoformat(record_dict['started_at'])
                        if record_dict['completed_at']:
                            record_dict['completed_at'] = datetime.fromisoformat(record_dict['completed_at'])
                        self.processing_records.append(ProcessingRecord(**record_dict))
            except Exception as e:
                logger.error(f"Failed to load processing log: {e}")
    
    def save_metadata(self):
        """Save metadata to disk."""
        try:
            data = {}
            for name, metadata in self.datasets.items():
                data[name] = asdict(metadata)
            
            with open(self.metadata_file, 'w') as f:
                json.dump(data, f, indent=2, default=str)
        except Exception as e:
            logger.error(f"Failed to save metadata: {e}")
    
    def save_processing_log(self):
        """Save processing log to disk."""
        try:
            data = [asdict(record) for record in self.processing_records]
            with open(self.processing_log, 'w') as f:
                json.dump(data, f, indent=2, default=str)
        except Exception as e:
            logger.error(f"Failed to save processing log: {e}")

#!/usr/bin/env python3
"""
Mock Napari viewer for testing and fallback when <PERSON>par<PERSON> is not available.
"""

import logging

from PyQt6.QtCore import Qt
from PyQt6.QtWidgets import QLabel, QVBoxLayout, QWidget

logger = logging.getLogger(__name__)


class MockNapariViewer(QWidget):
    """Mock Napari viewer widget for testing and fallback."""

    def __init__(self, parent=None):
        """Initialize the mock viewer."""
        super().__init__(parent)
        self.setup_ui()

    def setup_ui(self):
        """Setup the mock UI."""
        layout = QVBoxLayout(self)

        # Create informative label
        label = QLabel(
            "🔬 Napari Viewer Not Available\n\n"
            "The advanced Napari viewer is not available in this environment.\n"
            "This could be due to:\n"
            "• Napari not being installed\n"
            "• Testing environment limitations\n"
            "• System compatibility issues\n\n"
            "To enable the full viewer, install napari:\n"
            "pip install napari[all]\n\n"
            "Basic file viewing is still available in other tabs."
        )
        label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        label.setWordWrap(True)
        label.setStyleSheet(
            """
            QLabel {
                background-color: #f8f9fa;
                border: 2px solid #dee2e6;
                border-radius: 8px;
                padding: 20px;
                color: #495057;
                font-size: 12pt;
                line-height: 1.4;
            }
        """
        )

        layout.addWidget(label)

    def load_mrc_file(self, file_path):
        """Mock method for loading MRC files."""
        logger.info(f"Mock viewer: Would load {file_path}")
        return False

    def cleanup(self):
        """Mock cleanup method."""
        pass

    def close(self):
        """Mock close method."""
        super().close()

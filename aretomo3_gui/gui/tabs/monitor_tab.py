"""
Monitor tab manager for AreTomo3 GUI.
Handles the unified processing monitor tab.
"""

import logging
from typing import Optional

from PyQt6.QtWidgets import <PERSON><PERSON>abel, QVBoxLayout, QWidget

logger = logging.getLogger(__name__)


class MonitorTabManager:
    """Manages the processing monitor tab setup and functionality."""

    def __init__(self, main_window):
        """Initialize the monitor tab manager.

        Args:
            main_window: Reference to the main AreTomo3 GUI window
        """
        self.main_window = main_window
        self.processing_monitor = None

    def setup_tab(self, tab_widget: QWidget, layout: QVBoxLayout) -> None:
        """Set up the unified processing monitor tab.

        Args:
            tab_widget: The parent widget for the monitor tab
            layout: The layout to add widgets to
        """
        try:
            # Try to import and create UnifiedProcessingMonitor
            try:
                from ..widgets.unified_processing_monitor import (
                    UnifiedProcessingMonitor,
                )

                self.processing_monitor = UnifiedProcessingMonitor(tab_widget)
                layout.addWidget(self.processing_monitor)
                logger.info(
                    "Unified processing monitor tab setup completed successfully"
                )
            except ImportError:
                # Fallback if UnifiedProcessingMonitor is not available
                error_label = QLabel("Processing monitor not available")
                error_label.setStyleSheet(
                    "color: orange; font-weight: bold; padding: 20px;"
                )
                layout.addWidget(error_label)
                logger.warning(
                    "UnifiedProcessingMonitor not available, using placeholder"
                )

        except Exception as e:
            logger.error(
                f"Error setting up monitor tab: { str(e)}",
                exc_info=True,
            )
            # Add error placeholder
            error_label = QLabel(f"Error setting up monitor: {str(e)}")
            error_label.setStyleSheet("color: red; font-weight: bold; padding: 20px;")
            layout.addWidget(error_label)

    def get_processing_monitor(self):
        """Get the processing monitor instance."""
        return self.processing_monitor

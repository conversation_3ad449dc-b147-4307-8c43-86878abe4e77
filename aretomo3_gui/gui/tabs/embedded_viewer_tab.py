"""
Embedded Viewer Tab
Contains both CTF and Motion viewers in a unified interface
"""

from PyQt6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QTabWidget, QSplitter,
    QGroupBox, QLabel, QPushButton, QFrame, QMessageBox
)
from PyQt6.QtCore import Qt, pyqtSignal, pyqtSlot
from PyQt6.QtGui import QFont
import logging

from .embedded_viewers.ctf_viewer import EmbeddedCTFViewer
from .embedded_viewers.motion_viewer import EmbeddedMotionViewer

logger = logging.getLogger(__name__)


class EmbeddedViewerTab(QWidget):
    """Main tab containing embedded CTF and Motion viewers."""
    
    # Signals for main GUI communication
    analysis_completed = pyqtSignal(dict)
    data_loaded = pyqtSignal(str, dict)
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.ctf_viewer = None
        self.motion_viewer = None
        self.setup_ui()
        self.connect_signals()
        
    def setup_ui(self):
        """Setup the embedded viewer tab UI."""
        layout = QVBoxLayout(self)
        
        # Header
        header = self.create_header()
        layout.addWidget(header)
        
        # Main viewer area with tabs
        self.viewer_tabs = QTabWidget()
        self.viewer_tabs.setTabPosition(QTabWidget.TabPosition.North)
        
        # CTF Viewer Tab
        self.ctf_viewer = EmbeddedCTFViewer()
        self.viewer_tabs.addTab(self.ctf_viewer, "🔬 CTF Analysis")
        
        # Motion Viewer Tab  
        self.motion_viewer = EmbeddedMotionViewer()
        self.viewer_tabs.addTab(self.motion_viewer, "🎯 Motion Correction")
        
        # Combined Analysis Tab
        combined_tab = self.create_combined_analysis_tab()
        self.viewer_tabs.addTab(combined_tab, "📊 Combined Analysis")
        
        layout.addWidget(self.viewer_tabs)
        
        # Status footer
        footer = self.create_footer()
        layout.addWidget(footer)
        
    def create_header(self):
        """Create header with title and global controls."""
        header = QFrame()
        header.setFrameStyle(QFrame.Shape.StyledPanel)
        header.setStyleSheet("""
            QFrame { 
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0, 
                    stop:0 #2c3e50, stop:1 #3498db);
                color: white; 
                border-radius: 8px; 
                padding: 10px;
            }
        """)
        
        layout = QHBoxLayout(header)
        
        # Title
        title = QLabel("🔬 Integrated CTF & Motion Analysis")
        title.setFont(QFont("Arial", 16, QFont.Weight.Bold))
        title.setStyleSheet("color: white;")
        layout.addWidget(title)
        
        layout.addStretch()
        
        # Global action buttons
        self.load_session_btn = QPushButton("📁 Load Session")
        self.load_session_btn.clicked.connect(self.load_analysis_session)
        self.load_session_btn.setStyleSheet("""
            QPushButton {
                background-color: #27ae60;
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 4px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #2ecc71;
            }
        """)
        layout.addWidget(self.load_session_btn)
        
        self.save_session_btn = QPushButton("💾 Save Session")
        self.save_session_btn.clicked.connect(self.save_analysis_session)
        self.save_session_btn.setStyleSheet("""
            QPushButton {
                background-color: #e74c3c;
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 4px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #c0392b;
            }
        """)
        layout.addWidget(self.save_session_btn)
        
        return header
        
    def create_combined_analysis_tab(self):
        """Create combined analysis tab."""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        
        # Combined analysis header
        header_label = QLabel("📊 Combined CTF & Motion Analysis")
        header_label.setFont(QFont("Arial", 14, QFont.Weight.Bold))
        header_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        layout.addWidget(header_label)
        
        # Splitter for side-by-side comparison
        splitter = QSplitter(Qt.Orientation.Horizontal)
        
        # CTF Summary Panel
        ctf_summary = self.create_ctf_summary_panel()
        splitter.addWidget(ctf_summary)
        
        # Motion Summary Panel
        motion_summary = self.create_motion_summary_panel()
        splitter.addWidget(motion_summary)
        
        # Combined Statistics Panel
        combined_stats = self.create_combined_stats_panel()
        splitter.addWidget(combined_stats)
        
        splitter.setSizes([300, 300, 400])
        layout.addWidget(splitter)
        
        # Combined analysis controls
        controls = self.create_combined_controls()
        layout.addWidget(controls)
        
        return tab
        
    def create_ctf_summary_panel(self):
        """Create CTF summary panel."""
        panel = QGroupBox("🔬 CTF Summary")
        layout = QVBoxLayout(panel)
        
        self.ctf_summary_label = QLabel("No CTF data loaded")
        self.ctf_summary_label.setWordWrap(True)
        self.ctf_summary_label.setStyleSheet("QLabel { padding: 10px; background-color: #f8f9fa; border-radius: 4px; }")
        layout.addWidget(self.ctf_summary_label)
        
        return panel
        
    def create_motion_summary_panel(self):
        """Create motion summary panel."""
        panel = QGroupBox("🎯 Motion Summary")
        layout = QVBoxLayout(panel)
        
        self.motion_summary_label = QLabel("No motion data loaded")
        self.motion_summary_label.setWordWrap(True)
        self.motion_summary_label.setStyleSheet("QLabel { padding: 10px; background-color: #f8f9fa; border-radius: 4px; }")
        layout.addWidget(self.motion_summary_label)
        
        return panel
        
    def create_combined_stats_panel(self):
        """Create combined statistics panel."""
        panel = QGroupBox("📈 Combined Statistics")
        layout = QVBoxLayout(panel)
        
        self.combined_stats_label = QLabel("Load both CTF and motion data for combined analysis")
        self.combined_stats_label.setWordWrap(True)
        self.combined_stats_label.setStyleSheet("QLabel { padding: 10px; background-color: #f8f9fa; border-radius: 4px; }")
        layout.addWidget(self.combined_stats_label)
        
        return panel
        
    def create_combined_controls(self):
        """Create combined analysis controls."""
        controls = QFrame()
        controls.setFrameStyle(QFrame.Shape.StyledPanel)
        layout = QHBoxLayout(controls)
        
        # Analysis buttons
        self.correlate_btn = QPushButton("🔗 Correlate CTF & Motion")
        self.correlate_btn.clicked.connect(self.correlate_ctf_motion)
        self.correlate_btn.setEnabled(False)
        layout.addWidget(self.correlate_btn)
        
        self.quality_report_btn = QPushButton("📋 Generate Quality Report")
        self.quality_report_btn.clicked.connect(self.generate_quality_report)
        self.quality_report_btn.setEnabled(False)
        layout.addWidget(self.quality_report_btn)
        
        layout.addStretch()
        
        self.export_all_btn = QPushButton("💾 Export All Results")
        self.export_all_btn.clicked.connect(self.export_all_results)
        self.export_all_btn.setEnabled(False)
        layout.addWidget(self.export_all_btn)
        
        return controls
        
    def create_footer(self):
        """Create status footer."""
        footer = QFrame()
        footer.setFrameStyle(QFrame.Shape.StyledPanel)
        footer.setStyleSheet("QFrame { background-color: #ecf0f1; border-radius: 4px; }")
        
        layout = QHBoxLayout(footer)
        
        self.status_label = QLabel("Ready - Load CTF and motion data to begin analysis")
        self.status_label.setStyleSheet("QLabel { color: #7f8c8d; font-style: italic; }")
        layout.addWidget(self.status_label)
        
        layout.addStretch()
        
        # Data status indicators
        self.ctf_status = QLabel("CTF: ❌")
        self.ctf_status.setStyleSheet("QLabel { color: #e74c3c; }")
        layout.addWidget(self.ctf_status)
        
        self.motion_status = QLabel("Motion: ❌")
        self.motion_status.setStyleSheet("QLabel { color: #e74c3c; }")
        layout.addWidget(self.motion_status)
        
        return footer
        
    def connect_signals(self):
        """Connect signals between viewers and main tab."""
        if self.ctf_viewer:
            self.ctf_viewer.ctf_data_loaded.connect(self.on_ctf_data_loaded)
            self.ctf_viewer.ctf_analysis_updated.connect(self.on_ctf_analysis_updated)
            
        if self.motion_viewer:
            self.motion_viewer.motion_data_loaded.connect(self.on_motion_data_loaded)
            self.motion_viewer.motion_analysis_updated.connect(self.on_motion_analysis_updated)
            
    @pyqtSlot(dict)
    def on_ctf_data_loaded(self, ctf_data):
        """Handle CTF data loaded."""
        self.ctf_status.setText("CTF: ✅")
        self.ctf_status.setStyleSheet("QLabel { color: #27ae60; }")
        
        # Update CTF summary
        n_tilts = ctf_data.get('n_tilts', 0)
        summary = f"""
<b>CTF Data Loaded:</b><br>
• Number of tilts: {n_tilts}<br>
• File: {Path(ctf_data.get('file_path', '')).name}<br>
• Status: Ready for analysis
"""
        self.ctf_summary_label.setText(summary)
        
        self.update_combined_analysis_status()
        self.status_label.setText("CTF data loaded successfully")
        
    @pyqtSlot(dict)
    def on_motion_data_loaded(self, motion_data):
        """Handle motion data loaded."""
        self.motion_status.setText("Motion: ✅")
        self.motion_status.setStyleSheet("QLabel { color: #27ae60; }")
        
        # Update motion summary
        n_tilts = motion_data.get('n_tilts', 0)
        summary = f"""
<b>Motion Data Loaded:</b><br>
• Number of tilts: {n_tilts}<br>
• File: {Path(motion_data.get('file_path', '')).name}<br>
• Status: Ready for analysis
"""
        self.motion_summary_label.setText(summary)
        
        self.update_combined_analysis_status()
        self.status_label.setText("Motion data loaded successfully")
        
    @pyqtSlot(dict)
    def on_ctf_analysis_updated(self, analysis_results):
        """Handle CTF analysis update."""
        summary = f"""
<b>CTF Analysis Results:</b><br>
• Mean Defocus: {analysis_results.get('mean_defocus', 0):.2f} μm<br>
• Mean Resolution: {analysis_results.get('mean_resolution', 0):.1f} Å<br>
• Mean Quality: {analysis_results.get('mean_quality', 0):.3f}<br>
• Status: Analysis complete
"""
        self.ctf_summary_label.setText(summary)
        self.update_combined_analysis_status()
        
    @pyqtSlot(dict)
    def on_motion_analysis_updated(self, analysis_results):
        """Handle motion analysis update."""
        summary = f"""
<b>Motion Analysis Results:</b><br>
• Mean Motion: {analysis_results.get('mean_total_motion', 0):.2f} px<br>
• Max Motion: {analysis_results.get('max_total_motion', 0):.2f} px<br>
• Mean Frames: {analysis_results.get('mean_frames', 0):.0f}<br>
• Status: Analysis complete
"""
        self.motion_summary_label.setText(summary)
        self.update_combined_analysis_status()
        
    def update_combined_analysis_status(self):
        """Update combined analysis status and enable controls."""
        ctf_loaded = "✅" in self.ctf_status.text()
        motion_loaded = "✅" in self.motion_status.text()
        
        if ctf_loaded and motion_loaded:
            self.correlate_btn.setEnabled(True)
            self.quality_report_btn.setEnabled(True)
            self.export_all_btn.setEnabled(True)
            
            combined_text = """
<b>Combined Analysis Available:</b><br>
Both CTF and motion data are loaded.<br>
You can now perform combined analysis:<br>
• Correlate CTF quality with motion<br>
• Generate comprehensive quality report<br>
• Export all results together
"""
            self.combined_stats_label.setText(combined_text)
            
    def load_analysis_session(self):
        """Load a complete analysis session."""
        from PyQt6.QtWidgets import QFileDialog, QMessageBox
        
        file_path, _ = QFileDialog.getOpenFileName(
            self, "Load Analysis Session", "", 
            "Session Files (*.json);;All Files (*)"
        )
        
        if file_path:
            try:
                import json
                with open(file_path, 'r') as f:
                    session_data = json.load(f)
                    
                # Load CTF data if available
                if 'ctf_file' in session_data:
                    self.ctf_viewer.load_ctf_from_file(session_data['ctf_file'])
                    
                # Load motion data if available
                if 'motion_file' in session_data:
                    self.motion_viewer.load_motion_from_file(session_data['motion_file'])
                    
                QMessageBox.information(self, "Session Loaded", 
                                      f"Analysis session loaded from {Path(file_path).name}")
                                      
            except Exception as e:
                QMessageBox.warning(self, "Load Error", f"Failed to load session: {e}")
                
    def save_analysis_session(self):
        """Save current analysis session."""
        from PyQt6.QtWidgets import QFileDialog, QMessageBox
        
        file_path, _ = QFileDialog.getSaveFileName(
            self, "Save Analysis Session", "", 
            "Session Files (*.json);;All Files (*)"
        )
        
        if file_path:
            try:
                import json
                session_data = {}
                
                # Save CTF data path if loaded
                if self.ctf_viewer.ctf_data:
                    session_data['ctf_file'] = self.ctf_viewer.ctf_data.get('file_path', '')
                    
                # Save motion data path if loaded
                if self.motion_viewer.motion_data:
                    session_data['motion_file'] = self.motion_viewer.motion_data.get('file_path', '')
                    
                with open(file_path, 'w') as f:
                    json.dump(session_data, f, indent=2)
                    
                QMessageBox.information(self, "Session Saved", 
                                      f"Analysis session saved to {Path(file_path).name}")
                                      
            except Exception as e:
                QMessageBox.warning(self, "Save Error", f"Failed to save session: {e}")
                
    def correlate_ctf_motion(self):
        """Correlate CTF quality with motion data."""
        if not (self.ctf_viewer.ctf_data and self.motion_viewer.motion_data):
            return
            
        # Perform correlation analysis (simplified)
        ctf_qualities = [p['quality'] for p in self.ctf_viewer.ctf_data['ctf_params']]
        motion_totals = [s['total_motion'] for s in self.motion_viewer.motion_data['motion_stats']]
        
        if len(ctf_qualities) == len(motion_totals):
            import numpy as np
            correlation = np.corrcoef(ctf_qualities, motion_totals)[0, 1]
            
            correlation_text = f"""
<b>CTF-Motion Correlation Analysis:</b><br>
• Correlation coefficient: {correlation:.3f}<br>
• Interpretation: {'Strong negative' if correlation < -0.7 else 'Moderate negative' if correlation < -0.3 else 'Weak' if abs(correlation) < 0.3 else 'Moderate positive' if correlation < 0.7 else 'Strong positive'}<br>
• Meaning: {'Higher motion correlates with lower CTF quality' if correlation < -0.3 else 'Higher motion correlates with higher CTF quality' if correlation > 0.3 else 'No strong correlation between motion and CTF quality'}
"""
            self.combined_stats_label.setText(correlation_text)
            
    def generate_quality_report(self):
        """Generate comprehensive quality report."""
        from PyQt6.QtWidgets import QMessageBox
        
        if not (self.ctf_viewer.ctf_data and self.motion_viewer.motion_data):
            QMessageBox.warning(self, "Missing Data", "Both CTF and motion data are required for quality report")
            return
            
        # Generate report (simplified)
        report = "# AreTomo3 Quality Report\n\n"
        report += "## CTF Analysis Summary\n"
        report += f"- Number of tilts analyzed: {len(self.ctf_viewer.ctf_data['ctf_params'])}\n"
        report += f"- Mean defocus: {np.mean([p['defocus_u'] for p in self.ctf_viewer.ctf_data['ctf_params']]):.2f} μm\n"
        report += f"- Mean resolution: {np.mean([p['resolution'] for p in self.ctf_viewer.ctf_data['ctf_params']]):.1f} Å\n"
        
        report += "\n## Motion Correction Summary\n"
        report += f"- Number of tilts processed: {len(self.motion_viewer.motion_data['motion_stats'])}\n"
        report += f"- Mean total motion: {np.mean([s['total_motion'] for s in self.motion_viewer.motion_data['motion_stats']]):.2f} px\n"
        report += f"- Max total motion: {np.max([s['total_motion'] for s in self.motion_viewer.motion_data['motion_stats']]):.2f} px\n"
        
        QMessageBox.information(self, "Quality Report", "Quality report generated successfully!")
        
    def export_all_results(self):
        """Export all analysis results."""
        from PyQt6.QtWidgets import QFileDialog, QMessageBox
        
        directory = QFileDialog.getExistingDirectory(self, "Select Export Directory")
        
        if directory:
            try:
                # Export CTF results
                if self.ctf_viewer.ctf_data:
                    self.ctf_viewer.export_results()
                    
                # Export motion results
                if self.motion_viewer.motion_data:
                    self.motion_viewer.export_results()
                    
                QMessageBox.information(self, "Export Complete", 
                                      f"All results exported to {directory}")
                                      
            except Exception as e:
                QMessageBox.warning(self, "Export Error", f"Failed to export results: {e}")

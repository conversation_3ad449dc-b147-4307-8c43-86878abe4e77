#!/usr/bin/env python3
"""
Unified Live Processing Tab
Consolidates live processing monitoring and real-time analysis into a single tab.
"""

import logging
from pathlib import Path
from typing import Dict, List, Optional, Set

from PyQt6.QtCore import QFileSystemWatcher, Qt, QTimer, pyqtSignal
from PyQt6.QtGui import QFont
from PyQt6.QtWidgets import (
    QCheckBox,
    QComboBox,
    QFileDialog,
    QGroupBox,
    QHBoxLayout,
    QLabel,
    QLineEdit,
    QListWidget,
    QMessageBox,
    QProgressBar,
    QPushButton,
    QSpinBox,
    QSplitter,
    QTabWidget,
    QTextEdit,
    QVBoxLayout,
    QWidget,
)

from .live_processing_tab import LiveFileMonitor, LiveProcessor
from .realtime_analysis_tab import RealTimeAnalysisTab

logger = logging.getLogger(__name__)


class UnifiedLiveProcessingTab(QWidget):
    """
    Live Processing Tab - Real-time tomography reconstruction and monitoring.

    Features:
    - Live file monitoring and automatic processing
    - Real-time reconstruction progress tracking
    - On-the-fly quality assessment
    - CTF and motion correction visualization
    - Processing statistics and throughput monitoring
    """

    # Signals
    processing_started = pyqtSignal(str)  # file_path
    processing_finished = pyqtSignal(str, bool, str)  # file_path, success, message
    analysis_updated = pyqtSignal(dict)  # analysis_data

    def __init__(self, parent=None):
        """Initialize the instance."""
        super().__init__(parent)
        self.main_window = parent

        # Initialize components
        self.file_monitor = LiveFileMonitor(self)
        self.live_processor = LiveProcessor(self)
        self.analysis_widget = None

        # Data storage
        self.monitored_directories: Set[str] = set()
        self.processing_queue: List[str] = []
        self.current_status = "Idle"

        # Connect signals
        self.setup_signals()

        # Setup UI
        self.setup_ui()

        logger.info("Unified Live Processing Tab initialized")

    def setup_signals(self):
        """Set up signal connections."""
        # File monitor signals
        self.file_monitor.new_files_detected.connect(self.handle_new_files)
        self.file_monitor.status_update.connect(self.update_status)

        # Live processor signals
        self.live_processor.processing_started.connect(self.handle_processing_started)
        self.live_processor.processing_finished.connect(self.handle_processing_finished)
        self.live_processor.progress_update.connect(self.handle_progress_update)

        if self.main_window:
            self.live_processor.set_main_window(self.main_window)

    def setup_ui(self):
        """Set up the unified user interface."""
        layout = QVBoxLayout(self)
        layout.setSpacing(10)

        # Create main splitter (horizontal)
        main_splitter = QSplitter(Qt.Orientation.Horizontal)

        # Left panel: Live Processing Controls (40% width)
        left_panel = self.create_processing_controls()
        main_splitter.addWidget(left_panel)

        # Right panel: Real-time Analysis (60% width)
        right_panel = self.create_analysis_panel()
        main_splitter.addWidget(right_panel)

        # Set splitter proportions
        main_splitter.setSizes([400, 600])

        layout.addWidget(main_splitter)

        # Status bar at bottom
        self.create_status_bar(layout)

    # TODO: Refactor function - Function 'create_processing_controls' too long
    # (78 lines)
    def create_processing_controls(self):
        """Create the live processing controls panel."""
        widget = QWidget()
        layout = QVBoxLayout(widget)

        # Header
        header = QLabel("⚡ Live Processing Monitor")
        header.setFont(QFont("Arial", 14, QFont.Weight.Bold))
        header.setStyleSheet("color: #2c3e50; padding: 10px;")
        layout.addWidget(header)

        # Directory monitoring section
        monitor_group = QGroupBox("📁 Directory Monitoring")
        monitor_layout = QVBoxLayout(monitor_group)

        # Directory selection
        dir_layout = QHBoxLayout()
        self.dir_input = QLineEdit()
        self.dir_input.setPlaceholderText("Select directory to monitor...")
        self.browse_btn = QPushButton("Browse")
        self.browse_btn.clicked.connect(self.browse_directory)
        dir_layout.addWidget(self.dir_input)
        dir_layout.addWidget(self.browse_btn)
        monitor_layout.addLayout(dir_layout)

        # File type filters
        filter_layout = QHBoxLayout()
        self.mdoc_chk = QCheckBox("MDOC files")
        self.mdoc_chk.setChecked(True)
        self.mrc_chk = QCheckBox("MRC files")
        self.tiff_chk = QCheckBox("TIFF files")
        filter_layout.addWidget(self.mdoc_chk)
        filter_layout.addWidget(self.mrc_chk)
        filter_layout.addWidget(self.tiff_chk)
        monitor_layout.addLayout(filter_layout)

        # Monitoring controls
        control_layout = QHBoxLayout()
        self.start_monitor_btn = QPushButton("▶ Start Monitoring")
        self.start_monitor_btn.clicked.connect(self.start_monitoring)
        self.stop_monitor_btn = QPushButton("⏹ Stop Monitoring")
        self.stop_monitor_btn.clicked.connect(self.stop_monitoring)
        self.stop_monitor_btn.setEnabled(False)
        control_layout.addWidget(self.start_monitor_btn)
        control_layout.addWidget(self.stop_monitor_btn)
        monitor_layout.addLayout(control_layout)

        layout.addWidget(monitor_group)

        # Processing queue section
        queue_group = QGroupBox("📋 Processing Queue")
        queue_layout = QVBoxLayout(queue_group)

        self.queue_list = QListWidget()
        queue_layout.addWidget(self.queue_list)

        # Auto-processing option
        self.auto_process_chk = QCheckBox("Auto-process detected files")
        self.auto_process_chk.setChecked(True)
        queue_layout.addWidget(self.auto_process_chk)

        layout.addWidget(queue_group)

        # Processing status section
        status_group = QGroupBox("📊 Processing Status")
        status_layout = QVBoxLayout(status_group)

        self.current_file_label = QLabel("No file being processed")
        self.progress_bar = QProgressBar()
        self.progress_bar.setVisible(False)

        status_layout.addWidget(self.current_file_label)
        status_layout.addWidget(self.progress_bar)

        layout.addWidget(status_group)

        layout.addStretch()
        return widget

    def create_analysis_panel(self):
        """Create the real-time analysis panel."""
        # Use the existing RealTimeAnalysisTab but configure it for live mode
        self.analysis_widget = RealTimeAnalysisTab(self.main_window)

        # Set up for live processing mode with integrated viewer
        if hasattr(self.analysis_widget, "set_live_processing_mode"):
            self.analysis_widget.set_live_processing_mode(True)

        # Add integrated viewer subtab for live processing
        if hasattr(self.analysis_widget, "plot_tabs"):
            # Create integrated viewer tab
            viewer_tab = self.create_integrated_viewer_tab()
            self.analysis_widget.plot_tabs.addTab(viewer_tab, "🎥 Live Viewer")

        # Enable real-time file monitoring integration (will be set up after UI creation)
        # Note: monitoring directory will be added when input path is set

        return self.analysis_widget

    def create_status_bar(self, layout):
        """Create status bar at bottom."""
        status_widget = QWidget()
        status_layout = QHBoxLayout(status_widget)

        self.status_label = QLabel("Status: Idle")
        self.files_processed_label = QLabel("Files processed: 0")
        self.monitoring_status_label = QLabel("Monitoring: Stopped")

        status_layout.addWidget(self.status_label)
        status_layout.addStretch()
        status_layout.addWidget(self.files_processed_label)
        status_layout.addWidget(self.monitoring_status_label)

        layout.addWidget(status_widget)

    def create_integrated_viewer_tab(self):
        """Create integrated viewer tab for live processing."""
        viewer_widget = QWidget()
        viewer_layout = QVBoxLayout(viewer_widget)

        # Viewer controls
        controls_layout = QHBoxLayout()

        self.viewer_mode_combo = QComboBox()
        self.viewer_mode_combo.addItems(
            ["MDOC Analysis", "Live Tilt Series", "Real-time Plots"]
        )
        controls_layout.addWidget(QLabel("View Mode:"))
        controls_layout.addWidget(self.viewer_mode_combo)

        self.auto_refresh_viewer = QCheckBox("Auto-refresh")
        self.auto_refresh_viewer.setChecked(True)
        controls_layout.addWidget(self.auto_refresh_viewer)

        controls_layout.addStretch()
        viewer_layout.addLayout(controls_layout)

        # Viewer content area
        self.viewer_content = QLabel(
            "Live viewer will display real-time analysis here..."
        )
        self.viewer_content.setStyleSheet(
            "border: 1px solid #ccc; padding: 20px; text-align: center;"
        )
        self.viewer_content.setMinimumHeight(300)
        viewer_layout.addWidget(self.viewer_content)

        return viewer_widget

    def browse_directory(self):
        """Browse for directory to monitor."""
        directory = QFileDialog.getExistingDirectory(
            self, "Select Directory to Monitor"
        )
        if directory:
            self.dir_input.setText(directory)

    def start_monitoring(self):
        """Start monitoring the selected directory."""
        directory = self.dir_input.text().strip()
        if not directory or not Path(directory).exists():
            QMessageBox.warning(
                self, "Invalid Directory", "Please select a valid directory to monitor."
            )
            return

        # Set up file patterns based on checkboxes
        patterns = []
        if self.mdoc_chk.isChecked():
            patterns.append("*.mdoc")
        if self.mrc_chk.isChecked():
            patterns.append("*.mrc")
        if self.tiff_chk.isChecked():
            patterns.extend(["*.tiff", "*.tif"])

        if not patterns:
            QMessageBox.warning(
                self,
                "No File Types",
                "Please select at least one file type to monitor.",
            )
            return

        # Start monitoring
        self.file_monitor.file_patterns = patterns
        self.file_monitor.add_monitoring_directory(directory)
        self.file_monitor.start_monitoring()

        # Update UI
        self.start_monitor_btn.setEnabled(False)
        self.stop_monitor_btn.setEnabled(True)
        self.monitoring_status_label.setText("Monitoring: Active")

        # Start analysis monitoring for the same directory
        if self.analysis_widget:
            self.analysis_widget.add_monitoring_directory(directory)
            self.analysis_widget.start_monitoring()

        logger.info(f"Started monitoring directory: {directory}")

    def stop_monitoring(self):
        """Stop monitoring."""
        self.file_monitor.stop_monitoring()

        # Update UI
        self.start_monitor_btn.setEnabled(True)
        self.stop_monitor_btn.setEnabled(False)
        self.monitoring_status_label.setText("Monitoring: Stopped")

        logger.info("Stopped monitoring")

    def handle_new_files(self, files):
        """Handle newly detected files."""
        for file_path in files:
            self.queue_list.addItem(file_path)
            if self.auto_process_chk.isChecked():
                self.live_processor.add_file_to_queue(file_path)

        logger.info(f"Detected {len(files)} new files")

    def handle_processing_started(self, file_path):
        """Handle processing started."""
        self.current_file_label.setText(f"Processing: {Path(file_path).name}")
        self.progress_bar.setVisible(True)
        self.progress_bar.setValue(0)
        self.processing_started.emit(file_path)

    def handle_processing_finished(self, file_path, success, message):
        """Handle processing finished."""
        self.current_file_label.setText("No file being processed")
        self.progress_bar.setVisible(False)

        # Update files processed counter
        current_count = int(self.files_processed_label.text().split(": ")[1])
        self.files_processed_label.setText(f"Files processed: {current_count + 1}")

        self.processing_finished.emit(file_path, success, message)

        # Trigger analysis update if processing was successful
        if success and self.analysis_widget:
            # Add the output directory to analysis monitoring
            output_dir = Path(file_path).parent
            self.analysis_widget.add_monitoring_directory(str(output_dir))

    def handle_progress_update(self, file_path, progress):
        """Handle processing progress update."""
        self.progress_bar.setValue(progress)

    def update_status(self, status):
        """Update status display."""
        self.status_label.setText(f"Status: {status}")
        self.current_status = status

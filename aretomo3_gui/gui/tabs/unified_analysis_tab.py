#!/usr/bin/env python3
"""
Unified Analysis Tab - Combines Enhanced Analysis + Real-time Analysis
Removes redundancy by providing single comprehensive analysis interface
"""

import logging
import os
from pathlib import Path
from typing import Dict, List, Any, Optional

from PyQt6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QTabWidget, QComboBox, QLabel,
    QPushButton, QCheckBox, QSpinBox, QTextEdit, QSplitter, QGroupBox,
    QListWidget, QListWidgetItem, QProgressBar, QFrame
)
from PyQt6.QtCore import QTimer, pyqtSignal, Qt
from PyQt6.QtGui import QFont

# Import analysis components
from ...analysis.aretomo3_output_analyzer import analyze_aretomo3_output
from ...analysis.auto_plot_generator import AutoPlotGenerator
from ...analysis.realtime_monitor import RealtimeProcessingMonitor

# Import matplotlib components
import matplotlib.pyplot as plt
from matplotlib.backends.backend_qt5agg import FigureCanvasQTAgg as FigureCanvas
from matplotlib.figure import Figure

logger = logging.getLogger(__name__)


class UnifiedAnalysisTab(QWidget):
    """
    Unified Analysis & Monitoring Tab
    
    Combines functionality from:
    - Enhanced Analysis Tab (static analysis)
    - Real-time Analysis Tab (live monitoring)
    - Batch/Live processing analysis components
    
    Features:
    - Single analysis engine for all modes
    - Static, Live, and Batch analysis modes
    - Integrated plot generation and display
    - Real-time monitoring capabilities
    - Comprehensive dataset management
    """
    
    # Analysis modes
    STATIC_MODE = "Static Analysis"
    LIVE_MODE = "Live Monitoring" 
    BATCH_MODE = "Batch Analysis"
    
    # Signals
    analysis_updated = pyqtSignal(str)  # dataset_path
    mode_changed = pyqtSignal(str)      # analysis_mode
    
    def __init__(self, main_window=None):
        super().__init__()
        self.main_window = main_window
        
        # Analysis components
        self.analyzer = None
        self.plot_generator = None
        self.realtime_monitor = None
        
        # Current state
        self.analysis_mode = self.STATIC_MODE
        self.current_dataset = None
        self.analysis_data = None
        self.generated_plots = {}
        
        # UI components
        self.mode_combo = None
        self.dataset_list = None
        self.plot_canvas = None
        self.analysis_figure = None
        self.status_label = None
        self.progress_bar = None
        
        # Timers
        self.refresh_timer = QTimer()
        self.refresh_timer.timeout.connect(self.auto_refresh)
        
        self.setup_ui()
        self.setup_analysis_components()
        
        logger.info("Unified Analysis Tab initialized")
    
    def setup_ui(self):
        """Setup the unified analysis interface."""
        layout = QVBoxLayout(self)
        
        # Header with mode selection
        header_layout = QHBoxLayout()
        
        # Mode selection
        mode_label = QLabel("Analysis Mode:")
        mode_label.setFont(QFont("Arial", 10, QFont.Weight.Bold))
        self.mode_combo = QComboBox()
        self.mode_combo.addItems([self.STATIC_MODE, self.LIVE_MODE, self.BATCH_MODE])
        self.mode_combo.currentTextChanged.connect(self.on_mode_changed)
        
        header_layout.addWidget(mode_label)
        header_layout.addWidget(self.mode_combo)
        header_layout.addStretch()
        
        # Status and progress
        self.status_label = QLabel("Ready")
        self.progress_bar = QProgressBar()
        self.progress_bar.setVisible(False)
        
        header_layout.addWidget(self.status_label)
        header_layout.addWidget(self.progress_bar)
        
        layout.addLayout(header_layout)
        
        # Main content area
        main_splitter = QSplitter(Qt.Orientation.Horizontal)
        
        # Left panel - Dataset browser and controls
        left_panel = self.create_left_panel()
        main_splitter.addWidget(left_panel)
        
        # Right panel - Plot display
        right_panel = self.create_right_panel()
        main_splitter.addWidget(right_panel)
        
        # Set splitter proportions
        main_splitter.setSizes([300, 700])
        
        layout.addWidget(main_splitter)
        
        # Footer with auto-refresh controls
        footer_layout = QHBoxLayout()
        
        self.auto_refresh_check = QCheckBox("Auto-refresh")
        self.auto_refresh_check.toggled.connect(self.toggle_auto_refresh)
        
        self.refresh_interval = QSpinBox()
        self.refresh_interval.setRange(1, 60)
        self.refresh_interval.setValue(5)
        self.refresh_interval.setSuffix(" sec")
        
        refresh_button = QPushButton("🔄 Refresh Now")
        refresh_button.clicked.connect(self.manual_refresh)
        
        footer_layout.addWidget(self.auto_refresh_check)
        footer_layout.addWidget(QLabel("Interval:"))
        footer_layout.addWidget(self.refresh_interval)
        footer_layout.addWidget(refresh_button)
        footer_layout.addStretch()
        
        layout.addLayout(footer_layout)
    
    def create_left_panel(self):
        """Create left panel with dataset browser and controls."""
        panel = QWidget()
        layout = QVBoxLayout(panel)
        
        # Dataset browser
        browser_group = QGroupBox("📁 Dataset Browser")
        browser_layout = QVBoxLayout(browser_group)
        
        # Browse button
        browse_button = QPushButton("📂 Browse Directory")
        browse_button.clicked.connect(self.browse_directory)
        browser_layout.addWidget(browse_button)
        
        # Dataset list
        self.dataset_list = QListWidget()
        self.dataset_list.itemClicked.connect(self.on_dataset_selected)
        browser_layout.addWidget(self.dataset_list)
        
        layout.addWidget(browser_group)
        
        # Analysis controls
        controls_group = QGroupBox("⚙️ Analysis Controls")
        controls_layout = QVBoxLayout(controls_group)
        
        # Plot type selection - comprehensive list from Enhanced Analysis
        plot_type_label = QLabel("Plot Type:")
        self.plot_type_combo = QComboBox()
        self.plot_type_combo.addItems([
            "Interactive CTF Resolution",
            "Interactive CTF Defocus",
            "Combined CTF Analysis",
            "Motion Plots",
            "Enhanced CTF Analysis",
            "Alignment Plots",
            "Summary Plots",
            "File Distribution",
            "Processing Timeline",
            "All Plots"
        ])
        self.plot_type_combo.currentTextChanged.connect(self.on_plot_type_changed)
        
        controls_layout.addWidget(plot_type_label)
        controls_layout.addWidget(self.plot_type_combo)
        
        # Generate plots button
        generate_button = QPushButton("📊 Generate Plots")
        generate_button.clicked.connect(self.generate_plots)
        controls_layout.addWidget(generate_button)

        # Visualizer buttons
        visualizer_layout = QHBoxLayout()

        ctf_visualizer_button = QPushButton("🔬 CTF Visualizer")
        ctf_visualizer_button.clicked.connect(self.open_ctf_visualizer)
        visualizer_layout.addWidget(ctf_visualizer_button)

        motion_visualizer_button = QPushButton("📐 Motion Visualizer")
        motion_visualizer_button.clicked.connect(self.open_motion_visualizer)
        visualizer_layout.addWidget(motion_visualizer_button)

        controls_layout.addLayout(visualizer_layout)
        
        # Analysis info
        self.info_text = QTextEdit()
        self.info_text.setMaximumHeight(150)
        self.info_text.setReadOnly(True)
        controls_layout.addWidget(QLabel("Analysis Info:"))
        controls_layout.addWidget(self.info_text)
        
        layout.addWidget(controls_group)
        
        return panel
    
    def create_right_panel(self):
        """Create right panel with tabbed plot display."""
        panel = QWidget()
        layout = QVBoxLayout(panel)

        # Create tabbed plot display
        self.plot_tabs = QTabWidget()

        # Tab 1: Main Analysis Plots
        main_plots_tab = QWidget()
        main_plots_layout = QVBoxLayout(main_plots_tab)

        # Create matplotlib figure and canvas for main plots
        self.analysis_figure = Figure(figsize=(10, 8), facecolor='white')
        self.plot_canvas = FigureCanvas(self.analysis_figure)
        main_plots_layout.addWidget(self.plot_canvas)

        self.plot_tabs.addTab(main_plots_tab, "📊 Analysis Plots")

        # Tab 2: CTF Visualizer (embedded)
        self.ctf_viewer_tab = QWidget()
        ctf_layout = QVBoxLayout(self.ctf_viewer_tab)
        ctf_layout.addWidget(QLabel("CTF Visualizer will be embedded here"))
        self.plot_tabs.addTab(self.ctf_viewer_tab, "🔬 CTF Viewer")

        # Tab 3: Motion Visualizer (embedded)
        self.motion_viewer_tab = QWidget()
        motion_layout = QVBoxLayout(self.motion_viewer_tab)
        motion_layout.addWidget(QLabel("Motion Visualizer will be embedded here"))
        self.plot_tabs.addTab(self.motion_viewer_tab, "📐 Motion Viewer")

        # Tab 4: Interactive Plots
        self.interactive_tab = QWidget()
        interactive_layout = QVBoxLayout(self.interactive_tab)
        interactive_layout.addWidget(QLabel("Interactive plots will be displayed here"))
        self.plot_tabs.addTab(self.interactive_tab, "🎯 Interactive")

        layout.addWidget(self.plot_tabs)

        return panel
    
    def setup_analysis_components(self):
        """Setup analysis engine components."""
        try:
            # Auto-plot generator
            self.plot_generator = AutoPlotGenerator(theme="light")
            
            # Real-time monitor (for live mode)
            self.realtime_monitor = RealtimeProcessingMonitor(
                theme="light", 
                auto_plot=True
            )
            
            # Connect real-time monitor callbacks
            self.realtime_monitor.add_completion_callback(self.on_processing_completion)
            
            logger.info("Analysis components initialized successfully")
            
        except Exception as e:
            logger.error(f"Error setting up analysis components: {e}")
    
    def on_mode_changed(self, mode: str):
        """Handle analysis mode change."""
        self.analysis_mode = mode
        self.mode_changed.emit(mode)
        
        logger.info(f"Analysis mode changed to: {mode}")
        
        if mode == self.LIVE_MODE:
            self.setup_live_mode()
        elif mode == self.BATCH_MODE:
            self.setup_batch_mode()
        else:
            self.setup_static_mode()
        
        self.update_ui_for_mode()
    
    def setup_static_mode(self):
        """Setup for static analysis mode."""
        self.stop_live_monitoring()
        self.status_label.setText("Static Analysis Mode - Select dataset to analyze")
    
    def setup_live_mode(self):
        """Setup for live monitoring mode."""
        self.start_live_monitoring()
        self.status_label.setText("Live Monitoring Mode - Watching for new results")
    
    def setup_batch_mode(self):
        """Setup for batch analysis mode."""
        self.stop_live_monitoring()
        self.status_label.setText("Batch Analysis Mode - Monitoring batch processing")
    
    def start_live_monitoring(self):
        """Start live monitoring."""
        if self.realtime_monitor and not self.realtime_monitor.get_monitoring_status()['running']:
            # Get monitoring directories from main window
            directories = self.get_monitoring_directories()
            if directories:
                self.realtime_monitor.start_monitoring(directories)
                logger.info(f"Started live monitoring of {len(directories)} directories")
    
    def stop_live_monitoring(self):
        """Stop live monitoring."""
        if self.realtime_monitor:
            self.realtime_monitor.stop_monitoring()
            logger.info("Stopped live monitoring")
    
    def get_monitoring_directories(self):
        """Get OUTPUT directories to monitor from main window."""
        directories = []

        # Try to get from batch processing (these should already be output directories)
        if hasattr(self.main_window, 'batch_tab_manager'):
            batch_widget = self.main_window.batch_tab_manager.get_batch_widget()
            if batch_widget and hasattr(batch_widget, 'get_output_directories'):
                directories.extend(batch_widget.get_output_directories())

        # Try to get from live processing (convert input directory to output directory)
        if hasattr(self.main_window, 'live_processing_tab'):
            live_tab = self.main_window.live_processing_tab
            if hasattr(live_tab, 'get_monitoring_directory'):
                input_dir = live_tab.get_monitoring_directory()
                if input_dir:
                    # Convert input directory to output directory for analysis monitoring
                    output_dir = input_dir.replace("input", "output")
                    if not os.path.exists(output_dir):
                        output_dir = os.path.join(input_dir, "AreTomo3_Output")
                    directories.append(output_dir)
                    logger.info(f"Analysis monitoring: {input_dir} → {output_dir}")

        # Try to get from main window output directory setting
        if hasattr(self.main_window, 'output_dir') and self.main_window.output_dir.text().strip():
            output_dir = self.main_window.output_dir.text().strip()
            if output_dir not in directories:
                directories.append(output_dir)

        return directories
    
    def update_ui_for_mode(self):
        """Update UI elements based on current mode."""
        is_live = self.analysis_mode == self.LIVE_MODE
        
        # Enable/disable auto-refresh based on mode
        self.auto_refresh_check.setEnabled(is_live)
        if is_live and not self.auto_refresh_check.isChecked():
            self.auto_refresh_check.setChecked(True)
    
    def browse_directory(self):
        """Browse for analysis directory."""
        from PyQt6.QtWidgets import QFileDialog
        
        directory = QFileDialog.getExistingDirectory(
            self, 
            "Select Analysis Directory",
            os.path.expanduser("~")
        )
        
        if directory:
            self.scan_directory(directory)
    
    def scan_directory(self, directory: str):
        """Scan directory for AreTomo3 output datasets."""
        self.dataset_list.clear()
        
        try:
            # Look for AreTomo3 output directories
            for root, dirs, files in os.walk(directory):
                # Check if this looks like an AreTomo3 output directory
                if any(f.endswith('.mrc') or f.endswith('.rec') for f in files):
                    if any('aretomo' in f.lower() or 'session' in f.lower() for f in files):
                        item = QListWidgetItem(f"📁 {os.path.basename(root)}")
                        item.setData(Qt.ItemDataRole.UserRole, root)
                        self.dataset_list.addItem(item)
            
            logger.info(f"Found {self.dataset_list.count()} datasets in {directory}")
            
        except Exception as e:
            logger.error(f"Error scanning directory {directory}: {e}")
    
    def on_dataset_selected(self, item):
        """Handle dataset selection."""
        dataset_path = item.data(Qt.ItemDataRole.UserRole)
        self.current_dataset = dataset_path
        
        logger.info(f"Selected dataset: {dataset_path}")
        
        # Analyze the selected dataset
        self.analyze_dataset(dataset_path)
    
    def analyze_dataset(self, dataset_path: str):
        """Analyze a dataset."""
        try:
            self.status_label.setText("Analyzing dataset...")
            self.progress_bar.setVisible(True)
            
            # Perform analysis
            self.analysis_data = analyze_aretomo3_output(dataset_path)
            
            if self.analysis_data:
                # Update info display
                self.update_analysis_info()
                
                # Generate and display plots
                self.generate_plots()
                
                self.status_label.setText(f"Analysis complete: {os.path.basename(dataset_path)}")
                self.analysis_updated.emit(dataset_path)
            else:
                self.status_label.setText("Analysis failed - no data found")
            
        except Exception as e:
            logger.error(f"Error analyzing dataset {dataset_path}: {e}")
            self.status_label.setText(f"Analysis error: {str(e)}")
        finally:
            self.progress_bar.setVisible(False)
    
    def update_analysis_info(self):
        """Update analysis information display."""
        if not self.analysis_data:
            return
        
        info_text = f"""Dataset Analysis Summary:
        
Files Found: {sum(len(files) for files in self.analysis_data.get('files_found', {}).values())}
Data Types: {len(self.analysis_data.get('data_summary', {}))}
Plots Available: {len(self.analysis_data.get('plots_needed', []))}

Processing Status:
{self.format_processing_status()}

Data Summary:
{self.format_data_summary()}
"""
        
        self.info_text.setPlainText(info_text)
    
    def format_processing_status(self) -> str:
        """Format processing status for display."""
        if not self.analysis_data:
            return "No data"
        
        status = self.analysis_data.get('processing_status', {})
        lines = []
        for key, value in status.items():
            status_icon = "✅" if value else "❌"
            lines.append(f"  {status_icon} {key.replace('_', ' ').title()}")
        
        return "\n".join(lines) if lines else "  No status information"
    
    def format_data_summary(self) -> str:
        """Format data summary for display."""
        if not self.analysis_data:
            return "No data"
        
        summary = self.analysis_data.get('data_summary', {})
        lines = []
        for key, value in summary.items():
            if isinstance(value, (list, dict)):
                count = len(value)
                lines.append(f"  • {key.replace('_', ' ').title()}: {count} items")
            else:
                lines.append(f"  • {key.replace('_', ' ').title()}: {value}")
        
        return "\n".join(lines) if lines else "  No summary available"

    def generate_plots(self):
        """Generate plots for current dataset."""
        if not self.analysis_data or not self.plot_generator:
            return

        try:
            self.status_label.setText("Generating plots...")

            # Create temporary plots directory
            import tempfile
            with tempfile.TemporaryDirectory() as temp_dir:
                # Generate plots
                self.generated_plots = self.plot_generator.generate_all_plots(
                    self.analysis_data['data_summary'],
                    temp_dir
                )

                # Display the selected plot type
                self.display_current_plot()

            self.status_label.setText("Plots generated successfully")

        except Exception as e:
            logger.error(f"Error generating plots: {e}")
            self.status_label.setText(f"Plot generation error: {str(e)}")

    def on_plot_type_changed(self, plot_type: str):
        """Handle plot type selection change."""
        self.display_current_plot()

    def display_current_plot(self):
        """Display the currently selected plot type."""
        if not self.generated_plots:
            return

        plot_type = self.plot_type_combo.currentText()

        # Clear previous plots
        self.analysis_figure.clear()

        if plot_type == "All Plots":
            self.display_all_plots()
        else:
            self.display_single_plot(plot_type)

        self.plot_canvas.draw()

    def display_single_plot(self, plot_type: str):
        """Display a single plot type."""
        # Handle interactive plot types
        if plot_type.startswith("Interactive"):
            self.display_interactive_plot(plot_type)
            return

        # Map plot type names to generated plot files
        plot_mapping = {
            "Motion Plots": ["motion_correction", "frame_alignment", "motion_statistics"],
            "Enhanced CTF Analysis": ["ctf_estimation", "defocus_analysis", "astigmatism"],
            "Combined CTF Analysis": ["ctf_estimation", "defocus_analysis"],
            "Alignment Plots": ["alignment_analysis"],
            "Summary Plots": ["quality_metrics", "tilt_series_summary"],
            "Processing Summary": ["processing_overview"],
            "File Distribution": ["file_distribution"],
            "Processing Timeline": ["processing_timeline"]
        }

        plot_files = plot_mapping.get(plot_type, [])

        if not plot_files:
            ax = self.analysis_figure.add_subplot(111)
            ax.text(0.5, 0.5, f"No plots available for {plot_type}",
                   ha='center', va='center', transform=ax.transAxes)
            ax.set_title(plot_type)
            return

        # Find matching generated plots
        matching_plots = []
        for plot_key, plot_path in self.generated_plots.items():
            if any(pf in plot_key for pf in plot_files):
                matching_plots.append((plot_key, plot_path))

        if not matching_plots:
            ax = self.analysis_figure.add_subplot(111)
            ax.text(0.5, 0.5, f"Plots not yet generated for {plot_type}",
                   ha='center', va='center', transform=ax.transAxes)
            ax.set_title(plot_type)
            return

        # Display the plots
        if len(matching_plots) == 1:
            self.display_plot_image(matching_plots[0][1])
        else:
            self.display_multiple_plot_images(matching_plots)

    def display_all_plots(self):
        """Display all available plots in a grid."""
        if not self.generated_plots:
            return

        plot_count = len(self.generated_plots)
        if plot_count == 0:
            return

        # Calculate grid size
        cols = min(3, plot_count)
        rows = (plot_count + cols - 1) // cols

        for i, (plot_key, plot_path) in enumerate(self.generated_plots.items()):
            ax = self.analysis_figure.add_subplot(rows, cols, i + 1)

            try:
                # Load and display image
                import matplotlib.image as mpimg
                if os.path.exists(plot_path):
                    img = mpimg.imread(plot_path)
                    ax.imshow(img)
                    ax.set_title(plot_key.replace('_', ' ').title(), fontsize=8)
                else:
                    ax.text(0.5, 0.5, 'Plot not found', ha='center', va='center')
                    ax.set_title(plot_key.replace('_', ' ').title(), fontsize=8)

                ax.axis('off')

            except Exception as e:
                ax.text(0.5, 0.5, f'Error loading plot', ha='center', va='center')
                ax.set_title(plot_key.replace('_', ' ').title(), fontsize=8)
                ax.axis('off')

        self.analysis_figure.suptitle("AreTomo3 Analysis Overview", fontsize=12)
        self.analysis_figure.tight_layout()

    def display_plot_image(self, plot_path: str):
        """Display a single plot image."""
        ax = self.analysis_figure.add_subplot(111)

        try:
            import matplotlib.image as mpimg
            if os.path.exists(plot_path):
                img = mpimg.imread(plot_path)
                ax.imshow(img)
                ax.set_title(os.path.basename(plot_path).replace('.png', '').replace('_', ' ').title())
            else:
                ax.text(0.5, 0.5, 'Plot file not found', ha='center', va='center')
                ax.set_title('Plot Not Available')

            ax.axis('off')

        except Exception as e:
            ax.text(0.5, 0.5, f'Error loading plot: {str(e)}', ha='center', va='center')
            ax.set_title('Plot Error')
            ax.axis('off')

    def display_multiple_plot_images(self, plot_list):
        """Display multiple plot images in a grid."""
        plot_count = len(plot_list)
        cols = min(2, plot_count)
        rows = (plot_count + cols - 1) // cols

        for i, (plot_key, plot_path) in enumerate(plot_list):
            ax = self.analysis_figure.add_subplot(rows, cols, i + 1)

            try:
                import matplotlib.image as mpimg
                if os.path.exists(plot_path):
                    img = mpimg.imread(plot_path)
                    ax.imshow(img)
                    ax.set_title(plot_key.replace('_', ' ').title(), fontsize=10)
                else:
                    ax.text(0.5, 0.5, 'Plot not found', ha='center', va='center')
                    ax.set_title(plot_key.replace('_', ' ').title(), fontsize=10)

                ax.axis('off')

            except Exception as e:
                ax.text(0.5, 0.5, f'Error', ha='center', va='center')
                ax.set_title(plot_key.replace('_', ' ').title(), fontsize=10)
                ax.axis('off')

        self.analysis_figure.tight_layout()

    def toggle_auto_refresh(self, enabled: bool):
        """Toggle auto-refresh functionality."""
        if enabled and self.analysis_mode == self.LIVE_MODE:
            interval = self.refresh_interval.value() * 1000  # Convert to milliseconds
            self.refresh_timer.start(interval)
            logger.info(f"Auto-refresh enabled: {self.refresh_interval.value()}s interval")
        else:
            self.refresh_timer.stop()
            logger.info("Auto-refresh disabled")

    def auto_refresh(self):
        """Perform automatic refresh."""
        if self.analysis_mode == self.LIVE_MODE:
            self.refresh_datasets()

    def manual_refresh(self):
        """Perform manual refresh."""
        if self.current_dataset:
            self.analyze_dataset(self.current_dataset)
        else:
            self.refresh_datasets()

    def refresh_datasets(self):
        """Refresh the dataset list."""
        # Re-scan current directory if available
        if self.dataset_list.count() > 0:
            # Get the parent directory of the first item
            first_item = self.dataset_list.item(0)
            if first_item:
                dataset_path = first_item.data(Qt.ItemDataRole.UserRole)
                parent_dir = os.path.dirname(dataset_path)
                self.scan_directory(parent_dir)

    def on_processing_completion(self, output_dir: str, data: dict):
        """Handle processing completion from real-time monitor."""
        logger.info(f"Processing completed for {output_dir}")

        # Add to dataset list if not already present
        dataset_name = os.path.basename(output_dir.rstrip('/'))

        # Check if already in list
        for i in range(self.dataset_list.count()):
            item = self.dataset_list.item(i)
            if item.data(Qt.ItemDataRole.UserRole) == output_dir:
                # Update existing item
                item.setText(f"📁 {dataset_name} ✅")
                break
        else:
            # Add new item
            item = QListWidgetItem(f"📁 {dataset_name} ✅")
            item.setData(Qt.ItemDataRole.UserRole, output_dir)
            self.dataset_list.addItem(item)

        # If this is the current dataset, refresh analysis
        if self.current_dataset == output_dir:
            self.analyze_dataset(output_dir)

    def set_live_processing_mode(self, enabled: bool):
        """Set live processing mode (called from other tabs)."""
        if enabled:
            self.mode_combo.setCurrentText(self.LIVE_MODE)
        else:
            self.mode_combo.setCurrentText(self.STATIC_MODE)

    def set_batch_processing_mode(self, enabled: bool):
        """Set batch processing mode (called from other tabs)."""
        if enabled:
            self.mode_combo.setCurrentText(self.BATCH_MODE)
        else:
            self.mode_combo.setCurrentText(self.STATIC_MODE)

    def open_ctf_visualizer(self):
        """Open CTF visualizer in embedded tab."""
        if not self.current_dataset:
            self.status_label.setText("Please select a dataset first")
            return

        try:
            from ...analysis.ctf_analysis.ctf_visualizer import CTF2DVisualizer
            from ...analysis.ctf_analysis.ctf_parser import CTFDataParser

            # Parse CTF data
            parser = CTFDataParser(self.current_dataset)
            ctf_data = parser.parse_all()

            if ctf_data["power_spectra"] is None:
                self.status_label.setText("No CTF data available for visualization")
                return

            # Clear existing CTF viewer tab
            ctf_layout = self.ctf_viewer_tab.layout()
            if ctf_layout:
                while ctf_layout.count():
                    child = ctf_layout.takeAt(0)
                    if child.widget():
                        child.widget().deleteLater()
            else:
                ctf_layout = QVBoxLayout(self.ctf_viewer_tab)

            # Create embedded CTF visualizer
            ctf_visualizer = CTF2DVisualizer(ctf_data)

            # Create the interactive viewer and get the figure
            fig = ctf_visualizer.create_interactive_viewer()

            # Embed the matplotlib figure in Qt
            from matplotlib.backends.backend_qt5agg import FigureCanvasQTAgg as FigureCanvas
            from matplotlib.backends.backend_qt5agg import NavigationToolbar2QT as NavigationToolbar

            canvas = FigureCanvas(fig)
            toolbar = NavigationToolbar(canvas, self.ctf_viewer_tab)

            ctf_layout.addWidget(toolbar)
            ctf_layout.addWidget(canvas)

            # Switch to CTF viewer tab
            self.plot_tabs.setCurrentWidget(self.ctf_viewer_tab)

            self.status_label.setText("CTF visualizer embedded successfully")
            logger.info(f"CTF visualizer embedded for {self.current_dataset}")

        except Exception as e:
            logger.error(f"Error opening CTF visualizer: {e}")
            self.status_label.setText(f"CTF visualizer error: {str(e)}")

            # Show error in CTF viewer tab
            ctf_layout = self.ctf_viewer_tab.layout()
            if not ctf_layout:
                ctf_layout = QVBoxLayout(self.ctf_viewer_tab)

            error_label = QLabel(f"CTF Visualizer Error:\n{str(e)}\n\nPlease check that CTF data is available in the selected dataset.")
            error_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
            error_label.setStyleSheet("color: #dc3545; padding: 20px; font-size: 12px;")
            ctf_layout.addWidget(error_label)

    def open_motion_visualizer(self):
        """Open motion correction visualizer in embedded tab."""
        if not self.current_dataset:
            self.status_label.setText("Please select a dataset first")
            return

        try:
            from ...analysis.motion_analysis.motion_visualizer import MotionCorrectionVisualizer
            from ...analysis.motion_analysis.motion_parser import MotionCorrectionParser

            # Parse motion data
            parser = MotionCorrectionParser(self.current_dataset)
            motion_data = parser.parse_all()

            if not motion_data["has_motion_data"]:
                self.status_label.setText("No motion correction data available")
                return

            # Clear existing motion viewer tab
            motion_layout = self.motion_viewer_tab.layout()
            if motion_layout:
                while motion_layout.count():
                    child = motion_layout.takeAt(0)
                    if child.widget():
                        child.widget().deleteLater()
            else:
                motion_layout = QVBoxLayout(self.motion_viewer_tab)

            # Create embedded motion visualizer
            motion_visualizer = MotionCorrectionVisualizer(motion_data)

            # Create the interactive viewer and get the figure
            fig = motion_visualizer.create_interactive_viewer()

            # Embed the matplotlib figure in Qt
            from matplotlib.backends.backend_qt5agg import FigureCanvasQTAgg as FigureCanvas
            from matplotlib.backends.backend_qt5agg import NavigationToolbar2QT as NavigationToolbar

            canvas = FigureCanvas(fig)
            toolbar = NavigationToolbar(canvas, self.motion_viewer_tab)

            motion_layout.addWidget(toolbar)
            motion_layout.addWidget(canvas)

            # Switch to motion viewer tab
            self.plot_tabs.setCurrentWidget(self.motion_viewer_tab)

            self.status_label.setText("Motion visualizer embedded successfully")
            logger.info(f"Motion visualizer embedded for {self.current_dataset}")

        except Exception as e:
            logger.error(f"Error opening motion visualizer: {e}")
            self.status_label.setText(f"Motion visualizer error: {str(e)}")

            # Show error in motion viewer tab
            motion_layout = self.motion_viewer_tab.layout()
            if not motion_layout:
                motion_layout = QVBoxLayout(self.motion_viewer_tab)

            error_label = QLabel(f"Motion Visualizer Error:\n{str(e)}\n\nPlease check that motion correction data is available in the selected dataset.")
            error_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
            error_label.setStyleSheet("color: #dc3545; padding: 20px; font-size: 12px;")
            motion_layout.addWidget(error_label)

    def display_interactive_plot(self, plot_type: str):
        """Display interactive plot in the interactive tab."""
        if not self.analysis_data:
            return

        try:
            # Clear interactive tab
            interactive_layout = self.interactive_tab.layout()
            if interactive_layout:
                while interactive_layout.count():
                    child = interactive_layout.takeAt(0)
                    if child.widget():
                        child.widget().deleteLater()
            else:
                interactive_layout = QVBoxLayout(self.interactive_tab)

            # Create interactive plot based on type
            if plot_type == "Interactive CTF Resolution":
                self.create_interactive_ctf_resolution(interactive_layout)
            elif plot_type == "Interactive CTF Defocus":
                self.create_interactive_ctf_defocus(interactive_layout)
            else:
                # Fallback message
                label = QLabel(f"{plot_type}\nInteractive plotting functionality")
                label.setAlignment(Qt.AlignmentFlag.AlignCenter)
                interactive_layout.addWidget(label)

            # Switch to interactive tab
            self.plot_tabs.setCurrentWidget(self.interactive_tab)

        except Exception as e:
            logger.error(f"Error creating interactive plot: {e}")

    def create_interactive_ctf_resolution(self, layout):
        """Create interactive CTF resolution plot."""
        try:
            from ...analysis.interactive_plots import InteractivePlotter

            # Create interactive plotter
            plotter = InteractivePlotter()

            # Parse CTF data
            from ...analysis.ctf_analysis.ctf_parser import parse_ctf_data
            ctf_data = parse_ctf_data(self.current_dataset)

            if ctf_data and ctf_data.get("ctf_parameters"):
                # Create HTML content
                html_content = plotter.create_ctf_resolution_plot(ctf_data)

                # Create web view to display HTML
                try:
                    from PyQt6.QtWebEngineWidgets import QWebEngineView
                    web_view = QWebEngineView()
                    web_view.setHtml(html_content)
                    layout.addWidget(web_view)
                except ImportError:
                    # Fallback to text display
                    label = QLabel("Interactive CTF Resolution Plot\n(WebEngine not available)")
                    label.setAlignment(Qt.AlignmentFlag.AlignCenter)
                    layout.addWidget(label)
            else:
                label = QLabel("No CTF data available for interactive plotting")
                label.setAlignment(Qt.AlignmentFlag.AlignCenter)
                layout.addWidget(label)

        except Exception as e:
            logger.error(f"Error creating interactive CTF resolution plot: {e}")
            label = QLabel(f"Error creating interactive plot: {str(e)}")
            label.setAlignment(Qt.AlignmentFlag.AlignCenter)
            layout.addWidget(label)

    def create_interactive_ctf_defocus(self, layout):
        """Create interactive CTF defocus plot."""
        try:
            from ...analysis.interactive_plots import InteractivePlotter

            # Create interactive plotter
            plotter = InteractivePlotter()

            # Parse CTF data
            from ...analysis.ctf_analysis.ctf_parser import parse_ctf_data
            ctf_data = parse_ctf_data(self.current_dataset)

            if ctf_data and ctf_data.get("ctf_parameters"):
                # Create HTML content
                html_content = plotter.create_ctf_defocus_plot(ctf_data)

                # Create web view to display HTML
                try:
                    from PyQt6.QtWebEngineWidgets import QWebEngineView
                    web_view = QWebEngineView()
                    web_view.setHtml(html_content)
                    layout.addWidget(web_view)
                except ImportError:
                    # Fallback to text display
                    label = QLabel("Interactive CTF Defocus Plot\n(WebEngine not available)")
                    label.setAlignment(Qt.AlignmentFlag.AlignCenter)
                    layout.addWidget(label)
            else:
                label = QLabel("No CTF data available for interactive plotting")
                label.setAlignment(Qt.AlignmentFlag.AlignCenter)
                layout.addWidget(label)

        except Exception as e:
            logger.error(f"Error creating interactive CTF defocus plot: {e}")
            label = QLabel(f"Error creating interactive plot: {str(e)}")
            label.setAlignment(Qt.AlignmentFlag.AlignCenter)
            layout.addWidget(label)

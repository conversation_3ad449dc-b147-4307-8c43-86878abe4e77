"""
Theme management for AreTomo3 GUI
Provides light and dark themes with proper contrast and accessibility
"""

from typing import Dict, Any
from PyQt6.QtCore import QSettings


class ThemeManager:
    """Manages application themes and styling."""
    
    def __init__(self):
        """Initialize theme manager."""
        self.settings = QSettings("AreTomo3", "GUI")
        self.current_theme = self.settings.value("theme", "light")  # Default to light theme
        
    def get_current_theme(self) -> str:
        """Get the current theme name."""
        return self.current_theme
        
    def set_theme(self, theme_name: str):
        """Set the current theme."""
        if theme_name in ["light", "dark"]:
            self.current_theme = theme_name
            self.settings.setValue("theme", theme_name)
            
    def get_theme_stylesheet(self, theme_name: str = None) -> str:
        """Get the complete stylesheet for a theme."""
        if theme_name is None:
            theme_name = self.current_theme
            
        if theme_name == "light":
            return self.get_light_theme()
        else:
            return self.get_dark_theme()
            
    def get_light_theme(self) -> str:
        """Get light theme stylesheet."""
        return """
            /* Light Theme - Professional and Clean */
            QMainWindow {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #f8f9fa, stop:1 #e9ecef);
                color: #212529;
            }
            
            /* Tab Widget */
            QTabWidget::pane {
                border: 2px solid #dee2e6;
                border-radius: 8px;
                background: #ffffff;
                margin-top: 5px;
            }
            
            QTabBar::tab {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #ffffff, stop:1 #f8f9fa);
                color: #495057;
                padding: 8px 16px;
                margin: 2px;
                border: 1px solid #dee2e6;
                border-radius: 6px;
                font-weight: 600;
                min-width: 120px;
            }
            
            QTabBar::tab:selected {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #007bff, stop:1 #0056b3);
                color: white;
                border: 1px solid #0056b3;
            }
            
            QTabBar::tab:hover:!selected {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #e3f2fd, stop:1 #bbdefb);
                color: #1976d2;
                border: 1px solid #2196f3;
            }
            
            /* Buttons */
            QPushButton {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #ffffff, stop:1 #f8f9fa);
                color: #495057;
                border: 2px solid #dee2e6;
                border-radius: 6px;
                padding: 8px 16px;
                font-weight: 600;
                min-height: 20px;
            }
            
            QPushButton:hover {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #e3f2fd, stop:1 #bbdefb);
                border: 2px solid #2196f3;
                color: #1976d2;
            }
            
            QPushButton:pressed {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #1976d2, stop:1 #1565c0);
                color: white;
                border: 2px solid #1565c0;
            }
            
            QPushButton:disabled {
                background: #f8f9fa;
                color: #6c757d;
                border: 2px solid #e9ecef;
            }
            
            /* Input Fields */
            QLineEdit, QTextEdit, QPlainTextEdit {
                background: #ffffff;
                color: #212529;
                border: 2px solid #ced4da;
                border-radius: 4px;
                padding: 6px;
                font-size: 13px;
            }
            
            QLineEdit:focus, QTextEdit:focus, QPlainTextEdit:focus {
                border: 2px solid #007bff;
                background: #ffffff;
            }
            
            /* Dropdowns - Fixed color issues */
            QComboBox {
                background: #ffffff;
                color: #212529;
                border: 2px solid #ced4da;
                border-radius: 4px;
                padding: 6px;
                min-width: 100px;
            }
            
            QComboBox:hover {
                border: 2px solid #007bff;
            }
            
            QComboBox::drop-down {
                border: none;
                background: #f8f9fa;
                width: 20px;
                border-top-right-radius: 4px;
                border-bottom-right-radius: 4px;
            }
            
            QComboBox::down-arrow {
                image: none;
                border-left: 5px solid transparent;
                border-right: 5px solid transparent;
                border-top: 5px solid #495057;
                width: 0px;
                height: 0px;
            }
            
            QComboBox QAbstractItemView {
                background: #ffffff;
                color: #212529;
                border: 1px solid #ced4da;
                selection-background-color: #007bff;
                selection-color: #ffffff;
                outline: none;
            }
            
            QComboBox QAbstractItemView::item {
                background: #ffffff;
                color: #212529;
                padding: 6px;
                border: none;
            }
            
            QComboBox QAbstractItemView::item:hover {
                background: #e3f2fd;
                color: #1976d2;
            }
            
            QComboBox QAbstractItemView::item:selected {
                background: #007bff;
                color: #ffffff;
            }
            
            /* Spin Boxes */
            QSpinBox, QDoubleSpinBox {
                background: #ffffff;
                color: #212529;
                border: 2px solid #ced4da;
                border-radius: 4px;
                padding: 6px;
            }
            
            QSpinBox:focus, QDoubleSpinBox:focus {
                border: 2px solid #007bff;
            }
            
            /* Group Boxes */
            QGroupBox {
                font-weight: bold;
                color: #495057;
                border: 2px solid #dee2e6;
                border-radius: 8px;
                margin-top: 10px;
                padding-top: 10px;
                background: #ffffff;
            }
            
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 8px 0 8px;
                color: #007bff;
                font-weight: bold;
            }
            
            /* Labels */
            QLabel {
                color: #495057;
                background: transparent;
            }
            
            /* Checkboxes and Radio Buttons */
            QCheckBox, QRadioButton {
                color: #495057;
                spacing: 8px;
            }
            
            QCheckBox::indicator, QRadioButton::indicator {
                width: 16px;
                height: 16px;
                border: 2px solid #ced4da;
                border-radius: 3px;
                background: #ffffff;
            }
            
            QCheckBox::indicator:checked, QRadioButton::indicator:checked {
                background: #007bff;
                border: 2px solid #0056b3;
            }
            
            /* Progress Bars */
            QProgressBar {
                border: 2px solid #dee2e6;
                border-radius: 4px;
                background: #f8f9fa;
                text-align: center;
                color: #495057;
            }
            
            QProgressBar::chunk {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #28a745, stop:1 #1e7e34);
                border-radius: 2px;
            }
            
            /* Scrollbars */
            QScrollBar:vertical {
                background: #f8f9fa;
                width: 12px;
                border-radius: 6px;
            }
            
            QScrollBar::handle:vertical {
                background: #ced4da;
                border-radius: 6px;
                min-height: 20px;
            }
            
            QScrollBar::handle:vertical:hover {
                background: #adb5bd;
            }
            
            /* Menu Bar */
            QMenuBar {
                background: #ffffff;
                color: #495057;
                border-bottom: 1px solid #dee2e6;
            }
            
            QMenuBar::item {
                background: transparent;
                padding: 6px 12px;
            }
            
            QMenuBar::item:selected {
                background: #e3f2fd;
                color: #1976d2;
            }
            
            QMenu {
                background: #ffffff;
                color: #495057;
                border: 1px solid #dee2e6;
            }
            
            QMenu::item {
                padding: 6px 20px;
            }
            
            QMenu::item:selected {
                background: #007bff;
                color: #ffffff;
            }
        """
        
    def get_dark_theme(self) -> str:
        """Get dark theme stylesheet with fixed dropdown colors."""
        return """
            /* Dark Theme - Professional and Modern */
            QMainWindow {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #1e3c72, stop:1 #2a5298);
                color: #ffffff;
            }
            
            /* Tab Widget */
            QTabWidget::pane {
                border: 2px solid #34495e;
                border-radius: 10px;
                background: #2c3e50;
                margin-top: 5px;
            }
            
            QTabBar::tab {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #34495e, stop:1 #2c3e50);
                color: #ecf0f1;
                padding: 8px 16px;
                margin: 2px;
                border-radius: 6px;
                font-weight: bold;
                min-width: 120px;
            }
            
            QTabBar::tab:selected {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #3498db, stop:1 #2980b9);
                color: #ffffff;
            }
            
            QTabBar::tab:hover:!selected {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #5dade2, stop:1 #3498db);
                color: #ffffff;
            }
            
            /* Buttons */
            QPushButton {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #4a5568, stop:1 #2d3748);
                color: #ffffff;
                border: 2px solid #4a5568;
                border-radius: 8px;
                padding: 8px 16px;
                font-weight: bold;
                min-height: 20px;
            }

            QPushButton:hover {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #3498db, stop:1 #2980b9);
                border: 2px solid #3498db;
                color: #ffffff;
            }

            QPushButton:pressed {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #2980b9, stop:1 #21618c);
                color: #ffffff;
            }

            QPushButton:disabled {
                background: #2d3748;
                color: #718096;
                border: 2px solid #4a5568;
            }
            
            /* Input Fields */
            QLineEdit, QTextEdit, QPlainTextEdit {
                background: #2d3748;
                color: #ffffff;
                border: 2px solid #4a5568;
                border-radius: 6px;
                padding: 6px;
                font-size: 13px;
            }

            QLineEdit:focus, QTextEdit:focus, QPlainTextEdit:focus {
                border: 2px solid #3498db;
                background: #1a202c;
                color: #ffffff;
            }
            
            /* Dropdowns - Fixed color issues */
            QComboBox {
                background: #2d3748;
                color: #ffffff;
                border: 2px solid #4a5568;
                border-radius: 6px;
                padding: 6px;
                min-width: 100px;
            }

            QComboBox:hover {
                border: 2px solid #3498db;
                background: #1a202c;
            }

            QComboBox::drop-down {
                border: none;
                background: #1a202c;
                width: 20px;
                border-top-right-radius: 6px;
                border-bottom-right-radius: 6px;
            }

            QComboBox::down-arrow {
                image: none;
                border-left: 5px solid transparent;
                border-right: 5px solid transparent;
                border-top: 5px solid #ffffff;
                width: 0px;
                height: 0px;
            }

            QComboBox QAbstractItemView {
                background: #1a202c;
                color: #ffffff;
                border: 1px solid #4a5568;
                selection-background-color: #3498db;
                selection-color: #ffffff;
                outline: none;
            }

            QComboBox QAbstractItemView::item {
                background: #1a202c;
                color: #ffffff;
                padding: 6px;
                border: none;
            }

            QComboBox QAbstractItemView::item:hover {
                background: #2d3748;
                color: #3498db;
            }

            QComboBox QAbstractItemView::item:selected {
                background: #3498db;
                color: #ffffff;
            }
            
            /* Spin Boxes */
            QSpinBox, QDoubleSpinBox {
                background: #2d3748;
                color: #ffffff;
                border: 2px solid #4a5568;
                border-radius: 6px;
                padding: 6px;
            }

            QSpinBox:focus, QDoubleSpinBox:focus {
                border: 2px solid #3498db;
                background: #1a202c;
            }

            /* Group Boxes */
            QGroupBox {
                font-weight: bold;
                color: #ffffff;
                border: 2px solid #4a5568;
                border-radius: 10px;
                margin-top: 10px;
                padding-top: 10px;
                background: rgba(26, 32, 44, 0.5);
            }

            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 8px 0 8px;
                color: #3498db;
                font-weight: bold;
            }

            /* Labels */
            QLabel {
                color: #ffffff;
                background: transparent;
            }
            
            /* Checkboxes and Radio Buttons */
            QCheckBox, QRadioButton {
                color: #ffffff;
                spacing: 8px;
            }

            QCheckBox::indicator, QRadioButton::indicator {
                width: 16px;
                height: 16px;
                border: 2px solid #4a5568;
                border-radius: 3px;
                background: #2d3748;
            }

            QCheckBox::indicator:checked, QRadioButton::indicator:checked {
                background: #3498db;
                border: 2px solid #2980b9;
            }
            
            /* Progress Bars */
            QProgressBar {
                border: 2px solid #34495e;
                border-radius: 6px;
                background: #2c3e50;
                text-align: center;
                color: #ecf0f1;
            }
            
            QProgressBar::chunk {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #27ae60, stop:1 #229954);
                border-radius: 4px;
            }
            
            /* Scrollbars */
            QScrollBar:vertical {
                background: #2c3e50;
                width: 12px;
                border-radius: 6px;
            }
            
            QScrollBar::handle:vertical {
                background: #34495e;
                border-radius: 6px;
                min-height: 20px;
            }
            
            QScrollBar::handle:vertical:hover {
                background: #5d6d7e;
            }
            
            /* Menu Bar */
            QMenuBar {
                background: #1a202c;
                color: #ffffff;
                border-bottom: 1px solid #4a5568;
            }

            QMenuBar::item {
                background: transparent;
                padding: 6px 12px;
                color: #ffffff;
            }

            QMenuBar::item:selected {
                background: #2d3748;
                color: #3498db;
            }

            QMenu {
                background: #1a202c;
                color: #ffffff;
                border: 1px solid #4a5568;
            }

            QMenu::item {
                padding: 6px 20px;
                color: #ffffff;
            }

            QMenu::item:selected {
                background: #3498db;
                color: #ffffff;
            }

            /* Tab Widget - Enhanced */
            QTabWidget::pane {
                border: 2px solid #4a5568;
                background: #1a202c;
                border-radius: 10px;
                margin-top: 5px;
            }

            QTabBar::tab {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #2d3748, stop:1 #1a202c);
                color: #ffffff;
                padding: 8px 16px;
                margin: 2px;
                border-radius: 6px;
                font-weight: bold;
                min-width: 120px;
            }

            QTabBar::tab:selected {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #3498db, stop:1 #2980b9);
                color: #ffffff;
            }

            QTabBar::tab:hover:!selected {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #4a5568, stop:1 #2d3748);
                color: #3498db;
            }
        """

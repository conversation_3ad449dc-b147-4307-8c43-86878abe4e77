#!/usr/bin/env python3
"""
Napari Viewer Component for AreTomo3 GUI
Provides 3D volume visualization with Napari integration
"""

import logging
import os
from pathlib import Path
from typing import Optional, Any

from PyQt6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QPushButton, QLabel,
    QFileDialog, QMessageBox, QGroupBox, QCheckBox, QSlider,
    QSpinBox, QComboBox
)
from PyQt6.QtCore import Qt, pyqtSignal
from PyQt6.QtGui import QFont

logger = logging.getLogger(__name__)


class NapariViewerWidget(QWidget):
    """Napari viewer widget with fallback support"""
    
    # Signals
    volume_loaded = pyqtSignal(str)  # Emitted when volume is loaded
    viewer_error = pyqtSignal(str)   # Emitted on viewer errors
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.napari_viewer = None
        self.napari_widget = None
        self.current_volume_path = None
        
        # Initialize UI
        self.init_ui()
        
        # Try to initialize Napari
        self.init_napari()
        
    def init_ui(self):
        """Initialize the user interface"""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(10, 10, 10, 10)
        layout.setSpacing(10)
        
        # Controls section
        controls_group = QGroupBox("🎮 Viewer Controls")
        controls_layout = QHBoxLayout(controls_group)
        
        # Load volume button
        self.load_btn = QPushButton("📂 Load Volume")
        self.load_btn.clicked.connect(self.load_volume)
        controls_layout.addWidget(self.load_btn)
        
        # Reset view button
        self.reset_btn = QPushButton("🔄 Reset View")
        self.reset_btn.clicked.connect(self.reset_view)
        self.reset_btn.setEnabled(False)
        controls_layout.addWidget(self.reset_btn)
        
        # Screenshot button
        self.screenshot_btn = QPushButton("📸 Screenshot")
        self.screenshot_btn.clicked.connect(self.take_screenshot)
        self.screenshot_btn.setEnabled(False)
        controls_layout.addWidget(self.screenshot_btn)
        
        controls_layout.addStretch()
        layout.addWidget(controls_group)
        
        # Viewer settings
        settings_group = QGroupBox("⚙️ Display Settings")
        settings_layout = QHBoxLayout(settings_group)
        
        # Colormap selection
        settings_layout.addWidget(QLabel("Colormap:"))
        self.colormap_combo = QComboBox()
        self.colormap_combo.addItems(['gray', 'viridis', 'plasma', 'inferno', 'magma'])
        self.colormap_combo.currentTextChanged.connect(self.change_colormap)
        settings_layout.addWidget(self.colormap_combo)
        
        # Opacity control
        settings_layout.addWidget(QLabel("Opacity:"))
        self.opacity_slider = QSlider(Qt.Orientation.Horizontal)
        self.opacity_slider.setRange(0, 100)
        self.opacity_slider.setValue(100)
        self.opacity_slider.valueChanged.connect(self.change_opacity)
        settings_layout.addWidget(self.opacity_slider)
        
        settings_layout.addStretch()
        layout.addWidget(settings_group)
        
        # Viewer container
        self.viewer_container = QWidget()
        self.viewer_container.setMinimumHeight(400)
        self.viewer_container.setStyleSheet("""
            QWidget {
                background-color: #1a1a1a;
                border: 2px solid #555555;
                border-radius: 8px;
            }
        """)
        
        # Placeholder layout
        self.viewer_layout = QVBoxLayout(self.viewer_container)
        self.create_placeholder()
        
        layout.addWidget(self.viewer_container)
        
    def create_placeholder(self):
        """Create placeholder when Napari is not available"""
        placeholder_label = QLabel("🎮 3D Viewer")
        placeholder_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        placeholder_label.setFont(QFont("Arial", 16, QFont.Weight.Bold))
        placeholder_label.setStyleSheet("""
            QLabel {
                color: #888888;
                background-color: transparent;
                border: none;
                padding: 50px;
            }
        """)
        
        status_label = QLabel("Napari integration ready")
        status_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        status_label.setStyleSheet("""
            QLabel {
                color: #666666;
                background-color: transparent;
                border: none;
                font-size: 12px;
            }
        """)
        
        self.viewer_layout.addWidget(placeholder_label)
        self.viewer_layout.addWidget(status_label)
        
    def init_napari(self):
        """Initialize Napari viewer"""
        try:
            import napari
            
            # Create Napari viewer
            self.napari_viewer = napari.Viewer(show=False)
            
            # Get the Qt widget
            self.napari_widget = self.napari_viewer.window._qt_window
            
            # Clear placeholder and add Napari widget
            self.clear_viewer_layout()
            self.viewer_layout.addWidget(self.napari_widget)
            
            logger.info("Napari viewer initialized successfully")
            
        except ImportError:
            logger.warning("Napari not available, using placeholder")
            self.create_fallback_message()
            
        except Exception as e:
            logger.error(f"Failed to initialize Napari: {e}")
            self.create_error_message(str(e))
            
    def clear_viewer_layout(self):
        """Clear the viewer layout"""
        while self.viewer_layout.count():
            child = self.viewer_layout.takeAt(0)
            if child.widget():
                child.widget().deleteLater()
                
    def create_fallback_message(self):
        """Create fallback message when Napari is not available"""
        self.clear_viewer_layout()
        
        fallback_label = QLabel("📦 Napari Not Available")
        fallback_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        fallback_label.setFont(QFont("Arial", 14, QFont.Weight.Bold))
        fallback_label.setStyleSheet("""
            QLabel {
                color: #ff6b6b;
                background-color: transparent;
                border: none;
                padding: 30px;
            }
        """)
        
        install_label = QLabel("Install Napari: pip install napari[all]")
        install_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        install_label.setStyleSheet("""
            QLabel {
                color: #888888;
                background-color: transparent;
                border: none;
                font-size: 11px;
                font-family: monospace;
            }
        """)
        
        self.viewer_layout.addWidget(fallback_label)
        self.viewer_layout.addWidget(install_label)
        
    def create_error_message(self, error_msg: str):
        """Create error message"""
        self.clear_viewer_layout()
        
        error_label = QLabel(f"❌ Viewer Error")
        error_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        error_label.setFont(QFont("Arial", 14, QFont.Weight.Bold))
        error_label.setStyleSheet("""
            QLabel {
                color: #ff6b6b;
                background-color: transparent;
                border: none;
                padding: 20px;
            }
        """)
        
        details_label = QLabel(f"Details: {error_msg}")
        details_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        details_label.setWordWrap(True)
        details_label.setStyleSheet("""
            QLabel {
                color: #888888;
                background-color: transparent;
                border: none;
                font-size: 10px;
                padding: 10px;
            }
        """)
        
        self.viewer_layout.addWidget(error_label)
        self.viewer_layout.addWidget(details_label)
        
    def load_volume(self):
        """Load volume file"""
        file_path, _ = QFileDialog.getOpenFileName(
            self,
            "Select Volume File",
            "",
            "Volume Files (*.mrc *.rec *.tif *.tiff);;All Files (*)"
        )
        
        if file_path:
            self.load_volume_file(file_path)
            
    def load_volume_file(self, file_path: str):
        """Load volume from file path"""
        try:
            if self.napari_viewer is None:
                QMessageBox.warning(self, "Napari Error", "Napari viewer not available")
                return
                
            # Load volume data (placeholder - would need actual MRC reading)
            import numpy as np
            
            # For now, create dummy data
            volume_data = np.random.rand(64, 64, 64)
            
            # Add to Napari viewer
            self.napari_viewer.add_image(
                volume_data,
                name=Path(file_path).name,
                colormap=self.colormap_combo.currentText()
            )
            
            self.current_volume_path = file_path
            self.reset_btn.setEnabled(True)
            self.screenshot_btn.setEnabled(True)
            
            self.volume_loaded.emit(file_path)
            logger.info(f"Volume loaded: {file_path}")
            
        except Exception as e:
            error_msg = f"Failed to load volume: {e}"
            logger.error(error_msg)
            self.viewer_error.emit(error_msg)
            QMessageBox.critical(self, "Load Error", error_msg)
            
    def reset_view(self):
        """Reset viewer to default view"""
        if self.napari_viewer:
            self.napari_viewer.reset_view()
            
    def take_screenshot(self):
        """Take screenshot of current view"""
        if self.napari_viewer is None:
            return
            
        try:
            # Get screenshot
            screenshot = self.napari_viewer.screenshot()
            
            # Save screenshot
            save_path, _ = QFileDialog.getSaveFileName(
                self,
                "Save Screenshot",
                "napari_screenshot.png",
                "PNG Files (*.png);;All Files (*)"
            )
            
            if save_path:
                # Save screenshot (would need actual implementation)
                logger.info(f"Screenshot saved: {save_path}")
                QMessageBox.information(self, "Screenshot", f"Screenshot saved to:\n{save_path}")
                
        except Exception as e:
            error_msg = f"Failed to take screenshot: {e}"
            logger.error(error_msg)
            QMessageBox.critical(self, "Screenshot Error", error_msg)
            
    def change_colormap(self, colormap: str):
        """Change colormap of current layer"""
        if self.napari_viewer and len(self.napari_viewer.layers) > 0:
            self.napari_viewer.layers[-1].colormap = colormap
            
    def change_opacity(self, value: int):
        """Change opacity of current layer"""
        if self.napari_viewer and len(self.napari_viewer.layers) > 0:
            self.napari_viewer.layers[-1].opacity = value / 100.0
            
    def closeEvent(self, event):
        """Handle close event"""
        if self.napari_viewer:
            self.napari_viewer.close()
        event.accept()

#!/usr/bin/env python3
"""
Minimal AreTomo3 GUI that actually works
"""

import sys
import os
from PyQt6.QtWidgets import (
    QApplication, QMainWindow, QWidget, QVBoxLayout, QHBoxLayout,
    QLabel, QLineEdit, QPushButton, QTabWidget, QTextEdit, QStatusBar,
    QFileDialog, QMessageBox, QSpinBox, QDoubleSpinBox, QCheckBox,
    QGroupBox, QFormLayout
)
from PyQt6.QtCore import Qt


class MinimalAreTomoGUI(QMainWindow):
    """Minimal working AreTomo3 GUI"""
    
    def __init__(self):
        super().__init__()
        self.setWindowTitle("AreTomo3 GUI - Minimal Working Version")
        self.setGeometry(100, 100, 1000, 700)
        
        # Create central widget
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        layout = QVBoxLayout(central_widget)
        
        # Add title
        title = QLabel("AreTomo3 GUI - Minimal Working Version")
        title.setStyleSheet("font-size: 18px; font-weight: bold; margin: 10px;")
        layout.addWidget(title)
        
        # Create tab widget
        self.tab_widget = QTabWidget()
        layout.addWidget(self.tab_widget)
        
        # Create tabs
        self.create_main_tab()
        self.create_parameters_tab()
        self.create_log_tab()
        
        # Create status bar
        self.status_bar = QStatusBar()
        self.setStatusBar(self.status_bar)
        self.status_bar.showMessage("Ready - Minimal GUI loaded successfully")
        
    def create_main_tab(self):
        """Create main control tab"""
        main_tab = QWidget()
        layout = QVBoxLayout(main_tab)
        
        # Input/Output section
        io_group = QGroupBox("Input/Output")
        io_layout = QFormLayout(io_group)
        
        self.input_dir = QLineEdit()
        self.input_dir.setPlaceholderText("Select input directory...")
        input_btn = QPushButton("Browse")
        input_btn.clicked.connect(self.browse_input_dir)
        
        input_layout = QHBoxLayout()
        input_layout.addWidget(self.input_dir)
        input_layout.addWidget(input_btn)
        io_layout.addRow("Input Directory:", input_layout)
        
        self.output_dir = QLineEdit()
        self.output_dir.setPlaceholderText("Output directory (auto-generated)")
        io_layout.addRow("Output Directory:", self.output_dir)
        
        layout.addWidget(io_group)
        
        # AreTomo3 path section
        aretomo_group = QGroupBox("AreTomo3 Configuration")
        aretomo_layout = QFormLayout(aretomo_group)
        
        self.aretomo_path = QLineEdit()
        self.aretomo_path.setPlaceholderText("Path to AreTomo3 executable...")
        aretomo_btn = QPushButton("Browse")
        aretomo_btn.clicked.connect(self.browse_aretomo_path)
        
        aretomo_path_layout = QHBoxLayout()
        aretomo_path_layout.addWidget(self.aretomo_path)
        aretomo_path_layout.addWidget(aretomo_btn)
        aretomo_layout.addRow("AreTomo3 Path:", aretomo_path_layout)
        
        layout.addWidget(aretomo_group)
        
        # Control buttons
        button_layout = QHBoxLayout()
        
        self.load_btn = QPushButton("Load Tilt Series")
        self.load_btn.clicked.connect(self.load_tilt_series)
        button_layout.addWidget(self.load_btn)
        
        self.process_btn = QPushButton("Start Processing")
        self.process_btn.clicked.connect(self.start_processing)
        button_layout.addWidget(self.process_btn)
        
        self.stop_btn = QPushButton("Stop")
        self.stop_btn.clicked.connect(self.stop_processing)
        self.stop_btn.setEnabled(False)
        button_layout.addWidget(self.stop_btn)
        
        layout.addLayout(button_layout)
        layout.addStretch()
        
        self.tab_widget.addTab(main_tab, "Main")
        
    def create_parameters_tab(self):
        """Create parameters tab"""
        params_tab = QWidget()
        layout = QVBoxLayout(params_tab)
        
        # Microscope parameters
        microscope_group = QGroupBox("Microscope Parameters")
        microscope_layout = QFormLayout(microscope_group)
        
        self.pixel_size = QDoubleSpinBox()
        self.pixel_size.setRange(0.1, 10.0)
        self.pixel_size.setValue(1.91)
        self.pixel_size.setSuffix(" Å")
        microscope_layout.addRow("Pixel Size:", self.pixel_size)
        
        self.voltage = QSpinBox()
        self.voltage.setRange(80, 300)
        self.voltage.setValue(300)
        self.voltage.setSuffix(" kV")
        microscope_layout.addRow("Voltage:", self.voltage)
        
        self.cs = QDoubleSpinBox()
        self.cs.setRange(0.0, 10.0)
        self.cs.setValue(2.7)
        self.cs.setSuffix(" mm")
        microscope_layout.addRow("Cs:", self.cs)
        
        layout.addWidget(microscope_group)
        
        # Processing parameters
        processing_group = QGroupBox("Processing Parameters")
        processing_layout = QFormLayout(processing_group)
        
        self.tilt_axis = QDoubleSpinBox()
        self.tilt_axis.setRange(-180, 180)
        self.tilt_axis.setValue(-95.75)
        self.tilt_axis.setSuffix("°")
        processing_layout.addRow("Tilt Axis:", self.tilt_axis)
        
        self.vol_z = QSpinBox()
        self.vol_z.setRange(100, 4096)
        self.vol_z.setValue(2048)
        processing_layout.addRow("Volume Z:", self.vol_z)
        
        layout.addWidget(processing_group)
        layout.addStretch()
        
        self.tab_widget.addTab(params_tab, "Parameters")
        
    def create_log_tab(self):
        """Create log tab"""
        log_tab = QWidget()
        layout = QVBoxLayout(log_tab)
        
        self.log_text = QTextEdit()
        self.log_text.setReadOnly(True)
        self.log_text.append("AreTomo3 GUI - Minimal Version Loaded")
        self.log_text.append("Ready for processing...")
        layout.addWidget(self.log_text)
        
        # Log controls
        log_controls = QHBoxLayout()
        clear_btn = QPushButton("Clear Log")
        clear_btn.clicked.connect(self.log_text.clear)
        log_controls.addWidget(clear_btn)
        log_controls.addStretch()
        
        layout.addLayout(log_controls)
        
        self.tab_widget.addTab(log_tab, "Log")
        
    def browse_input_dir(self):
        """Browse for input directory"""
        dir_path = QFileDialog.getExistingDirectory(self, "Select Input Directory")
        if dir_path:
            self.input_dir.setText(dir_path)
            # Auto-generate output directory
            output_path = os.path.join(dir_path, "aretomo_output")
            self.output_dir.setText(output_path)
            self.log_text.append(f"Input directory set: {dir_path}")
            
    def browse_aretomo_path(self):
        """Browse for AreTomo3 executable"""
        file_path, _ = QFileDialog.getOpenFileName(
            self, "Select AreTomo3 Executable", "", "Executable Files (*)"
        )
        if file_path:
            self.aretomo_path.setText(file_path)
            self.log_text.append(f"AreTomo3 path set: {file_path}")
            
    def load_tilt_series(self):
        """Load tilt series from input directory"""
        input_dir = self.input_dir.text()
        if not input_dir:
            QMessageBox.warning(self, "No Input", "Please select an input directory first.")
            return
            
        if not os.path.exists(input_dir):
            QMessageBox.warning(self, "Invalid Path", "Input directory does not exist.")
            return
            
        self.log_text.append(f"Loading tilt series from: {input_dir}")
        # TODO: Implement actual tilt series loading
        self.log_text.append("Tilt series loading functionality will be implemented...")
        self.status_bar.showMessage("Tilt series loaded (placeholder)")
        
    def start_processing(self):
        """Start AreTomo3 processing"""
        if not self.input_dir.text():
            QMessageBox.warning(self, "No Input", "Please select an input directory first.")
            return
            
        if not self.aretomo_path.text():
            QMessageBox.warning(self, "No AreTomo3", "Please set the AreTomo3 executable path.")
            return
            
        self.log_text.append("Starting AreTomo3 processing...")
        self.log_text.append("Processing functionality will be implemented...")
        self.process_btn.setEnabled(False)
        self.stop_btn.setEnabled(True)
        self.status_bar.showMessage("Processing (placeholder)")
        
    def stop_processing(self):
        """Stop processing"""
        self.log_text.append("Stopping processing...")
        self.process_btn.setEnabled(True)
        self.stop_btn.setEnabled(False)
        self.status_bar.showMessage("Processing stopped")


def create_minimal_gui():
    """Create and return the minimal GUI"""
    return MinimalAreTomoGUI()

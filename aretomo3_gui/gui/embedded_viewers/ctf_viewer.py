"""
Embedded CTF Viewer Widget
Fully integrated CTF visualization directly in the main GUI
"""

import numpy as np
import matplotlib.pyplot as plt
from matplotlib.backends.backend_qt5agg import FigureCanvasQTAgg as FigureCanvas
from matplotlib.figure import Figure
from matplotlib.widgets import <PERSON>lider
from PyQt6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QLabel, QSlider, QPushButton,
    QCheckBox, QSpinBox, QDoubleSpinBox, QGroupBox, QGridLayout,
    QComboBox, QProgressBar, QTextEdit, QSplitter, QFrame
)
from PyQt6.QtCore import Qt, pyqtSignal, QTimer, QThread, pyqtSlot
from PyQt6.QtGui import QFont, QPalette
import logging
from typing import Dict, List, Optional, Tuple, Any
import mrcfile
from scipy import ndimage
from skimage import filters

logger = logging.getLogger(__name__)


class EmbeddedCTFViewer(QWidget):
    """Fully embedded CTF viewer widget for the main GUI."""
    
    # Signals for communication with main GUI
    ctf_data_loaded = pyqtSignal(dict)
    ctf_analysis_updated = pyqtSignal(dict)
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.ctf_data = None
        self.current_tilt_idx = 0
        self.power_spectra = None
        self.ctf_fits = None
        self.setup_ui()
        self.setup_matplotlib()
        
    def setup_ui(self):
        """Setup the embedded CTF viewer UI."""
        layout = QVBoxLayout(self)
        
        # Header with title and controls
        header = self.create_header()
        layout.addWidget(header)
        
        # Main content area with splitter
        splitter = QSplitter(Qt.Orientation.Horizontal)
        
        # Left panel: CTF visualization
        viz_panel = self.create_visualization_panel()
        splitter.addWidget(viz_panel)
        
        # Right panel: Controls and analysis
        control_panel = self.create_control_panel()
        splitter.addWidget(control_panel)
        
        # Set splitter proportions (70% viz, 30% controls)
        splitter.setSizes([700, 300])
        layout.addWidget(splitter)
        
        # Status bar
        self.status_label = QLabel("Ready - Load CTF data to begin")
        self.status_label.setStyleSheet("QLabel { color: #666; font-style: italic; }")
        layout.addWidget(self.status_label)
        
    def create_header(self):
        """Create header with title and main controls."""
        header = QFrame()
        header.setFrameStyle(QFrame.Shape.StyledPanel)
        header.setStyleSheet("QFrame { background-color: #f0f0f0; border-radius: 5px; }")
        
        layout = QHBoxLayout(header)
        
        # Title
        title = QLabel("🔬 CTF Analysis Viewer")
        title.setFont(QFont("Arial", 14, QFont.Weight.Bold))
        layout.addWidget(title)
        
        layout.addStretch()
        
        # Quick action buttons
        self.load_btn = QPushButton("📁 Load CTF Data")
        self.load_btn.clicked.connect(self.load_ctf_data)
        layout.addWidget(self.load_btn)
        
        self.auto_analyze_btn = QPushButton("🔍 Auto Analyze")
        self.auto_analyze_btn.clicked.connect(self.auto_analyze_ctf)
        self.auto_analyze_btn.setEnabled(False)
        layout.addWidget(self.auto_analyze_btn)
        
        self.export_btn = QPushButton("💾 Export Results")
        self.export_btn.clicked.connect(self.export_results)
        self.export_btn.setEnabled(False)
        layout.addWidget(self.export_btn)
        
        return header
        
    def create_visualization_panel(self):
        """Create the main visualization panel."""
        panel = QWidget()
        layout = QVBoxLayout(panel)
        
        # Matplotlib figure for CTF visualization
        self.figure = Figure(figsize=(10, 8), facecolor='white')
        self.canvas = FigureCanvas(self.figure)
        layout.addWidget(self.canvas)
        
        # Navigation controls
        nav_layout = QHBoxLayout()
        
        # Tilt navigation
        nav_layout.addWidget(QLabel("Tilt:"))
        self.tilt_slider = QSlider(Qt.Orientation.Horizontal)
        self.tilt_slider.setMinimum(0)
        self.tilt_slider.setMaximum(0)
        self.tilt_slider.valueChanged.connect(self.on_tilt_changed)
        nav_layout.addWidget(self.tilt_slider)
        
        self.tilt_label = QLabel("0/0")
        nav_layout.addWidget(self.tilt_label)
        
        # Previous/Next buttons
        self.prev_btn = QPushButton("◀ Prev")
        self.prev_btn.clicked.connect(self.prev_tilt)
        nav_layout.addWidget(self.prev_btn)
        
        self.next_btn = QPushButton("Next ▶")
        self.next_btn.clicked.connect(self.next_tilt)
        nav_layout.addWidget(self.next_btn)
        
        layout.addLayout(nav_layout)
        
        return panel
        
    def create_control_panel(self):
        """Create the control and analysis panel."""
        panel = QWidget()
        layout = QVBoxLayout(panel)
        
        # CTF Parameters Group
        params_group = QGroupBox("CTF Parameters")
        params_layout = QGridLayout(params_group)
        
        # Voltage
        params_layout.addWidget(QLabel("Voltage (kV):"), 0, 0)
        self.voltage_spin = QSpinBox()
        self.voltage_spin.setRange(80, 300)
        self.voltage_spin.setValue(300)
        params_layout.addWidget(self.voltage_spin, 0, 1)
        
        # Cs
        params_layout.addWidget(QLabel("Cs (mm):"), 1, 0)
        self.cs_spin = QDoubleSpinBox()
        self.cs_spin.setRange(0.1, 10.0)
        self.cs_spin.setValue(2.7)
        self.cs_spin.setSingleStep(0.1)
        params_layout.addWidget(self.cs_spin, 1, 1)
        
        # Pixel size
        params_layout.addWidget(QLabel("Pixel Size (Å):"), 2, 0)
        self.pixel_size_spin = QDoubleSpinBox()
        self.pixel_size_spin.setRange(0.1, 10.0)
        self.pixel_size_spin.setValue(1.0)
        self.pixel_size_spin.setSingleStep(0.1)
        params_layout.addWidget(self.pixel_size_spin, 2, 1)
        
        layout.addWidget(params_group)
        
        # Display Options Group
        display_group = QGroupBox("Display Options")
        display_layout = QVBoxLayout(display_group)
        
        self.log_scale_cb = QCheckBox("Log Scale")
        self.log_scale_cb.setChecked(True)
        self.log_scale_cb.toggled.connect(self.update_display)
        display_layout.addWidget(self.log_scale_cb)
        
        self.show_rings_cb = QCheckBox("Show CTF Rings")
        self.show_rings_cb.setChecked(True)
        self.show_rings_cb.toggled.connect(self.update_display)
        display_layout.addWidget(self.show_rings_cb)
        
        self.show_fit_cb = QCheckBox("Show CTF Fit")
        self.show_fit_cb.setChecked(True)
        self.show_fit_cb.toggled.connect(self.update_display)
        display_layout.addWidget(self.show_fit_cb)
        
        layout.addWidget(display_group)
        
        # Quality Assessment Group
        quality_group = QGroupBox("Quality Assessment")
        quality_layout = QVBoxLayout(quality_group)
        
        self.quality_text = QTextEdit()
        self.quality_text.setMaximumHeight(150)
        self.quality_text.setReadOnly(True)
        quality_layout.addWidget(self.quality_text)
        
        layout.addWidget(quality_group)
        
        # Progress bar
        self.progress_bar = QProgressBar()
        self.progress_bar.setVisible(False)
        layout.addWidget(self.progress_bar)
        
        layout.addStretch()
        
        return panel
        
    def setup_matplotlib(self):
        """Setup matplotlib plots."""
        self.figure.clear()
        
        # Create subplots: 2x2 layout
        gs = self.figure.add_gridspec(2, 2, height_ratios=[3, 1], width_ratios=[3, 1])
        
        # Main CTF display (top-left)
        self.ax_main = self.figure.add_subplot(gs[0, 0])
        self.ax_main.set_title("CTF Power Spectrum")
        self.ax_main.set_xlabel("Frequency (1/Å)")
        self.ax_main.set_ylabel("Frequency (1/Å)")
        
        # Radial profile (top-right)
        self.ax_radial = self.figure.add_subplot(gs[0, 1])
        self.ax_radial.set_title("Radial Profile")
        self.ax_radial.set_xlabel("Frequency (1/Å)")
        self.ax_radial.set_ylabel("Amplitude")
        
        # CTF fit (bottom, spanning both columns)
        self.ax_fit = self.figure.add_subplot(gs[1, :])
        self.ax_fit.set_title("CTF Fit Quality")
        self.ax_fit.set_xlabel("Frequency (1/Å)")
        self.ax_fit.set_ylabel("Correlation")
        
        self.figure.tight_layout()
        self.canvas.draw()
        
    def load_ctf_data(self):
        """Load CTF data from file or directory."""
        from PyQt6.QtWidgets import QFileDialog
        
        file_path, _ = QFileDialog.getOpenFileName(
            self, "Load CTF Data", "", 
            "CTF Files (*.txt *.log *.star);;All Files (*)"
        )
        
        if file_path:
            self.load_ctf_from_file(file_path)
            
    def load_ctf_from_file(self, file_path: str):
        """Load CTF data from specific file."""
        try:
            self.status_label.setText(f"Loading CTF data from {Path(file_path).name}...")
            
            # Parse CTF data (simplified - would use actual parser)
            self.ctf_data = self.parse_ctf_file(file_path)
            
            if self.ctf_data:
                self.setup_navigation()
                self.update_display()
                self.auto_analyze_btn.setEnabled(True)
                self.export_btn.setEnabled(True)
                self.status_label.setText(f"Loaded {len(self.ctf_data.get('tilts', []))} tilts")
                self.ctf_data_loaded.emit(self.ctf_data)
            else:
                self.status_label.setText("Failed to load CTF data")
                
        except Exception as e:
            logger.error(f"Error loading CTF data: {e}")
            self.status_label.setText(f"Error: {e}")
            
    def parse_ctf_file(self, file_path: str) -> Dict:
        """Parse CTF file (simplified implementation)."""
        # This would integrate with your existing CTF parser
        # For now, create dummy data for demonstration
        
        n_tilts = 41  # Typical tilt series
        tilts = np.linspace(-60, 60, n_tilts)
        
        # Generate dummy power spectra
        size = 512
        power_spectra = []
        ctf_params = []
        
        for i, tilt in enumerate(tilts):
            # Create dummy power spectrum with CTF rings
            x, y = np.meshgrid(np.linspace(-1, 1, size), np.linspace(-1, 1, size))
            r = np.sqrt(x**2 + y**2)
            
            # Simulate CTF with defocus variation
            defocus = 2.0 + 0.5 * np.sin(np.radians(tilt))  # Defocus varies with tilt
            ctf_freq = r * 10  # Frequency in 1/Å
            ctf = np.sin(np.pi * defocus * ctf_freq**2)
            
            # Add noise and envelope
            envelope = np.exp(-r**2 / 0.5)
            power_spectrum = (ctf**2 + 0.1 * np.random.random((size, size))) * envelope
            
            power_spectra.append(power_spectrum)
            ctf_params.append({
                'defocus_u': defocus,
                'defocus_v': defocus * 0.9,
                'astigmatism': defocus * 0.1,
                'resolution': 3.5 + np.random.random() * 2,
                'quality': 0.7 + np.random.random() * 0.3
            })
            
        return {
            'file_path': file_path,
            'tilts': tilts,
            'power_spectra': power_spectra,
            'ctf_params': ctf_params,
            'n_tilts': n_tilts
        }
        
    def setup_navigation(self):
        """Setup navigation controls based on loaded data."""
        if not self.ctf_data:
            return
            
        n_tilts = self.ctf_data['n_tilts']
        self.tilt_slider.setMaximum(n_tilts - 1)
        self.tilt_slider.setValue(0)
        self.current_tilt_idx = 0
        self.update_tilt_label()
        
    def update_tilt_label(self):
        """Update tilt label."""
        if self.ctf_data:
            n_tilts = self.ctf_data['n_tilts']
            current_tilt = self.ctf_data['tilts'][self.current_tilt_idx]
            self.tilt_label.setText(f"{self.current_tilt_idx + 1}/{n_tilts} ({current_tilt:.1f}°)")
            
    def on_tilt_changed(self, value):
        """Handle tilt slider change."""
        self.current_tilt_idx = value
        self.update_tilt_label()
        self.update_display()
        
    def prev_tilt(self):
        """Go to previous tilt."""
        if self.current_tilt_idx > 0:
            self.tilt_slider.setValue(self.current_tilt_idx - 1)
            
    def next_tilt(self):
        """Go to next tilt."""
        if self.ctf_data and self.current_tilt_idx < self.ctf_data['n_tilts'] - 1:
            self.tilt_slider.setValue(self.current_tilt_idx + 1)
            
    def update_display(self):
        """Update the CTF display."""
        if not self.ctf_data or self.current_tilt_idx >= len(self.ctf_data['power_spectra']):
            return
            
        # Get current data
        power_spectrum = self.ctf_data['power_spectra'][self.current_tilt_idx]
        ctf_params = self.ctf_data['ctf_params'][self.current_tilt_idx]
        
        # Clear axes
        self.ax_main.clear()
        self.ax_radial.clear()
        self.ax_fit.clear()
        
        # Display power spectrum
        if self.log_scale_cb.isChecked():
            display_data = np.log10(np.maximum(power_spectrum, 1e-10))
        else:
            display_data = power_spectrum
            
        im = self.ax_main.imshow(display_data, cmap='gray', origin='lower')
        self.ax_main.set_title(f"CTF Power Spectrum - Tilt {self.ctf_data['tilts'][self.current_tilt_idx]:.1f}°")
        
        # Add CTF rings if enabled
        if self.show_rings_cb.isChecked():
            self.add_ctf_rings()
            
        # Plot radial profile
        self.plot_radial_profile(power_spectrum)
        
        # Plot CTF fit
        if self.show_fit_cb.isChecked():
            self.plot_ctf_fit(ctf_params)
            
        # Update quality assessment
        self.update_quality_assessment(ctf_params)
        
        self.figure.tight_layout()
        self.canvas.draw()
        
    def add_ctf_rings(self):
        """Add CTF rings to the display."""
        # Add theoretical CTF rings based on current parameters
        center = (256, 256)  # Assuming 512x512 image
        defocus = self.ctf_data['ctf_params'][self.current_tilt_idx]['defocus_u']
        
        # Calculate ring radii (simplified)
        for i in range(1, 6):
            radius = 50 * i * np.sqrt(defocus)  # Simplified calculation
            if radius < 256:
                circle = plt.Circle(center, radius, fill=False, color='red', alpha=0.5, linewidth=1)
                self.ax_main.add_patch(circle)
                
    def plot_radial_profile(self, power_spectrum):
        """Plot radial profile of power spectrum."""
        # Calculate radial profile
        center = np.array(power_spectrum.shape) // 2
        y, x = np.ogrid[:power_spectrum.shape[0], :power_spectrum.shape[1]]
        r = np.sqrt((x - center[1])**2 + (y - center[0])**2)
        
        # Bin by radius
        r_int = r.astype(int)
        max_r = min(center)
        radial_profile = []
        radii = []
        
        for radius in range(max_r):
            mask = r_int == radius
            if np.any(mask):
                radial_profile.append(np.mean(power_spectrum[mask]))
                radii.append(radius)
                
        if radii:
            self.ax_radial.plot(radii, radial_profile, 'b-', linewidth=2)
            self.ax_radial.set_xlabel('Radius (pixels)')
            self.ax_radial.set_ylabel('Amplitude')
            self.ax_radial.grid(True, alpha=0.3)
            
    def plot_ctf_fit(self, ctf_params):
        """Plot CTF fit quality."""
        # Generate theoretical CTF curve
        freq = np.linspace(0, 0.5, 100)
        defocus = ctf_params['defocus_u']
        
        # Simplified CTF calculation
        ctf_theory = np.sin(np.pi * defocus * freq**2)
        
        self.ax_fit.plot(freq, ctf_theory, 'r-', linewidth=2, label='Theoretical CTF')
        self.ax_fit.set_xlabel('Frequency (1/Å)')
        self.ax_fit.set_ylabel('CTF')
        self.ax_fit.grid(True, alpha=0.3)
        self.ax_fit.legend()
        
    def update_quality_assessment(self, ctf_params):
        """Update quality assessment display."""
        quality_text = f"""
Current Tilt Quality Assessment:

Defocus U: {ctf_params['defocus_u']:.2f} μm
Defocus V: {ctf_params['defocus_v']:.2f} μm
Astigmatism: {ctf_params['astigmatism']:.2f} μm
Resolution: {ctf_params['resolution']:.1f} Å
Quality Score: {ctf_params['quality']:.3f}

Status: {'Good' if ctf_params['quality'] > 0.8 else 'Fair' if ctf_params['quality'] > 0.6 else 'Poor'}
"""
        self.quality_text.setText(quality_text)
        
    def auto_analyze_ctf(self):
        """Perform automatic CTF analysis."""
        if not self.ctf_data:
            return
            
        self.progress_bar.setVisible(True)
        self.status_label.setText("Performing automatic CTF analysis...")
        
        # Simulate analysis progress
        for i in range(101):
            self.progress_bar.setValue(i)
            QTimer.singleShot(10 * i, lambda: None)  # Simulate work
            
        self.progress_bar.setVisible(False)
        self.status_label.setText("Automatic CTF analysis completed")
        
        # Emit analysis results
        analysis_results = {
            'mean_defocus': np.mean([p['defocus_u'] for p in self.ctf_data['ctf_params']]),
            'mean_resolution': np.mean([p['resolution'] for p in self.ctf_data['ctf_params']]),
            'mean_quality': np.mean([p['quality'] for p in self.ctf_data['ctf_params']])
        }
        self.ctf_analysis_updated.emit(analysis_results)
        
    def export_results(self):
        """Export CTF analysis results."""
        from PyQt6.QtWidgets import QFileDialog
        
        file_path, _ = QFileDialog.getSaveFileName(
            self, "Export CTF Results", "", 
            "CSV Files (*.csv);;Text Files (*.txt);;All Files (*)"
        )
        
        if file_path and self.ctf_data:
            try:
                # Export to CSV (simplified)
                import pandas as pd
                
                df = pd.DataFrame(self.ctf_data['ctf_params'])
                df['tilt_angle'] = self.ctf_data['tilts']
                df.to_csv(file_path, index=False)
                
                self.status_label.setText(f"Results exported to {Path(file_path).name}")
            except Exception as e:
                self.status_label.setText(f"Export failed: {e}")

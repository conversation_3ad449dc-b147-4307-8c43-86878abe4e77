"""
Multi-GPU Manager Widget for AreTomo3 GUI.
Provides interface for managing multiple GPU devices.
"""

import logging
from typing import Dict, List, Optional

from PyQt6.QtCore import Qt, pyqtSignal
from PyQt6.QtWidgets import (QCheckBox, QComboBox, QGridLayout, QGroupBox,
                            QLabel, QPushButton, QSpinBox, QWidget)

import torch

class MultiGPUManager(QWidget):
    """Widget for managing multiple GPU devices."""
    
    gpu_config_changed = pyqtSignal(dict)  # Emitted when GPU configuration changes
    
    def __init__(self, parent=None):
        """Initialize the multi-GPU manager widget."""
        super().__init__(parent)
        self.logger = logging.getLogger(__name__)
        self.available_gpus = self._detect_gpus()
        self.selected_gpus = []
        self._init_ui()
        
    def _detect_gpus(self) -> List[Dict[str, any]]:
        """Detect available GPU devices."""
        gpus = []
        if torch.cuda.is_available():
            for i in range(torch.cuda.device_count()):
                gpu_info = {
                    'index': i,
                    'name': torch.cuda.get_device_name(i),
                    'memory': torch.cuda.get_device_properties(i).total_memory,
                    'compute_capability': torch.cuda.get_device_capability(i)
                }
                gpus.append(gpu_info)
                self.logger.info(f"Detected GPU {i}: {gpu_info['name']}")
        return gpus
    
    def _init_ui(self):
        """Initialize the user interface."""
        layout = QGridLayout()
        self.setLayout(layout)
        
        # GPU Selection Group
        gpu_group = QGroupBox("Available GPUs")
        gpu_layout = QGridLayout()
        gpu_group.setLayout(gpu_layout)
        
        for i, gpu in enumerate(self.available_gpus):
            checkbox = QCheckBox(f"{gpu['name']}")
            checkbox.setToolTip(f"Memory: {gpu['memory']/1e9:.1f}GB")
            checkbox.stateChanged.connect(
                lambda state, idx=i: self._on_gpu_selected(idx, state)
            )
            gpu_layout.addWidget(checkbox, i // 2, i % 2)
        
        layout.addWidget(gpu_group, 0, 0, 1, 2)
        
        # Memory Management
        memory_group = QGroupBox("Memory Management")
        memory_layout = QGridLayout()
        memory_group.setLayout(memory_layout)
        
        memory_layout.addWidget(QLabel("Memory per GPU:"), 0, 0)
        self.memory_spin = QSpinBox()
        self.memory_spin.setRange(1024, 32768)  # 1GB to 32GB
        self.memory_spin.setValue(4096)  # Default 4GB
        self.memory_spin.setSuffix(" MB")
        self.memory_spin.valueChanged.connect(self._config_changed)
        memory_layout.addWidget(self.memory_spin, 0, 1)
        
        layout.addWidget(memory_group, 1, 0, 1, 2)
        
        # Apply Button
        self.apply_btn = QPushButton("Apply GPU Configuration")
        self.apply_btn.clicked.connect(self._apply_config)
        layout.addWidget(self.apply_btn, 2, 0, 1, 2)
    
    def _on_gpu_selected(self, idx: int, state: int):
        """Handle GPU selection changes."""
        if state == Qt.Checked:
            if idx not in self.selected_gpus:
                self.selected_gpus.append(idx)
        else:
            if idx in self.selected_gpus:
                self.selected_gpus.remove(idx)
        self._config_changed()
    
    def _config_changed(self):
        """Handle configuration changes."""
        config = {
            'selected_gpus': self.selected_gpus,
            'memory_per_gpu': self.memory_spin.value()
        }
        self.gpu_config_changed.emit(config)
        self.logger.debug(f"GPU configuration changed: {config}")
    
    def _apply_config(self):
        """Apply the current GPU configuration."""
        if not self.selected_gpus:
            self.logger.warning("No GPUs selected")
            return
        
        config = {
            'gpu_indices': self.selected_gpus,
            'memory_limit': self.memory_spin.value(),
            'gpu_info': [self.available_gpus[i] for i in self.selected_gpus]
        }
        
        try:
            # Set memory limits for selected GPUs
            for gpu_idx in self.selected_gpus:
                torch.cuda.set_per_process_memory_fraction(
                    self.memory_spin.value() / 
                    self.available_gpus[gpu_idx]['memory'],
                    gpu_idx
                )
            self.logger.info(f"Applied GPU configuration: {config}")
        except Exception as e:
            self.logger.error(f"Error applying GPU configuration: {e}")
    
    def get_config(self) -> Dict[str, any]:
        """Get current GPU configuration."""
        return {
            'selected_gpus': self.selected_gpus,
            'memory_per_gpu': self.memory_spin.value(),
            'available_gpus': self.available_gpus
        }

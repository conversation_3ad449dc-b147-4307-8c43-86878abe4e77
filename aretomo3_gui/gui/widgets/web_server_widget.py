#!/usr/bin/env python3
"""
Web server control widget for AreTomo3 GUI.
Provides control and monitoring of the web interface server.
"""

import io
import threading
import webbrowser
from datetime import datetime
from pathlib import Path
from typing import Optional

import qrcode
from PIL import Image
from PyQt6.QtCore import Qt, QThread, QTimer, pyqtSignal, pyqtSlot
from PyQt6.QtGui import QFont, QIcon, QPixmap
from PyQt6.QtNetwork import QNetworkAccessManager, QNetworkReply, QNetworkRequest
from PyQt6.QtWidgets import (
    QCheckBox,
    QFrame,
    QGridLayout,
    QGroupBox,
    QHBoxLayout,
    QLabel,
    QLineEdit,
    QPushButton,
    QSpinBox,
    QSplitter,
    QTabWidget,
    QTextEdit,
    QVBoxLayout,
    QWidget,
)

from ...core.realtime_processor import RealTimeProcessor
from ...web.api_server import AreTomo3WebAPI, create_web_server


class WebServerThread(QThread):
    """Thread for running the web server."""

    server_started = pyqtSignal(str, int)  # host, port
    server_stopped = pyqtSignal()
    server_error = pyqtSignal(str)  # error message

    def __init__(self, web_api: AreTomo3WebAPI, host: str, port: int):
        """Initialize the instance."""
        super().__init__()
        self.web_api = web_api
        self.host = host
        self.port = port
        self.running = False

    def run(self):
        """Run the web server in this thread."""
        try:
            self.running = True
            self.server_started.emit(self.host, self.port)
            self.web_api.run(host=self.host, port=self.port, debug=False)
        except Exception as e:
            self.server_error.emit(str(e))
        finally:
            self.running = False
            self.server_stopped.emit()

    def stop(self):
        """Stop the web server."""
        self.running = False
        self.quit()
        self.wait()


class QRCodeWidget(QLabel):
    """Widget to display QR code for easy mobile access."""

    def __init__(self):
        """Initialize the instance."""
        super().__init__()
        self.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.setStyleSheet(
            """
            QLabel {
                border: 2px solid #dee2e6;
                border-radius: 8px;
                background-color: white;
                padding: 10px;
            }
        """
        )
        self.setMinimumSize(200, 200)
        self.setMaximumSize(200, 200)
        self.setText("QR Code will appear here\nwhen server starts")

    def generate_qr_code(self, url: str):
        """Generate and display QR code for the given URL."""
        try:
            # Generate QR code
            qr = qrcode.QRCode(
                version=1,
                error_correction=qrcode.constants.ERROR_CORRECT_L,
                box_size=10,
                border=4,
            )
            qr.add_data(url)
            qr.make(fit=True)

            # Create QR code image
            qr_image = qr.make_image(fill_color="black", back_color="white")

            # Convert to QPixmap
            buffer = io.BytesIO()
            qr_image.save(buffer, format="PNG")
            buffer.seek(0)

            pixmap = QPixmap()
            pixmap.loadFromData(buffer.getvalue())

            # Scale to fit widget
            scaled_pixmap = pixmap.scaled(
                180,
                180,
                Qt.AspectRatioMode.KeepAspectRatio,
                Qt.TransformationMode.SmoothTransformation,
            )

            self.setPixmap(scaled_pixmap)

        except Exception as e:
            self.setText(f"Error generating\nQR code:\n{str(e)}")


class WebServerControlWidget(QWidget):
    """Widget for controlling the web server."""

    def __init__(
        self, processor: RealTimeProcessor, session_manager=None, continue_manager=None
    ):
        """Initialize the instance."""
        super().__init__()
        self.processor = processor
        self.session_manager = session_manager
        self.continue_manager = continue_manager
        self.web_api: Optional[AreTomo3WebAPI] = None
        self.server_thread: Optional[WebServerThread] = None
        self.is_server_running = False
        self.port = 8080  # Store port for external access

        self.setup_ui()
        self.setup_network_manager()

    @property
    def is_running(self) -> bool:
        """Check if the web server is currently running."""
        return self.is_server_running

    def setup_ui(self):
        """Setup the user interface."""
        layout = QVBoxLayout(self)

        # Server configuration
        config_group = QGroupBox("🌐 Web Server Configuration")
        config_layout = QGridLayout(config_group)

        # Host configuration
        config_layout.addWidget(QLabel("Host:"), 0, 0)
        self.host_input = QLineEdit("0.0.0.0")
        self.host_input.setToolTip("Server host address (0.0.0.0 for all interfaces)")
        config_layout.addWidget(self.host_input, 0, 1)

        # Port configuration
        config_layout.addWidget(QLabel("Port:"), 0, 2)
        self.port_input = QSpinBox()
        self.port_input.setRange(1024, 65535)
        self.port_input.setValue(8080)
        self.port_input.setToolTip("Server port number")
        config_layout.addWidget(self.port_input, 0, 3)

        # Auto-start option
        self.autostart_checkbox = QCheckBox("Auto-start with GUI")
        self.autostart_checkbox.setToolTip(
            "Automatically start web server when GUI starts"
        )
        config_layout.addWidget(self.autostart_checkbox, 1, 0, 1, 2)

        # CORS option
        self.cors_checkbox = QCheckBox("Enable CORS")
        self.cors_checkbox.setChecked(True)
        self.cors_checkbox.setToolTip("Enable Cross-Origin Resource Sharing")
        config_layout.addWidget(self.cors_checkbox, 1, 2, 1, 2)

        layout.addWidget(config_group)

        # Server control
        control_group = QGroupBox("🎮 Server Control")
        control_layout = QHBoxLayout(control_group)

        self.start_button = QPushButton("▶️ Start Server")
        self.start_button.clicked.connect(self.start_server)
        self.start_button.setStyleSheet(
            """
            QPushButton {
                background-color: #28a745;
                color: white;
                font-weight: bold;
                padding: 12px 24px;
                border-radius: 6px;
                font-size: 14px;
            }
            QPushButton:hover {
                background-color: #218838;
            }
        """
        )

        self.stop_button = QPushButton("⏹️ Stop Server")
        self.stop_button.clicked.connect(self.stop_server)
        self.stop_button.setEnabled(False)
        self.stop_button.setStyleSheet(
            """
            QPushButton {
                background-color: #dc3545;
                color: white;
                font-weight: bold;
                padding: 12px 24px;
                border-radius: 6px;
                font-size: 14px;
            }
            QPushButton:hover {
                background-color: #c82333;
            }
            QPushButton:disabled {
                background-color: #6c757d;
            }
        """
        )

        self.restart_button = QPushButton("🔄 Restart Server")
        self.restart_button.clicked.connect(self.restart_server)
        self.restart_button.setEnabled(False)

        control_layout.addWidget(self.start_button)
        control_layout.addWidget(self.stop_button)
        control_layout.addWidget(self.restart_button)
        control_layout.addStretch()

        layout.addWidget(control_group)

        # Server status and access
        status_splitter = QSplitter(Qt.Orientation.Horizontal)

        # Status panel
        status_group = QGroupBox("📊 Server Status")
        status_layout = QVBoxLayout(status_group)

        self.status_label = QLabel("🔴 Server Stopped")
        self.status_label.setStyleSheet(
            """
            QLabel {
                font-size: 16px;
                font-weight: bold;
                padding: 10px;
                border-radius: 6px;
                background-color: #f8d7da;
                color: #721c24;
            }
        """
        )
        status_layout.addWidget(self.status_label)

        # Server URLs
        self.url_label = QLabel("Server URL: Not running")
        self.url_label.setWordWrap(True)
        status_layout.addWidget(self.url_label)

        self.api_url_label = QLabel("API Docs: Not running")
        self.api_url_label.setWordWrap(True)
        status_layout.addWidget(self.api_url_label)

        # Access buttons
        access_layout = QHBoxLayout()

        self.open_browser_button = QPushButton("🌐 Open in Browser")
        self.open_browser_button.clicked.connect(self.open_in_browser)
        self.open_browser_button.setEnabled(False)

        self.open_api_docs_button = QPushButton("📚 API Documentation")
        self.open_api_docs_button.clicked.connect(self.open_api_docs)
        self.open_api_docs_button.setEnabled(False)

        access_layout.addWidget(self.open_browser_button)
        access_layout.addWidget(self.open_api_docs_button)

        status_layout.addLayout(access_layout)
        status_splitter.addWidget(status_group)

        # QR Code panel
        qr_group = QGroupBox("📱 Mobile Access")
        qr_layout = QVBoxLayout(qr_group)

        self.qr_widget = QRCodeWidget()
        qr_layout.addWidget(self.qr_widget)

        qr_info = QLabel("Scan QR code with mobile device\nfor easy access")
        qr_info.setAlignment(Qt.AlignmentFlag.AlignCenter)
        qr_info.setStyleSheet("color: #6c757d; font-size: 12px;")
        qr_layout.addWidget(qr_info)

        status_splitter.addWidget(qr_group)

        # Set splitter proportions
        status_splitter.setStretchFactor(0, 2)
        status_splitter.setStretchFactor(1, 1)

        layout.addWidget(status_splitter)

        # Server logs
        logs_group = QGroupBox("📝 Server Logs")
        logs_layout = QVBoxLayout(logs_group)

        self.log_text = QTextEdit()
        self.log_text.setReadOnly(True)
        self.log_text.setMaximumHeight(150)
        self.log_text.setStyleSheet(
            """
            QTextEdit {
                background-color: #2c3e50;
                color: #ecf0f1;
                font-family: 'Courier New', monospace;
                font-size: 10pt;
                border: 1px solid #34495e;
                border-radius: 4px;
            }
        """
        )
        logs_layout.addWidget(self.log_text)

        # Log controls
        log_controls = QHBoxLayout()

        clear_logs_button = QPushButton("🗑️ Clear Logs")
        clear_logs_button.clicked.connect(self.clear_logs)

        save_logs_button = QPushButton("💾 Save Logs")
        save_logs_button.clicked.connect(self.save_logs)

        log_controls.addWidget(clear_logs_button)
        log_controls.addWidget(save_logs_button)
        log_controls.addStretch()

        logs_layout.addLayout(log_controls)
        layout.addWidget(logs_group)

    def setup_network_manager(self):
        """Setup network manager for server health checks."""
        self.network_manager = QNetworkAccessManager()

        # Health check timer
        self.health_check_timer = QTimer()
        self.health_check_timer.timeout.connect(self.check_server_health)
        self.health_check_timer.start(5000)  # Check every 5 seconds

    def start_server(self):
        """Start the web server."""
        if self.is_server_running:
            self.add_log("Server is already running")
            return

        host = self.host_input.text().strip()
        port = self.port_input.value()
        self.port = port  # Store port for external access

        try:
            # Create web API
            self.web_api = create_web_server(self.processor)

            # Set session manager and continue mode manager if available
            if self.session_manager:
                self.web_api.session_manager = self.session_manager
            if self.continue_manager:
                self.web_api.continue_manager = self.continue_manager

            # Set main window reference for real-time data access
            if hasattr(self, "parent") and self.parent():
                main_window = self.parent()
                while main_window.parent():
                    main_window = main_window.parent()
                if hasattr(main_window, "realtime_analysis_tab"):
                    self.web_api.set_main_window(main_window)

            # Start server in thread
            self.server_thread = WebServerThread(self.web_api, host, port)
            self.server_thread.server_started.connect(self.on_server_started)
            self.server_thread.server_stopped.connect(self.on_server_stopped)
            self.server_thread.server_error.connect(self.on_server_error)
            self.server_thread.start()

            self.add_log(f"Starting web server on {host}:{port}...")

        except Exception as e:
            self.add_log(f"Error starting server: {e}")

    def stop_server(self):
        """Stop the web server."""
        if not self.is_server_running or not self.server_thread:
            return

        self.add_log("Stopping web server...")
        self.server_thread.stop()

    def restart_server(self):
        """Restart the web server."""
        if self.is_server_running:
            self.stop_server()
            # Wait a moment then restart
            QTimer.singleShot(2000, self.start_server)

    @pyqtSlot(str, int)
    def on_server_started(self, host: str, port: int):
        """Handle server started signal."""
        self.is_server_running = True

        # Update UI
        self.start_button.setEnabled(False)
        self.stop_button.setEnabled(True)
        self.restart_button.setEnabled(True)
        self.open_browser_button.setEnabled(True)
        self.open_api_docs_button.setEnabled(True)

        # Update status
        self.status_label.setText("🟢 Server Running")
        self.status_label.setStyleSheet(
            """
            QLabel {
                font-size: 16px;
                font-weight: bold;
                padding: 10px;
                border-radius: 6px;
                background-color: #d4edda;
                color: #155724;
            }
        """
        )

        # Update URLs
        server_url = f"http://{host}:{port}"
        api_url = f"{server_url}/api/docs"

        self.url_label.setText(f"Server URL: <a href='{server_url}'>{server_url}</a>")
        self.url_label.setOpenExternalLinks(True)

        self.api_url_label.setText(f"API Docs: <a href='{api_url}'>{api_url}</a>")
        self.api_url_label.setOpenExternalLinks(True)

        # Generate QR code
        self.qr_widget.generate_qr_code(server_url)

        self.add_log(f"✅ Web server started successfully on {server_url}")

    @pyqtSlot()
    def on_server_stopped(self):
        """Handle server stopped signal."""
        self.is_server_running = False

        # Update UI
        self.start_button.setEnabled(True)
        self.stop_button.setEnabled(False)
        self.restart_button.setEnabled(False)
        self.open_browser_button.setEnabled(False)
        self.open_api_docs_button.setEnabled(False)

        # Update status
        self.status_label.setText("🔴 Server Stopped")
        self.status_label.setStyleSheet(
            """
            QLabel {
                font-size: 16px;
                font-weight: bold;
                padding: 10px;
                border-radius: 6px;
                background-color: #f8d7da;
                color: #721c24;
            }
        """
        )

        # Clear URLs
        self.url_label.setText("Server URL: Not running")
        self.api_url_label.setText("API Docs: Not running")

        # Clear QR code
        self.qr_widget.setText("QR Code will appear here\nwhen server starts")

        self.add_log("🛑 Web server stopped")

    @pyqtSlot(str)
    def on_server_error(self, error_message: str):
        """Handle server error signal."""
        self.add_log(f"❌ Server error: {error_message}")
        self.on_server_stopped()  # Reset UI state

    def open_in_browser(self):
        """Open the web interface in default browser."""
        if self.is_server_running:
            host = self.host_input.text().strip()
            port = self.port_input.value()
            url = f"http://{host}:{port}"
            webbrowser.open(url)
            self.add_log(f"🌐 Opened {url} in browser")

    def open_api_docs(self):
        """Open API documentation in browser."""
        if self.is_server_running:
            host = self.host_input.text().strip()
            port = self.port_input.value()
            url = f"http://{host}:{port}/api/docs"
            webbrowser.open(url)
            self.add_log(f"📚 Opened API docs at {url}")

    def check_server_health(self):
        """Check if server is responding."""
        if not self.is_server_running:
            return

        host = self.host_input.text().strip()
        port = self.port_input.value()
        url = f"http://{host}:{port}/api/status"

        from PyQt6.QtCore import QUrl

        request = QNetworkRequest(QUrl(url))
        reply = self.network_manager.get(request)
        reply.finished.connect(lambda: self.handle_health_check(reply))

    def handle_health_check(self, reply: QNetworkReply):
        """Handle health check response."""
        if reply.error() != QNetworkReply.NetworkError.NoError:
            # Server might be down
            if self.is_server_running:
                self.add_log("⚠️ Server health check failed")

    def add_log(self, message: str):
        """Add a message to the log."""
        from datetime import datetime

        timestamp = datetime.now().strftime("%H:%M:%S")
        log_entry = f"[{timestamp}] {message}"
        self.log_text.append(log_entry)

        # Auto-scroll to bottom
        cursor = self.log_text.textCursor()
        cursor.movePosition(cursor.MoveOperation.End)
        self.log_text.setTextCursor(cursor)

    def clear_logs(self):
        """Clear the log display."""
        self.log_text.clear()
        self.add_log("Logs cleared")

    def save_logs(self):
        """Save logs to file."""

        from PyQt6.QtWidgets import QFileDialog

        filename, _ = QFileDialog.getSaveFileName(
            self,
            "Save Server Logs",
            f"aretomo3_server_logs_{datetime.now().strftime('%Y%m%d_%H%M%S')}.txt",
            "Text Files (*.txt);;All Files (*)",
        )

        if filename:
            try:
                with open(filename, "w") as f:
                    f.write(self.log_text.toPlainText())
                self.add_log(f"💾 Logs saved to {filename}")
            except Exception as e:
                self.add_log(f"❌ Error saving logs: {e}")

    def closeEvent(self, event):
        """Handle widget close event."""
        if self.is_server_running:
            self.stop_server()
        event.accept()

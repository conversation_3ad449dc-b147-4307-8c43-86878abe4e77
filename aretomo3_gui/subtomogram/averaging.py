"""
Subtomogram Averaging Integration
Complete STA pipeline with alignment and classification
"""

import numpy as np
from scipy import ndimage
from scipy.spatial.transform import Rotation
from typing import List, Tuple, Dict, Any, Optional
from dataclasses import dataclass
import matplotlib.pyplot as plt


@dataclass
class Subtomogram:
    """Individual subtomogram representation."""
    id: str
    data: np.ndarray
    position: Tuple[float, float, float]
    orientation: Tuple[float, float, float]  # Euler angles
    score: float
    class_id: int = 0
    metadata: Dict[str, Any] = None


class SubtomogramExtractor:
    """Extract subtomograms from tomograms."""
    
    def __init__(self, box_size: int = 64):
        self.box_size = box_size
        
    def extract_subtomograms(self, tomogram: np.ndarray, positions: List[Tuple[float, float, float]]) -> List[Subtomogram]:
        """Extract subtomograms at specified positions."""
        subtomograms = []
        half_box = self.box_size // 2
        
        for i, (x, y, z) in enumerate(positions):
            # Convert to integer coordinates
            ix, iy, iz = int(x), int(y), int(z)
            
            # Check bounds
            if (ix - half_box >= 0 and ix + half_box < tomogram.shape[2] and
                iy - half_box >= 0 and iy + half_box < tomogram.shape[1] and
                iz - half_box >= 0 and iz + half_box < tomogram.shape[0]):
                
                # Extract subtomogram
                subtomo_data = tomogram[
                    iz - half_box:iz + half_box,
                    iy - half_box:iy + half_box,
                    ix - half_box:ix + half_box
                ]
                
                if subtomo_data.shape == (self.box_size, self.box_size, self.box_size):
                    subtomo = Subtomogram(
                        id=f"subtomo_{i:06d}",
                        data=subtomo_data,
                        position=(x, y, z),
                        orientation=(0.0, 0.0, 0.0),
                        score=0.0
                    )
                    subtomograms.append(subtomo)
                    
        return subtomograms


class SubtomogramAligner:
    """Align subtomograms to reference."""
    
    def __init__(self):
        self.reference = None
        self.max_iterations = 10
        self.angular_step = 5.0  # degrees
        
    def set_reference(self, reference: np.ndarray):
        """Set reference for alignment."""
        self.reference = reference
        
    def align_subtomogram(self, subtomogram: Subtomogram) -> Subtomogram:
        """Align single subtomogram to reference."""
        if self.reference is None:
            raise ValueError("No reference set")
            
        best_score = -np.inf
        best_angles = (0.0, 0.0, 0.0)
        best_shift = (0.0, 0.0, 0.0)
        
        # Angular search
        angles_to_test = np.arange(0, 360, self.angular_step)
        
        for phi in angles_to_test[::3]:  # Coarse search
            for theta in angles_to_test[::3]:
                for psi in angles_to_test[::3]:
                    # Rotate subtomogram
                    rotated = self._rotate_volume(subtomogram.data, (phi, theta, psi))
                    
                    # Calculate cross-correlation
                    score = self._cross_correlation(self.reference, rotated)
                    
                    if score > best_score:
                        best_score = score
                        best_angles = (phi, theta, psi)
        
        # Update subtomogram
        aligned_subtomo = Subtomogram(
            id=subtomogram.id,
            data=self._rotate_volume(subtomogram.data, best_angles),
            position=subtomogram.position,
            orientation=best_angles,
            score=best_score,
            class_id=subtomogram.class_id,
            metadata=subtomogram.metadata
        )
        
        return aligned_subtomo
        
    def _rotate_volume(self, volume: np.ndarray, angles: Tuple[float, float, float]) -> np.ndarray:
        """Rotate 3D volume by Euler angles."""
        phi, theta, psi = angles
        
        # Create rotation matrix
        rotation = Rotation.from_euler('zyz', [phi, theta, psi], degrees=True)
        
        # Apply rotation
        center = np.array(volume.shape) / 2
        rotated = ndimage.rotate(volume, rotation.as_matrix(), reshape=False, order=1)
        
        return rotated
        
    def _cross_correlation(self, ref: np.ndarray, vol: np.ndarray) -> float:
        """Calculate normalized cross-correlation."""
        # Normalize volumes
        ref_norm = (ref - np.mean(ref)) / np.std(ref)
        vol_norm = (vol - np.mean(vol)) / np.std(vol)
        
        # Calculate correlation
        correlation = np.sum(ref_norm * vol_norm) / ref_norm.size
        return correlation


class SubtomogramClassifier:
    """Classify subtomograms using clustering."""
    
    def __init__(self, n_classes: int = 3):
        self.n_classes = n_classes
        self.class_averages = {}
        
    def classify_subtomograms(self, subtomograms: List[Subtomogram]) -> List[Subtomogram]:
        """Classify subtomograms into classes."""
        if not subtomograms:
            return subtomograms
            
        # Extract features (simplified - use mean, std, etc.)
        features = []
        for subtomo in subtomograms:
            feature_vector = [
                np.mean(subtomo.data),
                np.std(subtomo.data),
                np.min(subtomo.data),
                np.max(subtomo.data),
                subtomo.score
            ]
            features.append(feature_vector)
            
        features = np.array(features)
        
        # Simple k-means clustering
        from sklearn.cluster import KMeans
        
        kmeans = KMeans(n_clusters=self.n_classes, random_state=42)
        class_labels = kmeans.fit_predict(features)
        
        # Assign class labels
        for i, subtomo in enumerate(subtomograms):
            subtomo.class_id = int(class_labels[i])
            
        return subtomograms
        
    def compute_class_averages(self, subtomograms: List[Subtomogram]) -> Dict[int, np.ndarray]:
        """Compute average for each class."""
        class_averages = {}
        
        for class_id in range(self.n_classes):
            class_subtomos = [s for s in subtomograms if s.class_id == class_id]
            
            if class_subtomos:
                # Stack and average
                stack = np.stack([s.data for s in class_subtomos])
                average = np.mean(stack, axis=0)
                class_averages[class_id] = average
                
        self.class_averages = class_averages
        return class_averages


class SubtomogramAveraging:
    """Main subtomogram averaging coordinator."""
    
    def __init__(self, box_size: int = 64):
        self.extractor = SubtomogramExtractor(box_size)
        self.aligner = SubtomogramAligner()
        self.classifier = SubtomogramClassifier()
        self.subtomograms = []
        
    def run_sta_pipeline(self, tomogram: np.ndarray, positions: List[Tuple[float, float, float]], 
                        reference: Optional[np.ndarray] = None) -> Dict[str, Any]:
        """Run complete STA pipeline."""
        results = {}
        
        # Step 1: Extract subtomograms
        print("Extracting subtomograms...")
        self.subtomograms = self.extractor.extract_subtomograms(tomogram, positions)
        results['n_extracted'] = len(self.subtomograms)
        
        if not self.subtomograms:
            return results
            
        # Step 2: Initial reference
        if reference is None:
            # Use average of all subtomograms as initial reference
            stack = np.stack([s.data for s in self.subtomograms])
            reference = np.mean(stack, axis=0)
            
        self.aligner.set_reference(reference)
        
        # Step 3: Iterative alignment and averaging
        print("Aligning subtomograms...")
        for iteration in range(3):  # Simplified - fewer iterations
            # Align all subtomograms
            aligned_subtomos = []
            for subtomo in self.subtomograms:
                aligned = self.aligner.align_subtomogram(subtomo)
                aligned_subtomos.append(aligned)
                
            self.subtomograms = aligned_subtomos
            
            # Update reference
            stack = np.stack([s.data for s in self.subtomograms])
            new_reference = np.mean(stack, axis=0)
            self.aligner.set_reference(new_reference)
            
        # Step 4: Classification
        print("Classifying subtomograms...")
        self.subtomograms = self.classifier.classify_subtomograms(self.subtomograms)
        
        # Step 5: Compute class averages
        class_averages = self.classifier.compute_class_averages(self.subtomograms)
        
        results.update({
            'subtomograms': self.subtomograms,
            'class_averages': class_averages,
            'final_reference': new_reference,
            'n_classes': len(class_averages)
        })
        
        return results
        
    def visualize_results(self, results: Dict[str, Any]):
        """Visualize STA results."""
        class_averages = results.get('class_averages', {})
        
        if not class_averages:
            return
            
        n_classes = len(class_averages)
        fig, axes = plt.subplots(1, n_classes, figsize=(4*n_classes, 4))
        
        if n_classes == 1:
            axes = [axes]
            
        for i, (class_id, average) in enumerate(class_averages.items()):
            # Show central slice
            central_slice = average[average.shape[0]//2, :, :]
            axes[i].imshow(central_slice, cmap='gray')
            axes[i].set_title(f'Class {class_id}')
            axes[i].axis('off')
            
        plt.tight_layout()
        return fig


# Alias for compatibility
SubtomogramAverager = SubtomogramAveraging

# Global STA instance
sta_processor = SubtomogramAveraging()

#!/usr/bin/env python3
"""
api_server.py - AreTomo3 GUI Professional Edition

Part of the AreTomo3 GUI suite for tomographic reconstruction.
This file implements secure web API server functionality.

Copyright (c) 2025 AreTomo3 GUI Development Team
Licensed under the MIT License
"""

"""
FastAPI web server for AreTomo3 GUI.
Provides REST API and WebSocket endpoints for remote access.
"""

import asyncio
import hashlib
import json
import logging
import random
import secrets
import uuid
from datetime import datetime, timedelta
from pathlib import Path
from typing import Any, Dict, List, Optional

logger = logging.getLogger(__name__)

# Optional JWT import
try:
    import jwt

    JWT_AVAILABLE = True
except ImportError:
    JWT_AVAILABLE = False
    logger.warning("PyJWT not available - JWT authentication disabled")

import uvicorn
from fastapi import (
    BackgroundTasks,
    Depends,
    FastAPI,
    HTTPException,
    Request,
    WebSocket,
    WebSocketDisconnect,
    status,
)
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import FileResponse, HTMLResponse, JSONResponse
from fastapi.staticfiles import StaticFiles
from pydantic import BaseModel, Field

# Optional security imports
try:
    from fastapi.middleware.trustedhost import TrustedHostMiddleware
    from fastapi.security import HTTPAuthorizationCredentials, HTTPBearer

    SECURITY_AVAILABLE = True
except ImportError:
    SECURITY_AVAILABLE = False
    logger.warning("Advanced security features not available")

from ..core.continue_mode_manager import ContinueModeManager
from ..core.realtime_processor import ProcessingJob, ProcessingStats, RealTimeProcessor
from ..core.results_tracker import ProcessingResult, ResultsTracker
from ..core.session_manager import SessionManager

# Pydantic models for API


class ProcessingRequest(BaseModel):
    """Request model for starting processing."""

    input_files: List[str] = Field(..., description="List of input file paths")
    output_directory: str = Field(..., description="Output directory path")
    parameters: Dict[str, Any] = Field(
        default_factory=dict, description="Processing parameters"
    )
    priority: int = Field(default=1, ge=1, le=10, description="Job priority (1-10)")


class JobResponse(BaseModel):
    """Response model for job information."""

    job_id: str
    file_path: str
    status: str
    progress: float
    created_at: datetime
    processing_time: Optional[float] = None
    result_path: Optional[str] = None
    error_message: Optional[str] = None


class StatsResponse(BaseModel):
    """Response model for processing statistics."""

    total_files: int
    processed_files: int
    failed_files: int
    avg_processing_time: float
    throughput_per_hour: float
    queue_size: int
    active_jobs: int


class SystemStatus(BaseModel):
    """System status information."""

    status: str  # running, stopped, error
    uptime: float
    version: str
    cpu_usage: float
    memory_usage: float
    disk_usage: float


class WebSocketManager:
    """Manages WebSocket connections for real-time updates."""

    def __init__(self):
        """Initialize the instance."""
        self.active_connections: List[WebSocket] = []

    async def connect(self, websocket: WebSocket):
        """Accept a new WebSocket connection."""
        await websocket.accept()
        self.active_connections.append(websocket)
        logger.info(
            f"WebSocket connected. Total connections: {len(self.active_connections)}"
        )

    def disconnect(self, websocket: WebSocket):
        """Remove a WebSocket connection."""
        if websocket in self.active_connections:
            self.active_connections.remove(websocket)
        logger.info(
            f"WebSocket disconnected. Total connections: {len(self.active_connections)}"
        )

    async def send_personal_message(self, message: str, websocket: WebSocket):
        """Send a message to a specific WebSocket."""
        try:
            await websocket.send_text(message)
        except Exception as e:
            logger.error(f"Error sending WebSocket message: {e}")
            self.disconnect(websocket)

    async def broadcast(self, message: str):
        """Broadcast a message to all connected WebSockets."""
        disconnected = []
        for connection in self.active_connections:
            try:
                await connection.send_text(message)
            except Exception as e:
                logger.error(f"Error broadcasting to WebSocket: {e}")
                disconnected.append(connection)

        # Remove disconnected connections
        for connection in disconnected:
            self.disconnect(connection)


class AreTomo3WebAPI:
    """Main web API class for AreTomo3."""

    def __init__(self, processor: RealTimeProcessor):
        """Initialize the instance."""
        self.processor = processor
        self.results_tracker = ResultsTracker()
        self.websocket_manager = WebSocketManager()
        self.session_manager = SessionManager()
        self.continue_manager = ContinueModeManager()

        # Security configuration
        self.jwt_secret = secrets.token_urlsafe(32) if JWT_AVAILABLE else None
        self.api_keys = set()
        self.rate_limits = {}
        self.security = HTTPBearer() if SECURITY_AVAILABLE else None

        # Generate initial API key
        initial_api_key = f"at3gui_{secrets.token_urlsafe(32)}"
        self.api_keys.add(initial_api_key)
        logger.info(f"Generated initial API key: {initial_api_key}")

        if not JWT_AVAILABLE:
            logger.warning("JWT authentication disabled - PyJWT not available")
        if not SECURITY_AVAILABLE:
            logger.warning("Advanced security features disabled")

        # HTTPS configuration
        self.enforce_https = False
        self.ssl_cert_path = None
        self.ssl_key_path = None

        # User management
        self.users = {}  # user_id -> user_info
        self.user_sessions = {}  # session_id -> user_id
        self.default_permissions = ["read", "process"]

        # Create default admin user
        self._create_default_admin()

        self.app = self.create_app()
        self.setup_routes()
        self.connect_processor_signals()

        # Store reference to main window for real-time data access
        self.main_window = None
        self.realtime_analysis_tab = None

        # Log storage for web interface
        self.log_entries = []
        self.max_log_entries = 1000  # Keep last 1000 log entries

        # Start session auto-save
        self.session_manager.start_auto_save()

    def _create_default_admin(self):
        """Create default admin user."""
        admin_password = secrets.token_urlsafe(16)
        admin_user = {
            "user_id": "admin",
            "username": "admin",
            "password_hash": hashlib.sha256(admin_password.encode()).hexdigest(),
            "permissions": ["read", "write", "admin", "process"],
            "created_at": datetime.now().isoformat(),
            "active": True,
        }
        self.users["admin"] = admin_user
        logger.info(f"Default admin user created with password: {admin_password}")

    def create_user(
        self, username: str, password: str, permissions: List[str] = None
    ) -> str:
        """Create a new user."""
        user_id = f"user_{secrets.token_urlsafe(8)}"
        password_hash = hashlib.sha256(password.encode()).hexdigest()

        user_info = {
            "user_id": user_id,
            "username": username,
            "password_hash": password_hash,
            "permissions": permissions or self.default_permissions,
            "created_at": datetime.now().isoformat(),
            "active": True,
        }

        self.users[user_id] = user_info
        logger.info(f"Created user: {username} ({user_id})")
        return user_id

    def authenticate_user(
        self, username: str, password: str
    ) -> Optional[Dict[str, Any]]:
        """Authenticate user with username/password."""
        password_hash = hashlib.sha256(password.encode()).hexdigest()

        for user_info in self.users.values():
            if (
                user_info["username"] == username
                and user_info["password_hash"] == password_hash
                and user_info["active"]
            ):
                return user_info

        logger.warning(f"Failed authentication attempt for user: {username}")
        return None

    def check_permission(self, user_id: str, required_permission: str) -> bool:
        """Check if user has required permission."""
        if user_id not in self.users:
            return False

        user_permissions = self.users[user_id].get("permissions", [])
        return required_permission in user_permissions or "admin" in user_permissions

    def generate_jwt_token(self, user_id: str, permissions: List[str] = None) -> str:
        """Generate a JWT token."""
        if not JWT_AVAILABLE or not self.jwt_secret:
            logger.warning("JWT not available - returning API key instead")
            return f"at3gui_{secrets.token_urlsafe(16)}"

        payload = {
            "user_id": user_id,
            "permissions": permissions or ["read"],
            "iat": datetime.utcnow(),
            "exp": datetime.utcnow() + timedelta(hours=24),
        }
        return jwt.encode(payload, self.jwt_secret, algorithm="HS256")

    def validate_jwt_token(self, token: str) -> Optional[Dict[str, Any]]:
        """Validate a JWT token."""
        if not JWT_AVAILABLE or not self.jwt_secret:
            return None

        try:
            payload = jwt.decode(token, self.jwt_secret, algorithms=["HS256"])
            return payload
        except jwt.ExpiredSignatureError:
            logger.warning("JWT token expired")
            return None
        except jwt.InvalidTokenError:
            logger.warning("Invalid JWT token")
            return None

    def validate_api_key(self, api_key: str) -> bool:
        """Validate an API key."""
        return api_key in self.api_keys

    def generate_api_key(self, user_id: str = "default") -> str:
        """Generate a new API key."""
        api_key = f"at3gui_{user_id}_{secrets.token_urlsafe(32)}"
        self.api_keys.add(api_key)
        logger.info(f"Generated new API key for user: {user_id}")
        return api_key

    def revoke_api_key(self, api_key: str) -> bool:
        """Revoke an API key."""
        if api_key in self.api_keys:
            self.api_keys.remove(api_key)
            logger.info(f"Revoked API key: {api_key[:20]}...")
            return True
        return False

    def list_api_keys(self) -> List[str]:
        """List all API keys (masked for security)."""
        return [f"{key[:20]}..." for key in self.api_keys]

    def rotate_api_keys(self) -> Dict[str, str]:
        """Rotate all API keys."""
        old_keys = list(self.api_keys)
        new_keys = {}

        self.api_keys.clear()

        for old_key in old_keys:
            # Extract user from old key if possible
            parts = old_key.split("_")
            user_id = parts[1] if len(parts) > 2 else "default"
            new_key = self.generate_api_key(user_id)
            new_keys[old_key[:20] + "..."] = new_key[:20] + "..."

        logger.info(f"Rotated {len(old_keys)} API keys")
        return new_keys

    def configure_https(self, cert_path: str, key_path: str, enforce: bool = True):
        """Configure HTTPS settings."""
        self.ssl_cert_path = cert_path
        self.ssl_key_path = key_path
        self.enforce_https = enforce
        logger.info(f"HTTPS configured - Enforcement: {enforce}")

    def generate_self_signed_cert(
        self, cert_path: str, key_path: str, hostname: str = "localhost"
    ) -> bool:
        """Generate self-signed certificate for development."""
        try:
            import datetime

            from cryptography import x509
            from cryptography.hazmat.primitives import hashes, serialization
            from cryptography.hazmat.primitives.asymmetric import rsa
            from cryptography.x509.oid import NameOID

            # Generate private key
            private_key = rsa.generate_private_key(
                public_exponent=65537,
                key_size=2048,
            )

            # Create certificate
            subject = issuer = x509.Name(
                [
                    x509.NameAttribute(NameOID.COUNTRY_NAME, "US"),
                    x509.NameAttribute(NameOID.STATE_OR_PROVINCE_NAME, "Development"),
                    x509.NameAttribute(NameOID.LOCALITY_NAME, "Local"),
                    x509.NameAttribute(NameOID.ORGANIZATION_NAME, "AreTomo3 GUI"),
                    x509.NameAttribute(NameOID.COMMON_NAME, hostname),
                ]
            )

            cert = (
                x509.CertificateBuilder()
                .subject_name(subject)
                .issuer_name(issuer)
                .public_key(private_key.public_key())
                .serial_number(x509.random_serial_number())
                .not_valid_before(datetime.datetime.utcnow())
                .not_valid_after(
                    datetime.datetime.utcnow() + datetime.timedelta(days=365)
                )
                .add_extension(
                    x509.SubjectAlternativeName(
                        [
                            x509.DNSName(hostname),
                            x509.DNSName("localhost"),
                            x509.IPAddress("127.0.0.1"),
                        ]
                    ),
                    critical=False,
                )
                .sign(private_key, hashes.SHA256())
            )

            # Write certificate
            with open(cert_path, "wb") as f:
                f.write(cert.public_bytes(serialization.Encoding.PEM))

            # Write private key
            with open(key_path, "wb") as f:
                f.write(
                    private_key.private_bytes(
                        encoding=serialization.Encoding.PEM,
                        format=serialization.PrivateFormat.PKCS8,
                        encryption_algorithm=serialization.NoEncryption(),
                    )
                )

            logger.info(f"Self-signed certificate generated: {cert_path}")
            return True

        except ImportError:
            logger.warning(
                "cryptography library not available for certificate generation"
            )
            return False
        except Exception as e:
            logger.error(f"Error generating certificate: {e}")
            return False

    def check_rate_limit(self, client_ip: str) -> bool:
        """Check if client is within rate limits."""
        now = datetime.now()
        if client_ip not in self.rate_limits:
            self.rate_limits[client_ip] = []

        # Clean old requests (older than 15 minutes)
        self.rate_limits[client_ip] = [
            req_time
            for req_time in self.rate_limits[client_ip]
            if now - req_time < timedelta(minutes=15)
        ]

        # Check if under limit (100 requests per 15 minutes)
        if len(self.rate_limits[client_ip]) >= 100:
            return False

        # Add current request
        self.rate_limits[client_ip].append(now)
        return True

    async def verify_token(self, credentials=None):
        """Verify JWT token or API key."""
        if not SECURITY_AVAILABLE:
            # If security not available, allow access
            return {"type": "no_auth", "message": "Security features disabled"}

        if not credentials:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Authentication required",
            )

        token = credentials.credentials

        # Try API key first
        if self.validate_api_key(token):
            return {"type": "api_key", "token": token}

        # Try JWT token
        payload = self.validate_jwt_token(token)
        if payload:
            return {"type": "jwt", "payload": payload}

        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Invalid authentication credentials",
            headers={"WWW-Authenticate": "Bearer"},
        )

    def set_main_window(self, main_window):
        """Set reference to main window for accessing real-time data."""
        self.main_window = main_window
        if hasattr(main_window, "realtime_analysis_tab"):
            self.realtime_analysis_tab = main_window.realtime_analysis_tab

        # Set up log capture from the main window
        self.setup_log_capture(main_window)

    def setup_log_capture(self, main_window):
        """Set up log capture from the main window."""
        try:
            # Connect to the main window's log_message method if available
            if hasattr(main_window, "log_message"):
                # Store original log_message method
                original_log_message = main_window.log_message

                # Create wrapper that captures logs
                def log_message_wrapper(message, level="INFO"):
                    """Execute log_message_wrapper operation."""
                    # Call original method
                    original_log_message(message, level)
                    # Capture for web interface
                    self.add_log_entry(message, level)

                # Replace the method
                main_window.log_message = log_message_wrapper
                logger.info("Log capture set up successfully")

            # Also set up Python logging handler to capture all logs
            self.setup_python_log_handler()

        except Exception as e:
            logger.error(f"Error setting up log capture: {e}")

    def setup_python_log_handler(self):
        """Set up Python logging handler to capture all log messages."""
        try:

            class WebLogHandler(logging.Handler):
                """Class WebLogHandler implementation."""

                def __init__(self, web_api):
                    """Initialize the instance."""
                    super().__init__()
                    self.web_api = web_api

                def emit(self, record):
                    """Execute emit operation."""
                    try:
                        message = self.format(record)
                        level = record.levelname
                        self.web_api.add_log_entry(message, level)
                    except Exception:
                        pass  # Avoid recursive logging errors

            # Create and add the handler
            web_handler = WebLogHandler(self)
            web_handler.setLevel(logging.INFO)

            # Add to root logger
            root_logger = logging.getLogger()
            root_logger.addHandler(web_handler)

            logger.info("Python log handler set up successfully")

        except Exception as e:
            logger.error(f"Error setting up Python log handler: {e}")

    def add_log_entry(self, message: str, level: str = "INFO"):
        """Add a log entry for web interface display."""

        log_entry = {
            "timestamp": datetime.datetime.now().isoformat(),
            "level": level.upper(),
            "message": message,
        }

        self.log_entries.append(log_entry)

        # Keep only the last max_log_entries
        if len(self.log_entries) > self.max_log_entries:
            self.log_entries = self.log_entries[-self.max_log_entries :]

        # Broadcast to WebSocket clients
        try:
            asyncio.create_task(self.broadcast_log_entry(log_entry))
        except Exception as e:
            # Avoid recursive logging errors
            pass

    async def broadcast_log_entry(self, log_entry: dict):
        """Broadcast a log entry to all WebSocket clients."""
        try:
            message = json.dumps({"type": "log_entry", "log": log_entry})
            await self.websocket_manager.broadcast(message)
        except Exception as e:
            # Avoid recursive logging errors
            pass

    def create_app(self) -> FastAPI:
        """Create and configure FastAPI application."""
        app = FastAPI(
            title="AreTomo3 GUI API",
            description="REST API for AreTomo3 tomographic reconstruction",
            version="2.0.0",
            docs_url="/api/docs",
            redoc_url="/api/redoc",
        )

        # Add CORS middleware with secure configuration
        allowed_origins = [
            "http://localhost:3000",
            "http://127.0.0.1:3000",
            "https://localhost:8080",
            "https://127.0.0.1:8080",
        ]

        app.add_middleware(
            CORSMiddleware,
            allow_origins=allowed_origins,  # Restricted origins for security
            allow_credentials=True,
            allow_methods=["GET", "POST", "PUT", "DELETE"],  # Specific methods only
            allow_headers=[
                "Authorization",
                "Content-Type",
                "X-API-Key",
            ],  # Specific headers only
            max_age=3600,  # Cache preflight requests for 1 hour
        )

        # Add trusted host middleware if available
        if SECURITY_AVAILABLE:
            app.add_middleware(
                TrustedHostMiddleware,
                allowed_hosts=["localhost", "127.0.0.1", "*.localhost"],
            )

        # Add security middleware
        @app.middleware("http")
        async def security_middleware(request: Request, call_next):
            # Get client IP
            client_ip = request.client.host

            # HTTPS enforcement for production
            if hasattr(self, "enforce_https") and self.enforce_https:
                if request.url.scheme != "https":
                    # Redirect to HTTPS
                    https_url = request.url.replace(scheme="https")
                    return JSONResponse(
                        status_code=status.HTTP_301_MOVED_PERMANENTLY,
                        headers={"Location": str(https_url)},
                    )

            # Check rate limiting for API endpoints
            if request.url.path.startswith("/api/"):
                if not self.check_rate_limit(client_ip):
                    return JSONResponse(
                        status_code=status.HTTP_429_TOO_MANY_REQUESTS,
                        content={"detail": "Rate limit exceeded"},
                    )

            response = await call_next(request)

            # Add security headers
            response.headers["X-Content-Type-Options"] = "nosniff"
            response.headers["X-Frame-Options"] = "DENY"
            response.headers["X-XSS-Protection"] = "1; mode=block"
            response.headers["Referrer-Policy"] = "strict-origin-when-cross-origin"

            # HTTPS security headers
            if request.url.scheme == "https":
                response.headers["Strict-Transport-Security"] = (
                    "max-age=31536000; includeSubDomains"
                )
                response.headers["Content-Security-Policy"] = (
                    "default-src 'self'; script-src 'self' 'unsafe-inline'; style-src 'self' 'unsafe-inline'"
                )

            return response

        return app

    def _validate_file_path(self, path: str) -> bool:
        """Validate file path for security."""
        try:
            # Import path validation from utils
            from ..utils.file_utils import validate_safe_path

            # Basic validation
            if not path or not isinstance(path, str):
                return False

            # Check for path traversal attempts
            if ".." in path or path.startswith("/"):
                logger.warning(f"Path traversal attempt: {path}")
                return False

            # Use the utility function for comprehensive validation
            return validate_safe_path(path)

        except Exception as e:
            logger.error(f"Error validating file path {path}: {e}")
            return False

    def setup_routes(self):
        """Setup API routes."""

        @self.app.get("/", response_class=HTMLResponse)
        async def root():
            """Serve the modern analysis dashboard."""
            return self.get_analysis_dashboard()

        @self.app.get("/dashboard", response_class=HTMLResponse)
        async def dashboard():
            """Serve the modern analysis dashboard."""
            return self.get_analysis_dashboard()

        @self.app.get("/legacy", response_class=HTMLResponse)
        async def legacy_dashboard():
            """Serve the legacy enhanced web interface."""
            return self.get_enhanced_web_interface()

        @self.app.websocket("/ws")
        async def websocket_endpoint(websocket: WebSocket):
            """WebSocket endpoint for real-time updates."""
            await self.websocket_manager.connect(websocket)
            try:
                # Send recent logs on connection
                recent_logs = (
                    self.log_entries[-50:]
                    if len(self.log_entries) > 50
                    else self.log_entries
                )
                if recent_logs:
                    await websocket.send_text(
                        json.dumps({"type": "initial_logs", "logs": recent_logs})
                    )

                while True:
                    # Keep connection alive
                    await websocket.receive_text()
            except WebSocketDisconnect:
                self.websocket_manager.disconnect(websocket)

        @self.app.websocket("/ws/logs")
        async def logs_websocket_endpoint(websocket: WebSocket):
            """WebSocket endpoint specifically for real-time log streaming."""
            await self.websocket_manager.connect(websocket)
            try:
                # Send recent logs on connection
                recent_logs = (
                    self.log_entries[-100:]
                    if len(self.log_entries) > 100
                    else self.log_entries
                )
                await websocket.send_text(
                    json.dumps({"type": "initial_logs", "logs": recent_logs})
                )

                while True:
                    # Keep connection alive and handle any incoming messages
                    message = await websocket.receive_text()
                    # Could handle log filtering requests here

            except WebSocketDisconnect:
                self.websocket_manager.disconnect(websocket)

        @self.app.get("/api/status", response_model=SystemStatus)
        async def get_system_status():
            """Get system status."""
            try:
                import psutil

                # Handle case where processor might be None
                processor_status = "stopped"
                if self.processor and hasattr(self.processor, "is_running"):
                    processor_status = (
                        "running" if self.processor.is_running else "stopped"
                    )
                elif self.processor is None:
                    processor_status = "disabled"

                return SystemStatus(
                    status=processor_status,
                    uptime=0.0,  # Calculate actual uptime
                    version="2.0.0",
                    cpu_usage=psutil.cpu_percent(),
                    memory_usage=psutil.virtual_memory().percent,
                    disk_usage=psutil.disk_usage("/").percent,
                )
            except Exception as e:
                logger.error(f"Error getting system status: {e}")
                # Return a basic status even if there are errors
                return SystemStatus(
                    status="error",
                    uptime=0.0,
                    version="2.0.0",
                    cpu_usage=0.0,
                    memory_usage=0.0,
                    disk_usage=0.0,
                )

        @self.app.get("/api/stats", response_model=StatsResponse)
        async def get_processing_stats():
            """Get processing statistics."""
            try:
                if self.processor and hasattr(self.processor, "get_stats"):
                    stats = self.processor.get_stats()
                    return StatsResponse(
                        total_files=stats.total_files,
                        processed_files=stats.processed_files,
                        failed_files=stats.failed_files,
                        avg_processing_time=stats.avg_processing_time,
                        throughput_per_hour=stats.throughput_per_hour,
                        queue_size=stats.queue_size,
                        active_jobs=stats.active_jobs,
                    )
                else:
                    # Return default stats when processor is not available
                    return StatsResponse(
                        total_files=0,
                        processed_files=0,
                        failed_files=0,
                        avg_processing_time=0.0,
                        throughput_per_hour=0.0,
                        queue_size=0,
                        active_jobs=0,
                    )
            except Exception as e:
                logger.error(f"Error getting processing stats: {e}")
                # Return default stats on error
                return StatsResponse(
                    total_files=0,
                    processed_files=0,
                    failed_files=0,
                    avg_processing_time=0.0,
                    throughput_per_hour=0.0,
                    queue_size=0,
                    active_jobs=0,
                )

        @self.app.get("/api/logs")
        async def get_logs(limit: int = 100):
            """Get recent log entries."""
            recent_logs = self.log_entries[-limit:] if limit > 0 else self.log_entries
            return {"logs": recent_logs}

        @self.app.post("/api/auth/generate-key")
        async def generate_new_api_key(user_id: str = "default"):
            """Generate a new API key."""
            new_key = self.generate_api_key(user_id)
            return {"api_key": new_key, "user_id": user_id}

        @self.app.post("/api/auth/revoke-key")
        async def revoke_api_key_endpoint(api_key: str):
            """Revoke an API key."""
            success = self.revoke_api_key(api_key)
            return {
                "success": success,
                "message": "API key revoked" if success else "API key not found",
            }

        @self.app.get("/api/auth/list-keys")
        async def list_api_keys_endpoint():
            """List all API keys (masked)."""
            keys = self.list_api_keys()
            return {"api_keys": keys, "count": len(keys)}

        @self.app.post("/api/auth/rotate-keys")
        async def rotate_api_keys_endpoint():
            """Rotate all API keys."""
            rotation_map = self.rotate_api_keys()
            return {
                "rotated_keys": rotation_map,
                "message": "All API keys rotated successfully",
            }

        @self.app.get("/api/session/current")
        async def get_current_session():
            """Get current session information."""
            if hasattr(self, "session_manager") and self.session_manager:
                return self.session_manager.get_session_for_web()
            return {"session_active": False, "message": "No session manager available"}

        @self.app.get("/api/session/list")
        async def get_all_sessions():
            """Get list of all sessions."""
            if hasattr(self, "session_manager") and self.session_manager:
                return self.session_manager.get_all_sessions_for_web()
            return {"sessions": []}

        @self.app.post("/api/session/create")
        async def create_session(
            session_name: str, processing_mode: str, input_dir: str, output_dir: str
        ):
            """Create a new session."""
            if hasattr(self, "session_manager") and self.session_manager:
                session = self.session_manager.create_new_session(
                    session_name, processing_mode, input_dir, output_dir
                )
                return {"success": True, "session_id": session.session_id}
            return {"success": False, "message": "No session manager available"}

        @self.app.get("/api/continue/sessions")
        async def get_continue_sessions():
            """Get all continue mode sessions."""
            if hasattr(self, "continue_manager") and self.continue_manager:
                return {"sessions": self.continue_manager.get_all_sessions()}
            return {"sessions": []}

        @self.app.post("/api/continue/pause/{session_id}")
        async def pause_continue_session(session_id: str):
            """Pause a continue mode session."""
            if hasattr(self, "continue_manager") and self.continue_manager:
                success = self.continue_manager.pause_processing(session_id)
                return {"success": success}
            return {"success": False, "message": "No continue manager available"}

        @self.app.post("/api/continue/resume/{session_id}")
        async def resume_continue_session(session_id: str):
            """Resume a continue mode session."""
            if hasattr(self, "continue_manager") and self.continue_manager:
                success = self.continue_manager.resume_processing(session_id)
                return {"success": success}
            return {"success": False, "message": "No continue manager available"}

        @self.app.get("/api/analysis/data")
        async def get_analysis_data():
            """Get real-time analysis data from the GUI."""
            try:
                if self.realtime_analysis_tab:
                    # Get parsed data from the real-time analysis tab
                    parsed_data = getattr(self.realtime_analysis_tab, "parsed_data", {})

                    # Extract quality metrics
                    quality_metrics = {}
                    if (
                        hasattr(self.realtime_analysis_tab, "parser")
                        and self.realtime_analysis_tab.parser
                    ):
                        quality_metrics = (
                            self.realtime_analysis_tab.parser.extract_quality_metrics()
                        )

                    return {
                        "success": True,
                        "data": parsed_data,
                        "quality_metrics": quality_metrics,
                        "last_updated": datetime.now().isoformat(),
                    }
                else:
                    return {
                        "success": False,
                        "message": "Real-time analysis tab not available",
                        "data": {},
                        "quality_metrics": {},
                    }
            except Exception as e:
                logger.error(f"Error getting analysis data: {e}")
                return {
                    "success": False,
                    "message": f"Error: {e}",
                    "data": {},
                    "quality_metrics": {},
                }

        @self.app.get("/api/plots/list")
        async def get_available_plots():
            """Get list of available analysis plots."""
            try:
                if self.realtime_analysis_tab:
                    # Check for generated plots
                    plots = []
                    if hasattr(self.realtime_analysis_tab, "plot_files"):
                        plots = list(self.realtime_analysis_tab.plot_files.keys())

                    return {
                        "success": True,
                        "plots": plots,
                        "last_updated": datetime.now().isoformat(),
                    }
                else:
                    return {"success": False, "plots": []}
            except Exception as e:
                logger.error(f"Error getting plots list: {e}")
                return {"success": False, "plots": []}

        @self.app.get("/api/jobs/{job_id}", response_model=JobResponse)
        async def get_job(job_id: str):
            """Get specific job information."""
            job = self.processor.get_job_status(job_id)
            if not job:
                raise HTTPException(status_code=404, detail="Job not found")

            return JobResponse(
                job_id=job.job_id,
                file_path=str(job.file_path),
                status=job.status,
                progress=job.progress,
                created_at=job.created_at,
                processing_time=job.processing_time,
                result_path=str(job.result_path) if job.result_path else None,
                error_message=job.error_message,
            )

        @self.app.post("/api/processing/start")
        async def start_processing():
            """Start real-time processing."""
            if self.processor.is_running:
                return {"message": "Processing already running"}

            # Start processing in background
            asyncio.create_task(self.processor.start_processing())
            return {"message": "Processing started"}

        @self.app.post("/api/processing/stop")
        async def stop_processing():
            """Stop real-time processing."""
            if not self.processor.is_running:
                return {"message": "Processing not running"}

            await self.processor.stop_processing()
            return {"message": "Processing stopped"}

        @self.app.get("/api/jobs")
        async def get_all_jobs():
            """Get all processing jobs with enhanced details."""
            jobs = self.processor.get_all_jobs()
            enhanced_jobs = []

            for job in jobs:
                # Get detailed job information
                job_details = {
                    "job_id": job.job_id,
                    "file_path": str(job.file_path),
                    "file_name": (
                        job.file_path.name
                        if hasattr(job.file_path, "name")
                        else str(job.file_path).split("/")[-1]
                    ),
                    "status": job.status,
                    "progress": job.progress * 100,
                    "created_at": job.created_at.isoformat(),
                    "result_path": str(job.result_path) if job.result_path else None,
                    "processing_time": getattr(job, "processing_time", None),
                    "error_message": getattr(job, "error_message", None),
                }

                # Add output file information if available
                if job.result_path and job.result_path.exists():
                    output_dir = job.result_path.parent
                    output_files = []

                    # Scan for common AreTomo3 output files
                    file_patterns = [
                        "*.mrc",
                        "*.rec",
                        "*.st",  # Tomograms and tilt series
                        "*.aln",
                        "*.xf",
                        "*.tlt",  # Alignment files
                        "*_CTF.txt",
                        "*_CTF.log",  # CTF files
                        "*.log",
                        "*.txt",  # Log files
                    ]

                    for pattern in file_patterns:
                        for file_path in output_dir.glob(pattern):
                            if file_path.is_file():
                                stat = file_path.stat()
                                output_files.append(
                                    {
                                        "name": file_path.name,
                                        "path": str(file_path),
                                        "size": self._format_file_size(stat.st_size),
                                        "modified": datetime.fromtimestamp(
                                            stat.st_mtime
                                        ).isoformat(),
                                        "type": self._get_file_type(file_path.suffix),
                                    }
                                )

                    job_details["output_files"] = output_files
                    job_details["output_count"] = len(output_files)
                    job_details["total_output_size"] = (
                        self._calculate_total_output_size(output_files)
                    )
                else:
                    job_details["output_files"] = []
                    job_details["output_count"] = 0
                    job_details["total_output_size"] = "0 B"

                # Extract processing parameters if available
                job_details["parameters"] = self._extract_processing_parameters(job)

                enhanced_jobs.append(job_details)

            return enhanced_jobs

        @self.app.post("/api/jobs", response_model=JobResponse)
        async def submit_job(
            request: ProcessingRequest, background_tasks: BackgroundTasks
        ):
            """Submit a new processing job."""
            # Validate input files
            for file_path in request.input_files:
                if not Path(file_path).exists():
                    raise HTTPException(
                        status_code=400, detail=f"File not found: {file_path}"
                    )

            # Create job
            job_id = str(uuid.uuid4())
            # This would need to be implemented in the processor
            # job = await self.processor.submit_manual_job(request)

            return JobResponse(
                job_id=job_id,
                file_path=request.input_files[0],
                status="queued",
                progress=0.0,
                created_at=datetime.now(),
            )

        @self.app.delete("/api/jobs/{job_id}")
        async def cancel_job(job_id: str):
            """Cancel a processing job."""
            job = self.processor.get_job_status(job_id)
            if not job:
                raise HTTPException(status_code=404, detail="Job not found")

            if job.status in ["completed", "failed"]:
                raise HTTPException(
                    status_code=400, detail="Cannot cancel completed job"
                )

            # Cancel job (would need to be implemented)
            return {"message": f"Job {job_id} cancelled"}

        @self.app.get("/api/results/{job_id}")
        async def download_results(job_id: str):
            """Download job results."""
            job = self.processor.get_job_status(job_id)
            if not job:
                raise HTTPException(status_code=404, detail="Job not found")

            if not job.result_path or not job.result_path.exists():
                raise HTTPException(status_code=404, detail="Results not found")

            return FileResponse(
                path=str(job.result_path),
                filename=job.result_path.name,
                media_type="application/octet-stream",
            )

        @self.app.get("/api/results/{job_id}/details")
        async def get_result_details(job_id: str):
            """Get detailed result information for a specific job."""
            job = self.processor.get_job_status(job_id)
            if not job:
                raise HTTPException(status_code=404, detail="Job not found")

            # Get output directory and scan for files
            output_dir = job.result_path.parent if job.result_path else None
            output_files = []

            if output_dir and output_dir.exists():
                for file_path in output_dir.glob("*"):
                    if file_path.is_file():
                        stat = file_path.stat()
                        output_files.append(
                            {
                                "name": file_path.name,
                                "path": str(file_path),
                                "size": self._format_file_size(stat.st_size),
                                "modified": datetime.fromtimestamp(
                                    stat.st_mtime
                                ).isoformat(),
                                "type": self._get_file_type(file_path.suffix),
                            }
                        )

            # Extract processing parameters from job or result files
            parameters = self._extract_processing_parameters(job)

            return {
                "job_id": job.job_id,
                "name": (
                    job.file_path.name
                    if hasattr(job.file_path, "name")
                    else str(job.file_path).split("/")[-1]
                ),
                "status": job.status,
                "progress": job.progress * 100,
                "processing_time": (
                    f"{(job.processing_time / 60):.1f} min"
                    if job.processing_time
                    else None
                ),
                "start_time": job.created_at.isoformat(),
                "end_time": (
                    datetime.now().isoformat() if job.status == "completed" else None
                ),
                "output_files": output_files,
                "parameters": parameters,
                "pixel_size": parameters.get("PixSize", "N/A"),
                "tilt_range": f"{parameters.get( 'TiltMin', 'N/A')}° to {parameters.get('TiltMax', 'N/A' )}°",
                "output_size": self._calculate_total_output_size(output_files),
            }

        @self.app.get("/api/files/view")
        async def view_file(path: str):
            """View file content (for text files) or metadata."""
            # Validate path for security
            if not self._validate_file_path(path):
                raise HTTPException(status_code=400, detail="Invalid file path")

            file_path = Path(path)
            if not file_path.exists():
                raise HTTPException(status_code=404, detail="File not found")

            file_type = self._get_file_type(file_path.suffix)

            if file_type == "text":
                try:
                    with open(file_path) as f:
                        content = f.read()
                    return {"type": "text", "content": content}
                except Exception as e:
                    raise HTTPException(
                        status_code=500, detail=f"Error reading file: {e}"
                    )

            elif file_type == "image":
                # For MRC files, return metadata
                try:
                    metadata = self._get_mrc_metadata(file_path)
                    return {"type": "metadata", "metadata": metadata}
                except Exception as e:
                    return {
                        "type": "binary",
                        "message": f"Binary file: {file_path.name}",
                    }

            else:
                return {"type": "binary", "message": f"Binary file: {file_path.name}"}

        @self.app.get("/api/files/download")
        async def download_file(path: str):
            """Download a specific file."""
            # Validate path for security
            if not self._validate_file_path(path):
                raise HTTPException(status_code=400, detail="Invalid file path")

            file_path = Path(path)
            if not file_path.exists():
                raise HTTPException(status_code=404, detail="File not found")

            return FileResponse(
                path=str(file_path),
                filename=file_path.name,
                media_type="application/octet-stream",
            )

        @self.app.get("/api/processing/live-stats")
        async def get_live_processing_stats():
            """Get live processing statistics with more detail."""
            stats = self.processor.get_stats()
            active_jobs = [
                job
                for job in self.processor.get_all_jobs()
                if job.status == "processing"
            ]

            return {
                "stats": {
                    "total_files": stats.total_files,
                    "processed_files": stats.processed_files,
                    "failed_files": stats.failed_files,
                    "avg_processing_time": stats.avg_processing_time,
                    "throughput_per_hour": stats.throughput_per_hour,
                    "queue_size": stats.queue_size,
                    "active_jobs": stats.active_jobs,
                },
                "active_jobs": [
                    {
                        "job_id": job.job_id,
                        "file_name": (
                            job.file_path.name
                            if hasattr(job.file_path, "name")
                            else str(job.file_path).split("/")[-1]
                        ),
                        "progress": job.progress * 100,
                        "elapsed_time": (
                            datetime.now() - job.created_at
                        ).total_seconds(),
                        "estimated_remaining": self._estimate_remaining_time(job),
                    }
                    for job in active_jobs
                ],
                "recent_completions": [
                    {
                        "file_name": (
                            job.file_path.name
                            if hasattr(job.file_path, "name")
                            else str(job.file_path).split("/")[-1]
                        ),
                        "completion_time": job.created_at.isoformat(),
                        "processing_time": job.processing_time,
                        "status": job.status,
                    }
                    for job in self.processor.get_all_jobs()[-10:]
                    if job.status in ["completed", "failed"]
                ],
            }

        @self.app.get("/api/analysis/quality-metrics")
        async def get_quality_metrics():
            """Get real-time quality metrics for all tilt series."""
            if self.realtime_analysis_tab and hasattr(
                self.realtime_analysis_tab, "quality_metrics"
            ):
                metrics_data = []
                for (
                    series_name,
                    metrics,
                ) in self.realtime_analysis_tab.quality_metrics.items():
                    metrics_data.append(metrics)

                return {
                    "timestamp": datetime.now().isoformat(),
                    "series_count": len(metrics_data),
                    "metrics": metrics_data,
                }
            else:
                return {
                    "timestamp": datetime.now().isoformat(),
                    "series_count": 0,
                    "metrics": [],
                }

        @self.app.get("/api/analysis/quality-metrics/{series_name}")
        async def get_series_quality_metrics(series_name: str):
            """Get quality metrics for a specific tilt series."""
            if self.realtime_analysis_tab and hasattr(
                self.realtime_analysis_tab, "quality_metrics"
            ):
                metrics = self.realtime_analysis_tab.quality_metrics.get(series_name)
                if metrics:
                    return metrics

            # Fallback if no data available
            return {
                "series": series_name,
                "timestamp": datetime.now().isoformat(),
                "motion": {},
                "ctf": {},
                "alignment": {},
                "overall_quality": "Unknown",
            }

        @self.app.get("/api/dashboard/summary")
        async def get_dashboard_summary():
            """Get dashboard summary statistics."""
            try:
                jobs = self.processor.get_all_jobs()
                stats = self.processor.get_stats()

                # Calculate quality distribution
                quality_distribution = {"excellent": 0, "good": 0, "fair": 0, "poor": 0}

                # This would be based on actual quality analysis
                for job in jobs:
                    if job.status == "completed":
                        # Simulate quality assessment
                        import random

                        quality = random.choice(["excellent", "good", "fair", "poor"])
                        quality_distribution[quality] += 1

                return {
                    "timestamp": datetime.now().isoformat(),
                    "total_datasets": len(jobs),
                    "completed": len([j for j in jobs if j.status == "completed"]),
                    "processing": len([j for j in jobs if j.status == "processing"]),
                    "failed": len([j for j in jobs if j.status == "failed"]),
                    "queued": len([j for j in jobs if j.status == "queued"]),
                    "quality_distribution": quality_distribution,
                    "processing_stats": {
                        "avg_processing_time": stats.avg_processing_time,
                        "throughput_per_hour": stats.throughput_per_hour,
                        "queue_size": stats.queue_size,
                        "active_jobs": stats.active_jobs,
                    },
                }
            except Exception as e:
                logger.error(f"Error getting dashboard summary: {e}")
                return {
                    "timestamp": datetime.now().isoformat(),
                    "total_datasets": 0,
                    "completed": 0,
                    "processing": 0,
                    "failed": 0,
                    "queued": 0,
                    "quality_distribution": {
                        "excellent": 0,
                        "good": 0,
                        "fair": 0,
                        "poor": 0,
                    },
                    "processing_stats": {
                        "avg_processing_time": 0,
                        "throughput_per_hour": 0,
                        "queue_size": 0,
                        "active_jobs": 0,
                    },
                }

        @self.app.get("/api/dashboard/realtime-plots/{job_id}")
        async def get_realtime_plots(job_id: str):
            """Get real-time analysis plots for a specific job."""
            try:
                # This would integrate with the real-time analysis system
                if self.realtime_analysis_tab:
                    # Get plot data from the analysis tab
                    plot_data = {
                        "motion_plot": self._generate_sample_plot_data("motion"),
                        "ctf_plot": self._generate_sample_plot_data("ctf"),
                        "alignment_plot": self._generate_sample_plot_data("alignment"),
                        "quality_plot": self._generate_sample_plot_data("quality"),
                    }

                    return {
                        "job_id": job_id,
                        "timestamp": datetime.now().isoformat(),
                        "plots": plot_data,
                    }
                else:
                    return {
                        "job_id": job_id,
                        "timestamp": datetime.now().isoformat(),
                        "plots": {},
                        "message": "Real-time analysis not available",
                    }
            except Exception as e:
                logger.error(f"Error getting real-time plots for job {job_id}: {e}")
                return {
                    "job_id": job_id,
                    "timestamp": datetime.now().isoformat(),
                    "plots": {},
                    "error": str(e),
                }

        @self.app.get("/viewer/{job_id}")
        async def viewer_page(job_id: str):
            """Serve the tomogram viewer page for a specific job."""
            # This would integrate with the existing viewer functionality
            return HTMLResponse(
 f""" <!DOCTYPE html> <html> <head> <title>AreTomo3 Viewer - {job_id}</title> <style> body {{ font-family: 'Inter', sans-serif; background: #0f1419; color: white; margin: 0; padding: 20px; }} .viewer-container {{ max-width: 1200px; margin: 0 auto; text-align: center; }} .back-btn {{ background: #3b82f6; color: white; border: none; padding: 10px 20px; border-radius: 6px; cursor: pointer; margin-bottom: 20px; }} </style> </head> <body> <div class="viewer-container"> <button class="back-btn" onclick="window.close()">← Back to Dashboard</button> <h1>Tomogram Viewer</h1> <h2>Job ID: {job_id}</h2> <p>Tomogram viewer integration would be implemented here.</p> <p>This would connect to the existing Napari viewer functionality.</p> </div> </body> </html> """
            )

        @self.app.get("/api/analysis/plots/{plot_type}")
        async def get_analysis_plot(plot_type: str):
            """Get analysis plots (PNG/SVG) for web display."""
            # This would serve generated plots from the analysis tab
            plot_types = ["motion", "ctf", "alignment", "quality", "comparative"]
            if plot_type not in plot_types:
                raise HTTPException(status_code=400, detail="Invalid plot type")

            # Return plot file or metadata
            return {"plot_type": plot_type, "available": False}

        @self.app.get("/api/analysis/reports/{series_name}")
        async def get_pdf_report(series_name: str):
            """Get PDF report for a specific tilt series."""
            # This would serve generated PDF reports
            return {"series": series_name, "report_available": False}

        @self.app.get("/api/analysis/live-table")
        async def get_live_analysis_table():
            """Get live analysis data in tabular format for web interface."""
            if self.realtime_analysis_tab and hasattr(
                self.realtime_analysis_tab, "series_data"
            ):
                data_rows = []
                for series_name, data in self.realtime_analysis_tab.series_data.items():
                    row = [
                        series_name,
                        data.get("status", "Unknown"),
                        data.get("motion_quality", "Unknown"),
                        data.get("ctf_quality", "Unknown"),
                        data.get("alignment_quality", "Unknown"),
                        data.get("overall_quality", "Unknown"),
                        data.get("processing_time", "N/A"),
                    ]
                    data_rows.append(row)

                return {
                    "timestamp": datetime.now().isoformat(),
                    "columns": [
                        "Series Name",
                        "Status",
                        "Motion Quality",
                        "CTF Quality",
                        "Alignment Quality",
                        "Overall Quality",
                        "Processing Time",
                    ],
                    "data": data_rows,
                }
            else:
                return {
                    "timestamp": datetime.now().isoformat(),
                    "columns": [
                        "Series Name",
                        "Status",
                        "Motion Quality",
                        "CTF Quality",
                        "Alignment Quality",
                        "Overall Quality",
                        "Processing Time",
                    ],
                    "data": [],
                }

        @self.app.get("/api/logs")
        async def get_logs(limit: int = 100):
            """Get recent log entries for web interface."""
            # Return the most recent log entries
            recent_logs = (
                self.log_entries[-limit:]
                if len(self.log_entries) > limit
                else self.log_entries
            )
            return {
                "timestamp": datetime.now().isoformat(),
                "total_entries": len(self.log_entries),
                "returned_entries": len(recent_logs),
                "logs": recent_logs,
            }

        # Session Management API Endpoints
        @self.app.get("/api/session/current")
        async def get_current_session():
            """Get current session information."""
            return self.session_manager.get_session_for_web()

        @self.app.get("/api/session/list")
        async def get_session_list():
            """Get list of all sessions."""
            return {
                "sessions": self.session_manager.get_session_list(),
                "total_sessions": len(self.session_manager.session_history),
            }

        @self.app.post("/api/session/create")
        async def create_session(
            session_name: str,
            processing_mode: str,
            input_dir: str = "",
            output_dir: str = "",
        ):
            """Create a new processing session."""
            try:
                session = self.session_manager.create_new_session(
                    session_name, processing_mode, input_dir, output_dir
                )
                return {
                    "success": True,
                    "session_id": session.session_id,
                    "message": f"Created session: {session_name}",
                }
            except Exception as e:
                raise HTTPException(status_code=500, detail=str(e))

        @self.app.post("/api/session/load/{session_id}")
        async def load_session(session_id: str):
            """Load an existing session."""
            try:
                session = self.session_manager.load_session(session_id)
                if session:
                    return {
                        "success": True,
                        "session": self.session_manager.get_session_for_web(),
                        "message": f"Loaded session: {session.session_name}",
                    }
                else:
                    raise HTTPException(status_code=404, detail="Session not found")
            except Exception as e:
                raise HTTPException(status_code=500, detail=str(e))

        @self.app.post("/api/session/update")
        async def update_session(**kwargs):
            """Update current session data."""
            try:
                success = self.session_manager.update_session(**kwargs)
                if success:
                    return {
                        "success": True,
                        "session": self.session_manager.get_session_for_web(),
                        "message": "Session updated successfully",
                    }
                else:
                    raise HTTPException(
                        status_code=400, detail="Failed to update session"
                    )
            except Exception as e:
                raise HTTPException(status_code=500, detail=str(e))

        # Continue Mode API Endpoints
        @self.app.get("/api/continue/sessions")
        async def get_continue_sessions():
            """Get all continue mode sessions."""
            return {
                "sessions": self.continue_manager.get_all_sessions(),
                "total_sessions": len(self.continue_manager.active_sessions),
            }

        @self.app.post("/api/continue/start")
        async def start_continue_processing(
            series_name: str, command: str, input_dir: str, output_dir: str
        ):
            """Start processing with continue mode support."""
            try:
                success, result = self.continue_manager.start_processing(
                    series_name, command, input_dir, output_dir
                )
                if success:
                    return {
                        "success": True,
                        "session_id": result,
                        "message": f"Started continue mode processing for {series_name}",
                    }
                else:
                    raise HTTPException(status_code=500, detail=result)
            except Exception as e:
                raise HTTPException(status_code=500, detail=str(e))

        @self.app.post("/api/continue/pause/{session_id}")
        async def pause_continue_processing(session_id: str):
            """Pause continue mode processing."""
            try:
                success = self.continue_manager.pause_processing(session_id)
                if success:
                    return {
                        "success": True,
                        "message": f"Paused processing for session {session_id}",
                    }
                else:
                    raise HTTPException(
                        status_code=400, detail="Failed to pause processing"
                    )
            except Exception as e:
                raise HTTPException(status_code=500, detail=str(e))

        @self.app.post("/api/continue/resume/{session_id}")
        async def resume_continue_processing(session_id: str):
            """Resume paused continue mode processing."""
            try:
                success = self.continue_manager.resume_processing(session_id)
                if success:
                    return {
                        "success": True,
                        "message": f"Resumed processing for session {session_id}",
                    }
                else:
                    raise HTTPException(
                        status_code=400, detail="Failed to resume processing"
                    )
            except Exception as e:
                raise HTTPException(status_code=500, detail=str(e))

        @self.app.post("/api/continue/stop/{session_id}")
        async def stop_continue_processing(session_id: str):
            """Stop continue mode processing."""
            try:
                success = self.continue_manager.stop_processing(session_id)
                if success:
                    return {
                        "success": True,
                        "message": f"Stopped processing for session {session_id}",
                    }
                else:
                    raise HTTPException(
                        status_code=400, detail="Failed to stop processing"
                    )
            except Exception as e:
                raise HTTPException(status_code=500, detail=str(e))

        @self.app.post("/api/continue/continue/{session_id}")
        async def continue_interrupted_processing(session_id: str):
            """Continue interrupted processing."""
            try:
                success, result = self.continue_manager.continue_processing(session_id)
                if success:
                    return {
                        "success": True,
                        "session_id": result,
                        "message": f"Continued processing for session {session_id}",
                    }
                else:
                    raise HTTPException(status_code=500, detail=result)
            except Exception as e:
                raise HTTPException(status_code=500, detail=str(e))

        @self.app.get("/api/continue/status/{session_id}")
        async def get_continue_session_status(session_id: str):
            """Get status of a continue mode session."""
            try:
                status = self.continue_manager.get_session_status(session_id)
                if status:
                    return status
                else:
                    raise HTTPException(status_code=404, detail="Session not found")
            except Exception as e:
                raise HTTPException(status_code=500, detail=str(e))

    def get_analysis_dashboard(self) -> str:
        """Return the modern analysis dashboard HTML."""
        try:
            # Try to read the template file
            template_path = (
                Path(__file__).parent / "templates" / "analysis_dashboard.html"
            )
            if template_path.exists():
                with open(template_path, encoding="utf-8") as f:
                    return f.read()
            else:
                # Fallback to embedded HTML if template file not found
                return self.get_embedded_analysis_dashboard()
        except Exception as e:
            logger.error(f"Error loading analysis dashboard template: {e}")
            return self.get_enhanced_web_interface()

    def get_embedded_analysis_dashboard(self) -> str:
        """Return embedded analysis dashboard as fallback."""
        return """
        <!DOCTYPE html>
        <html lang="en">
        <head>
            <meta charset="UTF-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>AreTomo3 Analysis Dashboard</title>
            <style>
                body {
                    font-family: 'Inter', 'Segoe UI', sans-serif;
                    background: linear-gradient(135deg, #0f1419 0%, #1e293b 100%);
                    color: white;
                    margin: 0;
                    padding: 20px;
                    min-height: 100vh;
                }
                .container {
                    max-width: 1400px;
                    margin: 0 auto;
                }
                .header {
                    text-align: center;
                    margin-bottom: 30px;
                    padding: 20px;
                    background: rgba(26, 31, 46, 0.8);
                    border-radius: 12px;
                }
                .dashboard-grid {
                    display: grid;
                    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
                    gap: 20px;
                }
                .analysis-card {
                    background: #1a1f2e;
                    border-radius: 12px;
                    padding: 20px;
                    border: 1px solid #374151;
                    box-shadow: 0 4px 20px rgba(0,0,0,0.2);
                }
                .status-indicator {
                    width: 12px;
                    height: 12px;
                    border-radius: 50%;
                    display: inline-block;
                    margin-right: 8px;
                }
                .status-excellent { background: #10b981; }
                .status-good { background: #22c55e; }
                .status-processing { background: #3b82f6; }
                .status-error { background: #ef4444; }
                .chart-placeholder {
                    height: 200px;
                    background: #252b3a;
                    border-radius: 8px;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    margin: 10px 0;
                    color: #a0a6b8;
                }
            </style>
        </head>
        <body>
            <div class="container">
                <div class="header">
                    <h1>🔬 AreTomo3 Analysis Dashboard</h1>
                    <p>Real-time tomographic reconstruction analysis and monitoring</p>
                </div>
                <div class="dashboard-grid" id="dashboard-content">
                    <div class="analysis-card">
                        <h3>Loading Dashboard...</h3>
                        <p>Please wait while the analysis dashboard loads.</p>
                        <div class="chart-placeholder">
                            <i class="fas fa-spinner fa-spin"></i> Loading data...
                        </div>
                    </div>
                </div>
            </div>
            <script>
                // Auto-refresh and load data
                setInterval(async () => {
                    try {
                        const response = await fetch('/api/jobs');
                        const jobs = await response.json();
                        updateDashboard(jobs);
                    } catch (error) {
                        console.error('Error loading data:', error);
                    }
                }, 5000);

                function updateDashboard(jobs) {
                    const container = document.getElementById('dashboard-content');
                    container.innerHTML = jobs.map(job => `
                        <div class="analysis-card">
                            <h4>
                                <span class="status-indicator status-${getStatusClass(job.status)}"></span>
                                ${job.file_name || job.job_id}
                            </h4>
                            <p>Status: ${job.status} | Progress: ${Math.round(job.progress || 0)}%</p>
                            <div class="row">
                                <div class="col-6">
                                    <div class="chart-placeholder">Motion Correction</div>
                                </div>
                                <div class="col-6">
                                    <div class="chart-placeholder">Alignment Quality</div>
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-6">
                                    <div class="chart-placeholder">CTF Estimation</div>
                                </div>
                                <div class="col-6">
                                    <div class="chart-placeholder">Reconstruction</div>
                                </div>
                            </div>
                            <small>Created: ${new Date(job.created_at).toLocaleString()}</small>
                        </div>
                    `).join('');
                }

                function getStatusClass(status) {
                    switch(status) {
                        case 'completed': return 'excellent';
                        case 'processing': return 'processing';
                        case 'failed': return 'error';
                        default: return 'good';
                    }
                }

                // Initial load
                updateDashboard([]);
            </script>
        </body>
        </html>
        """

    def get_enhanced_web_interface(self) -> str:
        """Return the enhanced web interface HTML with detailed results display."""
        return """
        <!DOCTYPE html>
        <html>
        <head>
            <title>AreTomo3 GUI - Enhanced Results Dashboard</title>
            <meta charset="utf-8">
            <meta name="viewport" content="width=device-width, initial-scale=1">
            <style>
                * { box-sizing: border-box; }
                body {
                    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
                    margin: 0; padding: 20px; background: linear-gradient(
                        135deg,
                        #667eea 0%,
                        #764ba2 100%
                    );
                    min-height: 100vh;
                }
                .container {
                    max-width: 1400px; margin: 0 auto; background: white;
                    border-radius: 12px; box-shadow: 0 8px 32px rgba(0,0,0,0.1);
                    overflow: hidden;
                }
                .header {
                    background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
                    color: white; padding: 20px; text-align: center;
                }
                .header h1 { margin: 0; font-size: 2.5em; font-weight: 300; }
                .header p { margin: 10px 0 0 0; opacity: 0.8; }

                .nav-tabs {
                    display: flex; background: #f8f9fa; border-bottom: 1px solid #dee2e6;
                }
                .nav-tab {
                    padding: 15px 25px; cursor: pointer; border: none; background: none;
                    font-size: 16px; font-weight: 500; color: #495057; transition: all 0.3s;
                }
                .nav-tab.active { background: white; color: #007bff; border-bottom: 3px solid #007bff; }
                .nav-tab:hover { background: #e9ecef; }

                .tab-content { display: none; padding: 20px; }
                .tab-content.active { display: block; }

                .stats-grid {
                    display: grid; grid-template-columns: repeat(
                        auto-fit,
                        minmax(200px,
                        1fr)
                    );
                    gap: 20px; margin-bottom: 30px;
                }
                .stat-card {
                    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                    color: white; padding: 20px; border-radius: 10px; text-align: center;
                    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
                }
                .stat-value { font-size: 2.5em; font-weight: bold; margin-bottom: 5px; }
                .stat-label { opacity: 0.9; font-size: 0.9em; }

                .controls {
                    text-align: center; margin: 20px 0; padding: 20px;
                    background: #f8f9fa; border-radius: 8px;
                }
                .btn {
                    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                    color: white; border: none; padding: 12px 24px; border-radius: 6px;
                    cursor: pointer; margin: 0 10px; font-size: 16px; font-weight: 500;
                    transition: all 0.3s; box-shadow: 0 2px 10px rgba(0,0,0,0.1);
                }
                .btn:hover { transform: translateY(
                    -2px); box-shadow: 0 4px 20px rgba(0,
                    0,
                    0,
                    0.2
                ); }
                .btn.danger { background: linear-gradient(
                    135deg,
                    #e74c3c 0%,
                    #c0392b 100%
                ); }
                .btn.success { background: linear-gradient(
                    135deg,
                    #2ecc71 0%,
                    #27ae60 100%
                ); }

                .results-container {
                    display: grid; grid-template-columns: 1fr 1fr; gap: 20px; margin-top: 20px;
                }
                .results-list {
                    background: #f8f9fa; border-radius: 8px; padding: 20px; max-height: 600px; overflow-y: auto;
                }
                .result-item {
                    background: white; border-radius: 6px; padding: 15px; margin-bottom: 15px;
                    border-left: 4px solid #007bff; box-shadow: 0 2px 5px rgba(
                        0,
                        0,
                        0,
                        0.1
                    );
                    transition: all 0.3s;
                }
                .result-item:hover { transform: translateX(
                    5px); box-shadow: 0 4px 15px rgba(0,
                    0,
                    0,
                    0.15
                ); }
                .result-item.success { border-left-color: #28a745; }
                .result-item.error { border-left-color: #dc3545; }
                .result-item.processing { border-left-color: #ffc107; }

                .result-header {
                    display: flex; justify-content: space-between; align-items: center; margin-bottom: 10px;
                }
                .result-title { font-weight: bold; color: #2c3e50; }
                .result-status {
                    padding: 4px 12px; border-radius: 20px; font-size: 0.8em; font-weight: bold;
                    text-transform: uppercase;
                }
                .status-success { background: #d4edda; color: #155724; }
                .status-error { background: #f8d7da; color: #721c24; }
                .status-processing { background: #fff3cd; color: #856404; }
                .status-queued { background: #d1ecf1; color: #0c5460; }

                .result-details {
                    font-size: 0.9em; color: #6c757d; line-height: 1.4;
                }
                .result-meta {
                    display: grid; grid-template-columns: 1fr 1fr; gap: 10px; margin-top: 10px;
                    font-size: 0.8em;
                }
                .meta-item { display: flex; justify-content: space-between; }

                .result-preview {
                    background: white; border-radius: 8px; padding: 20px;
                }
                .preview-placeholder {
                    text-align: center; color: #6c757d; padding: 40px;
                    border: 2px dashed #dee2e6; border-radius: 8px;
                }

                .log-container {
                    background: #2c3e50; color: #ecf0f1; padding: 20px; border-radius: 8px;
                    height: 400px; overflow-y: auto; font-family: 'Courier New', monospace;
                    font-size: 14px; line-height: 1.4;
                }
                .log-entry {
                    margin-bottom: 5px; padding: 5px; border-radius: 3px;
                }
                .log-info { background: rgba(52, 152, 219, 0.1); }
                .log-success { background: rgba(46, 204, 113, 0.1); }
                .log-error { background: rgba(231, 76, 60, 0.1); }
                .log-warning { background: rgba(241, 196, 15, 0.1); }

                .progress-bar {
                    width: 100%; height: 6px; background: #e9ecef; border-radius: 3px; overflow: hidden;
                    margin: 10px 0;
                }
                .progress-fill {
                    height: 100%; background: linear-gradient(
                        90deg,
                        #667eea 0%,
                        #764ba2 100%
                    );
                    transition: width 0.3s ease;
                }

                .file-browser {
                    background: #f8f9fa; border-radius: 8px; padding: 15px; margin-top: 15px;
                }
                .file-item {
                    display: flex; justify-content: space-between; align-items: center;
                    padding: 8px 12px; margin: 5px 0; background: white; border-radius: 4px;
                    border: 1px solid #dee2e6; transition: all 0.3s;
                }
                .file-item:hover { background: #e9ecef; }
                .file-name { font-weight: 500; }
                .file-size { color: #6c757d; font-size: 0.9em; }
                .file-actions { display: flex; gap: 10px; }
                .file-action {
                    padding: 4px 8px; border: none; border-radius: 4px; cursor: pointer;
                    font-size: 0.8em; transition: all 0.3s;
                }
                .action-view { background: #007bff; color: white; }
                .action-download { background: #28a745; color: white; }

                /* Session and Analysis Cards */
                .session-card, .analysis-card {
                    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                    color: white;
                    padding: 20px;
                    border-radius: 12px;
                    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
                    margin-bottom: 15px;
                }

                .session-details, .analysis-details {
                    display: grid;
                    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
                    gap: 10px;
                    margin-top: 15px;
                }

                .session-details div, .analysis-details div {
                    background: rgba(255,255,255,0.1);
                    padding: 8px 12px;
                    border-radius: 6px;
                    font-size: 0.9em;
                }

                .auto-refresh-btn {
                    margin-left: 10px;
                }

                @media (max-width: 768px) {
                    .results-container { grid-template-columns: 1fr; }
                    .stats-grid { grid-template-columns: repeat(
                        auto-fit,
                        minmax(150px,
                        1fr)
                    ); }
                    .session-details, .analysis-details { grid-template-columns: 1fr; }
                }
            </style>
        </head>
        <body>
            <div class="container">
                <div class="header">
                    <h1>🔬 AreTomo3 Results Dashboard</h1>
                    <p>Real-time monitoring and results visualization for tomographic reconstruction</p>
                </div>

                <div class="nav-tabs">
                    <button class="nav-tab active" onclick="showTab('overview')">📊 Overview</button>
                    <button class="nav-tab" onclick="showTab('results')">📁 Results</button>
                    <button class="nav-tab" onclick="showTab('monitoring')">📈 Monitoring</button>
                    <button class="nav-tab" onclick="showTab('logs')">📝 Logs</button>
                </div>

                <!-- Overview Tab -->
                <div id="overview" class="tab-content active">
                    <!-- Session Information -->
                    <div id="session-info" style="margin-bottom: 20px;">
                        <div class="preview-placeholder">
                            No active session. Session information will appear here when processing starts.
                        </div>
                    </div>

                    <!-- Analysis Summary -->
                    <div id="analysis-info" style="margin-bottom: 20px;">
                        <div class="preview-placeholder">
                            Analysis summary will appear here when data is available.
                        </div>
                    </div>

                    <div class="stats-grid">
                        <div class="stat-card">
                            <div class="stat-value" id="total-files">0</div>
                            <div class="stat-label">Total Tilt Series</div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-value" id="processed-files">0</div>
                            <div class="stat-label">Completed</div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-value" id="failed-files">0</div>
                            <div class="stat-label">Failed</div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-value" id="queue-size">0</div>
                            <div class="stat-label">In Queue</div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-value" id="throughput">0.0</div>
                            <div class="stat-label">Series/Hour</div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-value" id="avg-time">0.0</div>
                            <div class="stat-label">Avg Time (min)</div>
                        </div>
                    </div>

                    <div class="controls">
                        <button class="btn success" onclick="startProcessing()">▶️ Start Processing</button>
                        <button class="btn danger" onclick="stopProcessing()">⏹️ Stop Processing</button>
                        <button class="btn" onclick="refreshData()">🔄 Refresh</button>
                        <button class="btn" onclick="clearResults()">🗑️ Clear Results</button>
                    </div>
                </div>

                <!-- Results Tab -->
                <div id="results" class="tab-content">
                    <div class="results-container">
                        <div class="results-list">
                            <h3>📁 Processed Tilt Series</h3>
                            <div id="results-list-content">
                                <div class="preview-placeholder">
                                    No results yet. Start processing to see tilt series results here.
                                </div>
                            </div>
                        </div>
                        <div class="result-preview">
                            <h3>🔍 Result Details</h3>
                            <div id="result-preview-content">
                                <div class="preview-placeholder">
                                    Select a tilt series from the list to view detailed results and output files.
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Monitoring Tab -->
                <div id="monitoring" class="tab-content">
                    <h3>📈 Real-time Processing Monitor</h3>
                    <div id="current-job-info">
                        <div class="preview-placeholder">
                            No active processing jobs.
                        </div>
                    </div>
                    <div id="processing-chart" style="height: 400px; margin-top: 20px;">
                        <div class="preview-placeholder">
                            Processing timeline chart will appear here during active processing.
                        </div>
                    </div>
                </div>

                <!-- Logs Tab -->
                <div id="logs" class="tab-content">
                    <h3>📝 System Logs</h3>
                    <div class="log-container" id="log-container"></div>
                </div>
            </div>

            <script>
                let ws = null;
                let resultsData = [];
                let currentJobId = null;

                // Initialize WebSocket connection
                function initWebSocket() {
                    ws = new WebSocket(`ws://${window.location.host}/ws`);

                    ws.onopen = function() {
                        addLogEntry('Connected to AreTomo3 server', 'success');
                    };

                    ws.onmessage = function(event) {
                        const data = JSON.parse(event.data);
                        handleWebSocketMessage(data);
                    };

                    ws.onclose = function() {
                        addLogEntry(
                            'Disconnected from server. Attempting to reconnect...',
                            'warning'
                        );
                        setTimeout(initWebSocket, 3000);
                    };

                    ws.onerror = function(error) {
                        addLogEntry(`WebSocket error: ${error}`, 'error');
                    };
                }

                function handleWebSocketMessage(data) {
                    switch(data.type) {
                        case 'stats':
                            updateStats(data.stats);
                            break;
                        case 'job_update':
                            updateJobStatus(data);
                            break;
                        case 'result_ready':
                            addNewResult(data.result);
                            break;
                        case 'log':
                            addLogEntry(data.message, data.level, data.timestamp);
                            break;
                    }
                }

                function updateStats(stats) {
                    document.getElementById('total-files').textContent = stats.total_files;
                    document.getElementById('processed-files').textContent = stats.processed_files;
                    document.getElementById('failed-files').textContent = stats.failed_files;
                    document.getElementById('queue-size').textContent = stats.queue_size;
                    document.getElementById('throughput').textContent = stats.throughput_per_hour.toFixed(1);
                    document.getElementById('avg-time').textContent = (stats.avg_processing_time / 60).toFixed(1);
                }

                function addNewResult(result) {
                    resultsData.unshift(result);
                    updateResultsList();
                    addLogEntry(`Completed processing: ${result.name}`, 'success');
                }

                function updateResultsList() {
                    const container = document.getElementById('results-list-content');
                    if (resultsData.length === 0) {
                        container.innerHTML = '<div class="preview-placeholder">No results yet. Start processing to see tilt series results here.</div>';
                        return;
                    }

                    container.innerHTML = resultsData.map(result => `
                        <div class="result-item ${result.status}" onclick="showResultDetails('${result.id}')">
                            <div class="result-header">
                                <div class="result-title">${result.name}</div>
                                <div class="result-status status-${result.status}">${result.status}</div>
                            </div>
                            <div class="result-details">
                                ${result.description || 'Tomographic reconstruction completed'}
                            </div>
                            <div class="progress-bar">
                                <div class="progress-fill" style="width: ${result.progress}%"></div>
                            </div>
                            <div class="result-meta">
                                <div class="meta-item">
                                    <span>Processing Time:</span>
                                    <span>${result.processing_time || 'N/A'}</span>
                                </div>
                                <div class="meta-item">
                                    <span>Output Size:</span>
                                    <span>${result.output_size || 'N/A'}</span>
                                </div>
                                <div class="meta-item">
                                    <span>Pixel Size:</span>
                                    <span>${result.pixel_size || 'N/A'} Å</span>
                                </div>
                                <div class="meta-item">
                                    <span>Tilt Range:</span>
                                    <span>${result.tilt_range || 'N/A'}</span>
                                </div>
                            </div>
                        </div>
                    `).join('');
                }

                function showResultDetails(resultId) {
                    const result = resultsData.find(r => r.id === resultId);
                    if (!result) return;

                    const container = document.getElementById('result-preview-content');
                    container.innerHTML = `
                        <h4>${result.name}</h4>
                        <div class="result-meta" style="margin-bottom: 20px;">
                            <div class="meta-item">
                                <span><strong>Status:</strong></span>
                                <span class="result-status status-${result.status}">${result.status}</span>
                            </div>
                            <div class="meta-item">
                                <span><strong>Started:</strong></span>
                                <span>${result.start_time || 'N/A'}</span>
                            </div>
                            <div class="meta-item">
                                <span><strong>Completed:</strong></span>
                                <span>${result.end_time || 'N/A'}</span>
                            </div>
                            <div class="meta-item">
                                <span><strong>Processing Time:</strong></span>
                                <span>${result.processing_time || 'N/A'}</span>
                            </div>
                        </div>

                        <h5>📁 Output Files</h5>
                        <div class="file-browser">
                            ${(result.output_files || []).map(file => `
                                <div class="file-item">
                                    <div>
                                        <div class="file-name">${file.name}</div>
                                        <div class="file-size">${file.size}</div>
                                    </div>
                                    <div class="file-actions">
                                        <button class="file-action action-view" onclick="viewFile('${file.path}')">👁️ View</button>
                                        <button class="file-action action-download" onclick="downloadFile('${file.path}')">⬇️ Download</button>
                                    </div>
                                </div>
                            `).join('')}
                        </div>

                        <h5>⚙️ Processing Parameters</h5>
                        <div style="background: #f8f9fa; padding: 15px; border-radius: 6px; font-family: monospace; font-size: 0.9em;">
                            ${Object.entries(
                                result.parameters || {}).map(([key,
                                value]
                            ) =>
                                `<div><strong>${key}:</strong> ${value}</div>`
                            ).join('')}
                        </div>
                    `;
                }

                function addLogEntry(message, level = 'info', timestamp = null) {
                    const container = document.getElementById('log-container');
                    const displayTime = timestamp ? new Date(timestamp).toLocaleTimeString() : new Date().toLocaleTimeString();
                    const entry = document.createElement('div');
                    entry.className = `log-entry log-${level.toLowerCase()}`;
                    entry.innerHTML = `[${displayTime}] ${message}`;
                    container.appendChild(entry);
                    container.scrollTop = container.scrollHeight;
                }

                async function loadInitialLogs() {
                    try {
                        const response = await fetch('/api/logs?limit=50');
                        const data = await response.json();
                        const container = document.getElementById('log-container');
                        container.innerHTML = ''; // Clear existing logs

                        data.logs.forEach(log => {
                            addLogEntry(log.message, log.level, log.timestamp);
                        });

                        addLogEntry(
                            'Connected to AreTomo3 server - logs loaded',
                            'success'
                        );
                    } catch (error) {
                        addLogEntry(`Error loading logs: ${error}`, 'error');
                    }
                }

                function showTab(tabName) {
                    // Hide all tabs
                    document.querySelectorAll('.tab-content').forEach(tab => {
                        tab.classList.remove('active');
                    });
                    document.querySelectorAll('.nav-tab').forEach(tab => {
                        tab.classList.remove('active');
                    });

                    // Show selected tab
                    document.getElementById(tabName).classList.add('active');
                    event.target.classList.add('active');
                }

                async function startProcessing() {
                    try {
                        const response = await fetch(
                            '/api/processing/start',
                            { method: 'POST' }
                        );
                        const result = await response.json();
                        addLogEntry(`Processing started: ${result.message}`, 'success');
                    } catch (error) {
                        addLogEntry(`Error starting processing: ${error}`, 'error');
                    }
                }

                async function stopProcessing() {
                    try {
                        const response = await fetch(
                            '/api/processing/stop',
                            { method: 'POST' }
                        );
                        const result = await response.json();
                        addLogEntry(`Processing stopped: ${result.message}`, 'warning');
                    } catch (error) {
                        addLogEntry(`Error stopping processing: ${error}`, 'error');
                    }
                }

                async function refreshData() {
                    try {
                        const [statsResponse, jobsResponse] = await Promise.all([
                            fetch('/api/stats'),
                            fetch('/api/jobs')
                        ]);

                        const stats = await statsResponse.json();
                        const jobs = await jobsResponse.json();

                        updateStats(stats);
                        resultsData = jobs.map(job => ({
                            id: job.job_id,
                            name: job.file_path.split('/').pop(),
                            status: job.status,
                            progress: job.progress * 100,
                            processing_time: job.processing_time ? `${(job.processing_time / 60).toFixed(1)} min` : null,
                            start_time: new Date(job.created_at).toLocaleString(),
                            end_time: job.status === 'completed' ? 'Recently' : null,
                            output_files: job.result_path ? [
                                { name: 'Tomogram', path: job.result_path, size: 'Unknown' },
                                { name: 'Alignment', path: job.result_path.replace(
                                    '.mrc',
                                    '.aln'
                                ), size: 'Unknown' },
                                { name: 'CTF Data', path: job.result_path.replace(
                                    '.mrc',
                                    '_CTF.txt'
                                ), size: 'Unknown' }
                            ] : [],
                            parameters: {
                                'Pixel Size': '1.82 Å',
                                'Voltage': '300 kV',
                                'Tilt Axis': '175.9°',
                                'Binning': '4x'
                            }
                        }));
                        updateResultsList();
                        addLogEntry('Data refreshed successfully', 'info');
                    } catch (error) {
                        addLogEntry(`Error refreshing data: ${error}`, 'error');
                    }
                }

                function clearResults() {
                    if (confirm('Are you sure you want to clear all results?')) {
                        resultsData = [];
                        updateResultsList();
                        document.getElementById('result-preview-content').innerHTML =
                            '<div class="preview-placeholder">Select a tilt series from the list to view detailed results.</div>';
                        addLogEntry('Results cleared', 'info');
                    }
                }

                function viewFile(filePath) {
                    window.open(
                        `/api/files/view?path=${encodeURIComponent(filePath)}`,
                        '_blank'
                    );
                }

                function downloadFile(filePath) {
                    window.open(
                        `/api/files/download?path=${encodeURIComponent(filePath)}`,
                        '_blank'
                    );
                }

                // Auto-refresh functionality
                let autoRefreshInterval = null;
                let autoRefreshEnabled = true;

                function startAutoRefresh() {
                    if (autoRefreshInterval) {
                        clearInterval(autoRefreshInterval);
                    }

                    autoRefreshInterval = setInterval(async () => {
                        if (autoRefreshEnabled) {
                            await refreshData();
                            await loadSessionData();
                            await loadAnalysisData();
                        }
                    }, 5000); // Refresh every 5 seconds

                    addLogEntry('Auto-refresh enabled (5 second interval)', 'info');
                }

                function stopAutoRefresh() {
                    if (autoRefreshInterval) {
                        clearInterval(autoRefreshInterval);
                        autoRefreshInterval = null;
                    }
                    addLogEntry('Auto-refresh disabled', 'info');
                }

                function toggleAutoRefresh() {
                    autoRefreshEnabled = !autoRefreshEnabled;
                    const button = document.querySelector('.auto-refresh-btn');
                    if (button) {
                        button.textContent = autoRefreshEnabled ? '⏸️ Pause Auto-refresh' : '▶️ Resume Auto-refresh';
                        button.className = autoRefreshEnabled ? 'btn warning' : 'btn success';
                    }
                    addLogEntry(
                        `Auto-refresh ${autoRefreshEnabled ? 'enabled' : 'disabled'}`,
                        'info'
                    );
                }

                async function loadSessionData() {
                    try {
                        const response = await fetch('/api/session/current');
                        const sessionData = await response.json();

                        if (sessionData.session_active) {
                            updateSessionDisplay(sessionData);
                        }
                    } catch (error) {
                        console.error('Error loading session data:', error);
                    }
                }

                async function loadAnalysisData() {
                    try {
                        const response = await fetch('/api/analysis/data');
                        const analysisData = await response.json();

                        if (analysisData.success && analysisData.data) {
                            updateAnalysisDisplay(analysisData);
                        }
                    } catch (error) {
                        console.error('Error loading analysis data:', error);
                    }
                }

                function updateSessionDisplay(sessionData) {
                    // Update session information in the interface
                    const sessionInfo = document.getElementById('session-info');
                    if (sessionInfo) {
                        sessionInfo.innerHTML = `
                            <div class="session-card">
                                <h4>📊 Current Session: ${sessionData.session_name || 'Unknown'}</h4>
                                <div class="session-details">
                                    <div><strong>Mode:</strong> ${sessionData.processing_mode || 'N/A'}</div>
                                    <div><strong>Progress:</strong> ${sessionData.progress_percentage || 0}%</div>
                                    <div><strong>Status:</strong> ${sessionData.processing_status || 'Unknown'}</div>
                                    <div><strong>Completed Series:</strong> ${sessionData.completed_series || 0}</div>
                                    <div><strong>Total Series:</strong> ${sessionData.total_series || 0}</div>
                                </div>
                                <div class="progress-bar">
                                    <div class="progress-fill" style="width: ${sessionData.progress_percentage || 0}%"></div>
                                </div>
                            </div>
                        `;
                    }
                }

                function updateAnalysisDisplay(analysisData) {
                    // Update analysis information
                    const analysisInfo = document.getElementById('analysis-info');
                    if (analysisInfo && analysisData.quality_metrics) {
                        const metrics = analysisData.quality_metrics;

                        let motionSummary = '';
                        let ctfSummary = '';
                        let alignmentSummary = '';

                        if (metrics.motion && metrics.motion.length > 0) {
                            const avgMotion = metrics.motion.reduce(
                                (sum,
                                m) => sum + m.mean_motion,
                                0
                            ) / metrics.motion.length;
                            motionSummary = `<div><strong>Motion:</strong> ${avgMotion.toFixed(2)} px avg</div>`;
                        }

                        if (metrics.ctf && metrics.ctf.length > 0) {
                            const avgResolution = metrics.ctf.reduce(
                                (sum,
                                c) => sum + c.mean_resolution,
                                0
                            ) / metrics.ctf.length;
                            ctfSummary = `<div><strong>Resolution:</strong> ${avgResolution.toFixed(1)} Å avg</div>`;
                        }

                        if (metrics.alignment && metrics.alignment.length > 0) {
                            const avgScore = metrics.alignment.reduce(
                                (sum,
                                a) => sum + a.mean_score,
                                0
                            ) / metrics.alignment.length;
                            alignmentSummary = `<div><strong>Alignment:</strong> ${(avgScore * 100).toFixed(1)}% avg</div>`;
                        }

                        analysisInfo.innerHTML = `
                            <div class="analysis-card">
                                <h4>📈 Analysis Summary</h4>
                                <div class="analysis-details">
                                    ${motionSummary}
                                    ${ctfSummary}
                                    ${alignmentSummary}
                                    <div><strong>Overall Quality:</strong> ${metrics.summary?.overall_quality || 'Unknown'}</div>
                                    <div><strong>Last Updated:</strong> ${new Date(analysisData.last_updated).toLocaleTimeString()}</div>
                                </div>
                            </div>
                        `;
                    }
                }

                // Initialize
                document.addEventListener('DOMContentLoaded', function() {
                    initWebSocket();
                    loadInitialLogs();
                    refreshData();
                    loadSessionData();
                    loadAnalysisData();
                    startAutoRefresh();

                    // Add auto-refresh control button
                    const controls = document.querySelector('.controls');
                    if (controls) {
                        const autoRefreshBtn = document.createElement('button');
                        autoRefreshBtn.className = 'btn warning auto-refresh-btn';
                        autoRefreshBtn.textContent = '⏸️ Pause Auto-refresh';
                        autoRefreshBtn.onclick = toggleAutoRefresh;
                        controls.appendChild(autoRefreshBtn);
                    }

                    addLogEntry(
                        'AreTomo3 Results Dashboard initialized with auto-refresh',
                        'info'
                    );
                });
            </script>
        </body>
        </html>
        """

    def _format_file_size(self, size_bytes: int) -> str:
        """Format file size in human readable format."""
        if size_bytes == 0:
            return "0 B"
        size_names = ["B", "KB", "MB", "GB", "TB"]
        import math

        i = int(math.floor(math.log(size_bytes, 1024)))
        p = math.pow(1024, i)
        s = round(size_bytes / p, 2)
        return f"{s} {size_names[i]}"

    def _get_file_type(self, extension: str) -> str:
        """Determine file type from extension."""
        extension = extension.lower()
        if extension in [".txt", ".log", ".aln", ".xf", ".tlt"]:
            return "text"
        elif extension in [".mrc", ".mrcs", ".st", ".rec"]:
            return "image"
        elif extension in [".png", ".jpg", ".jpeg", ".tiff"]:
            return "plot"
        else:
            return "binary"

    def _extract_processing_parameters(self, job) -> Dict[str, Any]:
        """Extract processing parameters from job or result files."""
        # This would need to be implemented based on your job structure
        # For now, return default parameters
        return {
            "PixSize": "1.82",
            "Voltage": "300",
            "TiltAxis": "175.9",
            "AtBin": "4",
            "VolZ": "2048",
            "TiltMin": "-60",
            "TiltMax": "60",
        }

    def _calculate_total_output_size(self, output_files: List[Dict]) -> str:
        """Calculate total size of output files."""
        total_bytes = 0
        for file_info in output_files:
            # Extract numeric value from size string
            size_str = file_info.get("size", "0 B")
            try:
                size_parts = size_str.split()
                if len(size_parts) == 2:
                    value, unit = size_parts
                    value = float(value)
                    unit_multipliers = {
                        "B": 1,
                        "KB": 1024,
                        "MB": 1024**2,
                        "GB": 1024**3,
                        "TB": 1024**4,
                    }
                    total_bytes += value * unit_multipliers.get(unit, 1)
            except Exception:
                pass
        return self._format_file_size(int(total_bytes))

    def _get_mrc_metadata(self, file_path: Path) -> Dict[str, Any]:
        """Get metadata from MRC file."""
        try:
            # This would need mrcfile library
            # For now, return basic file info
            stat = file_path.stat()
            return {
                "file_size": self._format_file_size(stat.st_size),
                "modified": datetime.fromtimestamp(stat.st_mtime).isoformat(),
                "format": "MRC",
                "note": "MRC metadata extraction requires mrcfile library",
            }
        except Exception as e:
            return {"error": str(e)}

    def _estimate_remaining_time(self, job) -> float:
        """Estimate remaining processing time for a job."""
        if job.progress <= 0:
            return 0

        elapsed = (datetime.now() - job.created_at).total_seconds()
        estimated_total = elapsed / job.progress
        remaining = estimated_total - elapsed
        return max(0, remaining)

    def _generate_sample_plot_data(self, plot_type: str) -> Dict[str, Any]:
        """Generate sample plot data for demonstration purposes."""

        # Generate sample data based on plot type
        if plot_type == "motion":
            # Motion correction plot data
            tilt_angles = list(range(-60, 61, 3))
            drift_x = [random.uniform(-2, 2) for _ in tilt_angles]
            drift_y = [random.uniform(-2, 2) for _ in tilt_angles]

            return {
                "type": "motion",
                "x_data": tilt_angles,
                "y_data": [drift_x, drift_y],
                "labels": ["Drift X (pixels)", "Drift Y (pixels)"],
                "title": "Motion Correction",
                "x_label": "Tilt Angle (degrees)",
                "y_label": "Drift (pixels)",
            }

        elif plot_type == "ctf":
            # CTF estimation plot data
            tilt_angles = list(range(-60, 61, 3))
            defocus = [random.uniform(1.5, 4.0) for _ in tilt_angles]
            resolution = [random.uniform(3.0, 8.0) for _ in tilt_angles]

            return {
                "type": "ctf",
                "x_data": tilt_angles,
                "y_data": [defocus, resolution],
                "labels": ["Defocus (μm)", "Resolution (Å)"],
                "title": "CTF Estimation",
                "x_label": "Tilt Angle (degrees)",
                "y_label": "Value",
            }

        elif plot_type == "alignment":
            # Alignment quality plot data
            tilt_angles = list(range(-60, 61, 3))
            alignment_error = [random.uniform(0.5, 3.0) for _ in tilt_angles]

            return {
                "type": "alignment",
                "x_data": tilt_angles,
                "y_data": [alignment_error],
                "labels": ["Alignment Error (pixels)"],
                "title": "Alignment Quality",
                "x_label": "Tilt Angle (degrees)",
                "y_label": "Error (pixels)",
            }

        elif plot_type == "quality":
            # Overall quality score plot data
            tilt_angles = list(range(-60, 61, 3))
            quality_score = [random.uniform(60, 95) for _ in tilt_angles]

            return {
                "type": "quality",
                "x_data": tilt_angles,
                "y_data": [quality_score],
                "labels": ["Quality Score"],
                "title": "Overall Quality",
                "x_label": "Tilt Angle (degrees)",
                "y_label": "Quality Score (%)",
            }

        else:
            # Default empty plot data
            return {
                "type": "unknown",
                "x_data": [],
                "y_data": [],
                "labels": [],
                "title": "Unknown Plot Type",
                "x_label": "",
                "y_label": "",
            }

    def connect_processor_signals(self):
        """Connect processor signals to WebSocket broadcasts."""
        # This would need to be adapted for the Qt signal system
        # For now, we'll use a polling approach
        pass

    async def broadcast_stats_update(self, stats: ProcessingStats):
        """Broadcast statistics update to all WebSocket clients."""
        message = {
            "type": "stats",
            "stats": {
                "total_files": stats.total_files,
                "processed_files": stats.processed_files,
                "failed_files": stats.failed_files,
                "avg_processing_time": stats.avg_processing_time,
                "throughput_per_hour": stats.throughput_per_hour,
                "queue_size": stats.queue_size,
                "active_jobs": stats.active_jobs,
            },
        }
        await self.websocket_manager.broadcast(json.dumps(message))

    async def broadcast_job_update(self, job: ProcessingJob, event_type: str):
        """Broadcast job update to all WebSocket clients."""
        message = {
            "type": "job_update",
            "event": event_type,
            "job": {
                "job_id": job.job_id,
                "file_path": str(job.file_path),
                "status": job.status,
                "progress": job.progress,
            },
        }
        await self.websocket_manager.broadcast(json.dumps(message))

    async def broadcast_log_entry(self, log_entry: dict):
        """Broadcast log entry to all WebSocket clients."""
        message = {
            "type": "log",
            "timestamp": log_entry["timestamp"],
            "level": log_entry["level"],
            "message": log_entry["message"],
        }
        await self.websocket_manager.broadcast(json.dumps(message))

    def _calculate_overall_quality_score(self, metrics: dict) -> float:
        """Calculate overall quality score from metrics."""
        try:
            scores = []

            # Motion quality
            if "motion" in metrics and metrics["motion"]:
                motion_data = metrics["motion"]
                if "mean_motion" in motion_data:
                    motion_score = max(
                        0, 1.0 - motion_data["mean_motion"] / 5.0
                    )  # Normalize to 0-1
                    scores.append(motion_score)

            # CTF quality
            if "ctf" in metrics and metrics["ctf"]:
                ctf_data = metrics["ctf"]
                if "mean_cc" in ctf_data:
                    scores.append(ctf_data["mean_cc"])

            # Alignment quality
            if "alignment" in metrics and metrics["alignment"]:
                alignment_data = metrics["alignment"]
                if "mean_score" in alignment_data:
                    scores.append(alignment_data["mean_score"])

            return sum(scores) / len(scores) if scores else 0.0
        except Exception:
            return 0.0

    def _get_quality_color_coding(self, metrics: dict) -> str:
        """Get color coding based on quality score."""
        score = self._calculate_overall_quality_score(metrics)

        if score >= 0.8:
            return "green"
        elif score >= 0.6:
            return "yellow"
        elif score >= 0.3:
            return "orange"
        else:
            return "red"

    def _get_quality_recommendations(self, metrics: dict) -> list:
        """Get quality recommendations based on metrics."""
        recommendations = []

        try:
            # Motion recommendations
            if "motion" in metrics and metrics["motion"]:
                motion_data = metrics["motion"]
                if motion_data.get("mean_motion", 0) > 2.0:
                    recommendations.append(
                        "High motion detected - consider motion correction parameters"
                    )

            # CTF recommendations
            if "ctf" in metrics and metrics["ctf"]:
                ctf_data = metrics["ctf"]
                if ctf_data.get("mean_cc", 0) < 0.6:
                    recommendations.append(
                        "Low CTF correlation - check defocus range and CTF parameters"
                    )

            # Alignment recommendations
            if "alignment" in metrics and metrics["alignment"]:
                alignment_data = metrics["alignment"]
                if alignment_data.get("mean_score", 0) < 0.5:
                    recommendations.append(
                        "Poor alignment quality - review tilt series and alignment parameters"
                    )

            if not recommendations:
                score = self._calculate_overall_quality_score(metrics)
                if score >= 0.8:
                    recommendations.append(
                        "Excellent quality - processing parameters are optimal"
                    )
                elif score >= 0.6:
                    recommendations.append(
                        "Good quality - minor optimizations possible"
                    )
                else:
                    recommendations.append("Quality assessment in progress")

        except Exception:
            recommendations.append("Quality assessment unavailable")

        return recommendations

    def run(self, host: str = "0.0.0.0", port: int = 8080, debug: bool = False):
        """Run the web server."""
        logger.info(f"Starting AreTomo3 web server on {host}:{port}")
        uvicorn.run(
            self.app,
            host=host,
            port=port,
            log_level="debug" if debug else "info",
            reload=debug,
        )


def create_web_server(processor: RealTimeProcessor) -> AreTomo3WebAPI:
    """Factory function to create web server."""
    return AreTomo3WebAPI(processor)

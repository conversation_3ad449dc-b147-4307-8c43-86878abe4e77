#!/usr/bin/env python3
"""
Web Server for AreTomo3 GUI
Professional web interface with real-time monitoring and API.
"""

import os
import json
import logging
from pathlib import Path
from typing import Dict, List, Optional, Any
from datetime import datetime
import asyncio
import threading

logger = logging.getLogger(__name__)

# Try to import web framework dependencies
try:
    from flask import Flask, render_template, jsonify, request, send_file
    from flask_cors import CORS
    FLASK_AVAILABLE = True
except ImportError:
    FLASK_AVAILABLE = False
    logger.warning("Flask not available - web interface disabled")

try:
    import uvicorn
    from fastapi import FastAPI, HTTPException
    from fastapi.staticfiles import StaticFiles
    from fastapi.responses import H<PERSON><PERSON><PERSON>ponse, JSONResponse
    FASTAPI_AVAILABLE = True
except ImportError:
    FASTAPI_AVAILABLE = False
    logger.warning("FastAPI not available - advanced web features disabled")

try:
    import websockets
    WEBSOCKETS_AVAILABLE = True
except ImportError:
    WEBSOCKETS_AVAILABLE = False
    logger.warning("WebSockets not available - real-time updates disabled")


class WebServer:
    """Main web server for AreTomo3 GUI."""
    
    def __init__(self, host: str = "0.0.0.0", port: int = 8000):
        self.host = host
        self.port = port
        self.app = None
        self.server_thread = None
        self.is_running = False
        
        # Data storage
        self.processing_status = {}
        self.analysis_results = {}
        self.system_stats = {}
        
        if FLASK_AVAILABLE:
            self._setup_flask_app()
        else:
            logger.error("Flask not available - cannot create web server")
    
    def _setup_flask_app(self):
        """Set up Flask application."""
        self.app = Flask(__name__)
        CORS(self.app)  # Enable CORS for all routes
        
        # Register routes
        self._register_routes()
        
        logger.info("Flask web server configured")
    
    def _register_routes(self):
        """Register all web routes."""
        
        @self.app.route('/')
        def index():
            """Main dashboard page."""
            return render_template('dashboard.html')
        
        @self.app.route('/api/status')
        def get_status():
            """Get current processing status."""
            return jsonify({
                "status": "running",
                "timestamp": datetime.now().isoformat(),
                "processing_jobs": len(self.processing_status),
                "system_stats": self.system_stats
            })
        
        @self.app.route('/api/processing')
        def get_processing_status():
            """Get processing status for all jobs."""
            return jsonify(self.processing_status)
        
        @self.app.route('/api/processing/<job_id>')
        def get_job_status(job_id):
            """Get status for a specific job."""
            if job_id in self.processing_status:
                return jsonify(self.processing_status[job_id])
            else:
                return jsonify({"error": "Job not found"}), 404
        
        @self.app.route('/api/analysis')
        def get_analysis_results():
            """Get analysis results."""
            return jsonify(self.analysis_results)
        
        @self.app.route('/api/analysis/<dataset_id>')
        def get_dataset_analysis(dataset_id):
            """Get analysis results for a specific dataset."""
            if dataset_id in self.analysis_results:
                return jsonify(self.analysis_results[dataset_id])
            else:
                return jsonify({"error": "Dataset not found"}), 404
        
        @self.app.route('/api/plots/<plot_type>/<dataset_id>')
        def get_plot(plot_type, dataset_id):
            """Get plot for a dataset."""
            # This would serve generated plots
            plot_path = Path(f"plots/{dataset_id}_{plot_type}.png")
            if plot_path.exists():
                return send_file(plot_path)
            else:
                return jsonify({"error": "Plot not found"}), 404
        
        @self.app.route('/api/system')
        def get_system_info():
            """Get system information."""
            return jsonify(self.system_stats)
        
        @self.app.route('/api/datasets')
        def get_datasets():
            """Get list of all datasets."""
            datasets = []
            for job_id, status in self.processing_status.items():
                if "dataset" in status:
                    datasets.append({
                        "id": job_id,
                        "name": status["dataset"],
                        "status": status.get("status", "unknown"),
                        "progress": status.get("progress", 0)
                    })
            return jsonify(datasets)
        
        @self.app.route('/api/logs/<job_id>')
        def get_job_logs(job_id):
            """Get logs for a specific job."""
            # This would serve log files
            log_path = Path(f"logs/{job_id}.log")
            if log_path.exists():
                with open(log_path, 'r') as f:
                    logs = f.read()
                return jsonify({"logs": logs})
            else:
                return jsonify({"error": "Logs not found"}), 404
    
    def start_server(self):
        """Start the web server in a separate thread."""
        if not FLASK_AVAILABLE:
            logger.error("Cannot start web server - Flask not available")
            return False
        
        if self.is_running:
            logger.warning("Web server already running")
            return True
        
        try:
            self.server_thread = threading.Thread(
                target=self._run_server,
                daemon=True
            )
            self.server_thread.start()
            self.is_running = True
            
            logger.info(f"Web server started on http://{self.host}:{self.port}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to start web server: {e}")
            return False
    
    def _run_server(self):
        """Run the Flask server."""
        try:
            self.app.run(
                host=self.host,
                port=self.port,
                debug=False,
                threaded=True,
                use_reloader=False
            )
        except Exception as e:
            logger.error(f"Web server error: {e}")
            self.is_running = False
    
    def stop_server(self):
        """Stop the web server."""
        self.is_running = False
        logger.info("Web server stopped")
    
    def update_processing_status(self, job_id: str, status: Dict[str, Any]):
        """Update processing status for a job."""
        self.processing_status[job_id] = {
            **status,
            "last_updated": datetime.now().isoformat()
        }
    
    def update_analysis_results(self, dataset_id: str, results: Dict[str, Any]):
        """Update analysis results for a dataset."""
        self.analysis_results[dataset_id] = {
            **results,
            "last_updated": datetime.now().isoformat()
        }
    
    def update_system_stats(self, stats: Dict[str, Any]):
        """Update system statistics."""
        self.system_stats = {
            **stats,
            "last_updated": datetime.now().isoformat()
        }
    
    def get_server_info(self) -> Dict[str, Any]:
        """Get server information."""
        return {
            "host": self.host,
            "port": self.port,
            "is_running": self.is_running,
            "url": f"http://{self.host}:{self.port}",
            "flask_available": FLASK_AVAILABLE,
            "fastapi_available": FASTAPI_AVAILABLE,
            "websockets_available": WEBSOCKETS_AVAILABLE
        }


class WebSocketServer:
    """WebSocket server for real-time updates."""
    
    def __init__(self, host: str = "localhost", port: int = 8001):
        self.host = host
        self.port = port
        self.clients = set()
        self.server = None
        self.is_running = False
    
    async def register_client(self, websocket, path):
        """Register a new WebSocket client."""
        self.clients.add(websocket)
        logger.info(f"WebSocket client connected: {websocket.remote_address}")
        
        try:
            await websocket.wait_closed()
        finally:
            self.clients.remove(websocket)
            logger.info(f"WebSocket client disconnected: {websocket.remote_address}")
    
    async def broadcast_message(self, message: Dict[str, Any]):
        """Broadcast message to all connected clients."""
        if not self.clients:
            return
        
        message_json = json.dumps(message)
        disconnected_clients = set()
        
        for client in self.clients:
            try:
                await client.send(message_json)
            except Exception as e:
                logger.warning(f"Failed to send message to client: {e}")
                disconnected_clients.add(client)
        
        # Remove disconnected clients
        self.clients -= disconnected_clients
    
    def start_server(self):
        """Start the WebSocket server."""
        if not WEBSOCKETS_AVAILABLE:
            logger.error("Cannot start WebSocket server - websockets library not available")
            return False
        
        if self.is_running:
            logger.warning("WebSocket server already running")
            return True
        
        try:
            import websockets
            
            async def run_server():
                self.server = await websockets.serve(
                    self.register_client,
                    self.host,
                    self.port
                )
                self.is_running = True
                logger.info(f"WebSocket server started on ws://{self.host}:{self.port}")
                await self.server.wait_closed()
            
            # Run in event loop
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            
            server_thread = threading.Thread(
                target=lambda: loop.run_until_complete(run_server()),
                daemon=True
            )
            server_thread.start()
            
            return True
            
        except Exception as e:
            logger.error(f"Failed to start WebSocket server: {e}")
            return False
    
    def stop_server(self):
        """Stop the WebSocket server."""
        if self.server:
            self.server.close()
        self.is_running = False
        logger.info("WebSocket server stopped")
    
    def send_update(self, update_type: str, data: Dict[str, Any]):
        """Send update to all connected clients."""
        message = {
            "type": update_type,
            "data": data,
            "timestamp": datetime.now().isoformat()
        }
        
        # Schedule broadcast in event loop
        try:
            loop = asyncio.get_event_loop()
            loop.create_task(self.broadcast_message(message))
        except RuntimeError:
            # No event loop running
            pass


class WebInterface:
    """Main web interface coordinator."""
    
    def __init__(self, web_port: int = 8000, websocket_port: int = 8001):
        self.web_server = WebServer(port=web_port)
        self.websocket_server = WebSocketServer(port=websocket_port)
        
        logger.info("Web interface initialized")
    
    def start(self):
        """Start both web and WebSocket servers."""
        web_started = self.web_server.start_server()
        ws_started = self.websocket_server.start_server()
        
        return web_started or ws_started
    
    def stop(self):
        """Stop both servers."""
        self.web_server.stop_server()
        self.websocket_server.stop_server()
    
    def update_processing_status(self, job_id: str, status: Dict[str, Any]):
        """Update processing status and broadcast to clients."""
        self.web_server.update_processing_status(job_id, status)
        self.websocket_server.send_update("processing_update", {
            "job_id": job_id,
            "status": status
        })
    
    def update_analysis_results(self, dataset_id: str, results: Dict[str, Any]):
        """Update analysis results and broadcast to clients."""
        self.web_server.update_analysis_results(dataset_id, results)
        self.websocket_server.send_update("analysis_update", {
            "dataset_id": dataset_id,
            "results": results
        })
    
    def update_system_stats(self, stats: Dict[str, Any]):
        """Update system stats and broadcast to clients."""
        self.web_server.update_system_stats(stats)
        self.websocket_server.send_update("system_update", stats)
    
    def get_interface_info(self) -> Dict[str, Any]:
        """Get web interface information."""
        return {
            "web_server": self.web_server.get_server_info(),
            "websocket_server": {
                "host": self.websocket_server.host,
                "port": self.websocket_server.port,
                "is_running": self.websocket_server.is_running,
                "connected_clients": len(self.websocket_server.clients)
            }
        }


# Global web interface instance
web_interface = WebInterface()


def main():
    """Main entry point for web server."""
    import argparse
    
    parser = argparse.ArgumentParser(description="AreTomo3 GUI Web Server")
    parser.add_argument("--host", default="0.0.0.0", help="Host to bind to")
    parser.add_argument("--port", type=int, default=8000, help="Port to bind to")
    parser.add_argument("--ws-port", type=int, default=8001, help="WebSocket port")
    
    args = parser.parse_args()
    
    # Set up logging
    logging.basicConfig(level=logging.INFO)
    
    # Create and start web interface
    interface = WebInterface(web_port=args.port, websocket_port=args.ws_port)
    
    try:
        if interface.start():
            print(f"🌐 AreTomo3 GUI Web Interface")
            print(f"Web Server: http://{args.host}:{args.port}")
            print(f"WebSocket: ws://{args.host}:{args.ws_port}")
            print("Press Ctrl+C to stop")
            
            # Keep running
            import time
            while True:
                time.sleep(1)
        else:
            print("❌ Failed to start web interface")
            
    except KeyboardInterrupt:
        print("\n🛑 Stopping web interface...")
        interface.stop()
        print("✅ Web interface stopped")


if __name__ == "__main__":
    main()

#!/usr/bin/env python3
"""
Web Plot Server for AreTomo3 Results
Serves generated plots through a web interface for remote visualization
"""

import os
import json
import logging
from pathlib import Path
from typing import Dict, List, Any, Optional
from flask import Flask, render_template_string, jsonify, send_file, request
import threading
import time

logger = logging.getLogger(__name__)

class AreTomo3PlotServer:
    """Web server for serving AreTomo3 plots and results."""
    
    def __init__(self, host="0.0.0.0", port=8001):
        """Initialize the plot server."""
        self.host = host
        self.port = port
        self.app = Flask(__name__)
        self.app.config['SECRET_KEY'] = 'aretomo3_plot_server'
        self.running = False
        self.server_thread = None
        self.plot_registry = {}
        self.analysis_registry = {}
        
        self._setup_routes()
        logger.info(f"Plot server initialized on {host}:{port}")
    
    def _setup_routes(self):
        """Setup Flask routes."""
        
        @self.app.route('/')
        def index():
            """Main dashboard page."""
            return render_template_string(self._get_dashboard_template())
        
        @self.app.route('/api/datasets')
        def get_datasets():
            """Get list of available datasets."""
            datasets = []
            for dataset_id, data in self.plot_registry.items():
                analysis_data = self.analysis_registry.get(dataset_id, {})
                datasets.append({
                    'id': dataset_id,
                    'name': data.get('name', dataset_id),
                    'output_dir': data.get('output_dir', ''),
                    'plots': list(data.get('plots', {}).keys()),
                    'plot_count': len(data.get('plots', {})),
                    'timestamp': data.get('timestamp', ''),
                    'status': analysis_data.get('processing_status', {}),
                    'metrics': analysis_data.get('data_summary', {}).get('tilt_series_metrics', {})
                })
            return jsonify(datasets)
        
        @self.app.route('/api/dataset/<dataset_id>')
        def get_dataset_details(dataset_id):
            """Get detailed information about a dataset."""
            if dataset_id not in self.plot_registry:
                return jsonify({'error': 'Dataset not found'}), 404
            
            plot_data = self.plot_registry[dataset_id]
            analysis_data = self.analysis_registry.get(dataset_id, {})
            
            return jsonify({
                'id': dataset_id,
                'name': plot_data.get('name', dataset_id),
                'output_dir': plot_data.get('output_dir', ''),
                'plots': plot_data.get('plots', {}),
                'timestamp': plot_data.get('timestamp', ''),
                'analysis_data': analysis_data,
                'processing_status': analysis_data.get('processing_status', {}),
                'data_summary': analysis_data.get('data_summary', {})
            })
        
        @self.app.route('/api/plot/<dataset_id>/<plot_type>')
        def get_plot(dataset_id, plot_type):
            """Serve a specific plot image."""
            if dataset_id not in self.plot_registry:
                return jsonify({'error': 'Dataset not found'}), 404
            
            plots = self.plot_registry[dataset_id].get('plots', {})
            if plot_type not in plots:
                return jsonify({'error': 'Plot not found'}), 404
            
            plot_path = plots[plot_type]
            if not os.path.exists(plot_path):
                return jsonify({'error': 'Plot file not found'}), 404
            
            return send_file(plot_path, mimetype='image/png')
        
        @self.app.route('/api/plots/<dataset_id>')
        def get_all_plots(dataset_id):
            """Get all plots for a dataset."""
            if dataset_id not in self.plot_registry:
                return jsonify({'error': 'Dataset not found'}), 404
            
            plots = self.plot_registry[dataset_id].get('plots', {})
            plot_info = {}
            
            for plot_type, plot_path in plots.items():
                if os.path.exists(plot_path):
                    file_size = os.path.getsize(plot_path)
                    plot_info[plot_type] = {
                        'path': plot_path,
                        'size': file_size,
                        'url': f'/api/plot/{dataset_id}/{plot_type}',
                        'available': True
                    }
                else:
                    plot_info[plot_type] = {
                        'path': plot_path,
                        'available': False
                    }
            
            return jsonify(plot_info)
        
        @self.app.route('/api/refresh')
        def refresh_data():
            """Refresh all data."""
            return jsonify({
                'datasets': len(self.plot_registry),
                'timestamp': time.time()
            })
    
    def register_dataset(self, dataset_id: str, output_dir: str, plots: Dict[str, str], 
                        analysis_data: Optional[Dict[str, Any]] = None):
        """Register a dataset with its plots."""
        self.plot_registry[dataset_id] = {
            'name': dataset_id,
            'output_dir': output_dir,
            'plots': plots,
            'timestamp': time.time()
        }
        
        if analysis_data:
            self.analysis_registry[dataset_id] = analysis_data
        
        logger.info(f"Registered dataset {dataset_id} with {len(plots)} plots")
    
    def update_dataset(self, dataset_id: str, plots: Dict[str, str], 
                      analysis_data: Optional[Dict[str, Any]] = None):
        """Update an existing dataset."""
        if dataset_id in self.plot_registry:
            self.plot_registry[dataset_id]['plots'].update(plots)
            self.plot_registry[dataset_id]['timestamp'] = time.time()
            
            if analysis_data:
                self.analysis_registry[dataset_id] = analysis_data
            
            logger.info(f"Updated dataset {dataset_id} with {len(plots)} plots")
        else:
            logger.warning(f"Attempted to update non-existent dataset: {dataset_id}")
    
    def remove_dataset(self, dataset_id: str):
        """Remove a dataset from the registry."""
        if dataset_id in self.plot_registry:
            del self.plot_registry[dataset_id]
            if dataset_id in self.analysis_registry:
                del self.analysis_registry[dataset_id]
            logger.info(f"Removed dataset: {dataset_id}")
    
    def start_server(self):
        """Start the web server."""
        if self.running:
            logger.warning("Server is already running")
            return
        
        self.running = True
        
        def run_server():
            try:
                self.app.run(host=self.host, port=self.port, debug=False, threaded=True)
            except Exception as e:
                logger.error(f"Error running server: {e}")
                self.running = False
        
        self.server_thread = threading.Thread(target=run_server, daemon=True)
        self.server_thread.start()
        
        logger.info(f"Plot server started on http://{self.host}:{self.port}")
    
    def stop_server(self):
        """Stop the web server."""
        if not self.running:
            return
        
        self.running = False
        logger.info("Plot server stopped")
    
    def get_server_status(self) -> Dict[str, Any]:
        """Get server status information."""
        return {
            'running': self.running,
            'host': self.host,
            'port': self.port,
            'url': f"http://{self.host}:{self.port}",
            'datasets': len(self.plot_registry),
            'total_plots': sum(len(data.get('plots', {})) for data in self.plot_registry.values())
        }
    
    def _get_dashboard_template(self) -> str:
        """Get the HTML template for the dashboard."""
        return '''
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AreTomo3 Plot Dashboard</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            border-radius: 10px;
            margin-bottom: 20px;
            text-align: center;
        }
        .dataset-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(400px, 1fr));
            gap: 20px;
        }
        .dataset-card {
            background: white;
            border-radius: 10px;
            padding: 20px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            transition: transform 0.2s;
        }
        .dataset-card:hover {
            transform: translateY(-2px);
        }
        .dataset-title {
            font-size: 1.2em;
            font-weight: bold;
            margin-bottom: 10px;
            color: #333;
        }
        .plot-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
            gap: 10px;
            margin-top: 15px;
        }
        .plot-item {
            text-align: center;
            padding: 10px;
            background: #f8f9fa;
            border-radius: 5px;
            cursor: pointer;
            transition: background-color 0.2s;
        }
        .plot-item:hover {
            background: #e9ecef;
        }
        .plot-image {
            max-width: 100%;
            height: auto;
            border-radius: 5px;
            margin-bottom: 5px;
        }
        .status-indicator {
            display: inline-block;
            width: 10px;
            height: 10px;
            border-radius: 50%;
            margin-right: 5px;
        }
        .status-complete { background-color: #28a745; }
        .status-partial { background-color: #ffc107; }
        .status-pending { background-color: #6c757d; }
        .refresh-btn {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin-bottom: 20px;
        }
        .refresh-btn:hover {
            background: #0056b3;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>🔬 AreTomo3 Plot Dashboard</h1>
        <p>Real-time visualization of AreTomo3 processing results</p>
    </div>
    
    <button class="refresh-btn" onclick="loadDatasets()">🔄 Refresh Data</button>
    
    <div id="datasets-container" class="dataset-grid">
        <p>Loading datasets...</p>
    </div>

    <script>
        async function loadDatasets() {
            try {
                const response = await fetch('/api/datasets');
                const datasets = await response.json();
                
                const container = document.getElementById('datasets-container');
                
                if (datasets.length === 0) {
                    container.innerHTML = '<p>No datasets available yet.</p>';
                    return;
                }
                
                container.innerHTML = datasets.map(dataset => `
                    <div class="dataset-card">
                        <div class="dataset-title">
                            <span class="status-indicator status-${getStatusClass(dataset.status)}"></span>
                            ${dataset.name}
                        </div>
                        <p><strong>Plots:</strong> ${dataset.plot_count}</p>
                        <p><strong>Directory:</strong> ${dataset.output_dir}</p>
                        <div class="plot-grid">
                            ${dataset.plots.map(plot => `
                                <div class="plot-item" onclick="viewPlot('${dataset.id}', '${plot}')">
                                    <img src="/api/plot/${dataset.id}/${plot}" class="plot-image" 
                                         alt="${plot}" title="${plot}" loading="lazy">
                                    <div>${plot.replace('_', ' ')}</div>
                                </div>
                            `).join('')}
                        </div>
                    </div>
                `).join('');
            } catch (error) {
                console.error('Error loading datasets:', error);
                document.getElementById('datasets-container').innerHTML = 
                    '<p>Error loading datasets. Please refresh the page.</p>';
            }
        }
        
        function getStatusClass(status) {
            const completed = Object.values(status || {}).filter(Boolean).length;
            const total = Object.keys(status || {}).length;
            
            if (completed === total && total > 0) return 'complete';
            if (completed > 0) return 'partial';
            return 'pending';
        }
        
        function viewPlot(datasetId, plotType) {
            window.open(`/api/plot/${datasetId}/${plotType}`, '_blank');
        }
        
        // Load datasets on page load
        loadDatasets();
        
        // Auto-refresh every 30 seconds
        setInterval(loadDatasets, 30000);
    </script>
</body>
</html>
        '''

# Global plot server instance
plot_server = None

def start_plot_server(host="0.0.0.0", port=8001) -> AreTomo3PlotServer:
    """Start the global plot server."""
    global plot_server
    if plot_server is None:
        plot_server = AreTomo3PlotServer(host=host, port=port)
    
    if not plot_server.running:
        plot_server.start_server()
    
    return plot_server

def get_plot_server() -> Optional[AreTomo3PlotServer]:
    """Get the global plot server instance."""
    return plot_server

def stop_plot_server():
    """Stop the global plot server."""
    global plot_server
    if plot_server:
        plot_server.stop_server()
        plot_server = None

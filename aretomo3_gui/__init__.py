"""
AreTomo3 GUI - Professional GUI for AreTomo3 tomographic reconstruction software.

A modern, feature-rich graphical user interface for AreTomo3 that provides:
- Enhanced analysis and visualization capabilities
- Professional UI with dark/light themes
- Comprehensive file management and batch processing
- Advanced parameter configuration
- Real-time system monitoring
"""

__version__ = "2.0.0"
__author__ = "AreTomo3 Team"
__email__ = "<EMAIL>"

__all__ = ["__version__"]


def get_main_window():
    """Lazy import of main window to avoid circular imports."""
    from aretomo3_gui.gui.main_window import AreTomoGUI

    return AreTomoGUI

# Suppress common warnings for professional deployment (only in production)
import os
if not os.environ.get('PYTEST_CURRENT_TEST') and not os.environ.get('QT_QPA_PLATFORM') == 'offscreen':
    try:
        from .utils.warning_suppression import suppress_common_warnings
        suppress_common_warnings()
    except ImportError:
        pass

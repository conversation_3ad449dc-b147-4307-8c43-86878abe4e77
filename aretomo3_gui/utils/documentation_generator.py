#!/usr/bin/env python3
"""
AreTomo3 GUI Documentation Generator
Automated documentation generation for GUI components, parameters, and workflows.
"""

import ast
import inspect
import json
import logging
import re
from dataclasses import dataclass, field
from datetime import datetime
from pathlib import Path
from typing import Any, Dict, List, Optional, Tuple

logger = logging.getLogger(__name__)


@dataclass
class DocumentationSection:
    """Documentation section structure."""

    title: str
    content: str
    subsections: List["DocumentationSection"] = field(default_factory=list)
    code_examples: List[str] = field(default_factory=list)
    images: List[str] = field(default_factory=list)
    level: int = 1

    # TODO: Refactor class - Class 'DocumentationGenerator' too long (569
    # lines)


class DocumentationGenerator:
    """
    Automated documentation generator for AreTomo3 GUI.
    Generates comprehensive documentation from code, comments, and metadata.
    """

    def __init__(self, output_dir: Path = None):
        """Initialize the documentation generator."""
        self.output_dir = output_dir or Path.cwd() / "docs"
        self.output_dir.mkdir(parents=True, exist_ok=True)

        # Documentation structure
        self.sections: List[DocumentationSection] = []
        self.api_docs: Dict[str, Any] = {}
        self.parameter_docs: Dict[str, Any] = {}
        self.workflow_docs: Dict[str, Any] = {}

        # Source code paths
        self.source_paths = [
            Path(__file__).parent.parent,  # Main source directory
        ]

        logger.info(f"Documentation Generator initialized - Output: {self.output_dir}")

    def generate_full_documentation(self) -> Path:
        """Generate complete documentation suite."""
        logger.info("Starting full documentation generation...")

        try:
            # Generate different documentation types
            self._generate_api_documentation()
            self._generate_parameter_documentation()
            self._generate_workflow_documentation()
            self._generate_user_guide()
            self._generate_developer_guide()

            # Create main index
            index_file = self._generate_index()

            logger.info(f"Documentation generation completed: {index_file}")
            return index_file

        except Exception as e:
            logger.error(f"Error generating documentation: {e}")
            raise

    def _generate_api_documentation(self):
        """Generate API documentation from source code."""
        logger.info("Generating API documentation...")

        api_sections = []

        # Scan source directories
        for source_path in self.source_paths:
            if source_path.exists():
                api_sections.extend(self._scan_python_modules(source_path))

        # Create API documentation file
        api_content = self._format_api_documentation(api_sections)
        api_file = self.output_dir / "api_reference.md"
        api_file.write_text(api_content)

        logger.info(f"API documentation generated: {api_file}")

    def _scan_python_modules(self, source_path: Path) -> List[DocumentationSection]:
        """Scan Python modules for documentation."""
        sections = []

        for py_file in source_path.rglob("*.py"):
            if py_file.name.startswith("__"):
                continue

            try:
                module_doc = self._extract_module_documentation(py_file)
                if module_doc:
                    sections.append(module_doc)
            except Exception as e:
                logger.warning(f"Error processing {py_file}: {e}")

        return sections

    def _extract_module_documentation(
        self, py_file: Path
    ) -> Optional[DocumentationSection]:
        """Extract documentation from a Python module."""
        try:
            with open(py_file, encoding="utf-8") as f:
                source = f.read()

            # Parse AST
            tree = ast.parse(source)

            # Extract module docstring
            module_docstring = ast.get_docstring(tree)
            if not module_docstring:
                return None

            # Create module section
            module_name = py_file.stem
            section = DocumentationSection(
                title=f"Module: {module_name}", content=module_docstring, level=2
            )

            # Extract classes and functions
            for node in ast.walk(tree):
                if isinstance(node, ast.ClassDef):
                    class_doc = self._extract_class_documentation(node, source)
                    if class_doc:
                        section.subsections.append(class_doc)
                elif isinstance(node, ast.FunctionDef) and not node.name.startswith(
                    "_"
                ):
                    func_doc = self._extract_function_documentation(node, source)
                    if func_doc:
                        section.subsections.append(func_doc)

            return section

        except Exception as e:
            logger.warning(f"Error extracting documentation from {py_file}: {e}")
            return None

    def _extract_class_documentation(
        self, node: ast.ClassDef, source: str
    ) -> Optional[DocumentationSection]:
        """Extract documentation from a class."""
        docstring = ast.get_docstring(node)
        if not docstring:
            return None

        section = DocumentationSection(
            title=f"Class: {node.name}", content=docstring, level=3
        )

        # Extract methods
        for item in node.body:
            if isinstance(item, ast.FunctionDef) and not item.name.startswith("_"):
                method_doc = self._extract_function_documentation(item, source)
                if method_doc:
                    method_doc.title = f"Method: {item.name}"
                    method_doc.level = 4
                    section.subsections.append(method_doc)

        return section

    def _extract_function_documentation(
        self, node: ast.FunctionDef, source: str
    ) -> Optional[DocumentationSection]:
        """Extract documentation from a function."""
        docstring = ast.get_docstring(node)
        if not docstring:
            return None

        # Extract function signature
        signature = self._get_function_signature(node)

        content = f"**Signature:** `{signature}`\n\n{docstring}"

        return DocumentationSection(
            title=f"Function: {node.name}", content=content, level=3
        )

    def _get_function_signature(self, node: ast.FunctionDef) -> str:
        """Get function signature as string."""
        args = []

        # Regular arguments
        for arg in node.args.args:
            args.append(arg.arg)

        # Default arguments
        defaults_offset = len(node.args.args) - len(node.args.defaults)
        for i, default in enumerate(node.args.defaults):
            arg_index = defaults_offset + i
            if arg_index < len(node.args.args):
                args[arg_index] += f"={ast.unparse(default)}"

        # Keyword-only arguments
        for arg in node.args.kwonlyargs:
            args.append(f"{arg.arg}=...")

        return f"{node.name}({', '.join(args)})"

    def _generate_parameter_documentation(self):
        """Generate parameter documentation."""
        logger.info("Generating parameter documentation...")

        # Extract parameter information from parameter manager
        try:
            from ..gui.components.parameter_manager import ParameterManager

            param_manager = ParameterManager()

            param_sections = []

            # Get parameter categories
            categories = param_manager.get_parameter_categories()

            for category, params in categories.items():
                category_section = DocumentationSection(
                    title=f"Parameter Category: {category}",
                    content=f"Parameters for {category} configuration.",
                    level=2,
                )

                for param_name, param_info in params.items():
                    param_doc = self._format_parameter_documentation(
                        param_name, param_info
                    )
                    category_section.subsections.append(param_doc)

                param_sections.append(category_section)

            # Create parameter documentation file
            param_content = self._format_parameter_sections(param_sections)
            param_file = self.output_dir / "parameters.md"
            param_file.write_text(param_content)

            logger.info(f"Parameter documentation generated: {param_file}")

        except Exception as e:
            logger.warning(f"Error generating parameter documentation: {e}")

    def _format_parameter_documentation(
        self, param_name: str, param_info: Dict[str, Any]
    ) -> DocumentationSection:
        """Format parameter documentation."""
        content_parts = []

        # Description
        if "description" in param_info:
            content_parts.append(param_info["description"])

        # Type and default value
        if "type" in param_info:
            content_parts.append(f"**Type:** {param_info['type']}")

        if "default" in param_info:
            content_parts.append(f"**Default:** `{param_info['default']}`")

        # Valid range or options
        if "range" in param_info:
            content_parts.append(f"**Range:** {param_info['range']}")

        if "options" in param_info:
            content_parts.append(f"**Options:** {', '.join(param_info['options'])}")

        # Example
        if "example" in param_info:
            content_parts.append(f"**Example:** `{param_info['example']}`")

        return DocumentationSection(
            title=param_name, content="\n\n".join(content_parts), level=3
        )

    # TODO: Refactor function - Function '_generate_workflow_documentation'
    # too long (60 lines)
    def _generate_workflow_documentation(self):
        """Generate workflow documentation."""
        logger.info("Generating workflow documentation...")

        workflows = [
            {
                "title": "Basic Tomogram Processing",
                "description": "Standard workflow for processing tilt series data",
                "steps": [
                    "Load tilt series data",
                    "Configure processing parameters",
                    "Run AreTomo3 processing",
                    "Analyze results",
                    "Export final tomogram",
                ],
            },
            {
                "title": "Batch Processing",
                "description": "Processing multiple datasets efficiently",
                "steps": [
                    "Set up batch processing directory",
                    "Configure batch parameters",
                    "Start batch processing",
                    "Monitor progress",
                    "Review batch results",
                ],
            },
            {
                "title": "Real-time Analysis",
                "description": "Live monitoring and analysis during data collection",
                "steps": [
                    "Configure watch directory",
                    "Enable real-time processing",
                    "Monitor incoming data",
                    "Review quality metrics",
                    "Adjust parameters as needed",
                ],
            },
        ]

        workflow_sections = []
        for workflow in workflows:
            section = DocumentationSection(
                title=workflow["title"], content=workflow["description"], level=2
            )

            # Add steps
            steps_content = "## Steps\n\n"
            for i, step in enumerate(workflow["steps"], 1):
                steps_content += f"{i}. {step}\n"

            section.content += f"\n\n{steps_content}"
            workflow_sections.append(section)

        # Create workflow documentation file
        workflow_content = self._format_workflow_sections(workflow_sections)
        workflow_file = self.output_dir / "workflows.md"
        workflow_file.write_text(workflow_content)

        logger.info(f"Workflow documentation generated: {workflow_file}")

    # TODO: Refactor function - Function '_generate_user_guide' too long (70
    # lines)
    def _generate_user_guide(self):
        """Generate user guide."""
        logger.info("Generating user guide...")

        user_guide_content = """# AreTomo3 GUI User Guide

## Introduction

AreTomo3 GUI is a comprehensive graphical interface for the AreTomo3 tomogram reconstruction software. This guide will help you get started with processing your cryo-electron tomography data.

## Getting Started

### Installation

1. Install AreTomo3 GUI using pip:
   ```bash
   pip install aretomo3-gui
   ```

2. Launch the application:
   ```bash
   python -m aretomo3_gui
   ```

### First Steps

1. **Load your data**: Use the file browser to select your tilt series data
2. **Configure parameters**: Set up processing parameters in the Parameters tab
3. **Start processing**: Click the "Start Processing" button
4. **Monitor progress**: Watch the progress in the real-time analysis tab
5. **Review results**: Examine the generated plots and quality metrics

## Main Interface

### Tabs Overview

- **Main**: Primary control center for data loading and processing
- **Parameters**: Detailed parameter configuration
- **Real-time Analysis**: Live monitoring and quality assessment
- **Enhanced Analysis**: Comprehensive result analysis and visualization
- **Batch Processing**: Multi-dataset processing capabilities
- **Viewer**: 3D visualization of tomograms

### Key Features

- **Interactive plots**: Zoom and pan through your data
- **Real-time monitoring**: Live updates during processing
- **Quality assessment**: Automated quality metrics
- **Batch processing**: Handle multiple datasets efficiently
- **Web dashboard**: Remote monitoring capabilities

## Troubleshooting

### Common Issues

1. **Matplotlib plotting errors**: Ensure matplotlib is properly installed
2. **Interactive plots not working**: Install plotly and PyQt5-WebEngine
3. **CTF viewer issues**: Check that CTF data is available

### Getting Help

- Check the logs tab for detailed error messages
- Use the web dashboard for remote monitoring
- Consult the API reference for advanced usage
"""

        user_guide_file = self.output_dir / "user_guide.md"
        user_guide_file.write_text(user_guide_content)

        logger.info(f"User guide generated: {user_guide_file}")

    # TODO: Refactor function - Function '_generate_developer_guide' too long
    # (78 lines)
    def _generate_developer_guide(self):
        """Generate developer guide."""
        logger.info("Generating developer guide...")

        dev_guide_content = """# AreTomo3 GUI Developer Guide

## Architecture Overview

AreTomo3 GUI is built using PyQt5 and follows a modular architecture with clear separation of concerns.

### Core Components

- **GUI Layer**: PyQt5-based user interface components
- **Processing Layer**: AreTomo3 integration and job management
- **Analysis Layer**: Data analysis and visualization
- **Core Services**: Configuration, logging, and system monitoring

### Directory Structure

```
src/aretomo3_gui/
├── gui/                 # GUI components
│   ├── tabs/           # Tab implementations
│   ├── components/     # Reusable UI components
│   └── viewers/        # Data viewers
├── core/               # Core services
├── analysis/           # Analysis modules
├── utils/              # Utility functions
└── web/                # Web interface
```

## Development Setup

### Prerequisites

- Python 3.8+
- PyQt5
- Required dependencies (see requirements.txt)

### Installation

1. Clone the repository
2. Install in development mode:
   ```bash
   pip install -e .
   ```

### Running Tests

```bash
python -m pytest tests/
```

## Contributing

### Code Style

- Follow PEP 8 guidelines
- Use type hints where appropriate
- Document all public methods and classes
- Write comprehensive tests

### Adding New Features

1. Create feature branch
2. Implement feature with tests
3. Update documentation
4. Submit pull request

## API Reference

See the API reference documentation for detailed information about classes and methods.
"""

        dev_guide_file = self.output_dir / "developer_guide.md"
        dev_guide_file.write_text(dev_guide_content)

        logger.info(f"Developer guide generated: {dev_guide_file}")

    # TODO: Refactor function - Function '_generate_index' too long (51 lines)
    def _generate_index(self) -> Path:
        """Generate main documentation index."""
        index_content = (
            "# AreTomo3 GUI Documentation\n\n"
            f"*Generated on {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}*\n\n"
            "## Documentation Sections\n\n"
            "### User Documentation\n"
            "- [User Guide](user_guide.md) - Getting started and basic usage\n"
            "- [Workflows](workflows.md) - Common processing workflows\n"
            "- [Parameters](parameters.md) - Complete parameter reference\n\n"
            "### Developer Documentation\n"
            "- [Developer Guide](developer_guide.md) - Development setup and guidelines\n"
            "- [API Reference](api_reference.md) - Complete API documentation\n\n"
            "## Quick Start\n\n"
            "1. **Installation**: `pip install aretomo3-gui`\n"
            "2. **Launch**: `python -m aretomo3_gui`\n"
            "3. **Load data**: Use the file browser in the Main tab\n"
            "4. **Configure**: Set parameters in the Parameters tab\n"
            '5. **Process**: Click "Start Processing"\n'
            "6. **Analyze**: Review results in the Enhanced Analysis tab\n\n"
            "## Features\n\n"
            "- **Real-time processing**: Live monitoring during data collection\n"
            "- **Interactive visualization**: Zoom and pan through plots\n"
            "- **Batch processing**: Handle multiple datasets efficiently\n"
            "- **Web dashboard**: Remote monitoring capabilities\n"
            "- **Quality assessment**: Automated quality metrics\n"
            "- **3D visualization**: Integrated tomogram viewer\n\n"
            "## Support\n\n"
            "For issues and questions:\n"
            "- Check the troubleshooting section in the User Guide\n"
            "- Review the logs tab for error messages\n"
            "- Consult the API reference for advanced usage\n\n"
            "---\n\n"
            "*AreTomo3 GUI - Comprehensive tomogram reconstruction interface*\n"
        )

        index_file = self.output_dir / "README.md"
        index_file.write_text(index_content)

        return index_file

    def _format_api_documentation(self, sections: List[DocumentationSection]) -> str:
        """Format API documentation sections."""
        content = "# API Reference\n\n"
        content += "*Auto-generated API documentation*\n\n"

        for section in sections:
            content += self._format_section(section)

        return content

    def _format_parameter_sections(self, sections: List[DocumentationSection]) -> str:
        """Format parameter documentation sections."""
        content = "# Parameter Reference\n\n"
        content += "*Complete parameter documentation*\n\n"

        for section in sections:
            content += self._format_section(section)

        return content

    def _format_workflow_sections(self, sections: List[DocumentationSection]) -> str:
        """Format workflow documentation sections."""
        content = "# Workflow Guide\n\n"
        content += "*Common processing workflows*\n\n"

        for section in sections:
            content += self._format_section(section)

        return content

    def _format_section(self, section: DocumentationSection) -> str:
        """Format a documentation section."""
        content = ""

        # Add title with appropriate heading level
        heading = "#" * section.level
        content += f"{heading} {section.title}\n\n"

        # Add content
        if section.content:
            content += f"{section.content}\n\n"

        # Add code examples
        for example in section.code_examples:
            content += f"```python\n{example}\n```\n\n"

        # Add subsections
        for subsection in section.subsections:
            content += self._format_section(subsection)

        return content


# Global documentation generator instance
documentation_generator = DocumentationGenerator()


def generate_documentation(output_dir: Path = None) -> Path:
    """Convenience function to generate documentation."""
    if output_dir:
        documentation_generator.output_dir = output_dir

    return documentation_generator.generate_full_documentation()


if __name__ == "__main__":
    # Generate documentation when script is executed directly
    docs_path = generate_documentation()
    logger.info(f"Documentation generated: {docs_path}")

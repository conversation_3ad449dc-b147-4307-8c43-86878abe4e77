"""
Warning suppression for professional deployment
Enhanced version with font configuration fixes
"""

import warnings
import os
import sys

def suppress_common_warnings():
    """Suppress common warnings for cleaner professional output."""
    
    # Suppress plotly warnings about outdated CDN
    warnings.filterwarnings("ignore", message=".*plotly-latest.*")
    
    # Suppress vispy warnings
    warnings.filterwarnings("ignore", category=UserWarning, module="vispy")
    
    # Suppress matplotlib warnings
    warnings.filterwarnings("ignore", category=UserWarning, module="matplotlib")
    
    # Suppress PyQt warnings
    warnings.filterwarnings("ignore", category=DeprecationWarning, module="PyQt6")
    
    # Fix fontconfig warnings - professional approach
    if "FONTCONFIG_FILE" not in os.environ:
        # Unset problematic fontconfig environment
        os.environ.pop('FONTCONFIG_FILE', None)
        os.environ.pop('FONTCONFIG_PATH', None)
    
    # Set professional environment variables
    os.environ.setdefault('QT_LOGGING_RULES', '*.debug=false;qt.qpa.*=false')
    os.environ.setdefault('QT_ASSUME_STDERR_HAS_CONSOLE', '1')
    
    # Suppress Pango warnings
    os.environ.setdefault('PANGO_ENABLE_BACKEND', 'fc')
    
    # Redirect stderr for Pango warnings (professional approach)
    if hasattr(sys, '_called_from_test'):
        # Don't suppress during testing
        pass
    else:
        # Suppress Pango critical warnings in production
        import logging
        logging.getLogger('pango').setLevel(logging.ERROR)

def setup_professional_environment():
    """Set up professional environment for clean output."""
    
    # Professional Qt settings
    os.environ.setdefault('QT_AUTO_SCREEN_SCALE_FACTOR', '1')
    os.environ.setdefault('QT_ENABLE_HIGHDPI_SCALING', '1')
    os.environ.setdefault('QT_SCALE_FACTOR_ROUNDING_POLICY', 'RoundPreferFloor')
    
    # Professional matplotlib settings
    os.environ.setdefault('MPLBACKEND', 'Qt5Agg')
    
    # Professional display settings
    if 'DISPLAY' not in os.environ and os.name != 'nt':
        os.environ['DISPLAY'] = ':0'

# Auto-setup when imported (only in production, not during testing)
import os
if not os.environ.get('PYTEST_CURRENT_TEST') and not os.environ.get('QT_QPA_PLATFORM') == 'offscreen':
    suppress_common_warnings()
    setup_professional_environment()

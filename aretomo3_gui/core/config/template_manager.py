"""Configuration management for templates and settings."""

import json
import logging
from dataclasses import dataclass
from pathlib import Path
from typing import Any, Dict, Optional

import yaml

logger = logging.getLogger(__name__)


@dataclass
class ProcessingTemplate:
    """Template for processing parameters."""

    name: str
    description: str
    parameters: Dict[str, Any]
    metadata: Dict[str, Any]
    version: str = "1.0"


class ConfigManager:
    """Manages processing templates and settings."""

    def __init__(self):
        """Initialize the instance."""
        self.config_dir = Path.home() / ".aretomo3"
        self.templates_dir = self.config_dir / "templates"
        self.templates_dir.mkdir(parents=True, exist_ok=True)

        self.templates: Dict[str, ProcessingTemplate] = {}
        self.load_templates()

    def save_template(self, template: ProcessingTemplate):
        """Save a processing template to disk."""
        filepath = self.templates_dir / f"{template.name}.yaml"

        data = {
            "name": template.name,
            "description": template.description,
            "parameters": template.parameters,
            "metadata": template.metadata,
            "version": template.version,
        }

        with open(filepath, "w") as f:
            yaml.safe_dump(data, f)

        self.templates[template.name] = template

    def load_templates(self):
        """Load all templates from disk."""
        self.templates.clear()

        for file in self.templates_dir.glob("*.yaml"):
            try:
                with open(file) as f:
                    data = yaml.safe_load(f)

                template = ProcessingTemplate(
                    name=data["name"],
                    description=data["description"],
                    parameters=data["parameters"],
                    metadata=data.get("metadata", {}),
                    version=data.get("version", "1.0"),
                )

                self.templates[template.name] = template

            except Exception as e:
                logger.info(f"Error loading template {file}: {e}")

    def get_template(self, name: str) -> Optional[ProcessingTemplate]:
        """Get a template by name."""
        return self.templates.get(name)

    def delete_template(self, name: str):
        """Delete a template."""
        if name in self.templates:
            filepath = self.templates_dir / f"{name}.yaml"
            try:
                filepath.unlink()
                del self.templates[name]
            except Exception as e:
                logger.info(f"Error deleting template {name}: {e}")

    def list_templates(self) -> Dict[str, str]:
        """Get a dictionary of template names and descriptions."""
        return {name: template.description for name, template in self.templates.items()}

    def validate_template(self, template: ProcessingTemplate) -> bool:
        """Validate template parameters."""
        required_params = {"pixel_size", "voltage", "gpu_id", "max_iterations"}

        return all(param in template.parameters for param in required_params)

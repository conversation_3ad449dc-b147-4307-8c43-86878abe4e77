#!/usr/bin/env python3
"""
Enhanced File Organization System for AreTomo3 GUI.
Provides intelligent file management, organization, and cleanup.
"""

import hashlib
import json
import logging
import os
import shutil
import time
from dataclasses import asdict, dataclass
from datetime import datetime, timedelta
from pathlib import Path
from typing import Any, Dict, List, Optional, Tuple

logger = logging.getLogger(__name__)


@dataclass
class FileMetadata:
    """Metadata for organized files."""

    file_path: str
    original_path: str
    file_type: str
    size_bytes: int
    created_time: float
    modified_time: float
    checksum: str
    series_name: str
    processing_stage: str
    quality_score: Optional[float] = None
    tags: List[str] = None

    def __post_init__(self):
        """Execute __post_init__ operation."""
        if self.tags is None:
            self.tags = []


class FileOrganizer:
    """Enhanced file organization system for AreTomo3 processing."""

    def __init__(self, base_directory: str):
        """Initialize file organizer."""
        self.base_dir = Path(base_directory)
        self.base_dir.mkdir(parents=True, exist_ok=True)

        # Create standard directory structure
        self.directories = {
            "raw": self.base_dir / "raw_data",
            "processed": self.base_dir / "processed",
            "tomograms": self.base_dir / "tomograms",
            "alignments": self.base_dir / "alignments",
            "logs": self.base_dir / "logs",
            "metadata": self.base_dir / "metadata",
            "quality_control": self.base_dir / "quality_control",
            "backups": self.base_dir / "backups",
            "temp": self.base_dir / "temp",
            "exports": self.base_dir / "exports",
        }

        # Create directories
        for directory in self.directories.values():
            directory.mkdir(parents=True, exist_ok=True)

        # File metadata storage
        self.metadata_file = self.directories["metadata"] / "file_registry.json"
        self.file_registry: Dict[str, FileMetadata] = {}

        # Load existing metadata
        self.load_file_registry()

        logger.info(f"File organizer initialized with base directory: { self.base_dir}")

    def organize_input_files(
        self, input_files: List[str], series_name: str
    ) -> Dict[str, str]:
        """Organize input files into the raw data directory."""
        organized_files = {}

        try:
            # Create series-specific directory
            series_dir = self.directories["raw"] / series_name
            series_dir.mkdir(parents=True, exist_ok=True)

            for input_file in input_files:
                input_path = Path(input_file)
                if not input_path.exists():
                    logger.warning(f"Input file not found: {input_file}")
                    continue

                # Determine file type and create appropriate subdirectory
                file_type = self.determine_file_type(input_path)
                type_dir = series_dir / file_type
                type_dir.mkdir(parents=True, exist_ok=True)

                # Copy file to organized location
                organized_path = type_dir / input_path.name
                if not organized_path.exists():
                    shutil.copy2(input_file, organized_path)

                # Create metadata
                metadata = self.create_file_metadata(
                    organized_path, input_file, file_type, series_name, "raw"
                )
                self.register_file(str(organized_path), metadata)

                organized_files[input_file] = str(organized_path)
                logger.debug(f"Organized {input_file} -> {organized_path}")

            self.save_file_registry()
            logger.info(
                f"Organized { len(organized_files)} files for series: {series_name}"
            )

        except Exception as e:
            logger.error(f"Error organizing input files: {e}")

        return organized_files

    def organize_output_files(
        self, output_directory: str, series_name: str
    ) -> Dict[str, str]:
        """Organize output files from AreTomo3 processing."""
        organized_files = {}

        try:
            output_path = Path(output_directory)
            if not output_path.exists():
                logger.warning(f"Output directory not found: {output_directory}")
                return organized_files

            # Scan for output files
            output_files = list(output_path.rglob("*"))
            output_files = [f for f in output_files if f.is_file()]

            for output_file in output_files:
                # Determine output file category
                category = self.categorize_output_file(output_file)

                # Create target directory
                target_dir = self.directories[category] / series_name
                target_dir.mkdir(parents=True, exist_ok=True)

                # Move file to organized location
                organized_path = target_dir / output_file.name
                if not organized_path.exists():
                    shutil.move(str(output_file), str(organized_path))

                # Create metadata
                metadata = self.create_file_metadata(
                    organized_path, str(output_file), category, series_name, "processed"
                )
                self.register_file(str(organized_path), metadata)

                organized_files[str(output_file)] = str(organized_path)
                logger.debug(
                    f"Organized output { output_file.name} -> {organized_path}"
                )

            self.save_file_registry()
            logger.info(
                f"Organized { len(organized_files)} output files for series: {series_name}"
            )

        except Exception as e:
            logger.error(f"Error organizing output files: {e}")

        return organized_files

    def determine_file_type(self, file_path: Path) -> str:
        """Determine file type based on extension and content."""
        suffix = file_path.suffix.lower()

        type_mapping = {
            ".eer": "eer_frames",
            ".tif": "tiff_images",
            ".tiff": "tiff_images",
            ".mrc": "mrc_stacks",
            ".mrcs": "mrc_stacks",
            ".st": "tilt_series",
            ".mdoc": "metadata",
            ".txt": "text_files",
            ".log": "logs",
            ".gain": "calibration",
            ".dm4": "dm_files",
        }

        return type_mapping.get(suffix, "other")

    def categorize_output_file(self, file_path: Path) -> str:
        """Categorize output files for organization."""
        name = file_path.name.lower()
        suffix = file_path.suffix.lower()

        # Tomogram files
        if any(
            keyword in name for keyword in ["_rec", "_vol", "tomogram"]
        ) and suffix in [".mrc", ".rec"]:
            return "tomograms"

        # Alignment files
        if any(
            keyword in name for keyword in ["_ali", ".xf", ".tlt", "align"]
        ) or suffix in [".xf", ".tlt"]:
            return "alignments"

        # Log files
        if suffix in [".log", ".txt"] and any(
            keyword in name for keyword in ["log", "output"]
        ):
            return "logs"

        # Quality control files
        if any(keyword in name for keyword in ["ctf", "quality", "qc", "assessment"]):
            return "quality_control"

        # Default to processed
        return "processed"

    def create_file_metadata(
        self,
        file_path: Path,
        original_path: str,
        file_type: str,
        series_name: str,
        stage: str,
    ) -> FileMetadata:
        """Create metadata for a file."""
        try:
            stat = file_path.stat()
            checksum = self.calculate_checksum(file_path)

            return FileMetadata(
                file_path=str(file_path),
                original_path=original_path,
                file_type=file_type,
                size_bytes=stat.st_size,
                created_time=stat.st_ctime,
                modified_time=stat.st_mtime,
                checksum=checksum,
                series_name=series_name,
                processing_stage=stage,
            )

        except Exception as e:
            logger.error(f"Error creating metadata for {file_path}: {e}")
            return FileMetadata(
                file_path=str(file_path),
                original_path=original_path,
                file_type=file_type,
                size_bytes=0,
                created_time=time.time(),
                modified_time=time.time(),
                checksum="",
                series_name=series_name,
                processing_stage=stage,
            )

    def calculate_checksum(self, file_path: Path, chunk_size: int = 8192) -> str:
        """Calculate MD5 checksum of a file."""
        try:
            hash_md5 = hashlib.md5()
            with open(file_path, "rb") as f:
                for chunk in iter(lambda: f.read(chunk_size), b""):
                    hash_md5.update(chunk)
            return hash_md5.hexdigest()
        except Exception as e:
            logger.error(f"Error calculating checksum for {file_path}: {e}")
            return ""

    def register_file(self, file_path: str, metadata: FileMetadata):
        """Register a file in the file registry."""
        self.file_registry[file_path] = metadata

    def get_file_metadata(self, file_path: str) -> Optional[FileMetadata]:
        """Get metadata for a file."""
        return self.file_registry.get(file_path)

    def find_files_by_series(self, series_name: str) -> List[FileMetadata]:
        """Find all files belonging to a series."""
        return [
            metadata
            for metadata in self.file_registry.values()
            if metadata.series_name == series_name
        ]

    def find_files_by_type(self, file_type: str) -> List[FileMetadata]:
        """Find all files of a specific type."""
        return [
            metadata
            for metadata in self.file_registry.values()
            if metadata.file_type == file_type
        ]

    def get_storage_statistics(self) -> Dict[str, Any]:
        """Get storage statistics."""
        stats = {
            "total_files": len(self.file_registry),
            "total_size_bytes": sum(m.size_bytes for m in self.file_registry.values()),
            "by_type": {},
            "by_series": {},
            "by_stage": {},
        }

        # Group by type
        for metadata in self.file_registry.values():
            # By type
            if metadata.file_type not in stats["by_type"]:
                stats["by_type"][metadata.file_type] = {"count": 0, "size": 0}
            stats["by_type"][metadata.file_type]["count"] += 1
            stats["by_type"][metadata.file_type]["size"] += metadata.size_bytes

            # By series
            if metadata.series_name not in stats["by_series"]:
                stats["by_series"][metadata.series_name] = {"count": 0, "size": 0}
            stats["by_series"][metadata.series_name]["count"] += 1
            stats["by_series"][metadata.series_name]["size"] += metadata.size_bytes

            # By stage
            if metadata.processing_stage not in stats["by_stage"]:
                stats["by_stage"][metadata.processing_stage] = {"count": 0, "size": 0}
            stats["by_stage"][metadata.processing_stage]["count"] += 1
            stats["by_stage"][metadata.processing_stage]["size"] += metadata.size_bytes

        return stats

    def cleanup_old_files(self, days_old: int = 30, file_types: List[str] = None):
        """Clean up old files based on age and type."""
        cutoff_time = time.time() - (days_old * 24 * 3600)
        files_to_remove = []

        for file_path, metadata in self.file_registry.items():
            # Check age
            if metadata.modified_time < cutoff_time:
                # Check type filter
                if file_types is None or metadata.file_type in file_types:
                    files_to_remove.append(file_path)

        removed_count = 0
        for file_path in files_to_remove:
            try:
                if Path(file_path).exists():
                    Path(file_path).unlink()
                del self.file_registry[file_path]
                removed_count += 1
                logger.debug(f"Removed old file: {file_path}")
            except Exception as e:
                logger.error(f"Error removing file {file_path}: {e}")

        if removed_count > 0:
            self.save_file_registry()
            logger.info(f"Cleaned up {removed_count} old files")

        return removed_count

    def create_backup(self, series_name: str) -> str:
        """Create backup of series files."""
        try:
            backup_dir = (
                self.directories["backups"]
                / f"{series_name}_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
            )
            backup_dir.mkdir(parents=True, exist_ok=True)

            series_files = self.find_files_by_series(series_name)
            backed_up_count = 0

            for metadata in series_files:
                source_path = Path(metadata.file_path)
                if source_path.exists():
                    # Maintain relative structure in backup
                    relative_path = source_path.relative_to(self.base_dir)
                    backup_path = backup_dir / relative_path
                    backup_path.parent.mkdir(parents=True, exist_ok=True)

                    shutil.copy2(source_path, backup_path)
                    backed_up_count += 1

            logger.info(
                f"Created backup for series {series_name}: {backed_up_count} files -> {backup_dir}"
            )
            return str(backup_dir)

        except Exception as e:
            logger.error(f"Error creating backup for series {series_name}: {e}")
            return ""

    def save_file_registry(self):
        """Save file registry to disk."""
        try:
            registry_data = {
                path: asdict(metadata) for path, metadata in self.file_registry.items()
            }

            with open(self.metadata_file, "w") as f:
                json.dump(registry_data, f, indent=2)

        except Exception as e:
            logger.error(f"Error saving file registry: {e}")

    def load_file_registry(self):
        """Load file registry from disk."""
        try:
            if self.metadata_file.exists():
                with open(self.metadata_file) as f:
                    registry_data = json.load(f)

                self.file_registry = {
                    path: FileMetadata(**metadata)
                    for path, metadata in registry_data.items()
                }

                logger.info(
                    f"Loaded file registry with {len(self.file_registry)} files"
                )

        except Exception as e:
            logger.error(f"Error loading file registry: {e}")
            self.file_registry = {}

    def export_file_list(self, output_file: str, series_name: str = None) -> bool:
        """Export file list to CSV or JSON."""
        try:
            output_path = Path(output_file)

            # Filter files if series specified
            if series_name:
                files_to_export = self.find_files_by_series(series_name)
            else:
                files_to_export = list(self.file_registry.values())

            if output_path.suffix.lower() == ".json":
                # Export as JSON
                export_data = [asdict(metadata) for metadata in files_to_export]
                with open(output_path, "w") as f:
                    json.dump(export_data, f, indent=2)

            elif output_path.suffix.lower() == ".csv":
                # Export as CSV
                import csv

                with open(output_path, "w", newline="") as f:
                    if files_to_export:
                        writer = csv.DictWriter(
                            f, fieldnames=asdict(files_to_export[0]).keys()
                        )
                        writer.writeheader()
                        for metadata in files_to_export:
                            writer.writerow(asdict(metadata))

            logger.info(f"Exported { len(files_to_export)} files to {output_file}")
            return True

        except Exception as e:
            logger.error(f"Error exporting file list: {e}")
            return False

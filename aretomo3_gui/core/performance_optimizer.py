#!/usr/bin/env python3
"""
AreTomo3 GUI Performance Optimization Engine
Advanced performance monitoring, optimization, and resource management.
"""

import gc
import json
import logging
import queue
import threading
import time
from collections import deque
from dataclasses import dataclass, field
from datetime import datetime, timedelta
from pathlib import Path
from typing import Any, Callable, Dict, List, Optional

import numpy as np
import psutil

logger = logging.getLogger(__name__)


@dataclass
class PerformanceMetrics:
    """Performance metrics data structure."""

    timestamp: datetime
    cpu_usage: float
    memory_usage: float
    memory_available: int
    disk_io_read: int
    disk_io_write: int
    network_io_sent: int
    network_io_recv: int
    gpu_usage: float = 0.0
    gpu_memory: float = 0.0
    process_count: int = 0
    thread_count: int = 0


@dataclass
class OptimizationRule:
    """Performance optimization rule."""

    name: str
    condition: Callable[[PerformanceMetrics], bool]
    action: Callable[[], None]
    priority: int = 1
    cooldown: int = 60  # seconds
    last_triggered: Optional[datetime] = None
    enabled: bool = True


class PerformanceOptimizer:
    """
    Advanced performance optimization engine for AreTomo3 GUI.
    Monitors system resources and applies optimization strategies.
    """

    def __init__(self, monitoring_interval: int = 5):
        """Initialize the performance optimizer."""
        self.monitoring_interval = monitoring_interval
        self.is_monitoring = False
        self.monitor_thread = None

        # Performance data storage
        self.metrics_history: deque = deque(maxlen=1000)  # Keep last 1000 metrics
        self.current_metrics: Optional[PerformanceMetrics] = None

        # Optimization rules
        self.optimization_rules: List[OptimizationRule] = []
        self.optimization_queue = queue.Queue()

        # Configuration
        self.config = {
            "cpu_threshold_high": 80.0,
            "cpu_threshold_critical": 95.0,
            "memory_threshold_high": 80.0,
            "memory_threshold_critical": 95.0,
            "disk_threshold_high": 80.0,
            "auto_gc_enabled": True,
            "auto_gc_threshold": 85.0,
            "process_priority_optimization": True,
            "cache_optimization": True,
            "thread_pool_optimization": True,
        }

        # Performance statistics
        self.stats = {
            "optimizations_applied": 0,
            "memory_freed": 0,
            "cpu_time_saved": 0.0,
            "last_optimization": None,
        }

        # Initialize optimization rules
        self._setup_optimization_rules()

        logger.info("Performance Optimizer initialized")

    # TODO: Refactor function - Function '_setup_optimization_rules' too long
    # (58 lines)
    def _setup_optimization_rules(self):
        """Set up default optimization rules."""

        # Memory optimization rules
        self.optimization_rules.append(
            OptimizationRule(
                name="high_memory_gc",
                condition=lambda m: m.memory_usage
                > self.config["memory_threshold_high"],
                action=self._trigger_garbage_collection,
                priority=2,
                cooldown=30,
            )
        )

        self.optimization_rules.append(
            OptimizationRule(
                name="critical_memory_cleanup",
                condition=lambda m: m.memory_usage
                > self.config["memory_threshold_critical"],
                action=self._aggressive_memory_cleanup,
                priority=1,
                cooldown=60,
            )
        )

        # CPU optimization rules
        self.optimization_rules.append(
            OptimizationRule(
                name="high_cpu_optimization",
                condition=lambda m: m.cpu_usage > self.config["cpu_threshold_high"],
                action=self._optimize_cpu_usage,
                priority=2,
                cooldown=45,
            )
        )

        # Cache optimization rules
        self.optimization_rules.append(
            OptimizationRule(
                name="cache_cleanup",
                condition=lambda m: m.memory_usage > 70.0,
                action=self._optimize_cache,
                priority=3,
                cooldown=120,
            )
        )

        # Thread optimization rules
        self.optimization_rules.append(
            OptimizationRule(
                name="thread_optimization",
                condition=lambda m: m.thread_count > 50,
                action=self._optimize_threads,
                priority=3,
                cooldown=180,
            )
        )

    def start_monitoring(self):
        """Start performance monitoring."""
        if self.is_monitoring:
            logger.warning("Performance monitoring already running")
            return

        self.is_monitoring = True
        self.monitor_thread = threading.Thread(
            target=self._monitoring_loop, daemon=True
        )
        self.monitor_thread.start()

        logger.info("Performance monitoring started")

    def stop_monitoring(self):
        """Stop performance monitoring."""
        self.is_monitoring = False
        if self.monitor_thread:
            self.monitor_thread.join(timeout=5)

        logger.info("Performance monitoring stopped")

    def _monitoring_loop(self):
        """Main monitoring loop."""
        while self.is_monitoring:
            try:
                # Collect metrics
                metrics = self._collect_metrics()
                self.current_metrics = metrics
                self.metrics_history.append(metrics)

                # Check optimization rules
                self._check_optimization_rules(metrics)

                # Process optimization queue
                self._process_optimization_queue()

                time.sleep(self.monitoring_interval)

            except Exception as e:
                logger.error(f"Error in monitoring loop: {e}")
                time.sleep(self.monitoring_interval)

    def _collect_metrics(self) -> PerformanceMetrics:
        """Collect current performance metrics."""
        try:
            # System metrics
            cpu_usage = psutil.cpu_percent(interval=1)
            memory = psutil.virtual_memory()
            disk_io = psutil.disk_io_counters()
            network_io = psutil.net_io_counters()

            # Process metrics
            current_process = psutil.Process()
            process_count = len(psutil.pids())
            thread_count = current_process.num_threads()

            # GPU metrics (if available)
            gpu_usage, gpu_memory = self._get_gpu_metrics()

            metrics = PerformanceMetrics(
                timestamp=datetime.now(),
                cpu_usage=cpu_usage,
                memory_usage=memory.percent,
                memory_available=memory.available,
                disk_io_read=disk_io.read_bytes if disk_io else 0,
                disk_io_write=disk_io.write_bytes if disk_io else 0,
                network_io_sent=network_io.bytes_sent if network_io else 0,
                network_io_recv=network_io.bytes_recv if network_io else 0,
                gpu_usage=gpu_usage,
                gpu_memory=gpu_memory,
                process_count=process_count,
                thread_count=thread_count,
            )

            return metrics

        except Exception as e:
            logger.error(f"Error collecting metrics: {e}")
            return PerformanceMetrics(
                timestamp=datetime.now(),
                cpu_usage=0.0,
                memory_usage=0.0,
                memory_available=0,
                disk_io_read=0,
                disk_io_write=0,
                network_io_sent=0,
                network_io_recv=0,
            )

    def _get_gpu_metrics(self) -> tuple:
        """Get GPU metrics if available."""
        try:
            import GPUtil

            gpus = GPUtil.getGPUs()
            if gpus:
                gpu = gpus[0]  # Use first GPU
                return gpu.load * 100, gpu.memoryUtil * 100
        except ImportError:
            pass
        except Exception as e:
            logger.debug(f"Error getting GPU metrics: {e}")

        return 0.0, 0.0

    def _check_optimization_rules(self, metrics: PerformanceMetrics):
        """Check and trigger optimization rules."""
        current_time = datetime.now()

        for rule in self.optimization_rules:
            if not rule.enabled:
                continue

            # Check cooldown
            if rule.last_triggered:
                time_since_last = (current_time - rule.last_triggered).total_seconds()
                if time_since_last < rule.cooldown:
                    continue

            # Check condition
            if rule.condition(metrics):
                # Add to optimization queue
                self.optimization_queue.put((rule.priority, rule))
                rule.last_triggered = current_time
                logger.info(f"Optimization rule triggered: {rule.name}")

    def _process_optimization_queue(self):
        """Process optimization queue."""
        try:
            while not self.optimization_queue.empty():
                priority, rule = self.optimization_queue.get_nowait()

                try:
                    logger.info(f"Applying optimization: {rule.name}")
                    rule.action()
                    self.stats["optimizations_applied"] += 1
                    self.stats["last_optimization"] = datetime.now()

                except Exception as e:
                    logger.error(f"Error applying optimization { rule.name}: {e}")

        except queue.Empty:
            pass

    def _trigger_garbage_collection(self):
        """Trigger garbage collection."""
        if not self.config["auto_gc_enabled"]:
            return

        try:
            before_memory = psutil.virtual_memory().percent

            # Force garbage collection
            collected = gc.collect()

            after_memory = psutil.virtual_memory().percent
            memory_freed = before_memory - after_memory

            self.stats["memory_freed"] += memory_freed

            logger.info(
                f"Garbage collection completed: {collected} objects collected, "
                f"{ memory_freed:.1f}% memory freed"
            )

        except Exception as e:
            logger.error(f"Error in garbage collection: {e}")

    def _aggressive_memory_cleanup(self):
        """Aggressive memory cleanup for critical situations."""
        try:
            logger.warning("Performing aggressive memory cleanup")

            # Multiple garbage collection passes
            for _ in range(3):
                gc.collect()

            # Clear caches if available
            self._clear_caches()

            # Force memory compaction (Python-specific)
            import ctypes

            ctypes.CDLL("libc.so.6").malloc_trim(0)

            logger.info("Aggressive memory cleanup completed")

        except Exception as e:
            logger.error(f"Error in aggressive memory cleanup: {e}")

    def _optimize_cpu_usage(self):
        """Optimize CPU usage."""
        try:
            logger.info("Optimizing CPU usage")

            # Lower process priority if possible
            if self.config["process_priority_optimization"]:
                current_process = psutil.Process()
                try:
                    current_process.nice(5)  # Lower priority
                    logger.info("Process priority lowered")
                except Exception:
                    pass

            # Yield CPU time
            time.sleep(0.1)

        except Exception as e:
            logger.error(f"Error optimizing CPU usage: {e}")

    def _optimize_cache(self):
        """Optimize cache usage."""
        if not self.config["cache_optimization"]:
            return

        try:
            logger.info("Optimizing cache usage")
            self._clear_caches()

        except Exception as e:
            logger.error(f"Error optimizing cache: {e}")

    def _clear_caches(self):
        """Clear various caches."""
        try:
            # Clear matplotlib cache
            try:
                import matplotlib

                matplotlib.pyplot.close("all")
            except Exception:
                pass

            # Clear numpy cache
            try:
                # Clear any numpy caches if available
                pass
            except Exception:
                pass

            # Clear Qt cache
            try:
                from PyQt6.QtCore import QCoreApplication

                QCoreApplication.processEvents()
            except Exception:
                pass

            logger.info("Caches cleared")

        except Exception as e:
            logger.error(f"Error clearing caches: {e}")

    def _optimize_threads(self):
        """Optimize thread usage."""
        if not self.config["thread_pool_optimization"]:
            return

        try:
            logger.info("Optimizing thread usage")

            # This would need integration with actual thread pools
            # For now, just log the optimization
            pass

        except Exception as e:
            logger.error(f"Error optimizing threads: {e}")

    def get_performance_summary(self) -> Dict[str, Any]:
        """Get performance summary."""
        if not self.current_metrics:
            return {"status": "No metrics available"}

        # Calculate averages from recent history
        # Last 10 measurements
        recent_metrics = list(self.metrics_history)[-10:]

        if recent_metrics:
            avg_cpu = np.mean([m.cpu_usage for m in recent_metrics])
            avg_memory = np.mean([m.memory_usage for m in recent_metrics])
            max_cpu = max([m.cpu_usage for m in recent_metrics])
            max_memory = max([m.memory_usage for m in recent_metrics])
        else:
            avg_cpu = avg_memory = max_cpu = max_memory = 0.0

        return {
            "current": {
                "cpu_usage": self.current_metrics.cpu_usage,
                "memory_usage": self.current_metrics.memory_usage,
                "memory_available_gb": self.current_metrics.memory_available
                / (1024**3),
                "thread_count": self.current_metrics.thread_count,
                "gpu_usage": self.current_metrics.gpu_usage,
                "gpu_memory": self.current_metrics.gpu_memory,
            },
            "averages": {"cpu_usage": avg_cpu, "memory_usage": avg_memory},
            "peaks": {"cpu_usage": max_cpu, "memory_usage": max_memory},
            "optimization_stats": self.stats,
            "monitoring_active": self.is_monitoring,
            "rules_count": len([r for r in self.optimization_rules if r.enabled]),
        }

    def add_optimization_rule(self, rule: OptimizationRule):
        """Add custom optimization rule."""
        self.optimization_rules.append(rule)
        logger.info(f"Added optimization rule: {rule.name}")

    def remove_optimization_rule(self, rule_name: str):
        """Remove optimization rule."""
        self.optimization_rules = [
            r for r in self.optimization_rules if r.name != rule_name
        ]
        logger.info(f"Removed optimization rule: {rule_name}")

    def update_config(self, config_updates: Dict[str, Any]):
        """Update optimizer configuration."""
        self.config.update(config_updates)
        logger.info("Performance optimizer configuration updated")

    def force_optimization(self, optimization_type: str = "all"):
        """Force specific optimization."""
        try:
            if optimization_type in ["all", "memory"]:
                self._trigger_garbage_collection()

            if optimization_type in ["all", "cache"]:
                self._optimize_cache()

            if optimization_type in ["all", "cpu"]:
                self._optimize_cpu_usage()

            logger.info(f"Forced optimization completed: {optimization_type}")

        except Exception as e:
            logger.error(f"Error in forced optimization: {e}")


# Global performance optimizer instance
performance_optimizer = PerformanceOptimizer()


def optimize_performance(func):
    """Decorator to optimize performance of functions."""

    def wrapper(*args, **kwargs):
        """Execute wrapper operation."""
        start_time = time.time()

        try:
            result = func(*args, **kwargs)

            # Log performance
            execution_time = time.time() - start_time
            if execution_time > 1.0:  # Log slow functions
                logger.info(f"Function { func.__name__} took { execution_time:.2f}s")

            return result

        except Exception as e:
            logger.error(f"Error in optimized function {func.__name__}: {e}")
            raise

    return wrapper


def memory_efficient(func):
    """Decorator to ensure memory-efficient execution."""

    def wrapper(*args, **kwargs):
        """Execute wrapper operation."""
        # Force garbage collection before execution
        gc.collect()

        try:
            result = func(*args, **kwargs)

            # Force garbage collection after execution
            gc.collect()

            return result

        except Exception as e:
            # Clean up on error
            gc.collect()
            raise

    return wrapper

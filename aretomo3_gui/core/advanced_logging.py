#!/usr/bin/env python3
"""
AreTomo3 GUI Advanced Logging System
Provides comprehensive logging with rotation, filtering, and analysis.
"""

import gzip
import json
import logging
import logging.handlers
import queue
import threading
import time
from dataclasses import asdict, dataclass
from datetime import datetime, timedelta
from enum import Enum
from pathlib import Path
from typing import Any, Dict, List, Optional, Union

# Custom log levels
TRACE_LEVEL = 5
PERFORMANCE_LEVEL = 15

logging.addLevelName(TRACE_LEVEL, "TRACE")
logging.addLevelName(PERFORMANCE_LEVEL, "PERFORMANCE")


class LogCategory(Enum):
    """Log categories for better organization."""

    SYSTEM = "system"
    GUI = "gui"
    ANALYSIS = "analysis"
    PROCESSING = "processing"
    NETWORK = "network"
    PERFORMANCE = "performance"
    ERROR = "error"
    USER_ACTION = "user_action"


@dataclass
class LogEntry:
    """Structured log entry."""

    timestamp: datetime
    level: str
    category: LogCategory
    component: str
    message: str
    context: Dict[str, Any]
    thread_id: int
    process_id: int
    session_id: str
    user_id: Optional[str] = None
    performance_metrics: Optional[Dict[str, float]] = None


class StructuredFormatter(logging.Formatter):
    """Custom formatter for structured logging."""

    def __init__(self, include_context=True):
        """Initialize the instance."""
        super().__init__()
        self.include_context = include_context

    def format(self, record):
        """Format log record as structured data."""
        # Extract custom fields
        category = getattr(record, "category", LogCategory.SYSTEM)
        component = getattr(record, "component", "unknown")
        context = getattr(record, "context", {})
        session_id = getattr(record, "session_id", "default")
        user_id = getattr(record, "user_id", None)
        performance_metrics = getattr(record, "performance_metrics", None)

        log_entry = LogEntry(
            timestamp=datetime.fromtimestamp(record.created),
            level=record.levelname,
            category=category,
            component=component,
            message=record.getMessage(),
            context=context if self.include_context else {},
            thread_id=record.thread,
            process_id=record.process,
            session_id=session_id,
            user_id=user_id,
            performance_metrics=performance_metrics,
        )

        return json.dumps(asdict(log_entry), default=str, ensure_ascii=False)


class AdvancedLogger:
    """
    Advanced logging system with structured logging, rotation, and analysis.
    """

    def __init__(self, log_dir: Union[str, Path] = None):
        """Initialize the advanced logging system."""
        self.log_dir = Path(log_dir) if log_dir else Path.cwd() / "logs"
        self.log_dir.mkdir(parents=True, exist_ok=True)

        # Logging configuration
        self.max_file_size = 50 * 1024 * 1024  # 50MB
        self.backup_count = 10
        self.compression_enabled = True

        # Log queues for different categories
        self.log_queues: Dict[LogCategory, queue.Queue] = {}
        self.log_handlers: Dict[LogCategory, logging.Handler] = {}
        self.log_workers: Dict[LogCategory, threading.Thread] = {}

        # Session tracking
        self.session_id = self._generate_session_id()
        self.current_user_id = None

        # Performance tracking
        self.performance_buffer: List[Dict[str, Any]] = []
        self.performance_buffer_size = 1000

        # Initialize logging system
        self._setup_loggers()
        self._start_log_workers()

        # Root logger configuration
        self._configure_root_logger()
        
        # Get module logger for initialization message
        self.logger = logging.getLogger(__name__)
        self.logger.info(f"Advanced Logging System initialized - Session: {self.session_id}")

    def _generate_session_id(self) -> str:
        """Generate unique session ID."""
        return f"session_{datetime.now().strftime('%Y%m%d_%H%M%S')}_{id(self) % 10000}"

    def _setup_loggers(self):
        """Set up category-specific loggers."""
        for category in LogCategory:
            # Create log file path
            log_file = self.log_dir / f"{category.value}.log"

            # Create rotating file handler
            handler = logging.handlers.RotatingFileHandler(
                log_file, maxBytes=self.max_file_size, backupCount=self.backup_count
            )

            # Set structured formatter
            formatter = StructuredFormatter(include_context=True)
            handler.setFormatter(formatter)

            # Create logger
            logger = logging.getLogger(f"aretomo3.{category.value}")
            logger.setLevel(logging.DEBUG)
            logger.addHandler(handler)

            # Store references
            self.log_handlers[category] = handler
            self.log_queues[category] = queue.Queue()

    def _start_log_workers(self):
        """Start background workers for log processing."""
        for category in LogCategory:
            worker = threading.Thread(
                target=self._log_worker,
                args=(category,),
                daemon=True,
                name=f"LogWorker-{category.value}",
            )
            worker.start()
            self.log_workers[category] = worker

    def _log_worker(self, category: LogCategory):
        """Background worker for processing logs."""
        logger = logging.getLogger(f"aretomo3.{category.value}")
        log_queue = self.log_queues[category]

        while True:
            try:
                # Get log entry from queue
                log_entry = log_queue.get(timeout=1.0)
                if log_entry is None:  # Shutdown signal
                    break

                # Create log record
                record = logging.LogRecord(
                    name=logger.name,
                    level=getattr(logging, log_entry["level"]),
                    pathname="",
                    lineno=0,
                    msg=log_entry["message"],
                    args=(),
                    exc_info=None,
                )

                # Add custom attributes
                for key, value in log_entry.items():
                    if key != "message":
                        setattr(record, key, value)

                # Log the record
                logger.handle(record)

            except queue.Empty:
                continue
            except Exception as e:
                logger.info(f"Error in log worker for {category}: {e}")

    def _configure_root_logger(self):
        """Configure the root logger."""
        root_logger = logging.getLogger()
        root_logger.setLevel(logging.DEBUG)

        # Console handler for immediate feedback
        console_handler = logging.StreamHandler()
        console_formatter = logging.Formatter(
            "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
        )
        console_handler.setFormatter(console_formatter)
        console_handler.setLevel(logging.INFO)

        root_logger.addHandler(console_handler)

    def log(
        self,
        level: str,
        category: LogCategory,
        component: str,
        message: str,
        context: Dict[str, Any] = None,
        performance_metrics: Dict[str, float] = None,
    ):
        """Log a message with structured data."""
        log_entry = {
            "level": level.upper(),
            "category": category,
            "component": component,
            "message": message,
            "context": context or {},
            "session_id": self.session_id,
            "user_id": self.current_user_id,
            "performance_metrics": performance_metrics,
            "timestamp": datetime.now(),
        }

        # Add to appropriate queue
        try:
            self.log_queues[category].put_nowait(log_entry)
        except queue.Full:
            # Use module logger for this error message
            module_logger = logging.getLogger(__name__)
            module_logger.info(f"Log queue full for category {category}")

        # Store performance metrics
        if performance_metrics:
            self._store_performance_metrics(performance_metrics, component)

    def _store_performance_metrics(self, metrics: Dict[str, float], component: str):
        """Store performance metrics for analysis."""
        perf_entry = {
            "timestamp": datetime.now(),
            "component": component,
            "metrics": metrics,
            "session_id": self.session_id,
        }

        self.performance_buffer.append(perf_entry)

        # Trim buffer if too large
        if len(self.performance_buffer) > self.performance_buffer_size:
            self.performance_buffer = self.performance_buffer[
                -self.performance_buffer_size :
            ]

    def trace(self, component: str, message: str, context: Dict[str, Any] = None):
        """Log trace message."""
        self.log("TRACE", LogCategory.SYSTEM, component, message, context)

    def debug(self, component: str, message: str, context: Dict[str, Any] = None):
        """Log debug message."""
        self.log("DEBUG", LogCategory.SYSTEM, component, message, context)

    def info(self, component: str, message: str, context: Dict[str, Any] = None):
        """Log info message."""
        self.log("INFO", LogCategory.SYSTEM, component, message, context)

    def warning(self, component: str, message: str, context: Dict[str, Any] = None):
        """Log warning message."""
        self.log("WARNING", LogCategory.SYSTEM, component, message, context)

    def error(self, component: str, message: str, context: Dict[str, Any] = None):
        """Log error message."""
        self.log("ERROR", LogCategory.ERROR, component, message, context)

    def critical(self, component: str, message: str, context: Dict[str, Any] = None):
        """Log critical message."""
        self.log("CRITICAL", LogCategory.ERROR, component, message, context)

    def performance(
        self,
        component: str,
        message: str,
        metrics: Dict[str, float],
        context: Dict[str, Any] = None,
    ):
        """Log performance message."""
        self.log(
            "PERFORMANCE", LogCategory.PERFORMANCE, component, message, context, metrics
        )

    def user_action(self, action: str, component: str, context: Dict[str, Any] = None):
        """Log user action."""
        self.log(
            "INFO",
            LogCategory.USER_ACTION,
            component,
            f"User action: {action}",
            context,
        )

    def set_user_id(self, user_id: str):
        """Set current user ID for logging."""
        self.current_user_id = user_id

    def compress_old_logs(self):
        """Compress old log files."""
        if not self.compression_enabled:
            return

        for log_file in self.log_dir.glob("*.log.*"):
            if not log_file.name.endswith(".gz"):
                try:
                    with open(log_file, "rb") as f_in:
                        with gzip.open(f"{log_file}.gz", "wb") as f_out:
                            f_out.writelines(f_in)

                    log_file.unlink()  # Remove original file
                    module_logger = logging.getLogger(__name__)
                    module_logger.info(f"Compressed log file: {log_file}")

                except Exception as e:
                    module_logger = logging.getLogger(__name__)
                    module_logger.info(f"Error compressing {log_file}: {e}")

    def get_log_statistics(self) -> Dict[str, Any]:
        """Get logging statistics."""
        stats = {
            "session_id": self.session_id,
            "log_directory": str(self.log_dir),
            "categories": {},
            "performance_buffer_size": len(self.performance_buffer),
            "total_log_files": len(list(self.log_dir.glob("*.log*"))),
        }

        # Category-specific stats
        for category in LogCategory:
            queue_size = self.log_queues[category].qsize()
            log_file = self.log_dir / f"{category.value}.log"
            file_size = log_file.stat().st_size if log_file.exists() else 0

            stats["categories"][category.value] = {
                "queue_size": queue_size,
                "log_file_size_mb": file_size / (1024 * 1024),
                "worker_active": self.log_workers[category].is_alive(),
            }

        return stats

    def export_performance_data(self, output_file: Path = None) -> Path:
        """Export performance data to JSON file."""
        if output_file is None:
            output_file = self.log_dir / f"performance_data_{self.session_id}.json"

        with open(output_file, "w") as f:
            json.dump(self.performance_buffer, f, default=str, indent=2)

        return output_file

    def shutdown(self):
        """Shutdown the logging system gracefully."""
        module_logger = logging.getLogger(__name__)
        module_logger.info("Shutting down Advanced Logging System...")

        # Stop all workers
        for category in LogCategory:
            self.log_queues[category].put(None)  # Shutdown signal

        # Wait for workers to finish
        for worker in self.log_workers.values():
            worker.join(timeout=2.0)

        # Compress old logs
        self.compress_old_logs()

        module_logger.info("Advanced Logging System shutdown complete")


# Global advanced logger instance
advanced_logger = AdvancedLogger()


# Convenience functions
def log_trace(component: str, message: str, context: Dict[str, Any] = None):
    """Log trace message."""
    advanced_logger.trace(component, message, context)


def log_performance(
    component: str,
    message: str,
    metrics: Dict[str, float],
    context: Dict[str, Any] = None,
):
    """Log performance message."""
    advanced_logger.performance(component, message, metrics, context)


def log_user_action(action: str, component: str, context: Dict[str, Any] = None):
    """Log user action."""
    advanced_logger.user_action(action, component, context)

#!/usr/bin/env python3
"""
Resource Manager for AreTomo3 GUI Application
===========================================

This module provides system resource monitoring and management capabilities:
- Memory and disk usage monitoring
- Resource warning notifications
- Temporary file management
- File locking mechanisms
- GPU resource monitoring

Classes:
    ResourceMonitor: Monitors system resources and emits warnings
    FileManager: Handles file operations with proper cleanup

Author: AreTomo3 GUI Team
Date: May 2025
"""

# =============================================================================
# Imports
# =============================================================================

import fcntl
import logging

# Standard library imports
import os
import shutil
import subprocess
import tempfile
import threading
import time
from pathlib import Path
from typing import Callable, Dict, List, Optional

# Third-party imports
import psutil
from PyQt6.QtCore import QObject, pyqtSignal

# Local imports
from .error_handling import FileSystemError, GPUError, try_operation

# =============================================================================
# Logger Configuration
# =============================================================================

logger = logging.getLogger("resource_manager")

# =============================================================================
# Resource Monitor Class
# =============================================================================


class ResourceMonitor(QObject):
    """
    Monitors system resources and raises warnings when limits are exceeded.

    This class provides real-time monitoring of:
    - System memory usage
    - Disk space usage
    - Resource warning notifications via Qt signals

    Attributes:
        warning (pyqtSignal): Signal emitted when resource warnings occur
        memory_warning_threshold (float): Threshold for memory warnings (0.0-1.0)
        disk_warning_threshold (float): Threshold for disk warnings (0.0-1.0)
        check_interval (int): Interval between checks in milliseconds
    """

    warning = pyqtSignal(str)

    def __init__(self, parent=None):
        """
        Initialize the resource monitor.

        Args:
            parent: Optional parent QObject for Qt object hierarchy
        """
        super().__init__(parent)
        self._timer = None
        self.memory_warning_threshold = 0.9  # 90% memory usage triggers warning
        self.disk_warning_threshold = 0.9  # 90% disk usage triggers warning
        self.check_interval = 5000  # Check every 5 seconds

    # =========================================================================
    # Resource Monitoring Control
    # =========================================================================

    def start_monitoring(self) -> None:
        """
        Start periodic resource monitoring.

        Creates a QTimer that triggers resource checks at regular intervals
        defined by check_interval. Only creates a new timer if one is not
        already running.
        """
        from PyQt6.QtCore import QTimer

        if self._timer is None:
            self._timer = QTimer(self)
            self._timer.timeout.connect(self.check_all)
            self._timer.start(self.check_interval)
            logger.info("Resource monitoring started")

    def stop_monitoring(self) -> None:
        """
        Stop resource monitoring.

        Stops and deletes the monitoring timer if one exists.
        """
        if self._timer is not None:
            self._timer.stop()
            self._timer = None
            logger.info("Resource monitoring stopped")

    # =========================================================================
    # Resource Check Methods
    # =========================================================================

    def check_memory(self) -> None:
        """
        Check system memory usage.

        Emits a warning signal if memory usage exceeds the configured threshold.
        Uses psutil to get accurate system memory information.
        """
        memory_info = psutil.virtual_memory()
        usage_ratio = memory_info.percent / 100

        if usage_ratio > self.memory_warning_threshold:
            warning_msg = (
                f"High memory usage detected ({memory_info.percent}%). "
                "Consider closing unused applications."
            )
            self.warning.emit(warning_msg)
            logger.warning(f"Memory warning: {usage_ratio:.2%} used")

    def check_disk(self, path: str) -> None:
        """
        Check disk usage for a specified path.

        Args:
            path: The filesystem path to check

        Emits a warning signal if disk usage exceeds the configured threshold.
        """
        disk_info = psutil.disk_usage(path)
        usage_ratio = disk_info.percent / 100

        if usage_ratio > self.disk_warning_threshold:
            warning_msg = (
                f"Low disk space on {path} ({disk_info.percent}% used). "
                "Free up space to continue."
            )
            self.warning.emit(warning_msg)
            logger.warning(f"Disk warning for {path}: {usage_ratio:.2%} used")

    def check_all(self) -> None:
        """
        Perform all resource checks.

        Currently checks:
        - System memory usage
        - Root filesystem disk usage

        This method is called periodically by the monitoring timer.
        """
        logger.debug("Performing resource checks")
        self.check_memory()
        self.check_disk("/")


# =============================================================================
# File Management
# =============================================================================


class FileManager:
    """
    Manages file operations with proper error handling and cleanup.

    This class provides utilities for:
    - Creating and managing temporary directories
    - File locking mechanisms
    - Automatic resource cleanup

    All temporary resources are automatically cleaned up when the manager
    is destroyed or when cleanup() is called explicitly.

    Attributes:
        temp_dirs (List[str]): List of managed temporary directories
        lock_files (Dict[str, bool]): Dictionary of active file locks
    """

    def __init__(self):
        """Initialize the file manager with empty resource tracking."""
        self.temp_dirs: List[str] = []
        self.lock_files: Dict[str, bool] = {}
        logger.debug("FileManager initialized")

    def __del__(self):
        """
        Clean up all managed resources on destruction.

        Ensures all temporary directories and locks are properly cleaned up
        when the manager is destroyed, preventing resource leaks.
        """
        self.cleanup()

    def create_temp_directory(
        self, prefix: str = "aretomo_", cleanup: bool = True
    ) -> str:
        """
        Create and optionally track a temporary directory.

        Args:
            prefix: Prefix for the temporary directory name
            cleanup: Whether to register directory for automatic cleanup

        Returns:
            str: Absolute path to the created temporary directory

        Raises:
            FileSystemError: If directory creation fails

        Note:
            Directories marked for cleanup will be removed when the manager
            is cleaned up or destroyed.
        """
        try:
            temp_dir = tempfile.mkdtemp(prefix=prefix)
            if cleanup:
                self.temp_dirs.append(temp_dir)
                logger.debug(f"Created tracked temp directory: {temp_dir}")
            logger.debug(f"Created temporary directory: {temp_dir}")
            return temp_dir
        except Exception as e:
            raise FileSystemError(f"Failed to create temporary directory: { str(e)}")

    # =========================================================================
    # Resource Cleanup
    # =========================================================================

    def cleanup(self) -> None:
        """
        Clean up all managed resources.

        Performs:
        1. Removal of all tracked temporary directories
        2. Release of all file locks
        3. Reset of internal tracking collections

        Any errors during cleanup are logged but don't stop the process.
        """
        # Clean up temporary directories
        for temp_dir in self.temp_dirs:
            try:
                if os.path.exists(temp_dir):
                    shutil.rmtree(temp_dir)
                    logger.debug(f"Removed temporary directory: {temp_dir}")
            except Exception as e:
                logger.error(
                    f"Error removing temporary directory {temp_dir}: { str(e)}"
                )

        # Clear the directory list
        self.temp_dirs = []

        # Release all locks
        for path, lock_file in self.lock_files.items():
            try:
                lock_file.close()
                os.unlink(f"{path}.lock")  # Remove the lock file
                logger.debug(f"Released lock on: {path}")
            except Exception as e:
                logger.error(f"Error releasing lock on {path}: {str(e)}")

        # Clear the lock dictionary
        self.lock_files = {}
        logger.info("Cleanup completed")

    # =========================================================================
    # File Locking
    # =========================================================================

    def acquire_lock(self, path: str, timeout: float = 5.0) -> bool:
        """
        Acquire a lock on a file with timeout.

        Creates a lock file and waits up to timeout seconds for any existing
        lock to be released. Uses file creation exclusivity for atomic locking.

        Args:
            path: Path to the file to lock
            timeout: Maximum time to wait for lock in seconds

        Returns:
            bool: True if lock was acquired, False if timeout occurred

        Note:
            If this process already holds the lock, returns True immediately
        """
        if path in self.lock_files:
            logger.debug(f"Already have lock on: {path}")
            return True

        lock_path = f"{path}.lock"
        start_time = time.time()

        while time.time() - start_time < timeout:
            try:
                # Try to create lock file exclusively
                lock_file = open(lock_path, "x")
                self.lock_files[path] = lock_file
                logger.debug(f"Acquired lock on: {path}")
                return True
            except FileExistsError:
                # Lock exists, wait and retry
                time.sleep(0.1)
            except Exception as e:
                logger.error(f"Error acquiring lock on {path}: {str(e)}")
                return False

        logger.warning(f"Timeout acquiring lock on: {path}")
        return False

    def release_lock(self, path: str) -> bool:
        """
        Release a lock on a file.

        Closes and removes the lock file if this process holds the lock.

        Args:
            path: Path to the file to unlock

        Returns:
            bool: True if lock was released, False if we didn't hold the lock

        Note:
            It's safe to call this method multiple times on the same path,
            subsequent calls will just return False.
        """
        if path not in self.lock_files:
            logger.debug(f"No lock held for: {path}")
            return False

        try:
            # Remove from tracking dict first
            lock_file = self.lock_files.pop(path)

            # Close the file handle
            lock_file.close()

            # Remove the lock file
            lock_path = f"{path}.lock"
            if os.path.exists(lock_path):
                os.remove(lock_path)

            logger.debug(f"Released lock on: {path}")
            return True

        except Exception as e:
            logger.error(f"Error releasing lock on {path}: {str(e)}")
            return False


# =============================================================================
# Global Instances
# =============================================================================

# Global singleton instances for application-wide resource management
_file_manager: Optional[FileManager] = None
_resource_monitor: Optional[ResourceMonitor] = None


def get_file_manager() -> FileManager:
    """
    Get the singleton FileManager instance.

    Returns:
        FileManager: The global file manager instance

    Note:
        Creates the instance on first call, reuses it for subsequent calls.
    """
    global _file_manager
    if _file_manager is None:
        _file_manager = FileManager()
        logger.debug("Created global FileManager instance")
    return _file_manager


def get_resource_monitor() -> ResourceMonitor:
    """
    Get the singleton ResourceMonitor instance.

    Returns:
        ResourceMonitor: The global resource monitor instance

    Note:
        Creates the instance on first call, reuses it for subsequent calls.
    """
    global _resource_monitor
    if _resource_monitor is None:
        _resource_monitor = ResourceMonitor()
        logger.debug("Created global ResourceMonitor instance")
    return _resource_monitor


class ResourceManager:
    """
    Alias for ResourceMonitor for backward compatibility.
    """

    def __init__(self):
        """Initialize the instance."""
        self._monitor = get_resource_monitor()

    def __getattr__(self, name):
        """Execute __getattr__ operation."""
        return getattr(self._monitor, name)

#!/usr/bin/env python3
import logging
import os
import time
from dataclasses import dataclass
from pathlib import Path
from queue import Queue
from threading import Event, Thread
from typing import Any, Callable, Dict, List, Optional, Set

from PyQt6.QtCore import QFileSystemWatcher, QObject, pyqtSignal

logger = logging.getLogger(__name__)


@dataclass
class TiltSeriesStatus:
    """Class TiltSeriesStatus implementation."""

    position: str
    expected_count: int
    current_count: int
    last_file_time: float
    is_complete: bool
    mdoc_found: bool

    @property
    def completion_percentage(self) -> float:
        """Execute completion_percentage operation."""
        return (
            (self.current_count / self.expected_count * 100)
            if self.expected_count > 0
            else 0.0
        )


@dataclass
class FileEvent:
    """Represents a file system event."""

    event_type: str
    path: str
    is_directory: bool = False


class FileWatcher(QObject):
    """Watches directories for file changes."""

    def __init__(self, parent=None) -> None:
        """Initialize the instance."""
        super().__init__(parent)
        self._watcher = QFileSystemWatcher(self)
        self._callbacks: Dict[str, Callable[[FileEvent], None]] = {}
        self._watched_files: Set[str] = set()
        self._watched_dirs: Set[str] = set()

        # Connect signals
        self._watcher.directoryChanged.connect(self._on_directory_changed)
        self._watcher.fileChanged.connect(self._on_file_changed)

        # Timer for batching notifications
        from PyQt6.QtCore import QTimer

        self._timer = QTimer(self)
        self._timer.setSingleShot(True)
        self._timer.timeout.connect(self._process_pending_events)

        # Pending events
        self._pending_events: List[FileEvent] = []
        self._processing = False

    def watch(self, path: str, callback: Callable[[FileEvent], None]) -> None:
        """
        Watch a path for changes.

        Args:
            path: Path to watch (file or directory)
            callback: Function to call when changes occur
        """
        path = str(Path(path).resolve())
        self._callbacks[path] = callback

        # Add path to watch list
        if os.path.isdir(path):
            self._watched_dirs.add(path)
            self._watcher.addPath(path)
            # Also watch existing files in directory
            for item in os.listdir(path):
                full_path = os.path.join(path, item)
                if os.path.isfile(full_path):
                    self._watched_files.add(full_path)
                    self._watcher.addPath(full_path)
        else:
            self._watched_files.add(path)
            self._watcher.addPath(path)

        # Initial notification for directory contents
        if os.path.isdir(path):
            for item in os.listdir(path):
                full_path = os.path.join(path, item)
                is_dir = os.path.isdir(full_path)
                self._add_pending_event(full_path, is_dir, "created")

        self._schedule_process_events()

    def _add_pending_event(
        self, path: str, is_directory: bool, event_type: str
    ) -> None:
        """Add an event to the pending queue."""
        self._pending_events.append(FileEvent(event_type, path, is_directory))
        self._schedule_process_events()

    def _schedule_process_events(self) -> None:
        """Schedule processing of pending events."""
        if not self._timer.isActive():
            self._timer.start(100)  # 100ms delay before processing

    def _process_pending_events(self) -> None:
        """Process all pending events."""
        if self._processing:
            return

        self._processing = True
        try:
            while self._pending_events:
                event = self._pending_events.pop(0)
                containing_dir = str(Path(event.path).parent)

                if containing_dir in self._callbacks:
                    self._callbacks[containing_dir](event)

                if event.path in self._callbacks:
                    self._callbacks[event.path](event)
        finally:
            self._processing = False

    def _on_directory_changed(self, path: str) -> None:
        """Handle changes to directories."""
        # Get current files
        current_files = set()
        dir_path = Path(path)
        if dir_path.exists():
            for item in os.listdir(path):
                full_path = os.path.join(path, item)
                current_files.add(full_path)
                if os.path.isfile(full_path) and full_path not in self._watched_files:
                    # New file
                    self._watched_files.add(full_path)
                    self._watcher.addPath(full_path)
                    self._add_pending_event(full_path, False, "created")

        # Look for deleted files
        for file_path in self._watched_files.copy():
            if str(Path(file_path).parent) == path and file_path not in current_files:
                # File was deleted
                self._watched_files.remove(file_path)
                self._add_pending_event(file_path, False, "deleted")

    def _on_file_changed(self, path: str) -> None:
        """Handle changes to files."""
        if not os.path.exists(path):
            # File was deleted
            self._watched_files.discard(path)
            self._add_pending_event(path, False, "deleted")
        else:
            # File was modified
            self._add_pending_event(path, False, "modified")

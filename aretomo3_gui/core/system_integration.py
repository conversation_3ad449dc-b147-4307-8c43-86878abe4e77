#!/usr/bin/env python3
"""
AreTomo3 GUI System Integration Manager
Coordinates all system components and ensures seamless integration.
"""

import asyncio
import logging
import threading
from dataclasses import dataclass
from datetime import datetime
from pathlib import Path
from typing import Any, Dict, List, Optional

logger = logging.getLogger(__name__)


@dataclass
class SystemComponent:
    """Represents a system component with its status and dependencies."""

    name: str
    status: str = "inactive"
    dependencies: List[str] = None
    last_check: Optional[datetime] = None
    error_count: int = 0
    max_errors: int = 5


class SystemIntegrationManager:
    """
    Manages integration between all AreTomo3 GUI components.
    Ensures proper startup, shutdown, and communication between subsystems.
    """

    def __init__(self):
        """Initialize the system integration manager."""
        self.components = {}
        self.startup_order = []
        self.shutdown_order = []
        self.integration_status = "initializing"
        self.event_loop = None
        self.monitoring_thread = None
        self.is_running = False

        # Initialize component registry
        self._register_components()

        logger.info("System Integration Manager initialized")

    def _register_components(self):
        """Register all system components with their dependencies."""
        components_config = {
            "config_manager": {"dependencies": [], "critical": True},
            "theme_manager": {"dependencies": ["config_manager"], "critical": True},
            "session_manager": {"dependencies": ["config_manager"], "critical": True},
            "continue_mode_manager": {
                "dependencies": ["config_manager"],
                "critical": True,
            },
            "realtime_processor": {
                "dependencies": ["config_manager", "session_manager"],
                "critical": False,
            },
            "results_tracker": {"dependencies": ["config_manager"], "critical": False},
            "web_server": {
                "dependencies": ["config_manager", "results_tracker"],
                "critical": False,
            },
            "system_monitor": {"dependencies": ["config_manager"], "critical": False},
            "backup_system": {"dependencies": ["config_manager"], "critical": False},
            "gui_main_window": {
                "dependencies": ["config_manager", "theme_manager", "session_manager"],
                "critical": True,
            },
        }

        for name, config in components_config.items():
            self.components[name] = SystemComponent(
                name=name, dependencies=config.get("dependencies", [])
            )

        # Determine startup order based on dependencies
        self._calculate_startup_order()

    def _calculate_startup_order(self):
        """Calculate the optimal startup order based on dependencies."""
        visited = set()
        temp_visited = set()

        def visit(component_name):
            """Execute visit operation."""
            if component_name in temp_visited:
                raise ValueError(
                    f"Circular dependency detected involving {component_name}"
                )
            if component_name in visited:
                return

            temp_visited.add(component_name)
            component = self.components.get(component_name)
            if component and component.dependencies:
                for dep in component.dependencies:
                    visit(dep)

            temp_visited.remove(component_name)
            visited.add(component_name)
            if component_name not in self.startup_order:
                self.startup_order.append(component_name)

        for component_name in self.components:
            visit(component_name)

        # Shutdown order is reverse of startup
        self.shutdown_order = list(reversed(self.startup_order))

        logger.info(f"Calculated startup order: {self.startup_order}")

    async def start_system(self):
        """Start all system components in the correct order."""
        logger.info("Starting system integration...")
        self.integration_status = "starting"

        try:
            for component_name in self.startup_order:
                await self._start_component(component_name)

            self.integration_status = "running"
            self.is_running = True

            # Start monitoring thread
            self.monitoring_thread = threading.Thread(
                target=self._monitor_components, daemon=True
            )
            self.monitoring_thread.start()

            logger.info("System integration completed successfully")

        except Exception as e:
            logger.error(f"System startup failed: {e}")
            self.integration_status = "failed"
            await self.shutdown_system()
            raise

    async def _start_component(self, component_name: str):
        """Start a specific component."""
        component = self.components.get(component_name)
        if not component:
            logger.warning(f"Unknown component: {component_name}")
            return

        logger.info(f"Starting component: {component_name}")

        try:
            # Check dependencies first
            for dep_name in component.dependencies:
                dep_component = self.components.get(dep_name)
                if not dep_component or dep_component.status != "active":
                    raise RuntimeError(
                        f"Dependency {dep_name} not available for {component_name}"
                    )

            # Component-specific startup logic would go here
            # For now, we'll mark as active
            component.status = "active"
            component.last_check = datetime.now()
            component.error_count = 0

            logger.info(f"Component {component_name} started successfully")

        except Exception as e:
            logger.error(f"Failed to start component {component_name}: {e}")
            component.status = "failed"
            component.error_count += 1
            raise

    async def shutdown_system(self):
        """Shutdown all system components in reverse order."""
        logger.info("Shutting down system integration...")
        self.integration_status = "shutting_down"
        self.is_running = False

        for component_name in self.shutdown_order:
            await self._stop_component(component_name)

        self.integration_status = "stopped"
        logger.info("System shutdown completed")

    async def _stop_component(self, component_name: str):
        """Stop a specific component."""
        component = self.components.get(component_name)
        if not component:
            return

        logger.info(f"Stopping component: {component_name}")

        try:
            # Component-specific shutdown logic would go here
            component.status = "inactive"
            component.last_check = datetime.now()

        except Exception as e:
            logger.error(f"Error stopping component {component_name}: {e}")
            component.status = "error"

    def _monitor_components(self):
        """Monitor component health in a separate thread."""
        while self.is_running:
            try:
                for component in self.components.values():
                    if component.status == "active":
                        # Perform health check
                        self._health_check_component(component)

                # Sleep for monitoring interval
                threading.Event().wait(30)  # Check every 30 seconds

            except Exception as e:
                logger.error(f"Error in component monitoring: {e}")

    def _health_check_component(self, component: SystemComponent):
        """Perform health check on a component."""
        try:
            # Component-specific health check logic would go here
            component.last_check = datetime.now()

        except Exception as e:
            logger.warning(f"Health check failed for {component.name}: {e}")
            component.error_count += 1

            if component.error_count >= component.max_errors:
                logger.error(
                    f"Component { component.name} exceeded max errors, marking as failed"
                )
                component.status = "failed"

    def get_system_status(self) -> Dict[str, Any]:
        """Get comprehensive system status."""
        return {
            "integration_status": self.integration_status,
            "is_running": self.is_running,
            "components": {
                name: {
                    "status": comp.status,
                    "last_check": (
                        comp.last_check.isoformat() if comp.last_check else None
                    ),
                    "error_count": comp.error_count,
                    "dependencies": comp.dependencies,
                }
                for name, comp in self.components.items()
            },
            "startup_order": self.startup_order,
            "timestamp": datetime.now().isoformat(),
        }

    def restart_component(self, component_name: str):
        """Restart a specific component."""
        logger.info(f"Restarting component: {component_name}")

        async def _restart():
            await self._stop_component(component_name)
            await self._start_component(component_name)

        if self.event_loop:
            asyncio.run_coroutine_threadsafe(_restart(), self.event_loop)


# Global system integration manager instance
system_integration_manager = SystemIntegrationManager()

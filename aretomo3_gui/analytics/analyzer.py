"""
Advanced Statistics & Analytics
Comprehensive data analysis and statistical modeling
"""

import numpy as np
import pandas as pd
from scipy import stats
from sklearn.decomposition import PCA
from sklearn.cluster import KMeans
from sklearn.metrics import silhouette_score
import matplotlib.pyplot as plt
import seaborn as sns
from typing import Dict, List, Any, Tuple, Optional
from dataclasses import dataclass
import warnings
warnings.filterwarnings('ignore')


@dataclass
class AnalysisResult:
    """Container for analysis results."""
    name: str
    data: Any
    statistics: Dict[str, float]
    plots: List[str]
    interpretation: str


class StatisticalAnalyzer:
    """Advanced statistical analysis tools."""
    
    def __init__(self):
        self.results = {}
        
    def descriptive_statistics(self, data: np.ndarray, name: str = "dataset") -> AnalysisResult:
        """Compute comprehensive descriptive statistics."""
        
        # Basic statistics
        stats_dict = {
            'mean': np.mean(data),
            'median': np.median(data),
            'std': np.std(data),
            'var': np.var(data),
            'min': np.min(data),
            'max': np.max(data),
            'range': np.ptp(data),
            'skewness': stats.skew(data.flatten()),
            'kurtosis': stats.kurtosis(data.flatten()),
            'q25': np.percentile(data, 25),
            'q75': np.percentile(data, 75),
            'iqr': np.percentile(data, 75) - np.percentile(data, 25)
        }
        
        # Interpretation
        interpretation = f"""
        Dataset '{name}' Analysis:
        - Mean: {stats_dict['mean']:.3f}
        - Standard Deviation: {stats_dict['std']:.3f}
        - Skewness: {stats_dict['skewness']:.3f} ({'right-skewed' if stats_dict['skewness'] > 0 else 'left-skewed' if stats_dict['skewness'] < 0 else 'symmetric'})
        - Kurtosis: {stats_dict['kurtosis']:.3f} ({'heavy-tailed' if stats_dict['kurtosis'] > 0 else 'light-tailed'})
        """
        
        result = AnalysisResult(
            name=f"descriptive_{name}",
            data=data,
            statistics=stats_dict,
            plots=[],
            interpretation=interpretation
        )
        
        self.results[result.name] = result
        return result
        
    def hypothesis_testing(self, data1: np.ndarray, data2: np.ndarray, 
                          test_type: str = "ttest") -> AnalysisResult:
        """Perform hypothesis testing between two datasets."""
        
        if test_type == "ttest":
            statistic, p_value = stats.ttest_ind(data1.flatten(), data2.flatten())
            test_name = "Independent t-test"
        elif test_type == "mannwhitney":
            statistic, p_value = stats.mannwhitneyu(data1.flatten(), data2.flatten())
            test_name = "Mann-Whitney U test"
        elif test_type == "ks":
            statistic, p_value = stats.ks_2samp(data1.flatten(), data2.flatten())
            test_name = "Kolmogorov-Smirnov test"
        else:
            raise ValueError(f"Unknown test type: {test_type}")
            
        # Effect size (Cohen's d for t-test)
        if test_type == "ttest":
            pooled_std = np.sqrt(((len(data1) - 1) * np.var(data1) + 
                                (len(data2) - 1) * np.var(data2)) / 
                               (len(data1) + len(data2) - 2))
            effect_size = (np.mean(data1) - np.mean(data2)) / pooled_std
        else:
            effect_size = None
            
        stats_dict = {
            'statistic': statistic,
            'p_value': p_value,
            'effect_size': effect_size,
            'significant': p_value < 0.05
        }
        
        interpretation = f"""
        {test_name} Results:
        - Test statistic: {statistic:.3f}
        - p-value: {p_value:.6f}
        - Significant: {'Yes' if p_value < 0.05 else 'No'} (α = 0.05)
        {f'- Effect size (Cohen\'s d): {effect_size:.3f}' if effect_size else ''}
        """
        
        result = AnalysisResult(
            name=f"hypothesis_{test_type}",
            data=(data1, data2),
            statistics=stats_dict,
            plots=[],
            interpretation=interpretation
        )
        
        self.results[result.name] = result
        return result
        
    def correlation_analysis(self, data: np.ndarray) -> AnalysisResult:
        """Perform correlation analysis on multidimensional data."""
        
        if data.ndim > 2:
            # Flatten to 2D for correlation analysis
            data_2d = data.reshape(data.shape[0], -1)
        else:
            data_2d = data
            
        # Compute correlation matrix
        corr_matrix = np.corrcoef(data_2d)
        
        # Find strongest correlations
        mask = np.triu(np.ones_like(corr_matrix, dtype=bool), k=1)
        corr_values = corr_matrix[mask]
        
        stats_dict = {
            'mean_correlation': np.mean(np.abs(corr_values)),
            'max_correlation': np.max(np.abs(corr_values)),
            'min_correlation': np.min(np.abs(corr_values)),
            'strong_correlations': np.sum(np.abs(corr_values) > 0.7),
            'moderate_correlations': np.sum((np.abs(corr_values) > 0.3) & (np.abs(corr_values) <= 0.7)),
            'weak_correlations': np.sum(np.abs(corr_values) <= 0.3)
        }
        
        interpretation = f"""
        Correlation Analysis:
        - Mean absolute correlation: {stats_dict['mean_correlation']:.3f}
        - Strongest correlation: {stats_dict['max_correlation']:.3f}
        - Strong correlations (>0.7): {stats_dict['strong_correlations']}
        - Moderate correlations (0.3-0.7): {stats_dict['moderate_correlations']}
        - Weak correlations (<0.3): {stats_dict['weak_correlations']}
        """
        
        result = AnalysisResult(
            name="correlation_analysis",
            data=corr_matrix,
            statistics=stats_dict,
            plots=[],
            interpretation=interpretation
        )
        
        self.results[result.name] = result
        return result


class QualityMetrics:
    """Quality assessment metrics for tomographic data."""
    
    def __init__(self):
        self.metrics = {}
        
    def signal_to_noise_ratio(self, data: np.ndarray, signal_region: Optional[Tuple] = None,
                            noise_region: Optional[Tuple] = None) -> float:
        """Calculate signal-to-noise ratio."""
        
        if signal_region is None:
            # Use central region as signal
            center = tuple(s // 2 for s in data.shape)
            signal_size = tuple(s // 4 for s in data.shape)
            signal_region = tuple(slice(c - s//2, c + s//2) 
                                for c, s in zip(center, signal_size))
        
        if noise_region is None:
            # Use corner regions as noise
            noise_size = tuple(s // 8 for s in data.shape)
            noise_region = tuple(slice(0, s) for s in noise_size)
            
        signal = data[signal_region]
        noise = data[noise_region]
        
        signal_power = np.mean(signal**2)
        noise_power = np.mean(noise**2)
        
        snr = 10 * np.log10(signal_power / noise_power) if noise_power > 0 else np.inf
        return snr
        
    def contrast_to_noise_ratio(self, data: np.ndarray) -> float:
        """Calculate contrast-to-noise ratio."""
        
        # Simple CNR calculation
        mean_val = np.mean(data)
        std_val = np.std(data)
        
        cnr = mean_val / std_val if std_val > 0 else np.inf
        return cnr
        
    def resolution_estimate(self, data: np.ndarray) -> Dict[str, float]:
        """Estimate resolution using Fourier ring correlation."""
        
        # Simplified resolution estimation
        # In practice, would use proper FRC calculation
        
        fft_data = np.fft.fftn(data)
        power_spectrum = np.abs(fft_data)**2
        
        # Radial average
        center = tuple(s // 2 for s in data.shape)
        max_radius = min(center)
        
        radial_profile = []
        for r in range(max_radius):
            # Create spherical shell mask (simplified for 3D)
            mask = np.zeros(data.shape, dtype=bool)
            # This is a simplified version - proper implementation would create spherical shells
            if len(data.shape) == 3:
                z, y, x = np.ogrid[:data.shape[0], :data.shape[1], :data.shape[2]]
                distance = np.sqrt((z - center[0])**2 + (y - center[1])**2 + (x - center[2])**2)
                mask = (distance >= r) & (distance < r + 1)
            
            if np.any(mask):
                radial_profile.append(np.mean(power_spectrum[mask]))
            else:
                radial_profile.append(0)
                
        # Find resolution where signal drops to threshold
        threshold = 0.5 * np.max(radial_profile)
        resolution_idx = np.where(np.array(radial_profile) < threshold)[0]
        resolution_pixel = resolution_idx[0] if len(resolution_idx) > 0 else max_radius
        
        return {
            'resolution_pixel': float(resolution_pixel),
            'resolution_relative': float(resolution_pixel / max_radius),
            'nyquist_frequency': float(max_radius)
        }


class AdvancedAnalytics:
    """Main analytics coordinator."""
    
    def __init__(self):
        self.statistical_analyzer = StatisticalAnalyzer()
        self.quality_metrics = QualityMetrics()
        self.analysis_history = []
        
    def analyze_dataset(self, data: np.ndarray, name: str = "dataset") -> Dict[str, Any]:
        """Perform comprehensive analysis of dataset."""
        
        results = {}
        
        # Descriptive statistics
        desc_result = self.statistical_analyzer.descriptive_statistics(data, name)
        results['descriptive'] = desc_result
        
        # Quality metrics
        snr = self.quality_metrics.signal_to_noise_ratio(data)
        cnr = self.quality_metrics.contrast_to_noise_ratio(data)
        resolution = self.quality_metrics.resolution_estimate(data)
        
        results['quality'] = {
            'snr': snr,
            'cnr': cnr,
            'resolution': resolution
        }
        
        # Correlation analysis (if multidimensional)
        if data.ndim > 1:
            corr_result = self.statistical_analyzer.correlation_analysis(data)
            results['correlation'] = corr_result
            
        # Store in history
        analysis_record = {
            'name': name,
            'timestamp': pd.Timestamp.now(),
            'results': results
        }
        self.analysis_history.append(analysis_record)
        
        return results
        
    def compare_datasets(self, data1: np.ndarray, data2: np.ndarray, 
                        names: Tuple[str, str] = ("dataset1", "dataset2")) -> Dict[str, Any]:
        """Compare two datasets statistically."""
        
        results = {}
        
        # Individual analyses
        results['dataset1'] = self.analyze_dataset(data1, names[0])
        results['dataset2'] = self.analyze_dataset(data2, names[1])
        
        # Comparative tests
        ttest_result = self.statistical_analyzer.hypothesis_testing(data1, data2, "ttest")
        mannwhitney_result = self.statistical_analyzer.hypothesis_testing(data1, data2, "mannwhitney")
        
        results['comparison'] = {
            'ttest': ttest_result,
            'mannwhitney': mannwhitney_result
        }
        
        return results
        
    def generate_report(self, analysis_results: Dict[str, Any]) -> str:
        """Generate comprehensive analysis report."""
        
        report = "# AreTomo3 GUI - Advanced Analytics Report\n\n"
        
        if 'descriptive' in analysis_results:
            desc = analysis_results['descriptive']
            report += f"## Descriptive Statistics\n{desc.interpretation}\n\n"
            
        if 'quality' in analysis_results:
            quality = analysis_results['quality']
            report += f"""## Quality Metrics
- Signal-to-Noise Ratio: {quality['snr']:.2f} dB
- Contrast-to-Noise Ratio: {quality['cnr']:.2f}
- Resolution (pixels): {quality['resolution']['resolution_pixel']:.1f}
- Relative Resolution: {quality['resolution']['resolution_relative']:.3f}

"""
            
        if 'correlation' in analysis_results:
            corr = analysis_results['correlation']
            report += f"## Correlation Analysis\n{corr.interpretation}\n\n"
            
        return report


# Global analytics instance
analytics = AdvancedAnalytics()

{"meta": {"format": 3, "version": "7.8.0", "timestamp": "2025-06-08T22:21:32.909013", "branch_coverage": false, "show_contexts": false}, "files": {"aretomo3_gui/analytics/__init__.py": {"executed_lines": [1, 5, 7], "summary": {"covered_lines": 2, "num_statements": 2, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": [], "functions": {"": {"executed_lines": [1, 5, 7], "summary": {"covered_lines": 2, "num_statements": 2, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}}, "classes": {"": {"executed_lines": [1, 5, 7], "summary": {"covered_lines": 2, "num_statements": 2, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}}}, "aretomo3_gui/analytics/advanced_analytics.py": {"executed_lines": [2, 7, 8, 9, 10, 11, 12, 13, 14, 15, 17, 20, 21, 22, 23, 28, 29, 30, 31, 36, 37, 38, 39, 40, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 71, 72, 74, 75, 86, 88, 90, 91, 92, 93, 96, 106, 109, 112, 114, 129, 131, 133, 134, 135, 136, 137, 140, 141, 143, 154, 157, 160, 162, 177, 179, 181, 182, 183, 184, 185, 186, 188, 190, 192, 193, 194, 197, 198, 199, 200, 202, 205, 208, 209, 210, 211, 213, 216, 218, 220, 222, 223, 225, 226, 228, 229, 231, 232, 234, 235, 237, 239, 241, 243, 244, 245, 247, 248, 250, 251, 253, 254, 256, 257, 259, 261, 263, 275, 276, 278, 279, 281, 283, 284, 286, 288, 291, 292, 293, 296, 297, 300, 303, 304, 305, 316, 327, 329, 330, 332, 333, 335, 336, 337, 338, 339, 340, 341, 343, 344, 345, 347, 349, 357, 358, 360, 361, 362, 364, 367, 370, 372, 373, 375, 379, 380, 383, 384, 387, 388, 389, 390, 392, 399, 406, 407, 408, 410, 413, 416, 418, 419, 421, 425, 426, 429, 430, 433, 434, 436, 447, 448, 449, 452, 453, 455, 456, 457, 458, 461, 463, 465, 467, 469, 470, 471, 472, 474, 475, 477, 480, 482, 483, 484, 485, 488, 489, 492, 495, 514, 516, 522, 524, 525, 526, 529, 532, 533, 535, 537, 538, 540, 542], "summary": {"covered_lines": 237, "num_statements": 268, "percent_covered": 88.43283582089552, "percent_covered_display": "88", "missing_lines": 31, "excluded_lines": 0}, "missing_lines": [24, 25, 26, 32, 33, 34, 41, 42, 43, 125, 126, 127, 173, 174, 175, 306, 307, 308, 309, 323, 324, 325, 368, 376, 414, 422, 518, 519, 520, 536, 544], "excluded_lines": [], "functions": {"DataQualityAnalyzer.__init__": {"executed_lines": [75], "summary": {"covered_lines": 1, "num_statements": 1, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "DataQualityAnalyzer.analyze_motion_correction": {"executed_lines": [88, 90, 91, 92, 93, 96, 106, 109, 112, 114], "summary": {"covered_lines": 10, "num_statements": 13, "percent_covered": 76.92307692307692, "percent_covered_display": "77", "missing_lines": 3, "excluded_lines": 0}, "missing_lines": [125, 126, 127], "excluded_lines": []}, "DataQualityAnalyzer.analyze_ctf_estimation": {"executed_lines": [131, 133, 134, 135, 136, 137, 140, 141, 143, 154, 157, 160, 162], "summary": {"covered_lines": 13, "num_statements": 16, "percent_covered": 81.25, "percent_covered_display": "81", "missing_lines": 3, "excluded_lines": 0}, "missing_lines": [173, 174, 175], "excluded_lines": []}, "DataQualityAnalyzer._assess_motion_quality": {"executed_lines": [179, 181, 182, 183, 184, 185, 186, 188], "summary": {"covered_lines": 8, "num_statements": 8, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "DataQualityAnalyzer._assess_ctf_quality": {"executed_lines": [192, 193, 194, 197, 198, 199, 200, 202, 205, 208, 209, 210, 211, 213, 216], "summary": {"covered_lines": 15, "num_statements": 15, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "DataQualityAnalyzer._generate_motion_recommendations": {"executed_lines": [220, 222, 223, 225, 226, 228, 229, 231, 232, 234, 235, 237], "summary": {"covered_lines": 12, "num_statements": 12, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "DataQualityAnalyzer._generate_ctf_recommendations": {"executed_lines": [241, 243, 244, 245, 247, 248, 250, 251, 253, 254, 256, 257, 259], "summary": {"covered_lines": 13, "num_statements": 13, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "DataQualityAnalyzer._create_error_result": {"executed_lines": [263], "summary": {"covered_lines": 1, "num_statements": 1, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "StatisticalAnalyzer.__init__": {"executed_lines": [279], "summary": {"covered_lines": 1, "num_statements": 1, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "StatisticalAnalyzer.analyze_dataset_trends": {"executed_lines": [283, 284, 286, 288, 291, 292, 293, 296, 297, 300, 303, 304, 305, 316], "summary": {"covered_lines": 14, "num_statements": 21, "percent_covered": 66.66666666666667, "percent_covered_display": "67", "missing_lines": 7, "excluded_lines": 0}, "missing_lines": [306, 307, 308, 309, 323, 324, 325], "excluded_lines": []}, "StatisticalAnalyzer.perform_outlier_detection": {"executed_lines": [329, 330, 332, 333, 335, 336, 337, 338, 339, 340, 341, 343, 344, 345, 347, 349], "summary": {"covered_lines": 16, "num_statements": 16, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "MachineLearningAnalyzer.__init__": {"executed_lines": [361, 362], "summary": {"covered_lines": 2, "num_statements": 2, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "MachineLearningAnalyzer.perform_clustering_analysis": {"executed_lines": [367, 370, 372, 373, 375, 379, 380, 383, 384, 387, 388, 389, 390, 392, 399, 406, 407, 408], "summary": {"covered_lines": 18, "num_statements": 20, "percent_covered": 90.0, "percent_covered_display": "90", "missing_lines": 2, "excluded_lines": 0}, "missing_lines": [368, 376], "excluded_lines": []}, "MachineLearningAnalyzer.perform_pca_analysis": {"executed_lines": [413, 416, 418, 419, 421, 425, 426, 429, 430, 433, 434, 436, 447, 448, 449], "summary": {"covered_lines": 15, "num_statements": 17, "percent_covered": 88.23529411764706, "percent_covered_display": "88", "missing_lines": 2, "excluded_lines": 0}, "missing_lines": [414, 422], "excluded_lines": []}, "AdvancedAnalytics.__init__": {"executed_lines": [456, 457, 458, 461, 463], "summary": {"covered_lines": 5, "num_statements": 5, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "AdvancedAnalytics.analyze_processing_results": {"executed_lines": [467, 469, 470, 471, 472, 474, 475], "summary": {"covered_lines": 7, "num_statements": 7, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "AdvancedAnalytics.generate_comprehensive_report": {"executed_lines": [480, 482, 483, 484, 485, 488, 489, 492, 495, 514, 516], "summary": {"covered_lines": 11, "num_statements": 14, "percent_covered": 78.57142857142857, "percent_covered_display": "79", "missing_lines": 3, "excluded_lines": 0}, "missing_lines": [518, 519, 520], "excluded_lines": []}, "AdvancedAnalytics._generate_overall_recommendations": {"executed_lines": [524, 525, 526, 529, 532, 533, 535, 537, 538, 540], "summary": {"covered_lines": 10, "num_statements": 11, "percent_covered": 90.9090909090909, "percent_covered_display": "91", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [536], "excluded_lines": []}, "AdvancedAnalytics.get_analytics_summary": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [544], "excluded_lines": []}, "": {"executed_lines": [2, 7, 8, 9, 10, 11, 12, 13, 14, 15, 17, 20, 21, 22, 23, 28, 29, 30, 31, 36, 37, 38, 39, 40, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 71, 72, 74, 86, 129, 177, 190, 218, 239, 261, 275, 276, 278, 281, 327, 357, 358, 360, 364, 410, 452, 453, 455, 465, 477, 522, 542], "summary": {"covered_lines": 65, "num_statements": 74, "percent_covered": 87.83783783783784, "percent_covered_display": "88", "missing_lines": 9, "excluded_lines": 0}, "missing_lines": [24, 25, 26, 32, 33, 34, 41, 42, 43], "excluded_lines": []}}, "classes": {"AnalyticsResult": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "QualityMetrics": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "DataQualityAnalyzer": {"executed_lines": [75, 88, 90, 91, 92, 93, 96, 106, 109, 112, 114, 131, 133, 134, 135, 136, 137, 140, 141, 143, 154, 157, 160, 162, 179, 181, 182, 183, 184, 185, 186, 188, 192, 193, 194, 197, 198, 199, 200, 202, 205, 208, 209, 210, 211, 213, 216, 220, 222, 223, 225, 226, 228, 229, 231, 232, 234, 235, 237, 241, 243, 244, 245, 247, 248, 250, 251, 253, 254, 256, 257, 259, 263], "summary": {"covered_lines": 73, "num_statements": 79, "percent_covered": 92.40506329113924, "percent_covered_display": "92", "missing_lines": 6, "excluded_lines": 0}, "missing_lines": [125, 126, 127, 173, 174, 175], "excluded_lines": []}, "StatisticalAnalyzer": {"executed_lines": [279, 283, 284, 286, 288, 291, 292, 293, 296, 297, 300, 303, 304, 305, 316, 329, 330, 332, 333, 335, 336, 337, 338, 339, 340, 341, 343, 344, 345, 347, 349], "summary": {"covered_lines": 31, "num_statements": 38, "percent_covered": 81.57894736842105, "percent_covered_display": "82", "missing_lines": 7, "excluded_lines": 0}, "missing_lines": [306, 307, 308, 309, 323, 324, 325], "excluded_lines": []}, "MachineLearningAnalyzer": {"executed_lines": [361, 362, 367, 370, 372, 373, 375, 379, 380, 383, 384, 387, 388, 389, 390, 392, 399, 406, 407, 408, 413, 416, 418, 419, 421, 425, 426, 429, 430, 433, 434, 436, 447, 448, 449], "summary": {"covered_lines": 35, "num_statements": 39, "percent_covered": 89.74358974358974, "percent_covered_display": "90", "missing_lines": 4, "excluded_lines": 0}, "missing_lines": [368, 376, 414, 422], "excluded_lines": []}, "AdvancedAnalytics": {"executed_lines": [456, 457, 458, 461, 463, 467, 469, 470, 471, 472, 474, 475, 480, 482, 483, 484, 485, 488, 489, 492, 495, 514, 516, 524, 525, 526, 529, 532, 533, 535, 537, 538, 540], "summary": {"covered_lines": 33, "num_statements": 38, "percent_covered": 86.84210526315789, "percent_covered_display": "87", "missing_lines": 5, "excluded_lines": 0}, "missing_lines": [518, 519, 520, 536, 544], "excluded_lines": []}, "": {"executed_lines": [2, 7, 8, 9, 10, 11, 12, 13, 14, 15, 17, 20, 21, 22, 23, 28, 29, 30, 31, 36, 37, 38, 39, 40, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 71, 72, 74, 86, 129, 177, 190, 218, 239, 261, 275, 276, 278, 281, 327, 357, 358, 360, 364, 410, 452, 453, 455, 465, 477, 522, 542], "summary": {"covered_lines": 65, "num_statements": 74, "percent_covered": 87.83783783783784, "percent_covered_display": "88", "missing_lines": 9, "excluded_lines": 0}, "missing_lines": [24, 25, 26, 32, 33, 34, 41, 42, 43], "excluded_lines": []}}}, "aretomo3_gui/analytics/analyzer.py": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 141, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 141, "excluded_lines": 0}, "missing_lines": [6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 20, 21, 23, 24, 25, 26, 27, 30, 33, 34, 36, 40, 56, 64, 72, 73, 75, 79, 80, 81, 82, 83, 84, 85, 86, 87, 89, 92, 93, 96, 98, 100, 107, 115, 123, 124, 126, 129, 131, 133, 136, 139, 140, 142, 151, 160, 168, 169, 172, 175, 176, 178, 182, 184, 185, 186, 189, 191, 192, 194, 195, 197, 198, 200, 201, 203, 207, 208, 210, 211, 213, 219, 220, 223, 224, 226, 227, 229, 231, 232, 233, 234, 236, 237, 239, 242, 243, 244, 246, 253, 256, 257, 258, 259, 261, 264, 267, 268, 271, 272, 273, 275, 282, 283, 284, 287, 292, 294, 296, 300, 303, 304, 307, 308, 310, 315, 317, 320, 322, 323, 324, 326, 327, 328, 336, 337, 338, 340, 344], "excluded_lines": [], "functions": {"StatisticalAnalyzer.__init__": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [34], "excluded_lines": []}, "StatisticalAnalyzer.descriptive_statistics": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 5, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 5, "excluded_lines": 0}, "missing_lines": [40, 56, 64, 72, 73], "excluded_lines": []}, "StatisticalAnalyzer.hypothesis_testing": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 19, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 19, "excluded_lines": 0}, "missing_lines": [79, 80, 81, 82, 83, 84, 85, 86, 87, 89, 92, 93, 96, 98, 100, 107, 115, 123, 124], "excluded_lines": []}, "StatisticalAnalyzer.correlation_analysis": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 11, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 11, "excluded_lines": 0}, "missing_lines": [129, 131, 133, 136, 139, 140, 142, 151, 160, 168, 169], "excluded_lines": []}, "QualityMetrics.__init__": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [176], "excluded_lines": []}, "QualityMetrics.signal_to_noise_ratio": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 13, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 13, "excluded_lines": 0}, "missing_lines": [182, 184, 185, 186, 189, 191, 192, 194, 195, 197, 198, 200, 201], "excluded_lines": []}, "QualityMetrics.contrast_to_noise_ratio": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 4, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 4, "excluded_lines": 0}, "missing_lines": [207, 208, 210, 211], "excluded_lines": []}, "QualityMetrics.resolution_estimate": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 18, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 18, "excluded_lines": 0}, "missing_lines": [219, 220, 223, 224, 226, 227, 229, 231, 232, 233, 234, 236, 237, 239, 242, 243, 244, 246], "excluded_lines": []}, "AdvancedAnalytics.__init__": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 3, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 3, "excluded_lines": 0}, "missing_lines": [257, 258, 259], "excluded_lines": []}, "AdvancedAnalytics.analyze_dataset": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 13, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 13, "excluded_lines": 0}, "missing_lines": [264, 267, 268, 271, 272, 273, 275, 282, 283, 284, 287, 292, 294], "excluded_lines": []}, "AdvancedAnalytics.compare_datasets": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 7, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 7, "excluded_lines": 0}, "missing_lines": [300, 303, 304, 307, 308, 310, 315], "excluded_lines": []}, "AdvancedAnalytics.generate_report": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 11, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 11, "excluded_lines": 0}, "missing_lines": [320, 322, 323, 324, 326, 327, 328, 336, 337, 338, 340], "excluded_lines": []}, "": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 35, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 35, "excluded_lines": 0}, "missing_lines": [6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 20, 21, 23, 24, 25, 26, 27, 30, 33, 36, 75, 126, 172, 175, 178, 203, 213, 253, 256, 261, 296, 317, 344], "excluded_lines": []}}, "classes": {"AnalysisResult": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "StatisticalAnalyzer": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 36, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 36, "excluded_lines": 0}, "missing_lines": [34, 40, 56, 64, 72, 73, 79, 80, 81, 82, 83, 84, 85, 86, 87, 89, 92, 93, 96, 98, 100, 107, 115, 123, 124, 129, 131, 133, 136, 139, 140, 142, 151, 160, 168, 169], "excluded_lines": []}, "QualityMetrics": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 36, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 36, "excluded_lines": 0}, "missing_lines": [176, 182, 184, 185, 186, 189, 191, 192, 194, 195, 197, 198, 200, 201, 207, 208, 210, 211, 219, 220, 223, 224, 226, 227, 229, 231, 232, 233, 234, 236, 237, 239, 242, 243, 244, 246], "excluded_lines": []}, "AdvancedAnalytics": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 34, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 34, "excluded_lines": 0}, "missing_lines": [257, 258, 259, 264, 267, 268, 271, 272, 273, 275, 282, 283, 284, 287, 292, 294, 300, 303, 304, 307, 308, 310, 315, 320, 322, 323, 324, 326, 327, 328, 336, 337, 338, 340], "excluded_lines": []}, "": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 35, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 35, "excluded_lines": 0}, "missing_lines": [6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 20, 21, 23, 24, 25, 26, 27, 30, 33, 36, 75, 126, 172, 175, 178, 203, 213, 253, 256, 261, 296, 317, 344], "excluded_lines": []}}}, "aretomo3_gui/core/__init__.py": {"executed_lines": [1], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": [], "functions": {"": {"executed_lines": [1], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}}, "classes": {"": {"executed_lines": [1], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}}}, "aretomo3_gui/core/advanced_logging.py": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 190, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 190, "excluded_lines": 0}, "missing_lines": [7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 21, 22, 24, 25, 28, 31, 32, 33, 34, 35, 36, 37, 38, 41, 42, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 58, 61, 63, 64, 66, 69, 70, 71, 72, 73, 74, 76, 90, 93, 98, 100, 101, 104, 105, 106, 109, 110, 111, 114, 115, 118, 119, 122, 123, 126, 129, 130, 132, 134, 136, 138, 140, 143, 148, 149, 152, 153, 154, 157, 158, 160, 162, 163, 169, 170, 172, 174, 175, 177, 178, 180, 181, 182, 185, 196, 197, 198, 201, 203, 204, 205, 206, 208, 210, 211, 214, 215, 218, 219, 221, 223, 233, 246, 247, 248, 250, 251, 254, 255, 257, 259, 266, 269, 270, 274, 276, 278, 280, 282, 284, 286, 288, 290, 292, 294, 296, 298, 306, 310, 312, 320, 322, 324, 326, 327, 329, 330, 331, 332, 333, 334, 336, 337, 338, 340, 341, 342, 344, 346, 355, 356, 357, 358, 360, 366, 368, 370, 371, 373, 374, 376, 378, 380, 381, 384, 385, 388, 389, 392, 394, 398, 402, 404, 407, 414, 417, 419], "excluded_lines": [], "functions": {"StructuredFormatter.__init__": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 2, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 2, "excluded_lines": 0}, "missing_lines": [63, 64], "excluded_lines": []}, "StructuredFormatter.format": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 8, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 8, "excluded_lines": 0}, "missing_lines": [69, 70, 71, 72, 73, 74, 76, 90], "excluded_lines": []}, "AdvancedLogger.__init__": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 17, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 17, "excluded_lines": 0}, "missing_lines": [100, 101, 104, 105, 106, 109, 110, 111, 114, 115, 118, 119, 122, 123, 126, 129, 130], "excluded_lines": []}, "AdvancedLogger._generate_session_id": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [134], "excluded_lines": []}, "AdvancedLogger._setup_loggers": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 10, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 10, "excluded_lines": 0}, "missing_lines": [138, 140, 143, 148, 149, 152, 153, 154, 157, 158], "excluded_lines": []}, "AdvancedLogger._start_log_workers": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 4, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 4, "excluded_lines": 0}, "missing_lines": [162, 163, 169, 170], "excluded_lines": []}, "AdvancedLogger._log_worker": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 16, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 16, "excluded_lines": 0}, "missing_lines": [174, 175, 177, 178, 180, 181, 182, 185, 196, 197, 198, 201, 203, 204, 205, 206], "excluded_lines": []}, "AdvancedLogger._configure_root_logger": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 7, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 7, "excluded_lines": 0}, "missing_lines": [210, 211, 214, 215, 218, 219, 221], "excluded_lines": []}, "AdvancedLogger.log": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 8, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 8, "excluded_lines": 0}, "missing_lines": [233, 246, 247, 248, 250, 251, 254, 255], "excluded_lines": []}, "AdvancedLogger._store_performance_metrics": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 4, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 4, "excluded_lines": 0}, "missing_lines": [259, 266, 269, 270], "excluded_lines": []}, "AdvancedLogger.trace": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [276], "excluded_lines": []}, "AdvancedLogger.debug": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [280], "excluded_lines": []}, "AdvancedLogger.info": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [284], "excluded_lines": []}, "AdvancedLogger.warning": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [288], "excluded_lines": []}, "AdvancedLogger.error": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [292], "excluded_lines": []}, "AdvancedLogger.critical": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [296], "excluded_lines": []}, "AdvancedLogger.performance": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [306], "excluded_lines": []}, "AdvancedLogger.user_action": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [312], "excluded_lines": []}, "AdvancedLogger.set_user_id": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [322], "excluded_lines": []}, "AdvancedLogger.compress_old_logs": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 14, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 14, "excluded_lines": 0}, "missing_lines": [326, 327, 329, 330, 331, 332, 333, 334, 336, 337, 338, 340, 341, 342], "excluded_lines": []}, "AdvancedLogger.get_log_statistics": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 7, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 7, "excluded_lines": 0}, "missing_lines": [346, 355, 356, 357, 358, 360, 366], "excluded_lines": []}, "AdvancedLogger.export_performance_data": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 5, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 5, "excluded_lines": 0}, "missing_lines": [370, 371, 373, 374, 376], "excluded_lines": []}, "AdvancedLogger.shutdown": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 8, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 8, "excluded_lines": 0}, "missing_lines": [380, 381, 384, 385, 388, 389, 392, 394], "excluded_lines": []}, "log_trace": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [404], "excluded_lines": []}, "log_performance": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [414], "excluded_lines": []}, "log_user_action": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [419], "excluded_lines": []}, "": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 67, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 67, "excluded_lines": 0}, "missing_lines": [7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 21, 22, 24, 25, 28, 31, 32, 33, 34, 35, 36, 37, 38, 41, 42, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 58, 61, 66, 93, 98, 132, 136, 160, 172, 208, 223, 257, 274, 278, 282, 286, 290, 294, 298, 310, 320, 324, 344, 368, 378, 398, 402, 407, 417], "excluded_lines": []}}, "classes": {"LogCategory": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "LogEntry": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "StructuredFormatter": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 10, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 10, "excluded_lines": 0}, "missing_lines": [63, 64, 69, 70, 71, 72, 73, 74, 76, 90], "excluded_lines": []}, "AdvancedLogger": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 110, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 110, "excluded_lines": 0}, "missing_lines": [100, 101, 104, 105, 106, 109, 110, 111, 114, 115, 118, 119, 122, 123, 126, 129, 130, 134, 138, 140, 143, 148, 149, 152, 153, 154, 157, 158, 162, 163, 169, 170, 174, 175, 177, 178, 180, 181, 182, 185, 196, 197, 198, 201, 203, 204, 205, 206, 210, 211, 214, 215, 218, 219, 221, 233, 246, 247, 248, 250, 251, 254, 255, 259, 266, 269, 270, 276, 280, 284, 288, 292, 296, 306, 312, 322, 326, 327, 329, 330, 331, 332, 333, 334, 336, 337, 338, 340, 341, 342, 346, 355, 356, 357, 358, 360, 366, 370, 371, 373, 374, 376, 380, 381, 384, 385, 388, 389, 392, 394], "excluded_lines": []}, "": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 70, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 70, "excluded_lines": 0}, "missing_lines": [7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 21, 22, 24, 25, 28, 31, 32, 33, 34, 35, 36, 37, 38, 41, 42, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 58, 61, 66, 93, 98, 132, 136, 160, 172, 208, 223, 257, 274, 278, 282, 286, 290, 294, 298, 310, 320, 324, 344, 368, 378, 398, 402, 404, 407, 414, 417, 419], "excluded_lines": []}}}, "aretomo3_gui/core/automation/__init__.py": {"executed_lines": [1, 5, 6, 8], "summary": {"covered_lines": 3, "num_statements": 3, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": [], "functions": {"": {"executed_lines": [1, 5, 6, 8], "summary": {"covered_lines": 3, "num_statements": 3, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}}, "classes": {"": {"executed_lines": [1, 5, 6, 8], "summary": {"covered_lines": 3, "num_statements": 3, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}}}, "aretomo3_gui/core/automation/parameter_optimizer.py": {"executed_lines": [1, 3, 6, 7, 9, 19], "summary": {"covered_lines": 4, "num_statements": 21, "percent_covered": 19.047619047619047, "percent_covered_display": "19", "missing_lines": 17, "excluded_lines": 0}, "missing_lines": [11, 38, 41, 42, 43, 44, 47, 48, 49, 50, 51, 52, 55, 56, 57, 58, 60], "excluded_lines": [], "functions": {"ParameterOptimizer.__init__": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [11], "excluded_lines": []}, "ParameterOptimizer.optimize_parameters": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 16, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 16, "excluded_lines": 0}, "missing_lines": [38, 41, 42, 43, 44, 47, 48, 49, 50, 51, 52, 55, 56, 57, 58, 60], "excluded_lines": []}, "": {"executed_lines": [1, 3, 6, 7, 9, 19], "summary": {"covered_lines": 4, "num_statements": 4, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}}, "classes": {"ParameterOptimizer": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 17, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 17, "excluded_lines": 0}, "missing_lines": [11, 38, 41, 42, 43, 44, 47, 48, 49, 50, 51, 52, 55, 56, 57, 58, 60], "excluded_lines": []}, "": {"executed_lines": [1, 3, 6, 7, 9, 19], "summary": {"covered_lines": 4, "num_statements": 4, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}}}, "aretomo3_gui/core/automation/quality_predictor.py": {"executed_lines": [1, 3, 5, 8, 9, 11, 19], "summary": {"covered_lines": 5, "num_statements": 15, "percent_covered": 33.333333333333336, "percent_covered_display": "33", "missing_lines": 10, "excluded_lines": 0}, "missing_lines": [13, 33, 34, 35, 39, 42, 45, 47, 48, 52], "excluded_lines": [], "functions": {"QualityPredictor.__init__": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [13], "excluded_lines": []}, "QualityPredictor.predict_quality": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 9, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 9, "excluded_lines": 0}, "missing_lines": [33, 34, 35, 39, 42, 45, 47, 48, 52], "excluded_lines": []}, "": {"executed_lines": [1, 3, 5, 8, 9, 11, 19], "summary": {"covered_lines": 5, "num_statements": 5, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}}, "classes": {"QualityPredictor": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 10, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 10, "excluded_lines": 0}, "missing_lines": [13, 33, 34, 35, 39, 42, 45, 47, 48, 52], "excluded_lines": []}, "": {"executed_lines": [1, 3, 5, 8, 9, 11, 19], "summary": {"covered_lines": 5, "num_statements": 5, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}}}, "aretomo3_gui/core/automation/workflow_manager.py": {"executed_lines": [2, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 18, 20, 23, 24, 26, 27, 28, 29, 30, 33, 34, 36, 37, 38, 39, 40, 41, 42, 43, 46, 47, 48, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 61, 67, 68, 69, 71, 72, 73, 74, 75, 76, 77, 78, 79, 81, 89, 90, 93, 94, 95, 96, 97, 99, 101, 103, 104, 105, 106, 109, 110, 113, 114, 117, 118, 119, 121, 125, 127, 179, 186, 192, 219, 277, 309, 337, 366, 393, 433, 445, 470, 488, 505, 528, 550, 564], "summary": {"covered_lines": 91, "num_statements": 272, "percent_covered": 33.455882352941174, "percent_covered_display": "33", "missing_lines": 181, "excluded_lines": 0}, "missing_lines": [63, 64, 83, 84, 85, 86, 181, 182, 183, 184, 188, 189, 190, 194, 196, 197, 198, 199, 200, 203, 204, 205, 206, 207, 208, 209, 211, 213, 214, 216, 221, 222, 225, 226, 227, 229, 230, 232, 235, 236, 237, 239, 240, 241, 242, 243, 246, 251, 256, 258, 270, 271, 273, 274, 275, 279, 281, 282, 283, 286, 288, 289, 290, 292, 293, 294, 297, 298, 299, 301, 302, 304, 305, 307, 319, 320, 323, 324, 327, 328, 331, 332, 333, 335, 341, 344, 345, 346, 347, 348, 349, 352, 353, 354, 355, 356, 357, 364, 372, 383, 386, 387, 388, 389, 391, 401, 404, 405, 408, 409, 412, 413, 416, 425, 427, 428, 430, 431, 436, 443, 447, 448, 451, 452, 453, 454, 458, 459, 460, 462, 463, 465, 466, 472, 473, 475, 476, 479, 480, 482, 483, 486, 490, 491, 492, 494, 496, 497, 501, 507, 508, 509, 511, 512, 514, 515, 518, 519, 521, 522, 524, 530, 531, 532, 534, 536, 538, 541, 542, 544, 545, 547, 548, 552, 566, 573, 574, 575, 577, 579, 580], "excluded_lines": [], "functions": {"DatasetInfo.__post_init__": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 2, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 2, "excluded_lines": 0}, "missing_lines": [63, 64], "excluded_lines": []}, "WorkflowTask.__post_init__": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 4, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 4, "excluded_lines": 0}, "missing_lines": [83, 84, 85, 86], "excluded_lines": []}, "WorkflowManager.__init__": {"executed_lines": [101, 103, 104, 105, 106, 109, 110, 113, 114, 117, 118, 119, 121], "summary": {"covered_lines": 13, "num_statements": 13, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "WorkflowManager.load_default_templates": {"executed_lines": [127], "summary": {"covered_lines": 1, "num_statements": 1, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "WorkflowManager.add_monitored_directory": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 4, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 4, "excluded_lines": 0}, "missing_lines": [181, 182, 183, 184], "excluded_lines": []}, "WorkflowManager.remove_monitored_directory": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 3, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 3, "excluded_lines": 0}, "missing_lines": [188, 189, 190], "excluded_lines": []}, "WorkflowManager.scan_directory": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 17, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 17, "excluded_lines": 0}, "missing_lines": [194, 196, 197, 198, 199, 200, 203, 204, 205, 206, 207, 208, 209, 211, 213, 214, 216], "excluded_lines": []}, "WorkflowManager.analyze_dataset": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 25, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 25, "excluded_lines": 0}, "missing_lines": [221, 222, 225, 226, 227, 229, 230, 232, 235, 236, 237, 239, 240, 241, 242, 243, 246, 251, 256, 258, 270, 271, 273, 274, 275], "excluded_lines": []}, "WorkflowManager.parse_mdoc_metadata": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 19, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 19, "excluded_lines": 0}, "missing_lines": [279, 281, 282, 283, 286, 288, 289, 290, 292, 293, 294, 297, 298, 299, 301, 302, 304, 305, 307], "excluded_lines": []}, "WorkflowManager.classify_dataset_type": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 10, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 10, "excluded_lines": 0}, "missing_lines": [319, 320, 323, 324, 327, 328, 331, 332, 333, 335], "excluded_lines": []}, "WorkflowManager.estimate_dataset_quality": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 14, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 14, "excluded_lines": 0}, "missing_lines": [341, 344, 345, 346, 347, 348, 349, 352, 353, 354, 355, 356, 357, 364], "excluded_lines": []}, "WorkflowManager.calculate_priority": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 7, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 7, "excluded_lines": 0}, "missing_lines": [372, 383, 386, 387, 388, 389, 391], "excluded_lines": []}, "WorkflowManager.create_workflow_task": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 13, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 13, "excluded_lines": 0}, "missing_lines": [401, 404, 405, 408, 409, 412, 413, 416, 425, 427, 428, 430, 431], "excluded_lines": []}, "WorkflowManager.select_optimal_template": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 2, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 2, "excluded_lines": 0}, "missing_lines": [436, 443], "excluded_lines": []}, "WorkflowManager.add_to_queue": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 13, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 13, "excluded_lines": 0}, "missing_lines": [447, 448, 451, 452, 453, 454, 458, 459, 460, 462, 463, 465, 466], "excluded_lines": []}, "WorkflowManager.process_queue": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 9, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 9, "excluded_lines": 0}, "missing_lines": [472, 473, 475, 476, 479, 480, 482, 483, 486], "excluded_lines": []}, "WorkflowManager.start_task": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 7, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 7, "excluded_lines": 0}, "missing_lines": [490, 491, 492, 494, 496, 497, 501], "excluded_lines": []}, "WorkflowManager.complete_task": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 12, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 12, "excluded_lines": 0}, "missing_lines": [507, 508, 509, 511, 512, 514, 515, 518, 519, 521, 522, 524], "excluded_lines": []}, "WorkflowManager.cancel_task": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 12, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 12, "excluded_lines": 0}, "missing_lines": [530, 531, 532, 534, 536, 538, 541, 542, 544, 545, 547, 548], "excluded_lines": []}, "WorkflowManager.get_queue_status": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [552], "excluded_lines": []}, "WorkflowManager.export_workflow_report": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 7, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 7, "excluded_lines": 0}, "missing_lines": [566, 573, 574, 575, 577, 579, 580], "excluded_lines": []}, "": {"executed_lines": [2, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 18, 20, 23, 24, 26, 27, 28, 29, 30, 33, 34, 36, 37, 38, 39, 40, 41, 42, 43, 46, 47, 48, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 61, 67, 68, 69, 71, 72, 73, 74, 75, 76, 77, 78, 79, 81, 89, 90, 93, 94, 95, 96, 97, 99, 125, 179, 186, 192, 219, 277, 309, 337, 366, 393, 433, 445, 470, 488, 505, 528, 550, 564], "summary": {"covered_lines": 77, "num_statements": 77, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}}, "classes": {"WorkflowStatus": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "DatasetType": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "DatasetInfo": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 2, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 2, "excluded_lines": 0}, "missing_lines": [63, 64], "excluded_lines": []}, "WorkflowTask": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 4, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 4, "excluded_lines": 0}, "missing_lines": [83, 84, 85, 86], "excluded_lines": []}, "WorkflowManager": {"executed_lines": [101, 103, 104, 105, 106, 109, 110, 113, 114, 117, 118, 119, 121, 127], "summary": {"covered_lines": 14, "num_statements": 189, "percent_covered": 7.407407407407407, "percent_covered_display": "7", "missing_lines": 175, "excluded_lines": 0}, "missing_lines": [181, 182, 183, 184, 188, 189, 190, 194, 196, 197, 198, 199, 200, 203, 204, 205, 206, 207, 208, 209, 211, 213, 214, 216, 221, 222, 225, 226, 227, 229, 230, 232, 235, 236, 237, 239, 240, 241, 242, 243, 246, 251, 256, 258, 270, 271, 273, 274, 275, 279, 281, 282, 283, 286, 288, 289, 290, 292, 293, 294, 297, 298, 299, 301, 302, 304, 305, 307, 319, 320, 323, 324, 327, 328, 331, 332, 333, 335, 341, 344, 345, 346, 347, 348, 349, 352, 353, 354, 355, 356, 357, 364, 372, 383, 386, 387, 388, 389, 391, 401, 404, 405, 408, 409, 412, 413, 416, 425, 427, 428, 430, 431, 436, 443, 447, 448, 451, 452, 453, 454, 458, 459, 460, 462, 463, 465, 466, 472, 473, 475, 476, 479, 480, 482, 483, 486, 490, 491, 492, 494, 496, 497, 501, 507, 508, 509, 511, 512, 514, 515, 518, 519, 521, 522, 524, 530, 531, 532, 534, 536, 538, 541, 542, 544, 545, 547, 548, 552, 566, 573, 574, 575, 577, 579, 580], "excluded_lines": []}, "": {"executed_lines": [2, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 18, 20, 23, 24, 26, 27, 28, 29, 30, 33, 34, 36, 37, 38, 39, 40, 41, 42, 43, 46, 47, 48, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 61, 67, 68, 69, 71, 72, 73, 74, 75, 76, 77, 78, 79, 81, 89, 90, 93, 94, 95, 96, 97, 99, 125, 179, 186, 192, 219, 277, 309, 337, 366, 393, 433, 445, 470, 488, 505, 528, 550, 564], "summary": {"covered_lines": 77, "num_statements": 77, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}}}, "aretomo3_gui/core/backup_system.py": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 252, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 252, "excluded_lines": 0}, "missing_lines": [7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 19, 21, 24, 25, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 40, 46, 48, 53, 56, 72, 73, 76, 77, 80, 83, 85, 87, 89, 90, 91, 92, 94, 95, 108, 110, 111, 113, 115, 116, 117, 118, 131, 132, 134, 135, 137, 151, 152, 153, 155, 157, 160, 161, 162, 163, 166, 169, 170, 173, 174, 175, 180, 194, 195, 198, 200, 203, 205, 206, 207, 209, 211, 212, 214, 216, 217, 219, 221, 222, 224, 226, 231, 233, 235, 236, 237, 239, 241, 242, 243, 245, 247, 248, 249, 250, 252, 254, 258, 260, 261, 262, 264, 265, 268, 269, 270, 272, 273, 275, 277, 278, 279, 281, 283, 284, 285, 286, 287, 288, 289, 290, 291, 293, 297, 298, 299, 300, 301, 302, 303, 304, 305, 307, 308, 309, 311, 313, 314, 315, 317, 328, 329, 330, 332, 333, 334, 336, 337, 338, 341, 342, 343, 346, 347, 350, 353, 354, 356, 357, 359, 360, 361, 363, 367, 368, 369, 371, 372, 374, 376, 377, 378, 380, 381, 382, 385, 386, 389, 390, 392, 393, 395, 396, 397, 399, 401, 403, 408, 409, 410, 412, 413, 414, 416, 417, 419, 421, 423, 424, 427, 429, 431, 433, 434, 440, 441, 442, 443, 444, 445, 446, 448, 457, 459, 460, 462, 464, 468, 469, 470, 472, 473, 474, 476, 478, 480, 481, 482, 484, 486, 488, 491, 492, 493, 494, 496, 500, 503, 505, 508, 510], "excluded_lines": [], "functions": {"BackupSystem.__init__": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 10, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 10, "excluded_lines": 0}, "missing_lines": [48, 53, 56, 72, 73, 76, 77, 80, 83, 85], "excluded_lines": []}, "BackupSystem._load_metadata": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 9, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 9, "excluded_lines": 0}, "missing_lines": [89, 90, 91, 92, 94, 95, 108, 110, 111], "excluded_lines": []}, "BackupSystem._save_metadata": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 8, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 8, "excluded_lines": 0}, "missing_lines": [115, 116, 117, 118, 131, 132, 134, 135], "excluded_lines": []}, "BackupSystem.create_backup": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 24, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 24, "excluded_lines": 0}, "missing_lines": [151, 152, 153, 155, 157, 160, 161, 162, 163, 166, 169, 170, 173, 174, 175, 180, 194, 195, 198, 200, 203, 205, 206, 207], "excluded_lines": []}, "BackupSystem._generate_backup_id": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 2, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 2, "excluded_lines": 0}, "missing_lines": [211, 212], "excluded_lines": []}, "BackupSystem._get_source_paths": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 22, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 22, "excluded_lines": 0}, "missing_lines": [216, 217, 219, 221, 222, 224, 226, 231, 233, 235, 236, 237, 239, 241, 242, 243, 245, 247, 248, 249, 250, 252], "excluded_lines": []}, "BackupSystem._create_backup_archive": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 15, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 15, "excluded_lines": 0}, "missing_lines": [258, 260, 261, 262, 264, 265, 268, 269, 270, 272, 273, 275, 277, 278, 279], "excluded_lines": []}, "BackupSystem._calculate_checksum": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 9, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 9, "excluded_lines": 0}, "missing_lines": [283, 284, 285, 286, 287, 288, 289, 290, 291], "excluded_lines": []}, "BackupSystem._calculate_compression_ratio": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 16, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 16, "excluded_lines": 0}, "missing_lines": [297, 298, 299, 300, 301, 302, 303, 304, 305, 307, 308, 309, 311, 313, 314, 315], "excluded_lines": []}, "BackupSystem.restore_backup": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 22, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 22, "excluded_lines": 0}, "missing_lines": [328, 329, 330, 332, 333, 334, 336, 337, 338, 341, 342, 343, 346, 347, 350, 353, 354, 356, 357, 359, 360, 361], "excluded_lines": []}, "BackupSystem._verify_backup_integrity": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 5, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 5, "excluded_lines": 0}, "missing_lines": [367, 368, 369, 371, 372], "excluded_lines": []}, "BackupSystem.delete_backup": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 15, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 15, "excluded_lines": 0}, "missing_lines": [376, 377, 378, 380, 381, 382, 385, 386, 389, 390, 392, 393, 395, 396, 397], "excluded_lines": []}, "BackupSystem._cleanup_old_backups": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 10, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 10, "excluded_lines": 0}, "missing_lines": [401, 403, 408, 409, 410, 412, 413, 414, 416, 417], "excluded_lines": []}, "BackupSystem.get_backup_list": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 5, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 5, "excluded_lines": 0}, "missing_lines": [421, 423, 424, 427, 429], "excluded_lines": []}, "BackupSystem.get_backup_statistics": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 10, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 10, "excluded_lines": 0}, "missing_lines": [433, 434, 440, 441, 442, 443, 444, 445, 446, 448], "excluded_lines": []}, "BackupSystem._start_scheduler": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 7, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 7, "excluded_lines": 0}, "missing_lines": [459, 460, 462, 472, 473, 474, 476], "excluded_lines": []}, "BackupSystem._start_scheduler.run_scheduler": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 4, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 4, "excluded_lines": 0}, "missing_lines": [464, 468, 469, 470], "excluded_lines": []}, "BackupSystem.stop_scheduler": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 4, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 4, "excluded_lines": 0}, "missing_lines": [480, 481, 482, 484], "excluded_lines": []}, "BackupSystem.update_config": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 6, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 6, "excluded_lines": 0}, "missing_lines": [488, 491, 492, 493, 494, 496], "excluded_lines": []}, "create_backup": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [505], "excluded_lines": []}, "restore_backup": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [510], "excluded_lines": []}, "": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 47, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 47, "excluded_lines": 0}, "missing_lines": [7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 19, 21, 24, 25, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 40, 46, 87, 113, 137, 209, 214, 254, 281, 293, 317, 363, 374, 399, 419, 431, 457, 478, 486, 500, 503, 508], "excluded_lines": []}}, "classes": {"BackupMetadata": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "BackupSystem": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 203, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 203, "excluded_lines": 0}, "missing_lines": [48, 53, 56, 72, 73, 76, 77, 80, 83, 85, 89, 90, 91, 92, 94, 95, 108, 110, 111, 115, 116, 117, 118, 131, 132, 134, 135, 151, 152, 153, 155, 157, 160, 161, 162, 163, 166, 169, 170, 173, 174, 175, 180, 194, 195, 198, 200, 203, 205, 206, 207, 211, 212, 216, 217, 219, 221, 222, 224, 226, 231, 233, 235, 236, 237, 239, 241, 242, 243, 245, 247, 248, 249, 250, 252, 258, 260, 261, 262, 264, 265, 268, 269, 270, 272, 273, 275, 277, 278, 279, 283, 284, 285, 286, 287, 288, 289, 290, 291, 297, 298, 299, 300, 301, 302, 303, 304, 305, 307, 308, 309, 311, 313, 314, 315, 328, 329, 330, 332, 333, 334, 336, 337, 338, 341, 342, 343, 346, 347, 350, 353, 354, 356, 357, 359, 360, 361, 367, 368, 369, 371, 372, 376, 377, 378, 380, 381, 382, 385, 386, 389, 390, 392, 393, 395, 396, 397, 401, 403, 408, 409, 410, 412, 413, 414, 416, 417, 421, 423, 424, 427, 429, 433, 434, 440, 441, 442, 443, 444, 445, 446, 448, 459, 460, 462, 464, 468, 469, 470, 472, 473, 474, 476, 480, 481, 482, 484, 488, 491, 492, 493, 494, 496], "excluded_lines": []}, "": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 49, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 49, "excluded_lines": 0}, "missing_lines": [7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 19, 21, 24, 25, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 40, 46, 87, 113, 137, 209, 214, 254, 281, 293, 317, 363, 374, 399, 419, 431, 457, 478, 486, 500, 503, 505, 508, 510], "excluded_lines": []}}}, "aretomo3_gui/core/config/__init__.py": {"executed_lines": [1, 5, 13, 16], "summary": {"covered_lines": 3, "num_statements": 3, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": [], "functions": {"": {"executed_lines": [1, 5, 13, 16], "summary": {"covered_lines": 3, "num_statements": 3, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}}, "classes": {"": {"executed_lines": [1, 5, 13, 16], "summary": {"covered_lines": 3, "num_statements": 3, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}}}, "aretomo3_gui/core/config/config.py": {"executed_lines": [2, 3, 6, 7, 8, 11, 12, 15, 22, 30, 37, 44], "summary": {"covered_lines": 12, "num_statements": 12, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": [], "functions": {"": {"executed_lines": [2, 3, 6, 7, 8, 11, 12, 15, 22, 30, 37, 44], "summary": {"covered_lines": 12, "num_statements": 12, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}}, "classes": {"": {"executed_lines": [2, 3, 6, 7, 8, 11, 12, 15, 22, 30, 37, 44], "summary": {"covered_lines": 12, "num_statements": 12, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}}}, "aretomo3_gui/core/config/config_manager.py": {"executed_lines": [1, 3, 4, 5, 6, 7, 8, 10, 13, 14, 15, 17, 18, 19, 20, 21, 22, 23, 26, 27, 28, 30, 31, 32, 33, 36, 37, 39, 40, 41, 43, 58, 90, 118, 133, 158, 193, 199, 205, 222, 236, 240, 244, 250, 256, 272, 288], "summary": {"covered_lines": 43, "num_statements": 145, "percent_covered": 29.655172413793103, "percent_covered_display": "30", "missing_lines": 102, "excluded_lines": 0}, "missing_lines": [45, 46, 47, 49, 50, 51, 52, 54, 55, 56, 60, 61, 63, 70, 71, 73, 74, 75, 77, 78, 79, 81, 82, 84, 85, 86, 87, 88, 92, 93, 94, 95, 97, 98, 99, 101, 113, 114, 115, 116, 120, 129, 130, 131, 135, 136, 137, 138, 140, 141, 142, 144, 153, 154, 155, 156, 160, 189, 190, 191, 195, 196, 197, 201, 202, 203, 207, 219, 220, 224, 233, 234, 238, 242, 246, 247, 248, 252, 253, 254, 264, 267, 268, 270, 274, 275, 277, 278, 281, 282, 285, 286, 290, 292, 293, 296, 297, 298, 301, 302, 303, 305], "excluded_lines": [], "functions": {"ConfigManager.__init__": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 10, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 10, "excluded_lines": 0}, "missing_lines": [45, 46, 47, 49, 50, 51, 52, 54, 55, 56], "excluded_lines": []}, "ConfigManager._load_config": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 18, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 18, "excluded_lines": 0}, "missing_lines": [60, 61, 63, 70, 71, 73, 74, 75, 77, 78, 79, 81, 82, 84, 85, 86, 87, 88], "excluded_lines": []}, "ConfigManager._load_profiles": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 12, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 12, "excluded_lines": 0}, "missing_lines": [92, 93, 94, 95, 97, 98, 99, 101, 113, 114, 115, 116], "excluded_lines": []}, "ConfigManager._create_default_profile": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 4, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 4, "excluded_lines": 0}, "missing_lines": [120, 129, 130, 131], "excluded_lines": []}, "ConfigManager._load_presets": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 12, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 12, "excluded_lines": 0}, "missing_lines": [135, 136, 137, 138, 140, 141, 142, 144, 153, 154, 155, 156], "excluded_lines": []}, "ConfigManager._create_default_presets": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 4, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 4, "excluded_lines": 0}, "missing_lines": [160, 189, 190, 191], "excluded_lines": []}, "ConfigManager.save_profile": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 3, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 3, "excluded_lines": 0}, "missing_lines": [195, 196, 197], "excluded_lines": []}, "ConfigManager.save_preset": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 3, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 3, "excluded_lines": 0}, "missing_lines": [201, 202, 203], "excluded_lines": []}, "ConfigManager._save_profiles": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 3, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 3, "excluded_lines": 0}, "missing_lines": [207, 219, 220], "excluded_lines": []}, "ConfigManager._save_presets": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 3, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 3, "excluded_lines": 0}, "missing_lines": [224, 233, 234], "excluded_lines": []}, "ConfigManager.get_profile": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [238], "excluded_lines": []}, "ConfigManager.get_preset": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [242], "excluded_lines": []}, "ConfigManager.set_current_profile": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 3, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 3, "excluded_lines": 0}, "missing_lines": [246, 247, 248], "excluded_lines": []}, "ConfigManager.set_current_preset": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 3, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 3, "excluded_lines": 0}, "missing_lines": [252, 253, 254], "excluded_lines": []}, "ConfigManager.detect_optimal_parameters": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 4, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 4, "excluded_lines": 0}, "missing_lines": [264, 267, 268, 270], "excluded_lines": []}, "ConfigManager.backup_config": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 8, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 8, "excluded_lines": 0}, "missing_lines": [274, 275, 277, 278, 281, 282, 285, 286], "excluded_lines": []}, "ConfigManager.restore_config": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 10, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 10, "excluded_lines": 0}, "missing_lines": [290, 292, 293, 296, 297, 298, 301, 302, 303, 305], "excluded_lines": []}, "": {"executed_lines": [1, 3, 4, 5, 6, 7, 8, 10, 13, 14, 15, 17, 18, 19, 20, 21, 22, 23, 26, 27, 28, 30, 31, 32, 33, 36, 37, 39, 40, 41, 43, 58, 90, 118, 133, 158, 193, 199, 205, 222, 236, 240, 244, 250, 256, 272, 288], "summary": {"covered_lines": 43, "num_statements": 43, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}}, "classes": {"MicroscopeProfile": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "ProcessingPreset": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "ConfigManager": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 102, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 102, "excluded_lines": 0}, "missing_lines": [45, 46, 47, 49, 50, 51, 52, 54, 55, 56, 60, 61, 63, 70, 71, 73, 74, 75, 77, 78, 79, 81, 82, 84, 85, 86, 87, 88, 92, 93, 94, 95, 97, 98, 99, 101, 113, 114, 115, 116, 120, 129, 130, 131, 135, 136, 137, 138, 140, 141, 142, 144, 153, 154, 155, 156, 160, 189, 190, 191, 195, 196, 197, 201, 202, 203, 207, 219, 220, 224, 233, 234, 238, 242, 246, 247, 248, 252, 253, 254, 264, 267, 268, 270, 274, 275, 277, 278, 281, 282, 285, 286, 290, 292, 293, 296, 297, 298, 301, 302, 303, 305], "excluded_lines": []}, "": {"executed_lines": [1, 3, 4, 5, 6, 7, 8, 10, 13, 14, 15, 17, 18, 19, 20, 21, 22, 23, 26, 27, 28, 30, 31, 32, 33, 36, 37, 39, 40, 41, 43, 58, 90, 118, 133, 158, 193, 199, 205, 222, 236, 240, 244, 250, 256, 272, 288], "summary": {"covered_lines": 43, "num_statements": 43, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}}}, "aretomo3_gui/core/config/config_validation.py": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 106, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 106, "excluded_lines": 0}, "missing_lines": [3, 4, 5, 6, 7, 8, 9, 11, 13, 16, 19, 41, 42, 43, 44, 45, 46, 47, 49, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 70, 80, 81, 84, 85, 86, 87, 88, 89, 92, 93, 94, 95, 98, 99, 100, 103, 104, 106, 108, 109, 112, 119, 122, 123, 124, 125, 128, 135, 137, 138, 139, 142, 143, 144, 147, 148, 149, 151, 153, 154, 157, 167, 168, 169, 171, 172, 173, 174, 175, 176, 179, 180, 181, 183, 185, 186, 189, 199, 200, 203, 204, 205, 208, 209, 212, 213, 214, 215, 216, 217, 218, 220, 222, 223], "excluded_lines": [], "functions": {"AreTomo3Config.__init__": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 7, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 7, "excluded_lines": 0}, "missing_lines": [41, 42, 43, 44, 45, 46, 47], "excluded_lines": []}, "AreTomo3Config.validate": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 12, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 12, "excluded_lines": 0}, "missing_lines": [56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67], "excluded_lines": []}, "validate_aretomo_installation": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 20, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 20, "excluded_lines": 0}, "missing_lines": [80, 81, 84, 85, 86, 87, 88, 89, 92, 93, 94, 95, 98, 99, 100, 103, 104, 106, 108, 109], "excluded_lines": []}, "check_gpu_compatibility": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 5, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 5, "excluded_lines": 0}, "missing_lines": [119, 122, 123, 124, 125], "excluded_lines": []}, "check_system_requirements": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 13, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 13, "excluded_lines": 0}, "missing_lines": [135, 137, 138, 139, 142, 143, 144, 147, 148, 149, 151, 153, 154], "excluded_lines": []}, "validate_input_files": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 15, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 15, "excluded_lines": 0}, "missing_lines": [167, 168, 169, 171, 172, 173, 174, 175, 176, 179, 180, 181, 183, 185, 186], "excluded_lines": []}, "validate_output_directory": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 17, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 17, "excluded_lines": 0}, "missing_lines": [199, 200, 203, 204, 205, 208, 209, 212, 213, 214, 215, 216, 217, 218, 220, 222, 223], "excluded_lines": []}, "": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 17, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 17, "excluded_lines": 0}, "missing_lines": [3, 4, 5, 6, 7, 8, 9, 11, 13, 16, 19, 49, 70, 112, 128, 157, 189], "excluded_lines": []}}, "classes": {"AreTomo3Config": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 19, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 19, "excluded_lines": 0}, "missing_lines": [41, 42, 43, 44, 45, 46, 47, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67], "excluded_lines": []}, "": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 87, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 87, "excluded_lines": 0}, "missing_lines": [3, 4, 5, 6, 7, 8, 9, 11, 13, 16, 19, 49, 70, 80, 81, 84, 85, 86, 87, 88, 89, 92, 93, 94, 95, 98, 99, 100, 103, 104, 106, 108, 109, 112, 119, 122, 123, 124, 125, 128, 135, 137, 138, 139, 142, 143, 144, 147, 148, 149, 151, 153, 154, 157, 167, 168, 169, 171, 172, 173, 174, 175, 176, 179, 180, 181, 183, 185, 186, 189, 199, 200, 203, 204, 205, 208, 209, 212, 213, 214, 215, 216, 217, 218, 220, 222, 223], "excluded_lines": []}}}, "aretomo3_gui/core/config/template_manager.py": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 52, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 52, "excluded_lines": 0}, "missing_lines": [3, 4, 5, 6, 7, 9, 11, 14, 15, 18, 19, 20, 21, 22, 25, 28, 30, 31, 32, 34, 35, 37, 39, 41, 49, 50, 52, 54, 56, 58, 59, 60, 61, 63, 71, 73, 74, 76, 78, 80, 82, 83, 84, 85, 86, 87, 88, 90, 92, 94, 96, 98], "excluded_lines": [], "functions": {"ConfigManager.__init__": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 5, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 5, "excluded_lines": 0}, "missing_lines": [30, 31, 32, 34, 35], "excluded_lines": []}, "ConfigManager.save_template": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 5, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 5, "excluded_lines": 0}, "missing_lines": [39, 41, 49, 50, 52], "excluded_lines": []}, "ConfigManager.load_templates": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 9, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 9, "excluded_lines": 0}, "missing_lines": [56, 58, 59, 60, 61, 63, 71, 73, 74], "excluded_lines": []}, "ConfigManager.get_template": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [78], "excluded_lines": []}, "ConfigManager.delete_template": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 7, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 7, "excluded_lines": 0}, "missing_lines": [82, 83, 84, 85, 86, 87, 88], "excluded_lines": []}, "ConfigManager.list_templates": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [92], "excluded_lines": []}, "ConfigManager.validate_template": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 2, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 2, "excluded_lines": 0}, "missing_lines": [96, 98], "excluded_lines": []}, "": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 22, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 22, "excluded_lines": 0}, "missing_lines": [3, 4, 5, 6, 7, 9, 11, 14, 15, 18, 19, 20, 21, 22, 25, 28, 37, 54, 76, 80, 90, 94], "excluded_lines": []}}, "classes": {"ProcessingTemplate": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "ConfigManager": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 30, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 30, "excluded_lines": 0}, "missing_lines": [30, 31, 32, 34, 35, 39, 41, 49, 50, 52, 56, 58, 59, 60, 61, 63, 71, 73, 74, 78, 82, 83, 84, 85, 86, 87, 88, 92, 96, 98], "excluded_lines": []}, "": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 22, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 22, "excluded_lines": 0}, "missing_lines": [3, 4, 5, 6, 7, 9, 11, 14, 15, 18, 19, 20, 21, 22, 25, 28, 37, 54, 76, 80, 90, 94], "excluded_lines": []}}}, "aretomo3_gui/core/config_manager.py": {"executed_lines": [2, 7, 8, 9, 10, 11, 12, 13, 14, 15, 17, 19, 22, 23, 25, 26, 27, 30, 31, 32, 34, 35, 36, 37, 38, 39, 40, 43, 44, 49, 51, 54, 57, 58, 59, 62, 63, 64, 65, 68, 71, 74, 77, 79, 85, 87, 145, 147, 149, 154, 157, 159, 161, 165, 166, 178, 179, 185, 197, 199, 200, 201, 202, 204, 205, 206, 215, 218, 219, 220, 221, 223, 232, 245, 266, 291, 312, 325, 345, 371, 389, 410, 414, 433, 469, 473, 481, 503, 512], "summary": {"covered_lines": 85, "num_statements": 237, "percent_covered": 35.86497890295359, "percent_covered_display": "36", "missing_lines": 152, "excluded_lines": 0}, "missing_lines": [167, 168, 169, 171, 174, 175, 176, 181, 182, 183, 187, 188, 193, 195, 226, 228, 229, 230, 234, 241, 242, 243, 247, 248, 250, 253, 256, 258, 261, 263, 264, 273, 275, 276, 277, 279, 280, 281, 283, 285, 287, 288, 289, 293, 294, 295, 296, 306, 307, 309, 310, 314, 315, 316, 318, 319, 320, 321, 322, 323, 327, 328, 329, 332, 333, 334, 335, 338, 340, 341, 343, 353, 354, 355, 356, 358, 365, 366, 368, 369, 373, 374, 375, 376, 378, 379, 380, 383, 385, 386, 387, 391, 392, 393, 394, 396, 397, 398, 400, 403, 404, 406, 407, 408, 412, 418, 420, 430, 431, 435, 436, 437, 438, 439, 441, 442, 443, 445, 448, 449, 452, 453, 454, 455, 458, 459, 461, 462, 463, 465, 466, 467, 471, 475, 476, 477, 478, 479, 483, 486, 487, 488, 489, 492, 493, 495, 499, 501, 505, 506, 507, 508], "excluded_lines": [], "functions": {"ConfigManager.__init__": {"executed_lines": [51, 54, 57, 58, 59, 62, 63, 64, 65, 68, 71, 74, 77, 79], "summary": {"covered_lines": 14, "num_statements": 14, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "ConfigManager._create_default_config": {"executed_lines": [87], "summary": {"covered_lines": 1, "num_statements": 1, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "ConfigManager.load_all_configs": {"executed_lines": [147, 149, 154, 157, 159], "summary": {"covered_lines": 5, "num_statements": 5, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "ConfigManager._load_config_file": {"executed_lines": [165, 166, 178, 179], "summary": {"covered_lines": 4, "num_statements": 14, "percent_covered": 28.571428571428573, "percent_covered_display": "29", "missing_lines": 10, "excluded_lines": 0}, "missing_lines": [167, 168, 169, 171, 174, 175, 176, 181, 182, 183], "excluded_lines": []}, "ConfigManager._deep_merge": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 4, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 4, "excluded_lines": 0}, "missing_lines": [187, 188, 193, 195], "excluded_lines": []}, "ConfigManager._load_profiles": {"executed_lines": [199, 200, 201, 202, 204, 205, 206, 215, 218, 219, 220, 221, 223], "summary": {"covered_lines": 13, "num_statements": 17, "percent_covered": 76.47058823529412, "percent_covered_display": "76", "missing_lines": 4, "excluded_lines": 0}, "missing_lines": [226, 228, 229, 230], "excluded_lines": []}, "ConfigManager._create_default_profile": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 4, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 4, "excluded_lines": 0}, "missing_lines": [234, 241, 242, 243], "excluded_lines": []}, "ConfigManager.save_all_configs": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 9, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 9, "excluded_lines": 0}, "missing_lines": [247, 248, 250, 253, 256, 258, 261, 263, 264], "excluded_lines": []}, "ConfigManager._save_config_file": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 12, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 12, "excluded_lines": 0}, "missing_lines": [273, 275, 276, 277, 279, 280, 281, 283, 285, 287, 288, 289], "excluded_lines": []}, "ConfigManager.save_profiles": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 8, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 8, "excluded_lines": 0}, "missing_lines": [293, 294, 295, 296, 306, 307, 309, 310], "excluded_lines": []}, "ConfigManager.get": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 9, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 9, "excluded_lines": 0}, "missing_lines": [314, 315, 316, 318, 319, 320, 321, 322, 323], "excluded_lines": []}, "ConfigManager.set": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 11, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 11, "excluded_lines": 0}, "missing_lines": [327, 328, 329, 332, 333, 334, 335, 338, 340, 341, 343], "excluded_lines": []}, "ConfigManager.create_profile": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 9, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 9, "excluded_lines": 0}, "missing_lines": [353, 354, 355, 356, 358, 365, 366, 368, 369], "excluded_lines": []}, "ConfigManager.load_profile": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 11, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 11, "excluded_lines": 0}, "missing_lines": [373, 374, 375, 376, 378, 379, 380, 383, 385, 386, 387], "excluded_lines": []}, "ConfigManager.delete_profile": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 13, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 13, "excluded_lines": 0}, "missing_lines": [391, 392, 393, 394, 396, 397, 398, 400, 403, 404, 406, 407, 408], "excluded_lines": []}, "ConfigManager.get_profiles": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [412], "excluded_lines": []}, "ConfigManager.export_config": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 4, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 4, "excluded_lines": 0}, "missing_lines": [418, 420, 430, 431], "excluded_lines": []}, "ConfigManager.import_config": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 23, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 23, "excluded_lines": 0}, "missing_lines": [435, 436, 437, 438, 439, 441, 442, 443, 445, 448, 449, 452, 453, 454, 455, 458, 459, 461, 462, 463, 465, 466, 467], "excluded_lines": []}, "ConfigManager.register_change_callback": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [471], "excluded_lines": []}, "ConfigManager._trigger_change_callbacks": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 5, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 5, "excluded_lines": 0}, "missing_lines": [475, 476, 477, 478, 479], "excluded_lines": []}, "ConfigManager.validate_config": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 10, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 10, "excluded_lines": 0}, "missing_lines": [483, 486, 487, 488, 489, 492, 493, 495, 499, 501], "excluded_lines": []}, "ConfigManager.reset_to_defaults": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 4, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 4, "excluded_lines": 0}, "missing_lines": [505, 506, 507, 508], "excluded_lines": []}, "": {"executed_lines": [2, 7, 8, 9, 10, 11, 12, 13, 14, 15, 17, 19, 22, 23, 25, 26, 27, 30, 31, 32, 34, 35, 36, 37, 38, 39, 40, 43, 44, 49, 85, 145, 161, 185, 197, 232, 245, 266, 291, 312, 325, 345, 371, 389, 410, 414, 433, 469, 473, 481, 503, 512], "summary": {"covered_lines": 48, "num_statements": 48, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}}, "classes": {"ConfigFormat": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "ConfigProfile": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "ConfigManager": {"executed_lines": [51, 54, 57, 58, 59, 62, 63, 64, 65, 68, 71, 74, 77, 79, 87, 147, 149, 154, 157, 159, 165, 166, 178, 179, 199, 200, 201, 202, 204, 205, 206, 215, 218, 219, 220, 221, 223], "summary": {"covered_lines": 37, "num_statements": 189, "percent_covered": 19.576719576719576, "percent_covered_display": "20", "missing_lines": 152, "excluded_lines": 0}, "missing_lines": [167, 168, 169, 171, 174, 175, 176, 181, 182, 183, 187, 188, 193, 195, 226, 228, 229, 230, 234, 241, 242, 243, 247, 248, 250, 253, 256, 258, 261, 263, 264, 273, 275, 276, 277, 279, 280, 281, 283, 285, 287, 288, 289, 293, 294, 295, 296, 306, 307, 309, 310, 314, 315, 316, 318, 319, 320, 321, 322, 323, 327, 328, 329, 332, 333, 334, 335, 338, 340, 341, 343, 353, 354, 355, 356, 358, 365, 366, 368, 369, 373, 374, 375, 376, 378, 379, 380, 383, 385, 386, 387, 391, 392, 393, 394, 396, 397, 398, 400, 403, 404, 406, 407, 408, 412, 418, 420, 430, 431, 435, 436, 437, 438, 439, 441, 442, 443, 445, 448, 449, 452, 453, 454, 455, 458, 459, 461, 462, 463, 465, 466, 467, 471, 475, 476, 477, 478, 479, 483, 486, 487, 488, 489, 492, 493, 495, 499, 501, 505, 506, 507, 508], "excluded_lines": []}, "": {"executed_lines": [2, 7, 8, 9, 10, 11, 12, 13, 14, 15, 17, 19, 22, 23, 25, 26, 27, 30, 31, 32, 34, 35, 36, 37, 38, 39, 40, 43, 44, 49, 85, 145, 161, 185, 197, 232, 245, 266, 291, 312, 325, 345, 371, 389, 410, 414, 433, 469, 473, 481, 503, 512], "summary": {"covered_lines": 48, "num_statements": 48, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}}}, "aretomo3_gui/core/continue_mode_manager.py": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 243, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 243, "excluded_lines": 0}, "missing_lines": [6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 17, 20, 23, 24, 25, 26, 27, 28, 31, 32, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 51, 54, 56, 61, 63, 64, 67, 69, 74, 78, 79, 82, 100, 105, 113, 114, 115, 118, 120, 123, 125, 126, 127, 129, 131, 132, 133, 134, 136, 137, 139, 140, 141, 144, 146, 147, 149, 151, 152, 154, 155, 156, 158, 160, 161, 162, 163, 165, 166, 168, 169, 170, 173, 175, 176, 178, 180, 181, 183, 184, 185, 187, 189, 190, 191, 192, 194, 195, 197, 199, 200, 203, 204, 205, 207, 208, 210, 212, 214, 216, 217, 219, 220, 222, 223, 224, 226, 228, 229, 230, 231, 233, 235, 239, 242, 245, 248, 256, 257, 258, 260, 261, 263, 266, 268, 269, 270, 272, 274, 275, 277, 278, 281, 282, 283, 285, 286, 288, 289, 291, 292, 295, 296, 297, 298, 300, 318, 320, 325, 330, 338, 339, 340, 341, 343, 345, 348, 351, 356, 357, 359, 361, 363, 364, 366, 368, 370, 372, 373, 375, 392, 393, 395, 396, 400, 402, 403, 404, 405, 406, 408, 425, 428, 432, 434, 435, 438, 440, 441, 442, 446, 447, 449, 451, 452, 454, 456, 457, 458, 460, 461, 462, 463, 464, 466, 468, 469, 470, 473, 475, 477, 478, 481, 484, 485, 486, 488, 491, 493, 495, 497, 499, 501, 505, 506, 507, 508, 510, 511], "excluded_lines": [], "functions": {"ContinueModeManager.__init__": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 6, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 6, "excluded_lines": 0}, "missing_lines": [56, 61, 63, 64, 67, 69], "excluded_lines": []}, "ContinueModeManager.start_processing": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 14, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 14, "excluded_lines": 0}, "missing_lines": [78, 79, 82, 100, 105, 113, 114, 115, 118, 120, 123, 125, 126, 127], "excluded_lines": []}, "ContinueModeManager.pause_processing": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 18, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 18, "excluded_lines": 0}, "missing_lines": [131, 132, 133, 134, 136, 137, 139, 140, 141, 144, 146, 147, 149, 151, 152, 154, 155, 156], "excluded_lines": []}, "ContinueModeManager.resume_processing": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 18, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 18, "excluded_lines": 0}, "missing_lines": [160, 161, 162, 163, 165, 166, 168, 169, 170, 173, 175, 176, 178, 180, 181, 183, 184, 185], "excluded_lines": []}, "ContinueModeManager.stop_processing": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 24, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 24, "excluded_lines": 0}, "missing_lines": [189, 190, 191, 192, 194, 195, 197, 199, 200, 203, 204, 205, 207, 208, 210, 212, 214, 216, 217, 219, 220, 222, 223, 224], "excluded_lines": []}, "ContinueModeManager.continue_processing": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 20, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 20, "excluded_lines": 0}, "missing_lines": [228, 229, 230, 231, 233, 235, 239, 242, 245, 248, 256, 257, 258, 260, 261, 263, 266, 268, 269, 270], "excluded_lines": []}, "ContinueModeManager.get_session_status": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 18, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 18, "excluded_lines": 0}, "missing_lines": [274, 275, 277, 278, 281, 282, 283, 285, 286, 288, 289, 291, 292, 295, 296, 297, 298, 300], "excluded_lines": []}, "ContinueModeManager.get_all_sessions": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [320], "excluded_lines": []}, "ContinueModeManager.add_continue_parameters": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 6, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 6, "excluded_lines": 0}, "missing_lines": [330, 338, 339, 340, 341, 343], "excluded_lines": []}, "ContinueModeManager.create_continue_command": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 10, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 10, "excluded_lines": 0}, "missing_lines": [348, 351, 356, 357, 359, 361, 363, 364, 366, 368], "excluded_lines": []}, "ContinueModeManager.save_session_state": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 7, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 7, "excluded_lines": 0}, "missing_lines": [372, 373, 375, 392, 393, 395, 396], "excluded_lines": []}, "ContinueModeManager.load_existing_sessions": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 20, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 20, "excluded_lines": 0}, "missing_lines": [402, 403, 404, 405, 406, 408, 425, 428, 432, 434, 435, 438, 440, 441, 442, 446, 447, 449, 451, 452], "excluded_lines": []}, "ContinueModeManager.cleanup_completed_sessions": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 16, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 16, "excluded_lines": 0}, "missing_lines": [456, 457, 458, 460, 461, 462, 463, 464, 466, 468, 469, 470, 473, 475, 477, 478], "excluded_lines": []}, "ContinueModeManager.create_session": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 5, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 5, "excluded_lines": 0}, "missing_lines": [484, 485, 486, 488, 491], "excluded_lines": []}, "ContinueModeManager.pause_session": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [495], "excluded_lines": []}, "ContinueModeManager.resume_session": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [499], "excluded_lines": []}, "ContinueModeManager.update_session_progress": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 6, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 6, "excluded_lines": 0}, "missing_lines": [505, 506, 507, 508, 510, 511], "excluded_lines": []}, "": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 52, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 52, "excluded_lines": 0}, "missing_lines": [6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 17, 20, 23, 24, 25, 26, 27, 28, 31, 32, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 51, 54, 74, 129, 158, 187, 226, 272, 318, 325, 345, 370, 400, 454, 481, 493, 497, 501], "excluded_lines": []}}, "classes": {"ProcessingState": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "ContinueSession": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "ContinueModeManager": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 191, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 191, "excluded_lines": 0}, "missing_lines": [56, 61, 63, 64, 67, 69, 78, 79, 82, 100, 105, 113, 114, 115, 118, 120, 123, 125, 126, 127, 131, 132, 133, 134, 136, 137, 139, 140, 141, 144, 146, 147, 149, 151, 152, 154, 155, 156, 160, 161, 162, 163, 165, 166, 168, 169, 170, 173, 175, 176, 178, 180, 181, 183, 184, 185, 189, 190, 191, 192, 194, 195, 197, 199, 200, 203, 204, 205, 207, 208, 210, 212, 214, 216, 217, 219, 220, 222, 223, 224, 228, 229, 230, 231, 233, 235, 239, 242, 245, 248, 256, 257, 258, 260, 261, 263, 266, 268, 269, 270, 274, 275, 277, 278, 281, 282, 283, 285, 286, 288, 289, 291, 292, 295, 296, 297, 298, 300, 320, 330, 338, 339, 340, 341, 343, 348, 351, 356, 357, 359, 361, 363, 364, 366, 368, 372, 373, 375, 392, 393, 395, 396, 402, 403, 404, 405, 406, 408, 425, 428, 432, 434, 435, 438, 440, 441, 442, 446, 447, 449, 451, 452, 456, 457, 458, 460, 461, 462, 463, 464, 466, 468, 469, 470, 473, 475, 477, 478, 484, 485, 486, 488, 491, 495, 499, 505, 506, 507, 508, 510, 511], "excluded_lines": []}, "": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 52, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 52, "excluded_lines": 0}, "missing_lines": [6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 17, 20, 23, 24, 25, 26, 27, 28, 31, 32, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 51, 54, 74, 129, 158, 187, 226, 272, 318, 325, 345, 370, 400, 454, 481, 493, 497, 501], "excluded_lines": []}}}, "aretomo3_gui/core/data_validation.py": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 206, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 206, "excluded_lines": 0}, "missing_lines": [7, 8, 9, 10, 11, 12, 13, 15, 16, 17, 19, 22, 25, 26, 27, 28, 31, 32, 35, 36, 37, 38, 39, 40, 41, 44, 45, 48, 49, 50, 51, 53, 55, 72, 75, 80, 82, 83, 84, 87, 88, 90, 92, 94, 116, 118, 133, 147, 149, 151, 152, 153, 156, 159, 160, 161, 163, 164, 165, 174, 177, 187, 188, 190, 192, 193, 202, 205, 206, 207, 218, 219, 220, 221, 222, 224, 234, 235, 236, 246, 248, 250, 251, 252, 253, 254, 255, 256, 258, 261, 265, 268, 269, 270, 272, 273, 274, 275, 276, 277, 278, 281, 282, 283, 285, 286, 288, 289, 290, 291, 292, 293, 294, 295, 305, 307, 309, 311, 312, 313, 314, 315, 316, 317, 326, 329, 333, 334, 342, 344, 348, 349, 350, 351, 352, 360, 362, 366, 367, 368, 369, 370, 378, 379, 380, 382, 386, 387, 388, 389, 390, 391, 399, 400, 401, 403, 407, 408, 409, 410, 411, 419, 421, 425, 426, 427, 428, 436, 438, 442, 443, 444, 445, 446, 447, 455, 456, 463, 466, 468, 469, 470, 472, 473, 483, 484, 493, 503, 504, 513, 515, 517, 518, 519, 520, 523, 524, 534, 535, 545, 546, 555, 557, 559, 560, 564], "excluded_lines": [], "functions": {"ValidationReport.__post_init__": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 2, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 2, "excluded_lines": 0}, "missing_lines": [55, 72], "excluded_lines": []}, "DataValidator.__init__": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 6, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 6, "excluded_lines": 0}, "missing_lines": [82, 83, 84, 87, 88, 90], "excluded_lines": []}, "DataValidator._register_builtin_validators": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [94], "excluded_lines": []}, "DataValidator._register_file_validators": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [118], "excluded_lines": []}, "DataValidator.validate_data": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 13, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 13, "excluded_lines": 0}, "missing_lines": [147, 149, 151, 152, 153, 156, 159, 160, 161, 163, 164, 165, 174], "excluded_lines": []}, "DataValidator.validate_file": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 19, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 19, "excluded_lines": 0}, "missing_lines": [187, 188, 190, 192, 193, 202, 205, 206, 207, 218, 219, 220, 221, 222, 224, 234, 235, 236, 246], "excluded_lines": []}, "DataValidator._get_field_value": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 8, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 8, "excluded_lines": 0}, "missing_lines": [250, 251, 252, 253, 254, 255, 256, 258], "excluded_lines": []}, "DataValidator._validate_field": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 25, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 25, "excluded_lines": 0}, "missing_lines": [265, 268, 269, 270, 272, 273, 274, 275, 276, 277, 278, 281, 282, 283, 285, 286, 288, 289, 290, 291, 292, 293, 294, 295, 305], "excluded_lines": []}, "DataValidator._apply_custom_rules": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 9, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 9, "excluded_lines": 0}, "missing_lines": [309, 311, 312, 313, 314, 315, 316, 317, 326], "excluded_lines": []}, "DataValidator._validate_required": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 3, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 3, "excluded_lines": 0}, "missing_lines": [333, 334, 342], "excluded_lines": []}, "DataValidator._validate_numeric": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 6, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 6, "excluded_lines": 0}, "missing_lines": [348, 349, 350, 351, 352, 360], "excluded_lines": []}, "DataValidator._validate_positive": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 8, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 8, "excluded_lines": 0}, "missing_lines": [366, 367, 368, 369, 370, 378, 379, 380], "excluded_lines": []}, "DataValidator._validate_range": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 9, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 9, "excluded_lines": 0}, "missing_lines": [386, 387, 388, 389, 390, 391, 399, 400, 401], "excluded_lines": []}, "DataValidator._validate_path": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 6, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 6, "excluded_lines": 0}, "missing_lines": [407, 408, 409, 410, 411, 419], "excluded_lines": []}, "DataValidator._validate_file_exists": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 5, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 5, "excluded_lines": 0}, "missing_lines": [425, 426, 427, 428, 436], "excluded_lines": []}, "DataValidator._validate_tilt_angles": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 9, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 9, "excluded_lines": 0}, "missing_lines": [442, 443, 444, 445, 446, 447, 455, 456, 463], "excluded_lines": []}, "DataValidator._validate_mrc_file": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 11, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 11, "excluded_lines": 0}, "missing_lines": [468, 469, 470, 472, 473, 483, 484, 493, 503, 504, 513], "excluded_lines": []}, "DataValidator._validate_mdoc_file": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 11, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 11, "excluded_lines": 0}, "missing_lines": [517, 518, 519, 520, 523, 524, 534, 535, 545, 546, 555], "excluded_lines": []}, "DataValidator.register_custom_rule": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 2, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 2, "excluded_lines": 0}, "missing_lines": [559, 560], "excluded_lines": []}, "": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 52, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 52, "excluded_lines": 0}, "missing_lines": [7, 8, 9, 10, 11, 12, 13, 15, 16, 17, 19, 22, 25, 26, 27, 28, 31, 32, 35, 36, 37, 38, 39, 40, 41, 44, 45, 48, 49, 50, 51, 53, 75, 80, 92, 116, 133, 177, 248, 261, 307, 329, 344, 362, 382, 403, 421, 438, 466, 515, 557, 564], "excluded_lines": []}}, "classes": {"ValidationSeverity": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "ValidationResult": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "ValidationReport": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 2, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 2, "excluded_lines": 0}, "missing_lines": [55, 72], "excluded_lines": []}, "DataValidator": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 152, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 152, "excluded_lines": 0}, "missing_lines": [82, 83, 84, 87, 88, 90, 94, 118, 147, 149, 151, 152, 153, 156, 159, 160, 161, 163, 164, 165, 174, 187, 188, 190, 192, 193, 202, 205, 206, 207, 218, 219, 220, 221, 222, 224, 234, 235, 236, 246, 250, 251, 252, 253, 254, 255, 256, 258, 265, 268, 269, 270, 272, 273, 274, 275, 276, 277, 278, 281, 282, 283, 285, 286, 288, 289, 290, 291, 292, 293, 294, 295, 305, 309, 311, 312, 313, 314, 315, 316, 317, 326, 333, 334, 342, 348, 349, 350, 351, 352, 360, 366, 367, 368, 369, 370, 378, 379, 380, 386, 387, 388, 389, 390, 391, 399, 400, 401, 407, 408, 409, 410, 411, 419, 425, 426, 427, 428, 436, 442, 443, 444, 445, 446, 447, 455, 456, 463, 468, 469, 470, 472, 473, 483, 484, 493, 503, 504, 513, 517, 518, 519, 520, 523, 524, 534, 535, 545, 546, 555, 559, 560], "excluded_lines": []}, "": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 52, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 52, "excluded_lines": 0}, "missing_lines": [7, 8, 9, 10, 11, 12, 13, 15, 16, 17, 19, 22, 25, 26, 27, 28, 31, 32, 35, 36, 37, 38, 39, 40, 41, 44, 45, 48, 49, 50, 51, 53, 75, 80, 92, 116, 133, 177, 248, 261, 307, 329, 344, 362, 382, 403, 421, 438, 466, 515, 557, 564], "excluded_lines": []}}}, "aretomo3_gui/core/database_manager.py": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 237, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 237, "excluded_lines": 0}, "missing_lines": [7, 8, 9, 10, 11, 12, 13, 14, 15, 17, 20, 21, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 37, 39, 40, 41, 42, 43, 44, 45, 46, 49, 50, 53, 54, 55, 56, 57, 58, 59, 60, 62, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 76, 82, 84, 85, 87, 88, 91, 94, 96, 100, 102, 103, 106, 126, 143, 157, 174, 177, 180, 183, 186, 189, 193, 194, 196, 197, 199, 200, 201, 202, 203, 204, 205, 206, 207, 208, 210, 211, 213, 215, 216, 217, 219, 242, 243, 245, 246, 248, 250, 251, 252, 253, 256, 257, 259, 260, 263, 264, 265, 268, 269, 270, 272, 273, 275, 276, 277, 279, 280, 281, 283, 285, 286, 287, 290, 292, 293, 294, 296, 300, 301, 303, 304, 306, 307, 308, 310, 311, 312, 314, 316, 317, 318, 320, 321, 323, 325, 327, 328, 329, 331, 349, 350, 352, 353, 355, 359, 360, 362, 363, 365, 366, 367, 369, 370, 371, 373, 375, 376, 378, 380, 388, 389, 390, 392, 393, 410, 411, 415, 419, 420, 422, 423, 425, 426, 427, 429, 431, 432, 434, 436, 438, 439, 441, 444, 445, 447, 450, 453, 454, 456, 459, 462, 463, 465, 467, 469, 490, 492, 505, 507, 508, 509, 511, 512, 515, 519, 522, 526, 529, 536, 538, 540, 545, 546, 548, 550, 551, 552, 553, 554, 555, 559], "excluded_lines": [], "functions": {"ProcessingRecord.__post_init__": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 8, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 8, "excluded_lines": 0}, "missing_lines": [39, 40, 41, 42, 43, 44, 45, 46], "excluded_lines": []}, "AnalysisRecord.__post_init__": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 10, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 10, "excluded_lines": 0}, "missing_lines": [64, 65, 66, 67, 68, 69, 70, 71, 72, 73], "excluded_lines": []}, "DatabaseManager.__init__": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 7, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 7, "excluded_lines": 0}, "missing_lines": [84, 85, 87, 88, 91, 94, 96], "excluded_lines": []}, "DatabaseManager._initialize_database": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 14, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 14, "excluded_lines": 0}, "missing_lines": [102, 103, 106, 126, 143, 157, 174, 177, 180, 183, 186, 189, 193, 194], "excluded_lines": []}, "DatabaseManager._get_connection": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 12, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 12, "excluded_lines": 0}, "missing_lines": [199, 200, 201, 202, 203, 204, 205, 206, 207, 208, 210, 211], "excluded_lines": []}, "DatabaseManager.add_processing_record": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 8, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 8, "excluded_lines": 0}, "missing_lines": [215, 216, 217, 219, 242, 243, 245, 246], "excluded_lines": []}, "DatabaseManager.update_processing_record": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 22, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 22, "excluded_lines": 0}, "missing_lines": [250, 251, 252, 253, 256, 257, 259, 260, 263, 264, 265, 268, 269, 270, 272, 273, 275, 276, 277, 279, 280, 281], "excluded_lines": []}, "DatabaseManager.get_processing_record": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 7, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 7, "excluded_lines": 0}, "missing_lines": [285, 286, 287, 290, 292, 293, 294], "excluded_lines": []}, "DatabaseManager.get_processing_records": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 17, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 17, "excluded_lines": 0}, "missing_lines": [300, 301, 303, 304, 306, 307, 308, 310, 311, 312, 314, 316, 317, 318, 320, 321, 323], "excluded_lines": []}, "DatabaseManager.add_analysis_record": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 8, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 8, "excluded_lines": 0}, "missing_lines": [327, 328, 329, 331, 349, 350, 352, 353], "excluded_lines": []}, "DatabaseManager.get_analysis_records": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 14, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 14, "excluded_lines": 0}, "missing_lines": [359, 360, 362, 363, 365, 366, 367, 369, 370, 371, 373, 375, 376, 378], "excluded_lines": []}, "DatabaseManager.add_quality_metrics": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 7, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 7, "excluded_lines": 0}, "missing_lines": [388, 389, 390, 392, 393, 410, 411], "excluded_lines": []}, "DatabaseManager.get_quality_metrics": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 11, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 11, "excluded_lines": 0}, "missing_lines": [419, 420, 422, 423, 425, 426, 427, 429, 431, 432, 434], "excluded_lines": []}, "DatabaseManager.get_statistics": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 14, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 14, "excluded_lines": 0}, "missing_lines": [438, 439, 441, 444, 445, 447, 450, 453, 454, 456, 459, 462, 463, 465], "excluded_lines": []}, "DatabaseManager._row_to_processing_record": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [469], "excluded_lines": []}, "DatabaseManager._row_to_analysis_record": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [492], "excluded_lines": []}, "DatabaseManager.cleanup_old_records": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 15, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 15, "excluded_lines": 0}, "missing_lines": [507, 508, 509, 511, 512, 515, 519, 522, 526, 529, 536, 538, 540, 545, 546], "excluded_lines": []}, "DatabaseManager.vacuum_database": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 6, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 6, "excluded_lines": 0}, "missing_lines": [550, 551, 552, 553, 554, 555], "excluded_lines": []}, "": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 55, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 55, "excluded_lines": 0}, "missing_lines": [7, 8, 9, 10, 11, 12, 13, 14, 15, 17, 20, 21, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 37, 49, 50, 53, 54, 55, 56, 57, 58, 59, 60, 62, 76, 82, 100, 196, 197, 213, 248, 283, 296, 325, 355, 380, 415, 436, 467, 490, 505, 548, 559], "excluded_lines": []}}, "classes": {"ProcessingRecord": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 8, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 8, "excluded_lines": 0}, "missing_lines": [39, 40, 41, 42, 43, 44, 45, 46], "excluded_lines": []}, "AnalysisRecord": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 10, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 10, "excluded_lines": 0}, "missing_lines": [64, 65, 66, 67, 68, 69, 70, 71, 72, 73], "excluded_lines": []}, "DatabaseManager": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 164, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 164, "excluded_lines": 0}, "missing_lines": [84, 85, 87, 88, 91, 94, 96, 102, 103, 106, 126, 143, 157, 174, 177, 180, 183, 186, 189, 193, 194, 199, 200, 201, 202, 203, 204, 205, 206, 207, 208, 210, 211, 215, 216, 217, 219, 242, 243, 245, 246, 250, 251, 252, 253, 256, 257, 259, 260, 263, 264, 265, 268, 269, 270, 272, 273, 275, 276, 277, 279, 280, 281, 285, 286, 287, 290, 292, 293, 294, 300, 301, 303, 304, 306, 307, 308, 310, 311, 312, 314, 316, 317, 318, 320, 321, 323, 327, 328, 329, 331, 349, 350, 352, 353, 359, 360, 362, 363, 365, 366, 367, 369, 370, 371, 373, 375, 376, 378, 388, 389, 390, 392, 393, 410, 411, 419, 420, 422, 423, 425, 426, 427, 429, 431, 432, 434, 438, 439, 441, 444, 445, 447, 450, 453, 454, 456, 459, 462, 463, 465, 469, 492, 507, 508, 509, 511, 512, 515, 519, 522, 526, 529, 536, 538, 540, 545, 546, 550, 551, 552, 553, 554, 555], "excluded_lines": []}, "": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 55, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 55, "excluded_lines": 0}, "missing_lines": [7, 8, 9, 10, 11, 12, 13, 14, 15, 17, 20, 21, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 37, 49, 50, 53, 54, 55, 56, 57, 58, 59, 60, 62, 76, 82, 100, 196, 197, 213, 248, 283, 296, 325, 355, 380, 415, 436, 467, 490, 505, 548, 559], "excluded_lines": []}}}, "aretomo3_gui/core/dependency_check.py": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 78, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 78, "excluded_lines": 3}, "missing_lines": [3, 4, 5, 6, 7, 9, 12, 14, 15, 16, 17, 19, 20, 21, 22, 23, 28, 29, 30, 33, 35, 36, 37, 38, 39, 42, 44, 45, 46, 49, 51, 52, 53, 56, 57, 58, 61, 62, 64, 65, 68, 69, 70, 71, 73, 74, 75, 80, 81, 84, 86, 89, 97, 98, 99, 102, 103, 105, 108, 110, 113, 114, 116, 124, 126, 127, 129, 130, 131, 132, 133, 134, 136, 137, 138, 140, 141, 142], "excluded_lines": [145, 146, 147], "functions": {"check_dependency": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 12, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 12, "excluded_lines": 0}, "missing_lines": [14, 15, 16, 17, 19, 20, 21, 22, 23, 28, 29, 30], "excluded_lines": []}, "check_cuda": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 5, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 5, "excluded_lines": 0}, "missing_lines": [35, 36, 37, 38, 39], "excluded_lines": []}, "check_aretomo": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 23, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 23, "excluded_lines": 0}, "missing_lines": [44, 45, 46, 49, 51, 52, 53, 56, 57, 58, 61, 62, 64, 65, 68, 69, 70, 71, 73, 74, 75, 80, 81], "excluded_lines": []}, "check_environment": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 8, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 8, "excluded_lines": 0}, "missing_lines": [86, 89, 97, 98, 99, 102, 103, 105], "excluded_lines": []}, "check_dependencies": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 4, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 4, "excluded_lines": 0}, "missing_lines": [110, 113, 114, 116], "excluded_lines": []}, "print_status": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 14, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 14, "excluded_lines": 0}, "missing_lines": [126, 127, 129, 130, 131, 132, 133, 134, 136, 137, 138, 140, 141, 142], "excluded_lines": []}, "": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 12, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 12, "excluded_lines": 3}, "missing_lines": [3, 4, 5, 6, 7, 9, 12, 33, 42, 84, 108, 124], "excluded_lines": [145, 146, 147]}}, "classes": {"": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 78, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 78, "excluded_lines": 3}, "missing_lines": [3, 4, 5, 6, 7, 9, 12, 14, 15, 16, 17, 19, 20, 21, 22, 23, 28, 29, 30, 33, 35, 36, 37, 38, 39, 42, 44, 45, 46, 49, 51, 52, 53, 56, 57, 58, 61, 62, 64, 65, 68, 69, 70, 71, 73, 74, 75, 80, 81, 84, 86, 89, 97, 98, 99, 102, 103, 105, 108, 110, 113, 114, 116, 124, 126, 127, 129, 130, 131, 132, 133, 134, 136, 137, 138, 140, 141, 142], "excluded_lines": [145, 146, 147]}}}, "aretomo3_gui/core/enhanced_database_manager.py": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 207, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 207, "excluded_lines": 0}, "missing_lines": [7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 19, 22, 23, 26, 27, 28, 29, 30, 33, 36, 38, 39, 41, 43, 44, 45, 46, 47, 48, 49, 50, 52, 54, 55, 56, 57, 59, 60, 61, 62, 64, 67, 70, 74, 75, 76, 79, 80, 81, 82, 85, 88, 89, 90, 92, 96, 98, 99, 100, 101, 103, 105, 106, 112, 115, 118, 119, 120, 122, 128, 129, 130, 131, 133, 134, 136, 137, 138, 140, 142, 144, 145, 146, 147, 149, 151, 152, 153, 154, 155, 156, 158, 159, 161, 163, 164, 166, 168, 169, 170, 172, 173, 176, 177, 178, 180, 182, 183, 184, 186, 188, 189, 192, 193, 194, 195, 196, 198, 200, 201, 203, 205, 206, 207, 208, 210, 211, 213, 218, 221, 222, 224, 226, 227, 228, 230, 232, 234, 236, 237, 240, 241, 242, 243, 244, 245, 247, 250, 256, 258, 267, 268, 271, 278, 280, 282, 284, 285, 287, 371, 373, 375, 377, 385, 386, 387, 388, 390, 391, 392, 393, 395, 397, 398, 399, 400, 401, 403, 405, 406, 407, 408, 409, 410, 411, 413, 414, 415, 417, 419, 420, 423, 426, 428, 429, 431, 433, 434, 435, 436, 438, 440, 441, 445, 448, 450], "excluded_lines": [], "functions": {"TransactionManager.__init__": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 2, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 2, "excluded_lines": 0}, "missing_lines": [38, 39], "excluded_lines": []}, "TransactionManager.__enter__": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 8, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 8, "excluded_lines": 0}, "missing_lines": [43, 44, 45, 46, 47, 48, 49, 50], "excluded_lines": []}, "TransactionManager.__exit__": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 9, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 9, "excluded_lines": 0}, "missing_lines": [54, 55, 56, 57, 59, 60, 61, 62, 64], "excluded_lines": []}, "ConnectionPool.__init__": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 12, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 12, "excluded_lines": 0}, "missing_lines": [74, 75, 76, 79, 80, 81, 82, 85, 88, 89, 90, 92], "excluded_lines": []}, "ConnectionPool._initialize_pool": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 4, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 4, "excluded_lines": 0}, "missing_lines": [98, 99, 100, 101], "excluded_lines": []}, "ConnectionPool._create_connection": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 17, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 17, "excluded_lines": 0}, "missing_lines": [105, 106, 112, 115, 118, 119, 120, 122, 128, 129, 130, 131, 133, 134, 136, 137, 138], "excluded_lines": []}, "ConnectionPool.get_connection": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 14, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 14, "excluded_lines": 0}, "missing_lines": [142, 144, 145, 146, 147, 149, 151, 152, 153, 154, 155, 156, 158, 159], "excluded_lines": []}, "ConnectionPool.return_connection": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 15, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 15, "excluded_lines": 0}, "missing_lines": [163, 164, 166, 168, 169, 170, 172, 173, 176, 177, 178, 180, 182, 183, 184], "excluded_lines": []}, "ConnectionPool._close_connection": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 10, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 10, "excluded_lines": 0}, "missing_lines": [188, 189, 192, 193, 194, 195, 196, 198, 200, 201], "excluded_lines": []}, "ConnectionPool._cleanup_loop": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 14, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 14, "excluded_lines": 0}, "missing_lines": [205, 206, 207, 208, 210, 211, 213, 218, 221, 222, 224, 226, 227, 228], "excluded_lines": []}, "ConnectionPool.close_all": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 11, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 11, "excluded_lines": 0}, "missing_lines": [232, 234, 236, 237, 240, 241, 242, 243, 244, 245, 247], "excluded_lines": []}, "EnhancedDatabaseManager.__init__": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 6, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 6, "excluded_lines": 0}, "missing_lines": [258, 267, 268, 271, 278, 280], "excluded_lines": []}, "EnhancedDatabaseManager._initialize_schema": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 4, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 4, "excluded_lines": 0}, "missing_lines": [284, 285, 287, 371], "excluded_lines": []}, "EnhancedDatabaseManager.get_connection": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [375], "excluded_lines": []}, "EnhancedDatabaseManager.execute_query": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 14, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 14, "excluded_lines": 0}, "missing_lines": [385, 386, 387, 388, 390, 391, 392, 393, 395, 397, 398, 399, 400, 401], "excluded_lines": []}, "EnhancedDatabaseManager.execute_transaction": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 10, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 10, "excluded_lines": 0}, "missing_lines": [405, 406, 407, 408, 409, 410, 411, 413, 414, 415], "excluded_lines": []}, "EnhancedDatabaseManager.close": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 2, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 2, "excluded_lines": 0}, "missing_lines": [419, 420], "excluded_lines": []}, "DatabaseConnectionContext.__init__": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 2, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 2, "excluded_lines": 0}, "missing_lines": [428, 429], "excluded_lines": []}, "DatabaseConnectionContext.__enter__": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 4, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 4, "excluded_lines": 0}, "missing_lines": [433, 434, 435, 436], "excluded_lines": []}, "DatabaseConnectionContext.__exit__": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 2, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 2, "excluded_lines": 0}, "missing_lines": [440, 441], "excluded_lines": []}, "get_enhanced_database_manager": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [450], "excluded_lines": []}, "": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 45, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 45, "excluded_lines": 0}, "missing_lines": [7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 19, 22, 23, 26, 27, 28, 29, 30, 33, 36, 41, 52, 67, 70, 96, 103, 140, 161, 186, 203, 230, 250, 256, 282, 373, 377, 403, 417, 423, 426, 431, 438, 445, 448], "excluded_lines": []}}, "classes": {"ConnectionInfo": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "TransactionManager": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 19, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 19, "excluded_lines": 0}, "missing_lines": [38, 39, 43, 44, 45, 46, 47, 48, 49, 50, 54, 55, 56, 57, 59, 60, 61, 62, 64], "excluded_lines": []}, "ConnectionPool": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 97, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 97, "excluded_lines": 0}, "missing_lines": [74, 75, 76, 79, 80, 81, 82, 85, 88, 89, 90, 92, 98, 99, 100, 101, 105, 106, 112, 115, 118, 119, 120, 122, 128, 129, 130, 131, 133, 134, 136, 137, 138, 142, 144, 145, 146, 147, 149, 151, 152, 153, 154, 155, 156, 158, 159, 163, 164, 166, 168, 169, 170, 172, 173, 176, 177, 178, 180, 182, 183, 184, 188, 189, 192, 193, 194, 195, 196, 198, 200, 201, 205, 206, 207, 208, 210, 211, 213, 218, 221, 222, 224, 226, 227, 228, 232, 234, 236, 237, 240, 241, 242, 243, 244, 245, 247], "excluded_lines": []}, "EnhancedDatabaseManager": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 37, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 37, "excluded_lines": 0}, "missing_lines": [258, 267, 268, 271, 278, 280, 284, 285, 287, 371, 375, 385, 386, 387, 388, 390, 391, 392, 393, 395, 397, 398, 399, 400, 401, 405, 406, 407, 408, 409, 410, 411, 413, 414, 415, 419, 420], "excluded_lines": []}, "DatabaseConnectionContext": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 8, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 8, "excluded_lines": 0}, "missing_lines": [428, 429, 433, 434, 435, 436, 440, 441], "excluded_lines": []}, "": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 46, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 46, "excluded_lines": 0}, "missing_lines": [7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 19, 22, 23, 26, 27, 28, 29, 30, 33, 36, 41, 52, 67, 70, 96, 103, 140, 161, 186, 203, 230, 250, 256, 282, 373, 377, 403, 417, 423, 426, 431, 438, 445, 448, 450], "excluded_lines": []}}}, "aretomo3_gui/core/enhanced_parameters.py": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 154, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 154, "excluded_lines": 0}, "missing_lines": [7, 8, 9, 10, 12, 15, 18, 19, 20, 21, 22, 23, 26, 29, 30, 31, 32, 33, 34, 35, 36, 39, 40, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 62, 65, 67, 68, 69, 70, 74, 78, 92, 106, 120, 136, 151, 166, 182, 195, 208, 223, 238, 253, 268, 283, 297, 311, 324, 339, 354, 369, 384, 399, 414, 429, 444, 460, 475, 490, 506, 521, 534, 547, 560, 575, 588, 604, 617, 631, 644, 658, 659, 660, 661, 663, 665, 666, 668, 670, 672, 674, 676, 678, 679, 680, 681, 683, 684, 685, 686, 689, 691, 692, 693, 694, 695, 696, 697, 699, 700, 701, 702, 703, 704, 706, 707, 708, 710, 711, 713, 715, 716, 718, 722, 726, 728, 730, 731, 733, 734, 737, 738, 741, 742, 743, 744, 745, 746, 748, 750, 752, 754, 756, 757, 760, 761, 762, 765, 766, 768], "excluded_lines": [], "functions": {"EnhancedParameterManager.__init__": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 4, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 4, "excluded_lines": 0}, "missing_lines": [67, 68, 69, 70], "excluded_lines": []}, "EnhancedParameterManager._initialize_parameters": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 44, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 44, "excluded_lines": 0}, "missing_lines": [78, 92, 106, 120, 136, 151, 166, 182, 195, 208, 223, 238, 253, 268, 283, 297, 311, 324, 339, 354, 369, 384, 399, 414, 429, 444, 460, 475, 490, 506, 521, 534, 547, 560, 575, 588, 604, 617, 631, 644, 658, 659, 660, 661], "excluded_lines": []}, "EnhancedParameterManager._add_parameter": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 2, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 2, "excluded_lines": 0}, "missing_lines": [665, 666], "excluded_lines": []}, "EnhancedParameterManager.get_parameter": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [670], "excluded_lines": []}, "EnhancedParameterManager.get_value": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [674], "excluded_lines": []}, "EnhancedParameterManager.set_value": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 8, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 8, "excluded_lines": 0}, "missing_lines": [678, 679, 680, 681, 683, 684, 685, 686], "excluded_lines": []}, "EnhancedParameterManager._validate_value": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 21, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 21, "excluded_lines": 0}, "missing_lines": [691, 692, 693, 694, 695, 696, 697, 699, 700, 701, 702, 703, 704, 706, 707, 708, 710, 711, 713, 715, 716], "excluded_lines": []}, "EnhancedParameterManager.get_parameters_by_category": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [722], "excluded_lines": []}, "EnhancedParameterManager.build_command_args": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 15, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 15, "excluded_lines": 0}, "missing_lines": [728, 730, 731, 733, 734, 737, 738, 741, 742, 743, 744, 745, 746, 748, 750], "excluded_lines": []}, "EnhancedParameterManager.validate_all_parameters": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 9, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 9, "excluded_lines": 0}, "missing_lines": [754, 756, 757, 760, 761, 762, 765, 766, 768], "excluded_lines": []}, "": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 48, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 48, "excluded_lines": 0}, "missing_lines": [7, 8, 9, 10, 12, 15, 18, 19, 20, 21, 22, 23, 26, 29, 30, 31, 32, 33, 34, 35, 36, 39, 40, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 62, 65, 74, 663, 668, 672, 676, 689, 718, 726, 752], "excluded_lines": []}}, "classes": {"ParameterCategory": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "ParameterType": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "ParameterDefinition": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "EnhancedParameterManager": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 106, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 106, "excluded_lines": 0}, "missing_lines": [67, 68, 69, 70, 78, 92, 106, 120, 136, 151, 166, 182, 195, 208, 223, 238, 253, 268, 283, 297, 311, 324, 339, 354, 369, 384, 399, 414, 429, 444, 460, 475, 490, 506, 521, 534, 547, 560, 575, 588, 604, 617, 631, 644, 658, 659, 660, 661, 665, 666, 670, 674, 678, 679, 680, 681, 683, 684, 685, 686, 691, 692, 693, 694, 695, 696, 697, 699, 700, 701, 702, 703, 704, 706, 707, 708, 710, 711, 713, 715, 716, 722, 728, 730, 731, 733, 734, 737, 738, 741, 742, 743, 744, 745, 746, 748, 750, 754, 756, 757, 760, 761, 762, 765, 766, 768], "excluded_lines": []}, "": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 48, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 48, "excluded_lines": 0}, "missing_lines": [7, 8, 9, 10, 12, 15, 18, 19, 20, 21, 22, 23, 26, 29, 30, 31, 32, 33, 34, 35, 36, 39, 40, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 62, 65, 74, 663, 668, 672, 676, 689, 718, 726, 752], "excluded_lines": []}}}, "aretomo3_gui/core/enhanced_realtime_processor.py": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 118, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 118, "excluded_lines": 0}, "missing_lines": [6, 7, 8, 9, 10, 12, 13, 14, 16, 19, 23, 24, 25, 26, 28, 29, 30, 31, 32, 33, 34, 35, 36, 39, 49, 51, 53, 54, 56, 59, 62, 63, 64, 67, 69, 70, 73, 76, 78, 80, 82, 83, 85, 87, 88, 89, 91, 92, 94, 96, 98, 99, 100, 101, 102, 105, 106, 108, 110, 112, 113, 114, 115, 116, 118, 120, 122, 125, 127, 129, 131, 132, 134, 135, 137, 140, 141, 144, 149, 150, 152, 153, 154, 155, 156, 158, 161, 164, 165, 167, 169, 171, 173, 175, 176, 179, 182, 183, 184, 185, 187, 189, 190, 192, 194, 196, 199, 200, 202, 203, 205, 206, 207, 209, 212, 213, 214, 217], "excluded_lines": [], "functions": {"EnhancedRealTimeProcessor.__init__": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 10, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 10, "excluded_lines": 0}, "missing_lines": [29, 30, 31, 32, 33, 34, 35, 36, 39, 49], "excluded_lines": []}, "EnhancedRealTimeProcessor.start_monitoring": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 13, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 13, "excluded_lines": 0}, "missing_lines": [53, 54, 56, 59, 62, 63, 64, 67, 69, 70, 73, 76, 78], "excluded_lines": []}, "EnhancedRealTimeProcessor.stop_monitoring": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 9, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 9, "excluded_lines": 0}, "missing_lines": [82, 83, 85, 87, 88, 89, 91, 92, 94], "excluded_lines": []}, "EnhancedRealTimeProcessor.add_file_to_queue": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 8, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 8, "excluded_lines": 0}, "missing_lines": [98, 99, 100, 101, 102, 105, 106, 108], "excluded_lines": []}, "EnhancedRealTimeProcessor._processing_worker": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 6, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 6, "excluded_lines": 0}, "missing_lines": [112, 113, 114, 115, 116, 118], "excluded_lines": []}, "EnhancedRealTimeProcessor._process_file_with_signals": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 19, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 19, "excluded_lines": 0}, "missing_lines": [122, 125, 127, 129, 131, 132, 134, 135, 137, 140, 141, 144, 149, 150, 152, 153, 154, 155, 156], "excluded_lines": []}, "EnhancedRealTimeProcessor._simulate_aretomo_processing": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 4, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 4, "excluded_lines": 0}, "missing_lines": [161, 164, 165, 167], "excluded_lines": []}, "EnhancedRealTimeProcessor.get_current_stats": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [171], "excluded_lines": []}, "EnhancedRealTimeProcessor.set_auto_processing": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 2, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 2, "excluded_lines": 0}, "missing_lines": [175, 176], "excluded_lines": []}, "EnhancedTiltSeriesEventHandler.__init__": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 3, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 3, "excluded_lines": 0}, "missing_lines": [183, 184, 185], "excluded_lines": []}, "EnhancedTiltSeriesEventHandler.on_created": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 4, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 4, "excluded_lines": 0}, "missing_lines": [189, 190, 192, 194], "excluded_lines": []}, "EnhancedTiltSeriesEventHandler._wait_for_file_stability": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 2, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 2, "excluded_lines": 0}, "missing_lines": [199, 217], "excluded_lines": []}, "EnhancedTiltSeriesEventHandler._wait_for_file_stability.check_stability": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 10, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 10, "excluded_lines": 0}, "missing_lines": [200, 202, 203, 205, 206, 207, 209, 212, 213, 214], "excluded_lines": []}, "": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 27, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 27, "excluded_lines": 0}, "missing_lines": [6, 7, 8, 9, 10, 12, 13, 14, 16, 19, 23, 24, 25, 26, 28, 51, 80, 96, 110, 120, 158, 169, 173, 179, 182, 187, 196], "excluded_lines": []}}, "classes": {"EnhancedRealTimeProcessor": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 72, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 72, "excluded_lines": 0}, "missing_lines": [29, 30, 31, 32, 33, 34, 35, 36, 39, 49, 53, 54, 56, 59, 62, 63, 64, 67, 69, 70, 73, 76, 78, 82, 83, 85, 87, 88, 89, 91, 92, 94, 98, 99, 100, 101, 102, 105, 106, 108, 112, 113, 114, 115, 116, 118, 122, 125, 127, 129, 131, 132, 134, 135, 137, 140, 141, 144, 149, 150, 152, 153, 154, 155, 156, 161, 164, 165, 167, 171, 175, 176], "excluded_lines": []}, "EnhancedTiltSeriesEventHandler": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 19, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 19, "excluded_lines": 0}, "missing_lines": [183, 184, 185, 189, 190, 192, 194, 199, 200, 202, 203, 205, 206, 207, 209, 212, 213, 214, 217], "excluded_lines": []}, "": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 27, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 27, "excluded_lines": 0}, "missing_lines": [6, 7, 8, 9, 10, 12, 13, 14, 16, 19, 23, 24, 25, 26, 28, 51, 80, 96, 110, 120, 158, 169, 173, 179, 182, 187, 196], "excluded_lines": []}}}, "aretomo3_gui/core/error_handling.py": {"executed_lines": [2, 44, 45, 48, 49, 50, 51, 54, 60, 61, 64, 65, 70, 71, 72, 73, 76, 88, 89, 99, 100, 101, 102, 104, 110, 116, 122, 134, 135, 147, 172, 173, 183, 186, 187, 197, 200, 201, 215, 241, 319, 384], "summary": {"covered_lines": 36, "num_statements": 108, "percent_covered": 33.333333333333336, "percent_covered_display": "33", "missing_lines": 72, "excluded_lines": 0}, "missing_lines": [77, 78, 79, 80, 81, 106, 107, 108, 112, 113, 114, 118, 119, 120, 124, 125, 126, 161, 162, 163, 166, 231, 232, 267, 270, 271, 272, 278, 281, 282, 285, 287, 291, 297, 299, 307, 308, 309, 312, 314, 355, 356, 357, 359, 360, 361, 364, 365, 366, 368, 373, 374, 402, 417, 419, 420, 423, 424, 425, 428, 429, 430, 433, 434, 435, 438, 439, 446, 455, 456, 459, 460], "excluded_lines": [], "functions": {"ErrorSeverity.__ge__": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 3, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 3, "excluded_lines": 0}, "missing_lines": [106, 107, 108], "excluded_lines": []}, "ErrorSeverity.__le__": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 3, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 3, "excluded_lines": 0}, "missing_lines": [112, 113, 114], "excluded_lines": []}, "ErrorSeverity.__gt__": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 3, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 3, "excluded_lines": 0}, "missing_lines": [118, 119, 120], "excluded_lines": []}, "ErrorSeverity.__lt__": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 3, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 3, "excluded_lines": 0}, "missing_lines": [124, 125, 126], "excluded_lines": []}, "AreTomo3Error.__init__": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 4, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 4, "excluded_lines": 0}, "missing_lines": [161, 162, 163, 166], "excluded_lines": []}, "GPUError.__init__": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 2, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 2, "excluded_lines": 0}, "missing_lines": [231, 232], "excluded_lines": []}, "handle_exception": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 17, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 17, "excluded_lines": 0}, "missing_lines": [267, 270, 271, 272, 278, 281, 282, 285, 287, 291, 297, 299, 307, 308, 309, 312, 314], "excluded_lines": []}, "try_operation": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 12, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 12, "excluded_lines": 0}, "missing_lines": [355, 356, 357, 359, 360, 361, 364, 365, 366, 368, 373, 374], "excluded_lines": []}, "install_global_exception_handler": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 3, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 3, "excluded_lines": 0}, "missing_lines": [402, 459, 460], "excluded_lines": []}, "install_global_exception_handler.global_exception_handler": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 17, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 17, "excluded_lines": 0}, "missing_lines": [417, 419, 420, 423, 424, 425, 428, 429, 430, 433, 434, 435, 438, 439, 446, 455, 456], "excluded_lines": []}, "": {"executed_lines": [2, 44, 45, 48, 49, 50, 51, 54, 60, 61, 64, 65, 70, 71, 72, 73, 76, 88, 89, 99, 100, 101, 102, 104, 110, 116, 122, 134, 135, 147, 172, 173, 183, 186, 187, 197, 200, 201, 215, 241, 319, 384], "summary": {"covered_lines": 36, "num_statements": 41, "percent_covered": 87.8048780487805, "percent_covered_display": "88", "missing_lines": 5, "excluded_lines": 0}, "missing_lines": [77, 78, 79, 80, 81], "excluded_lines": []}}, "classes": {"ErrorSeverity": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 12, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 12, "excluded_lines": 0}, "missing_lines": [106, 107, 108, 112, 113, 114, 118, 119, 120, 124, 125, 126], "excluded_lines": []}, "AreTomo3Error": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 4, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 4, "excluded_lines": 0}, "missing_lines": [161, 162, 163, 166], "excluded_lines": []}, "ProcessingError": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "FileSystemError": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "GPUError": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 2, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 2, "excluded_lines": 0}, "missing_lines": [231, 232], "excluded_lines": []}, "": {"executed_lines": [2, 44, 45, 48, 49, 50, 51, 54, 60, 61, 64, 65, 70, 71, 72, 73, 76, 88, 89, 99, 100, 101, 102, 104, 110, 116, 122, 134, 135, 147, 172, 173, 183, 186, 187, 197, 200, 201, 215, 241, 319, 384], "summary": {"covered_lines": 36, "num_statements": 90, "percent_covered": 40.0, "percent_covered_display": "40", "missing_lines": 54, "excluded_lines": 0}, "missing_lines": [77, 78, 79, 80, 81, 267, 270, 271, 272, 278, 281, 282, 285, 287, 291, 297, 299, 307, 308, 309, 312, 314, 355, 356, 357, 359, 360, 361, 364, 365, 366, 368, 373, 374, 402, 417, 419, 420, 423, 424, 425, 428, 429, 430, 433, 434, 435, 438, 439, 446, 455, 456, 459, 460], "excluded_lines": []}}}, "aretomo3_gui/core/error_recovery.py": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 225, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 225, "excluded_lines": 0}, "missing_lines": [7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 18, 21, 24, 25, 26, 27, 30, 31, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 47, 53, 55, 56, 57, 58, 59, 60, 63, 64, 65, 68, 69, 71, 73, 75, 88, 90, 108, 122, 124, 127, 130, 133, 136, 137, 139, 141, 142, 143, 145, 149, 150, 151, 154, 156, 166, 168, 171, 178, 179, 180, 182, 184, 187, 188, 191, 193, 195, 200, 201, 202, 203, 204, 205, 207, 209, 211, 214, 215, 216, 219, 220, 221, 222, 225, 226, 228, 229, 231, 233, 234, 237, 238, 240, 241, 244, 246, 247, 249, 250, 251, 253, 255, 257, 260, 262, 263, 264, 266, 268, 270, 271, 272, 273, 274, 275, 276, 277, 278, 279, 280, 281, 283, 285, 287, 289, 290, 295, 297, 299, 301, 303, 304, 305, 310, 312, 315, 317, 318, 320, 323, 325, 327, 328, 329, 331, 333, 335, 336, 337, 339, 340, 341, 342, 343, 345, 348, 351, 353, 355, 357, 358, 359, 360, 362, 364, 366, 368, 369, 370, 371, 372, 374, 376, 377, 379, 380, 381, 382, 384, 387, 388, 390, 392, 394, 396, 397, 398, 399, 401, 403, 404, 406, 412, 413, 415, 416, 417, 421, 422, 423, 426, 428, 439, 442, 445, 446, 448, 449, 450, 451, 452, 454, 456, 460], "excluded_lines": [], "functions": {"ErrorRecoverySystem.__init__": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 12, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 12, "excluded_lines": 0}, "missing_lines": [55, 56, 57, 58, 59, 60, 63, 64, 65, 68, 69, 71], "excluded_lines": []}, "ErrorRecoverySystem._register_default_strategies": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [75], "excluded_lines": []}, "ErrorRecoverySystem._register_error_patterns": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [90], "excluded_lines": []}, "ErrorRecoverySystem.handle_error": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 11, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 11, "excluded_lines": 0}, "missing_lines": [122, 124, 127, 130, 133, 136, 137, 139, 141, 142, 143], "excluded_lines": []}, "ErrorRecoverySystem._create_error_record": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 5, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 5, "excluded_lines": 0}, "missing_lines": [149, 150, 151, 154, 156], "excluded_lines": []}, "ErrorRecoverySystem._check_duplicate_error": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 5, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 5, "excluded_lines": 0}, "missing_lines": [168, 171, 178, 179, 180], "excluded_lines": []}, "ErrorRecoverySystem._store_error": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 4, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 4, "excluded_lines": 0}, "missing_lines": [184, 187, 188, 191], "excluded_lines": []}, "ErrorRecoverySystem._log_error": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 8, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 8, "excluded_lines": 0}, "missing_lines": [195, 200, 201, 202, 203, 204, 205, 207], "excluded_lines": []}, "ErrorRecoverySystem._attempt_recovery": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 12, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 12, "excluded_lines": 0}, "missing_lines": [211, 214, 215, 216, 219, 220, 221, 222, 225, 226, 228, 229], "excluded_lines": []}, "ErrorRecoverySystem._execute_recovery": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 19, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 19, "excluded_lines": 0}, "missing_lines": [233, 234, 237, 238, 240, 241, 244, 246, 247, 249, 250, 251, 253, 255, 257, 260, 262, 263, 264], "excluded_lines": []}, "ErrorRecoverySystem._get_recovery_strategy_key": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 14, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 14, "excluded_lines": 0}, "missing_lines": [268, 270, 271, 272, 273, 274, 275, 276, 277, 278, 279, 280, 281, 283], "excluded_lines": []}, "ErrorRecoverySystem._check_recovery_cooldown": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 5, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 5, "excluded_lines": 0}, "missing_lines": [287, 289, 290, 295, 297], "excluded_lines": []}, "ErrorRecoverySystem._count_recent_recovery_attempts": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 6, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 6, "excluded_lines": 0}, "missing_lines": [301, 303, 304, 305, 310, 312], "excluded_lines": []}, "ErrorRecoverySystem._recover_memory_error": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 8, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 8, "excluded_lines": 0}, "missing_lines": [317, 318, 320, 323, 325, 327, 328, 329], "excluded_lines": []}, "ErrorRecoverySystem._recover_file_error": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 9, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 9, "excluded_lines": 0}, "missing_lines": [333, 335, 336, 337, 339, 340, 341, 342, 343], "excluded_lines": []}, "ErrorRecoverySystem._recover_permission_error": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 2, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 2, "excluded_lines": 0}, "missing_lines": [348, 351], "excluded_lines": []}, "ErrorRecoverySystem._recover_network_error": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 5, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 5, "excluded_lines": 0}, "missing_lines": [355, 357, 358, 359, 360], "excluded_lines": []}, "ErrorRecoverySystem._recover_gui_freeze": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 7, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 7, "excluded_lines": 0}, "missing_lines": [364, 366, 368, 369, 370, 371, 372], "excluded_lines": []}, "ErrorRecoverySystem._recover_plot_error": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 6, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 6, "excluded_lines": 0}, "missing_lines": [376, 377, 379, 380, 381, 382], "excluded_lines": []}, "ErrorRecoverySystem._recover_data_corruption": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 2, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 2, "excluded_lines": 0}, "missing_lines": [387, 388], "excluded_lines": []}, "ErrorRecoverySystem._recover_config_error": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 6, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 6, "excluded_lines": 0}, "missing_lines": [392, 394, 396, 397, 398, 399], "excluded_lines": []}, "ErrorRecoverySystem.get_error_summary": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 13, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 13, "excluded_lines": 0}, "missing_lines": [403, 404, 406, 412, 413, 415, 416, 417, 421, 422, 423, 426, 428], "excluded_lines": []}, "error_handler": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 2, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 2, "excluded_lines": 0}, "missing_lines": [442, 456], "excluded_lines": []}, "error_handler.decorator": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 3, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 3, "excluded_lines": 0}, "missing_lines": [445, 446, 454], "excluded_lines": []}, "error_handler.decorator.wrapper": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 5, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 5, "excluded_lines": 0}, "missing_lines": [448, 449, 450, 451, 452], "excluded_lines": []}, "": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 54, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 54, "excluded_lines": 0}, "missing_lines": [7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 18, 21, 24, 25, 26, 27, 30, 31, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 47, 53, 73, 88, 108, 145, 166, 182, 193, 209, 231, 266, 285, 299, 315, 331, 345, 353, 362, 374, 384, 390, 401, 439, 460], "excluded_lines": []}}, "classes": {"ErrorSeverity": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "ErrorRecord": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "ErrorRecoverySystem": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 161, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 161, "excluded_lines": 0}, "missing_lines": [55, 56, 57, 58, 59, 60, 63, 64, 65, 68, 69, 71, 75, 90, 122, 124, 127, 130, 133, 136, 137, 139, 141, 142, 143, 149, 150, 151, 154, 156, 168, 171, 178, 179, 180, 184, 187, 188, 191, 195, 200, 201, 202, 203, 204, 205, 207, 211, 214, 215, 216, 219, 220, 221, 222, 225, 226, 228, 229, 233, 234, 237, 238, 240, 241, 244, 246, 247, 249, 250, 251, 253, 255, 257, 260, 262, 263, 264, 268, 270, 271, 272, 273, 274, 275, 276, 277, 278, 279, 280, 281, 283, 287, 289, 290, 295, 297, 301, 303, 304, 305, 310, 312, 317, 318, 320, 323, 325, 327, 328, 329, 333, 335, 336, 337, 339, 340, 341, 342, 343, 348, 351, 355, 357, 358, 359, 360, 364, 366, 368, 369, 370, 371, 372, 376, 377, 379, 380, 381, 382, 387, 388, 392, 394, 396, 397, 398, 399, 403, 404, 406, 412, 413, 415, 416, 417, 421, 422, 423, 426, 428], "excluded_lines": []}, "": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 64, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 64, "excluded_lines": 0}, "missing_lines": [7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 18, 21, 24, 25, 26, 27, 30, 31, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 47, 53, 73, 88, 108, 145, 166, 182, 193, 209, 231, 266, 285, 299, 315, 331, 345, 353, 362, 374, 384, 390, 401, 439, 442, 445, 446, 448, 449, 450, 451, 452, 454, 456, 460], "excluded_lines": []}}}, "aretomo3_gui/core/file_organization.py": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 226, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 226, "excluded_lines": 0}, "missing_lines": [7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 18, 21, 22, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 37, 39, 40, 43, 46, 48, 49, 52, 66, 67, 70, 71, 74, 76, 78, 82, 84, 86, 87, 89, 90, 91, 92, 93, 96, 97, 98, 101, 102, 103, 106, 109, 111, 112, 114, 115, 119, 120, 122, 124, 128, 130, 131, 132, 133, 134, 137, 138, 140, 142, 145, 146, 149, 150, 151, 154, 157, 159, 160, 164, 165, 169, 170, 172, 174, 176, 178, 192, 194, 196, 197, 200, 203, 206, 209, 212, 215, 218, 219, 222, 224, 233, 234, 235, 237, 249, 250, 251, 263, 265, 266, 267, 268, 269, 270, 271, 272, 273, 275, 277, 279, 281, 283, 285, 291, 293, 299, 301, 310, 312, 313, 314, 315, 318, 319, 320, 321, 324, 325, 326, 327, 329, 331, 333, 334, 336, 338, 340, 341, 343, 344, 345, 346, 347, 348, 349, 350, 351, 352, 354, 355, 356, 358, 360, 362, 363, 367, 369, 370, 372, 373, 374, 376, 377, 378, 380, 381, 383, 386, 388, 389, 390, 392, 394, 395, 399, 400, 402, 403, 405, 407, 408, 409, 410, 412, 417, 421, 422, 423, 425, 427, 428, 431, 432, 434, 436, 438, 439, 440, 442, 444, 446, 447, 448, 451, 452, 453, 455, 456, 458, 459, 460], "excluded_lines": [], "functions": {"FileMetadata.__post_init__": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 2, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 2, "excluded_lines": 0}, "missing_lines": [39, 40], "excluded_lines": []}, "FileOrganizer.__init__": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 9, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 9, "excluded_lines": 0}, "missing_lines": [48, 49, 52, 66, 67, 70, 71, 74, 76], "excluded_lines": []}, "FileOrganizer.organize_input_files": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 24, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 24, "excluded_lines": 0}, "missing_lines": [82, 84, 86, 87, 89, 90, 91, 92, 93, 96, 97, 98, 101, 102, 103, 106, 109, 111, 112, 114, 115, 119, 120, 122], "excluded_lines": []}, "FileOrganizer.organize_output_files": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 24, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 24, "excluded_lines": 0}, "missing_lines": [128, 130, 131, 132, 133, 134, 137, 138, 140, 142, 145, 146, 149, 150, 151, 154, 157, 159, 160, 164, 165, 169, 170, 172], "excluded_lines": []}, "FileOrganizer.determine_file_type": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 3, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 3, "excluded_lines": 0}, "missing_lines": [176, 178, 192], "excluded_lines": []}, "FileOrganizer.categorize_output_file": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 11, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 11, "excluded_lines": 0}, "missing_lines": [196, 197, 200, 203, 206, 209, 212, 215, 218, 219, 222], "excluded_lines": []}, "FileOrganizer.create_file_metadata": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 7, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 7, "excluded_lines": 0}, "missing_lines": [233, 234, 235, 237, 249, 250, 251], "excluded_lines": []}, "FileOrganizer.calculate_checksum": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 9, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 9, "excluded_lines": 0}, "missing_lines": [265, 266, 267, 268, 269, 270, 271, 272, 273], "excluded_lines": []}, "FileOrganizer.register_file": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [277], "excluded_lines": []}, "FileOrganizer.get_file_metadata": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [281], "excluded_lines": []}, "FileOrganizer.find_files_by_series": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [285], "excluded_lines": []}, "FileOrganizer.find_files_by_type": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [293], "excluded_lines": []}, "FileOrganizer.get_storage_statistics": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 15, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 15, "excluded_lines": 0}, "missing_lines": [301, 310, 312, 313, 314, 315, 318, 319, 320, 321, 324, 325, 326, 327, 329], "excluded_lines": []}, "FileOrganizer.cleanup_old_files": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 20, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 20, "excluded_lines": 0}, "missing_lines": [333, 334, 336, 338, 340, 341, 343, 344, 345, 346, 347, 348, 349, 350, 351, 352, 354, 355, 356, 358], "excluded_lines": []}, "FileOrganizer.create_backup": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 18, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 18, "excluded_lines": 0}, "missing_lines": [362, 363, 367, 369, 370, 372, 373, 374, 376, 377, 378, 380, 381, 383, 386, 388, 389, 390], "excluded_lines": []}, "FileOrganizer.save_file_registry": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 6, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 6, "excluded_lines": 0}, "missing_lines": [394, 395, 399, 400, 402, 403], "excluded_lines": []}, "FileOrganizer.load_file_registry": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 9, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 9, "excluded_lines": 0}, "missing_lines": [407, 408, 409, 410, 412, 417, 421, 422, 423], "excluded_lines": []}, "FileOrganizer.export_file_list": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 22, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 22, "excluded_lines": 0}, "missing_lines": [427, 428, 431, 432, 434, 436, 438, 439, 440, 442, 444, 446, 447, 448, 451, 452, 453, 455, 456, 458, 459, 460], "excluded_lines": []}, "": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 43, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 43, "excluded_lines": 0}, "missing_lines": [7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 18, 21, 22, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 37, 43, 46, 78, 124, 174, 194, 224, 263, 275, 279, 283, 291, 299, 331, 360, 392, 405, 425], "excluded_lines": []}}, "classes": {"FileMetadata": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 2, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 2, "excluded_lines": 0}, "missing_lines": [39, 40], "excluded_lines": []}, "FileOrganizer": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 181, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 181, "excluded_lines": 0}, "missing_lines": [48, 49, 52, 66, 67, 70, 71, 74, 76, 82, 84, 86, 87, 89, 90, 91, 92, 93, 96, 97, 98, 101, 102, 103, 106, 109, 111, 112, 114, 115, 119, 120, 122, 128, 130, 131, 132, 133, 134, 137, 138, 140, 142, 145, 146, 149, 150, 151, 154, 157, 159, 160, 164, 165, 169, 170, 172, 176, 178, 192, 196, 197, 200, 203, 206, 209, 212, 215, 218, 219, 222, 233, 234, 235, 237, 249, 250, 251, 265, 266, 267, 268, 269, 270, 271, 272, 273, 277, 281, 285, 293, 301, 310, 312, 313, 314, 315, 318, 319, 320, 321, 324, 325, 326, 327, 329, 333, 334, 336, 338, 340, 341, 343, 344, 345, 346, 347, 348, 349, 350, 351, 352, 354, 355, 356, 358, 362, 363, 367, 369, 370, 372, 373, 374, 376, 377, 378, 380, 381, 383, 386, 388, 389, 390, 394, 395, 399, 400, 402, 403, 407, 408, 409, 410, 412, 417, 421, 422, 423, 427, 428, 431, 432, 434, 436, 438, 439, 440, 442, 444, 446, 447, 448, 451, 452, 453, 455, 456, 458, 459, 460], "excluded_lines": []}, "": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 43, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 43, "excluded_lines": 0}, "missing_lines": [7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 18, 21, 22, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 37, 43, 46, 78, 124, 174, 194, 224, 263, 275, 279, 283, 291, 299, 331, 360, 392, 405, 425], "excluded_lines": []}}}, "aretomo3_gui/core/file_watcher.py": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 99, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 99, "excluded_lines": 0}, "missing_lines": [2, 3, 4, 5, 6, 7, 8, 9, 11, 13, 16, 17, 20, 21, 22, 23, 24, 25, 27, 28, 30, 37, 38, 41, 42, 43, 46, 49, 51, 52, 53, 54, 55, 58, 59, 62, 64, 65, 66, 69, 70, 72, 80, 81, 84, 85, 86, 88, 89, 90, 91, 92, 94, 95, 98, 99, 100, 101, 102, 104, 106, 110, 111, 113, 115, 116, 118, 120, 121, 123, 124, 125, 126, 127, 129, 130, 132, 133, 135, 137, 140, 141, 142, 143, 144, 145, 146, 148, 149, 150, 153, 154, 156, 157, 159, 161, 163, 164, 167], "excluded_lines": [], "functions": {"TiltSeriesStatus.completion_percentage": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [30], "excluded_lines": []}, "FileWatcher.__init__": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 13, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 13, "excluded_lines": 0}, "missing_lines": [51, 52, 53, 54, 55, 58, 59, 62, 64, 65, 66, 69, 70], "excluded_lines": []}, "FileWatcher.watch": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 18, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 18, "excluded_lines": 0}, "missing_lines": [80, 81, 84, 85, 86, 88, 89, 90, 91, 92, 94, 95, 98, 99, 100, 101, 102, 104], "excluded_lines": []}, "FileWatcher._add_pending_event": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 2, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 2, "excluded_lines": 0}, "missing_lines": [110, 111], "excluded_lines": []}, "FileWatcher._schedule_process_events": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 2, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 2, "excluded_lines": 0}, "missing_lines": [115, 116], "excluded_lines": []}, "FileWatcher._process_pending_events": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 12, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 12, "excluded_lines": 0}, "missing_lines": [120, 121, 123, 124, 125, 126, 127, 129, 130, 132, 133, 135], "excluded_lines": []}, "FileWatcher._on_directory_changed": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 14, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 14, "excluded_lines": 0}, "missing_lines": [140, 141, 142, 143, 144, 145, 146, 148, 149, 150, 153, 154, 156, 157], "excluded_lines": []}, "FileWatcher._on_file_changed": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 4, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 4, "excluded_lines": 0}, "missing_lines": [161, 163, 164, 167], "excluded_lines": []}, "": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 33, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 33, "excluded_lines": 0}, "missing_lines": [2, 3, 4, 5, 6, 7, 8, 9, 11, 13, 16, 17, 20, 21, 22, 23, 24, 25, 27, 28, 37, 38, 41, 42, 43, 46, 49, 72, 106, 113, 118, 137, 159], "excluded_lines": []}}, "classes": {"TiltSeriesStatus": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [30], "excluded_lines": []}, "FileEvent": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "FileWatcher": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 65, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 65, "excluded_lines": 0}, "missing_lines": [51, 52, 53, 54, 55, 58, 59, 62, 64, 65, 66, 69, 70, 80, 81, 84, 85, 86, 88, 89, 90, 91, 92, 94, 95, 98, 99, 100, 101, 102, 104, 110, 111, 115, 116, 120, 121, 123, 124, 125, 126, 127, 129, 130, 132, 133, 135, 140, 141, 142, 143, 144, 145, 146, 148, 149, 150, 153, 154, 156, 157, 161, 163, 164, 167], "excluded_lines": []}, "": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 33, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 33, "excluded_lines": 0}, "missing_lines": [2, 3, 4, 5, 6, 7, 8, 9, 11, 13, 16, 17, 20, 21, 22, 23, 24, 25, 27, 28, 37, 38, 41, 42, 43, 46, 49, 72, 106, 113, 118, 137, 159], "excluded_lines": []}}}, "aretomo3_gui/core/logging_config.py": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 106, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 106, "excluded_lines": 3}, "missing_lines": [36, 37, 40, 41, 42, 43, 50, 53, 56, 57, 60, 61, 66, 67, 74, 87, 97, 98, 100, 110, 121, 156, 157, 160, 161, 164, 165, 168, 169, 172, 173, 174, 177, 180, 181, 184, 185, 186, 187, 190, 191, 194, 195, 196, 197, 198, 199, 200, 201, 203, 206, 220, 225, 239, 240, 243, 244, 246, 247, 248, 250, 251, 252, 253, 255, 257, 258, 259, 260, 261, 264, 265, 266, 267, 270, 271, 272, 273, 274, 275, 278, 279, 280, 281, 282, 283, 286, 287, 288, 289, 291, 292, 297, 298, 299, 300, 302, 305, 306, 308, 317, 318, 319, 321, 322, 323], "excluded_lines": [327, 328, 329], "functions": {"LogFilter.__init__": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 2, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 2, "excluded_lines": 0}, "missing_lines": [97, 98], "excluded_lines": []}, "LogFilter.filter": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [110], "excluded_lines": []}, "setup_logging": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 29, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 29, "excluded_lines": 0}, "missing_lines": [156, 157, 160, 161, 164, 165, 168, 169, 172, 173, 174, 177, 180, 181, 184, 185, 186, 187, 190, 191, 194, 195, 196, 197, 198, 199, 200, 201, 203], "excluded_lines": []}, "get_logger": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [220], "excluded_lines": []}, "log_system_info": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 53, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 53, "excluded_lines": 0}, "missing_lines": [239, 240, 243, 244, 246, 247, 248, 250, 251, 252, 253, 255, 257, 258, 259, 260, 261, 264, 265, 266, 267, 270, 271, 272, 273, 274, 275, 278, 279, 280, 281, 282, 283, 286, 287, 288, 289, 291, 292, 297, 298, 299, 300, 302, 305, 306, 308, 317, 318, 319, 321, 322, 323], "excluded_lines": []}, "": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 20, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 20, "excluded_lines": 3}, "missing_lines": [36, 37, 40, 41, 42, 43, 50, 53, 56, 57, 60, 61, 66, 67, 74, 87, 100, 121, 206, 225], "excluded_lines": [327, 328, 329]}}, "classes": {"LogFilter": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 3, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 3, "excluded_lines": 0}, "missing_lines": [97, 98, 110], "excluded_lines": []}, "": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 103, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 103, "excluded_lines": 3}, "missing_lines": [36, 37, 40, 41, 42, 43, 50, 53, 56, 57, 60, 61, 66, 67, 74, 87, 100, 121, 156, 157, 160, 161, 164, 165, 168, 169, 172, 173, 174, 177, 180, 181, 184, 185, 186, 187, 190, 191, 194, 195, 196, 197, 198, 199, 200, 201, 203, 206, 220, 225, 239, 240, 243, 244, 246, 247, 248, 250, 251, 252, 253, 255, 257, 258, 259, 260, 261, 264, 265, 266, 267, 270, 271, 272, 273, 274, 275, 278, 279, 280, 281, 282, 283, 286, 287, 288, 289, 291, 292, 297, 298, 299, 300, 302, 305, 306, 308, 317, 318, 319, 321, 322, 323], "excluded_lines": [327, 328, 329]}}}, "aretomo3_gui/core/memory_manager.py": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 241, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 241, "excluded_lines": 0}, "missing_lines": [7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 19, 21, 24, 25, 28, 29, 30, 31, 32, 33, 34, 37, 38, 41, 42, 43, 44, 45, 48, 51, 53, 54, 55, 56, 57, 59, 63, 65, 66, 68, 69, 70, 71, 73, 75, 77, 80, 81, 82, 85, 88, 89, 91, 93, 96, 102, 103, 104, 106, 108, 109, 110, 112, 114, 115, 123, 126, 128, 129, 130, 131, 132, 135, 136, 138, 140, 141, 143, 144, 147, 149, 151, 153, 154, 155, 157, 159, 160, 161, 162, 163, 164, 165, 166, 168, 170, 172, 173, 174, 177, 178, 179, 181, 191, 194, 195, 197, 198, 200, 202, 203, 205, 208, 209, 210, 212, 213, 216, 218, 220, 222, 223, 225, 226, 227, 230, 231, 233, 234, 236, 238, 239, 241, 244, 245, 246, 247, 250, 252, 253, 255, 265, 268, 270, 279, 280, 283, 284, 285, 288, 290, 292, 294, 295, 297, 298, 301, 304, 306, 308, 310, 311, 312, 314, 315, 317, 319, 320, 321, 322, 323, 324, 325, 326, 328, 330, 331, 332, 334, 335, 336, 337, 338, 339, 340, 342, 343, 345, 347, 350, 353, 354, 357, 358, 359, 360, 361, 363, 365, 368, 371, 372, 375, 377, 379, 380, 381, 383, 386, 388, 389, 390, 391, 392, 393, 395, 396, 398, 400, 402, 404, 405, 406, 407, 409, 428, 430, 431, 432, 434, 436, 438, 440, 444, 447, 449, 452, 454, 457, 459], "excluded_lines": [], "functions": {"MemoryAwareCache.__init__": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 6, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 6, "excluded_lines": 0}, "missing_lines": [53, 54, 55, 56, 57, 59], "excluded_lines": []}, "MemoryAwareCache.get": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 6, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 6, "excluded_lines": 0}, "missing_lines": [65, 66, 68, 69, 70, 71], "excluded_lines": []}, "MemoryAwareCache.put": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 9, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 9, "excluded_lines": 0}, "missing_lines": [75, 77, 80, 81, 82, 85, 88, 89, 91], "excluded_lines": []}, "MemoryAwareCache._evict_if_needed": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 4, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 4, "excluded_lines": 0}, "missing_lines": [96, 102, 103, 104], "excluded_lines": []}, "MemoryAwareCache.clear": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 3, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 3, "excluded_lines": 0}, "missing_lines": [108, 109, 110], "excluded_lines": []}, "MemoryAwareCache.get_stats": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 2, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 2, "excluded_lines": 0}, "missing_lines": [114, 115], "excluded_lines": []}, "MemoryLeakDetector.__init__": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 7, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 7, "excluded_lines": 0}, "missing_lines": [128, 129, 130, 131, 132, 135, 136], "excluded_lines": []}, "MemoryLeakDetector.start_monitoring": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 6, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 6, "excluded_lines": 0}, "missing_lines": [140, 141, 143, 144, 147, 149], "excluded_lines": []}, "MemoryLeakDetector.stop_monitoring": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 3, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 3, "excluded_lines": 0}, "missing_lines": [153, 154, 155], "excluded_lines": []}, "MemoryLeakDetector._monitoring_loop": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 8, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 8, "excluded_lines": 0}, "missing_lines": [159, 160, 161, 162, 163, 164, 165, 166], "excluded_lines": []}, "MemoryLeakDetector._take_snapshot": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 13, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 13, "excluded_lines": 0}, "missing_lines": [170, 172, 173, 174, 177, 178, 179, 181, 191, 194, 195, 197, 198], "excluded_lines": []}, "MemoryLeakDetector._analyze_trends": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 9, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 9, "excluded_lines": 0}, "missing_lines": [202, 203, 205, 208, 209, 210, 212, 213, 216], "excluded_lines": []}, "MemoryLeakDetector._trigger_detailed_analysis": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 10, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 10, "excluded_lines": 0}, "missing_lines": [220, 222, 223, 225, 226, 227, 230, 231, 233, 234], "excluded_lines": []}, "MemoryLeakDetector.get_leak_report": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 11, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 11, "excluded_lines": 0}, "missing_lines": [238, 239, 241, 244, 245, 246, 247, 250, 252, 253, 255], "excluded_lines": []}, "MemoryManager.__init__": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 8, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 8, "excluded_lines": 0}, "missing_lines": [270, 279, 280, 283, 284, 285, 288, 290], "excluded_lines": []}, "MemoryManager.start_monitoring": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 7, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 7, "excluded_lines": 0}, "missing_lines": [294, 295, 297, 298, 301, 304, 306], "excluded_lines": []}, "MemoryManager.stop_monitoring": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 5, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 5, "excluded_lines": 0}, "missing_lines": [310, 311, 312, 314, 315], "excluded_lines": []}, "MemoryManager._monitoring_loop": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 8, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 8, "excluded_lines": 0}, "missing_lines": [319, 320, 321, 322, 323, 324, 325, 326], "excluded_lines": []}, "MemoryManager._check_memory_pressure": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 12, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 12, "excluded_lines": 0}, "missing_lines": [330, 331, 332, 334, 335, 336, 337, 338, 339, 340, 342, 343], "excluded_lines": []}, "MemoryManager._trigger_cleanup": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 9, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 9, "excluded_lines": 0}, "missing_lines": [347, 350, 353, 354, 357, 358, 359, 360, 361], "excluded_lines": []}, "MemoryManager._trigger_emergency_cleanup": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 5, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 5, "excluded_lines": 0}, "missing_lines": [365, 368, 371, 372, 375], "excluded_lines": []}, "MemoryManager._cleanup_dead_references": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 3, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 3, "excluded_lines": 0}, "missing_lines": [379, 380, 381], "excluded_lines": []}, "MemoryManager.track_object": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 3, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 3, "excluded_lines": 0}, "missing_lines": [386, 395, 396], "excluded_lines": []}, "MemoryManager.track_object.cleanup_wrapper": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 6, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 6, "excluded_lines": 0}, "missing_lines": [388, 389, 390, 391, 392, 393], "excluded_lines": []}, "MemoryManager.register_cleanup_callback": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [400], "excluded_lines": []}, "MemoryManager.get_memory_stats": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 9, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 9, "excluded_lines": 0}, "missing_lines": [404, 405, 406, 407, 409, 428, 430, 431, 432], "excluded_lines": []}, "MemoryManager.force_cleanup": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [436], "excluded_lines": []}, "MemoryManager.get_cache": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [440], "excluded_lines": []}, "get_memory_manager": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [449], "excluded_lines": []}, "track_object": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [454], "excluded_lines": []}, "register_cleanup_callback": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [459], "excluded_lines": []}, "": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 63, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 63, "excluded_lines": 0}, "missing_lines": [7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 19, 21, 24, 25, 28, 29, 30, 31, 32, 33, 34, 37, 38, 41, 42, 43, 44, 45, 48, 51, 63, 73, 93, 106, 112, 123, 126, 138, 151, 157, 168, 200, 218, 236, 265, 268, 292, 308, 317, 328, 345, 363, 377, 383, 398, 402, 434, 438, 444, 447, 452, 457], "excluded_lines": []}}, "classes": {"MemorySnapshot": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "ObjectTracker": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "MemoryAwareCache": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 30, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 30, "excluded_lines": 0}, "missing_lines": [53, 54, 55, 56, 57, 59, 65, 66, 68, 69, 70, 71, 75, 77, 80, 81, 82, 85, 88, 89, 91, 96, 102, 103, 104, 108, 109, 110, 114, 115], "excluded_lines": []}, "MemoryLeakDetector": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 67, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 67, "excluded_lines": 0}, "missing_lines": [128, 129, 130, 131, 132, 135, 136, 140, 141, 143, 144, 147, 149, 153, 154, 155, 159, 160, 161, 162, 163, 164, 165, 166, 170, 172, 173, 174, 177, 178, 179, 181, 191, 194, 195, 197, 198, 202, 203, 205, 208, 209, 210, 212, 213, 216, 220, 222, 223, 225, 226, 227, 230, 231, 233, 234, 238, 239, 241, 244, 245, 246, 247, 250, 252, 253, 255], "excluded_lines": []}, "MemoryManager": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 78, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 78, "excluded_lines": 0}, "missing_lines": [270, 279, 280, 283, 284, 285, 288, 290, 294, 295, 297, 298, 301, 304, 306, 310, 311, 312, 314, 315, 319, 320, 321, 322, 323, 324, 325, 326, 330, 331, 332, 334, 335, 336, 337, 338, 339, 340, 342, 343, 347, 350, 353, 354, 357, 358, 359, 360, 361, 365, 368, 371, 372, 375, 379, 380, 381, 386, 388, 389, 390, 391, 392, 393, 395, 396, 400, 404, 405, 406, 407, 409, 428, 430, 431, 432, 436, 440], "excluded_lines": []}, "": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 66, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 66, "excluded_lines": 0}, "missing_lines": [7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 19, 21, 24, 25, 28, 29, 30, 31, 32, 33, 34, 37, 38, 41, 42, 43, 44, 45, 48, 51, 63, 73, 93, 106, 112, 123, 126, 138, 151, 157, 168, 200, 218, 236, 265, 268, 292, 308, 317, 328, 345, 363, 377, 383, 398, 402, 434, 438, 444, 447, 449, 452, 454, 457, 459], "excluded_lines": []}}}, "aretomo3_gui/core/multi_format_handler.py": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 192, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 192, "excluded_lines": 0}, "missing_lines": [7, 8, 9, 10, 11, 12, 14, 17, 20, 21, 22, 23, 24, 25, 26, 29, 30, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 45, 51, 53, 64, 70, 72, 85, 87, 88, 89, 92, 94, 99, 103, 104, 106, 108, 109, 110, 112, 113, 114, 115, 118, 120, 124, 126, 127, 128, 130, 131, 132, 133, 135, 137, 141, 144, 146, 147, 150, 151, 153, 157, 162, 163, 164, 166, 167, 170, 173, 174, 175, 176, 178, 179, 181, 182, 183, 184, 185, 186, 187, 188, 190, 191, 194, 197, 199, 212, 216, 219, 220, 221, 223, 225, 227, 228, 229, 231, 233, 235, 238, 245, 246, 247, 248, 251, 253, 258, 259, 260, 261, 263, 264, 266, 267, 268, 269, 270, 271, 272, 273, 275, 276, 279, 282, 284, 297, 301, 303, 305, 307, 309, 310, 311, 312, 314, 316, 318, 320, 321, 322, 325, 326, 328, 329, 331, 333, 336, 343, 344, 345, 346, 347, 348, 349, 351, 353, 355, 357, 361, 372, 373, 374, 376, 378, 380, 387, 390, 391, 393, 395, 396, 399, 400, 403, 404, 407, 408, 409, 412, 413, 414, 415, 417], "excluded_lines": [], "functions": {"MultiFormatHandler.__init__": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 3, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 3, "excluded_lines": 0}, "missing_lines": [53, 64, 70], "excluded_lines": []}, "MultiFormatHandler.scan_input_directory": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 9, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 9, "excluded_lines": 0}, "missing_lines": [85, 87, 88, 89, 92, 94, 99, 103, 104], "excluded_lines": []}, "MultiFormatHandler._has_subdirectories_with_data": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 8, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 8, "excluded_lines": 0}, "missing_lines": [108, 109, 110, 112, 113, 114, 115, 118], "excluded_lines": []}, "MultiFormatHandler._scan_subdirectory_organization": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 9, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 9, "excluded_lines": 0}, "missing_lines": [124, 126, 127, 128, 130, 131, 132, 133, 135], "excluded_lines": []}, "MultiFormatHandler._scan_single_directory_organization": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 7, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 7, "excluded_lines": 0}, "missing_lines": [141, 144, 146, 147, 150, 151, 153], "excluded_lines": []}, "MultiFormatHandler._analyze_directory_for_series": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 25, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 25, "excluded_lines": 0}, "missing_lines": [162, 163, 164, 166, 167, 170, 173, 174, 175, 176, 178, 179, 181, 182, 183, 184, 185, 186, 187, 188, 190, 191, 194, 197, 199], "excluded_lines": []}, "MultiFormatHandler._group_files_by_series": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 10, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 10, "excluded_lines": 0}, "missing_lines": [216, 219, 220, 221, 223, 225, 227, 228, 229, 231], "excluded_lines": []}, "MultiFormatHandler._extract_series_name": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 7, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 7, "excluded_lines": 0}, "missing_lines": [235, 238, 245, 246, 247, 248, 251], "excluded_lines": []}, "MultiFormatHandler._create_series_info_from_files": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 19, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 19, "excluded_lines": 0}, "missing_lines": [258, 259, 260, 261, 263, 264, 266, 267, 268, 269, 270, 271, 272, 273, 275, 276, 279, 282, 284], "excluded_lines": []}, "MultiFormatHandler._extract_tilt_angles": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 9, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 9, "excluded_lines": 0}, "missing_lines": [301, 303, 305, 307, 309, 310, 311, 312, 314], "excluded_lines": []}, "MultiFormatHandler._parse_mdoc_tilt_angles": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 9, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 9, "excluded_lines": 0}, "missing_lines": [318, 320, 321, 322, 325, 326, 328, 329, 331], "excluded_lines": []}, "MultiFormatHandler._extract_angle_from_filename": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 9, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 9, "excluded_lines": 0}, "missing_lines": [336, 343, 344, 345, 346, 347, 348, 349, 351], "excluded_lines": []}, "MultiFormatHandler.get_format_parameters": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [355], "excluded_lines": []}, "MultiFormatHandler.validate_series_completeness": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 6, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 6, "excluded_lines": 0}, "missing_lines": [361, 372, 373, 374, 376, 378], "excluded_lines": []}, "MultiFormatHandler.generate_aretomo3_command": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 18, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 18, "excluded_lines": 0}, "missing_lines": [387, 390, 391, 393, 395, 396, 399, 400, 403, 404, 407, 408, 409, 412, 413, 414, 415, 417], "excluded_lines": []}, "": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 43, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 43, "excluded_lines": 0}, "missing_lines": [7, 8, 9, 10, 11, 12, 14, 17, 20, 21, 22, 23, 24, 25, 26, 29, 30, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 45, 51, 72, 106, 120, 137, 157, 212, 233, 253, 297, 316, 333, 353, 357, 380], "excluded_lines": []}}, "classes": {"InputFormat": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "TiltSeriesInfo": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "MultiFormatHandler": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 149, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 149, "excluded_lines": 0}, "missing_lines": [53, 64, 70, 85, 87, 88, 89, 92, 94, 99, 103, 104, 108, 109, 110, 112, 113, 114, 115, 118, 124, 126, 127, 128, 130, 131, 132, 133, 135, 141, 144, 146, 147, 150, 151, 153, 162, 163, 164, 166, 167, 170, 173, 174, 175, 176, 178, 179, 181, 182, 183, 184, 185, 186, 187, 188, 190, 191, 194, 197, 199, 216, 219, 220, 221, 223, 225, 227, 228, 229, 231, 235, 238, 245, 246, 247, 248, 251, 258, 259, 260, 261, 263, 264, 266, 267, 268, 269, 270, 271, 272, 273, 275, 276, 279, 282, 284, 301, 303, 305, 307, 309, 310, 311, 312, 314, 318, 320, 321, 322, 325, 326, 328, 329, 331, 336, 343, 344, 345, 346, 347, 348, 349, 351, 355, 361, 372, 373, 374, 376, 378, 387, 390, 391, 393, 395, 396, 399, 400, 403, 404, 407, 408, 409, 412, 413, 414, 415, 417], "excluded_lines": []}, "": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 43, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 43, "excluded_lines": 0}, "missing_lines": [7, 8, 9, 10, 11, 12, 14, 17, 20, 21, 22, 23, 24, 25, 26, 29, 30, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 45, 51, 72, 106, 120, 137, 157, 212, 233, 253, 297, 316, 333, 353, 357, 380], "excluded_lines": []}}}, "aretomo3_gui/core/performance_monitor.py": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 175, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 175, "excluded_lines": 0}, "missing_lines": [7, 8, 9, 10, 11, 12, 13, 14, 16, 18, 21, 22, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 38, 39, 42, 43, 44, 45, 46, 47, 48, 49, 50, 53, 58, 60, 61, 62, 63, 64, 65, 68, 69, 70, 73, 74, 76, 78, 80, 81, 82, 84, 85, 88, 89, 91, 93, 94, 95, 96, 98, 100, 101, 102, 103, 104, 106, 108, 109, 110, 112, 114, 116, 117, 120, 121, 122, 125, 126, 127, 130, 131, 132, 134, 147, 148, 149, 158, 160, 163, 164, 167, 168, 170, 172, 173, 176, 180, 181, 184, 188, 189, 192, 193, 196, 197, 199, 201, 202, 205, 206, 209, 210, 213, 214, 216, 217, 219, 221, 222, 225, 226, 229, 231, 232, 234, 236, 237, 239, 241, 242, 244, 245, 248, 250, 251, 253, 255, 257, 258, 260, 261, 262, 264, 266, 268, 270, 271, 273, 274, 275, 277, 279, 281, 282, 284, 286, 287, 289, 308, 310, 312, 314, 315, 316, 317, 319, 321, 323, 325, 326, 327, 328, 332], "excluded_lines": [], "functions": {"PerformanceMonitor.__init__": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 12, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 12, "excluded_lines": 0}, "missing_lines": [60, 61, 62, 63, 64, 65, 68, 69, 70, 73, 74, 76], "excluded_lines": []}, "PerformanceMonitor.start_monitoring": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 7, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 7, "excluded_lines": 0}, "missing_lines": [80, 81, 82, 84, 85, 88, 89], "excluded_lines": []}, "PerformanceMonitor.stop_monitoring": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 4, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 4, "excluded_lines": 0}, "missing_lines": [93, 94, 95, 96], "excluded_lines": []}, "PerformanceMonitor._monitoring_loop": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 9, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 9, "excluded_lines": 0}, "missing_lines": [100, 101, 102, 103, 104, 106, 108, 109, 110], "excluded_lines": []}, "PerformanceMonitor._collect_metrics": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 16, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 16, "excluded_lines": 0}, "missing_lines": [114, 116, 117, 120, 121, 122, 125, 126, 127, 130, 131, 132, 134, 147, 148, 149], "excluded_lines": []}, "PerformanceMonitor._store_metrics": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 5, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 5, "excluded_lines": 0}, "missing_lines": [160, 163, 164, 167, 168], "excluded_lines": []}, "PerformanceMonitor._check_optimization_triggers": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 12, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 12, "excluded_lines": 0}, "missing_lines": [172, 173, 176, 180, 181, 184, 188, 189, 192, 193, 196, 197], "excluded_lines": []}, "PerformanceMonitor._optimize_memory": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 10, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 10, "excluded_lines": 0}, "missing_lines": [201, 202, 205, 206, 209, 210, 213, 214, 216, 217], "excluded_lines": []}, "PerformanceMonitor._optimize_cpu": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 8, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 8, "excluded_lines": 0}, "missing_lines": [221, 222, 225, 226, 229, 234, 236, 237], "excluded_lines": []}, "PerformanceMonitor._optimize_cpu.reset_interval": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 2, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 2, "excluded_lines": 0}, "missing_lines": [231, 232], "excluded_lines": []}, "PerformanceMonitor._cleanup_caches": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 7, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 7, "excluded_lines": 0}, "missing_lines": [241, 242, 244, 245, 248, 250, 251], "excluded_lines": []}, "PerformanceMonitor._cleanup_plot_cache": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 7, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 7, "excluded_lines": 0}, "missing_lines": [255, 257, 258, 260, 261, 262, 264], "excluded_lines": []}, "PerformanceMonitor._cleanup_data_cache": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 7, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 7, "excluded_lines": 0}, "missing_lines": [268, 270, 271, 273, 274, 275, 277], "excluded_lines": []}, "PerformanceMonitor.get_performance_summary": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 6, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 6, "excluded_lines": 0}, "missing_lines": [281, 282, 284, 286, 287, 289], "excluded_lines": []}, "PerformanceMonitor.cache_plot": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [310], "excluded_lines": []}, "PerformanceMonitor.get_cached_plot": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 4, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 4, "excluded_lines": 0}, "missing_lines": [314, 315, 316, 317], "excluded_lines": []}, "PerformanceMonitor.cache_data": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [321], "excluded_lines": []}, "PerformanceMonitor.get_cached_data": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 4, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 4, "excluded_lines": 0}, "missing_lines": [325, 326, 327, 328], "excluded_lines": []}, "": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 53, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 53, "excluded_lines": 0}, "missing_lines": [7, 8, 9, 10, 11, 12, 13, 14, 16, 18, 21, 22, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 38, 39, 42, 43, 44, 45, 46, 47, 48, 49, 50, 53, 58, 78, 91, 98, 112, 158, 170, 199, 219, 239, 253, 266, 279, 308, 312, 319, 323, 332], "excluded_lines": []}}, "classes": {"PerformanceMetrics": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "OptimizationSettings": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "PerformanceMonitor": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 122, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 122, "excluded_lines": 0}, "missing_lines": [60, 61, 62, 63, 64, 65, 68, 69, 70, 73, 74, 76, 80, 81, 82, 84, 85, 88, 89, 93, 94, 95, 96, 100, 101, 102, 103, 104, 106, 108, 109, 110, 114, 116, 117, 120, 121, 122, 125, 126, 127, 130, 131, 132, 134, 147, 148, 149, 160, 163, 164, 167, 168, 172, 173, 176, 180, 181, 184, 188, 189, 192, 193, 196, 197, 201, 202, 205, 206, 209, 210, 213, 214, 216, 217, 221, 222, 225, 226, 229, 231, 232, 234, 236, 237, 241, 242, 244, 245, 248, 250, 251, 255, 257, 258, 260, 261, 262, 264, 268, 270, 271, 273, 274, 275, 277, 281, 282, 284, 286, 287, 289, 310, 314, 315, 316, 317, 321, 325, 326, 327, 328], "excluded_lines": []}, "": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 53, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 53, "excluded_lines": 0}, "missing_lines": [7, 8, 9, 10, 11, 12, 13, 14, 16, 18, 21, 22, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 38, 39, 42, 43, 44, 45, 46, 47, 48, 49, 50, 53, 58, 78, 91, 98, 112, 158, 170, 199, 219, 239, 253, 266, 279, 308, 312, 319, 323, 332], "excluded_lines": []}}}, "aretomo3_gui/core/performance_optimizer.py": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 261, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 261, "excluded_lines": 0}, "missing_lines": [7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 19, 20, 22, 25, 26, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 43, 44, 47, 48, 49, 50, 51, 52, 53, 56, 62, 64, 65, 66, 69, 70, 73, 74, 77, 91, 99, 101, 105, 109, 120, 132, 143, 154, 164, 166, 167, 168, 170, 171, 174, 176, 178, 180, 181, 182, 184, 186, 188, 189, 191, 192, 193, 196, 199, 201, 203, 204, 205, 207, 209, 211, 212, 213, 214, 217, 218, 219, 222, 224, 239, 241, 242, 243, 254, 256, 257, 259, 260, 261, 262, 263, 264, 265, 266, 268, 270, 272, 274, 275, 276, 279, 280, 281, 282, 285, 287, 288, 289, 291, 293, 294, 295, 297, 298, 299, 300, 301, 303, 304, 306, 307, 309, 311, 312, 314, 315, 318, 320, 321, 323, 325, 330, 331, 333, 335, 336, 339, 340, 343, 346, 348, 350, 352, 353, 355, 357, 358, 361, 362, 363, 364, 365, 366, 367, 370, 372, 373, 375, 377, 378, 380, 381, 382, 384, 385, 387, 389, 391, 392, 394, 395, 396, 399, 401, 402, 403, 406, 407, 409, 410, 411, 413, 415, 416, 418, 420, 421, 423, 424, 428, 430, 431, 433, 435, 436, 440, 442, 443, 444, 445, 446, 448, 450, 467, 469, 470, 472, 474, 477, 479, 481, 482, 484, 486, 487, 488, 490, 491, 493, 494, 496, 498, 499, 503, 506, 509, 511, 513, 514, 517, 518, 519, 521, 523, 524, 525, 527, 530, 533, 536, 538, 539, 542, 544, 546, 548, 549, 551], "excluded_lines": [], "functions": {"PerformanceOptimizer.__init__": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 11, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 11, "excluded_lines": 0}, "missing_lines": [64, 65, 66, 69, 70, 73, 74, 77, 91, 99, 101], "excluded_lines": []}, "PerformanceOptimizer._setup_optimization_rules": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 5, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 5, "excluded_lines": 0}, "missing_lines": [109, 120, 132, 143, 154], "excluded_lines": []}, "PerformanceOptimizer.start_monitoring": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 7, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 7, "excluded_lines": 0}, "missing_lines": [166, 167, 168, 170, 171, 174, 176], "excluded_lines": []}, "PerformanceOptimizer.stop_monitoring": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 4, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 4, "excluded_lines": 0}, "missing_lines": [180, 181, 182, 184], "excluded_lines": []}, "PerformanceOptimizer._monitoring_loop": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 11, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 11, "excluded_lines": 0}, "missing_lines": [188, 189, 191, 192, 193, 196, 199, 201, 203, 204, 205], "excluded_lines": []}, "PerformanceOptimizer._collect_metrics": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 14, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 14, "excluded_lines": 0}, "missing_lines": [209, 211, 212, 213, 214, 217, 218, 219, 222, 224, 239, 241, 242, 243], "excluded_lines": []}, "PerformanceOptimizer._get_gpu_metrics": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 11, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 11, "excluded_lines": 0}, "missing_lines": [256, 257, 259, 260, 261, 262, 263, 264, 265, 266, 268], "excluded_lines": []}, "PerformanceOptimizer._check_optimization_rules": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 12, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 12, "excluded_lines": 0}, "missing_lines": [272, 274, 275, 276, 279, 280, 281, 282, 285, 287, 288, 289], "excluded_lines": []}, "PerformanceOptimizer._process_optimization_queue": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 12, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 12, "excluded_lines": 0}, "missing_lines": [293, 294, 295, 297, 298, 299, 300, 301, 303, 304, 306, 307], "excluded_lines": []}, "PerformanceOptimizer._trigger_garbage_collection": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 11, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 11, "excluded_lines": 0}, "missing_lines": [311, 312, 314, 315, 318, 320, 321, 323, 325, 330, 331], "excluded_lines": []}, "PerformanceOptimizer._aggressive_memory_cleanup": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 10, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 10, "excluded_lines": 0}, "missing_lines": [335, 336, 339, 340, 343, 346, 348, 350, 352, 353], "excluded_lines": []}, "PerformanceOptimizer._optimize_cpu_usage": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 12, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 12, "excluded_lines": 0}, "missing_lines": [357, 358, 361, 362, 363, 364, 365, 366, 367, 370, 372, 373], "excluded_lines": []}, "PerformanceOptimizer._optimize_cache": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 7, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 7, "excluded_lines": 0}, "missing_lines": [377, 378, 380, 381, 382, 384, 385], "excluded_lines": []}, "PerformanceOptimizer._clear_caches": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 18, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 18, "excluded_lines": 0}, "missing_lines": [389, 391, 392, 394, 395, 396, 399, 401, 402, 403, 406, 407, 409, 410, 411, 413, 415, 416], "excluded_lines": []}, "PerformanceOptimizer._optimize_threads": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 7, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 7, "excluded_lines": 0}, "missing_lines": [420, 421, 423, 424, 428, 430, 431], "excluded_lines": []}, "PerformanceOptimizer.get_performance_summary": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 10, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 10, "excluded_lines": 0}, "missing_lines": [435, 436, 440, 442, 443, 444, 445, 446, 448, 450], "excluded_lines": []}, "PerformanceOptimizer.add_optimization_rule": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 2, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 2, "excluded_lines": 0}, "missing_lines": [469, 470], "excluded_lines": []}, "PerformanceOptimizer.remove_optimization_rule": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 2, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 2, "excluded_lines": 0}, "missing_lines": [474, 477], "excluded_lines": []}, "PerformanceOptimizer.update_config": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 2, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 2, "excluded_lines": 0}, "missing_lines": [481, 482], "excluded_lines": []}, "PerformanceOptimizer.force_optimization": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 10, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 10, "excluded_lines": 0}, "missing_lines": [486, 487, 488, 490, 491, 493, 494, 496, 498, 499], "excluded_lines": []}, "optimize_performance": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 2, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 2, "excluded_lines": 0}, "missing_lines": [509, 527], "excluded_lines": []}, "optimize_performance.wrapper": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 10, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 10, "excluded_lines": 0}, "missing_lines": [511, 513, 514, 517, 518, 519, 521, 523, 524, 525], "excluded_lines": []}, "memory_efficient": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 2, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 2, "excluded_lines": 0}, "missing_lines": [533, 551], "excluded_lines": []}, "memory_efficient.wrapper": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 8, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 8, "excluded_lines": 0}, "missing_lines": [536, 538, 539, 542, 544, 546, 548, 549], "excluded_lines": []}, "": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 61, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 61, "excluded_lines": 0}, "missing_lines": [7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 19, 20, 22, 25, 26, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 43, 44, 47, 48, 49, 50, 51, 52, 53, 56, 62, 105, 164, 178, 186, 207, 254, 270, 291, 309, 333, 355, 375, 387, 418, 433, 467, 472, 479, 484, 503, 506, 530], "excluded_lines": []}}, "classes": {"PerformanceMetrics": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "OptimizationRule": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "PerformanceOptimizer": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 178, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 178, "excluded_lines": 0}, "missing_lines": [64, 65, 66, 69, 70, 73, 74, 77, 91, 99, 101, 109, 120, 132, 143, 154, 166, 167, 168, 170, 171, 174, 176, 180, 181, 182, 184, 188, 189, 191, 192, 193, 196, 199, 201, 203, 204, 205, 209, 211, 212, 213, 214, 217, 218, 219, 222, 224, 239, 241, 242, 243, 256, 257, 259, 260, 261, 262, 263, 264, 265, 266, 268, 272, 274, 275, 276, 279, 280, 281, 282, 285, 287, 288, 289, 293, 294, 295, 297, 298, 299, 300, 301, 303, 304, 306, 307, 311, 312, 314, 315, 318, 320, 321, 323, 325, 330, 331, 335, 336, 339, 340, 343, 346, 348, 350, 352, 353, 357, 358, 361, 362, 363, 364, 365, 366, 367, 370, 372, 373, 377, 378, 380, 381, 382, 384, 385, 389, 391, 392, 394, 395, 396, 399, 401, 402, 403, 406, 407, 409, 410, 411, 413, 415, 416, 420, 421, 423, 424, 428, 430, 431, 435, 436, 440, 442, 443, 444, 445, 446, 448, 450, 469, 470, 474, 477, 481, 482, 486, 487, 488, 490, 491, 493, 494, 496, 498, 499], "excluded_lines": []}, "": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 83, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 83, "excluded_lines": 0}, "missing_lines": [7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 19, 20, 22, 25, 26, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 43, 44, 47, 48, 49, 50, 51, 52, 53, 56, 62, 105, 164, 178, 186, 207, 254, 270, 291, 309, 333, 355, 375, 387, 418, 433, 467, 472, 479, 484, 503, 506, 509, 511, 513, 514, 517, 518, 519, 521, 523, 524, 525, 527, 530, 533, 536, 538, 539, 542, 544, 546, 548, 549, 551], "excluded_lines": []}}}, "aretomo3_gui/core/plugin_system.py": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 228, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 228, "excluded_lines": 78}, "missing_lines": [7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 19, 22, 25, 26, 27, 28, 29, 30, 31, 32, 35, 36, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 51, 54, 56, 57, 58, 59, 60, 105, 112, 114, 121, 123, 130, 133, 151, 169, 187, 192, 194, 195, 196, 197, 198, 201, 210, 215, 217, 219, 221, 223, 225, 227, 228, 229, 231, 234, 235, 236, 237, 239, 240, 241, 242, 243, 247, 248, 250, 252, 254, 255, 256, 259, 267, 268, 269, 272, 275, 289, 290, 292, 294, 295, 296, 299, 309, 310, 311, 313, 314, 315, 316, 318, 319, 320, 322, 324, 325, 326, 329, 330, 331, 334, 335, 338, 339, 340, 341, 344, 347, 348, 349, 350, 353, 354, 356, 357, 359, 360, 361, 363, 365, 366, 368, 369, 370, 372, 376, 382, 384, 385, 390, 392, 394, 404, 405, 406, 408, 410, 411, 412, 414, 415, 416, 417, 418, 421, 422, 424, 425, 427, 428, 429, 431, 441, 442, 443, 445, 447, 448, 449, 450, 451, 454, 455, 457, 458, 460, 461, 462, 464, 475, 476, 477, 479, 480, 481, 483, 485, 486, 487, 488, 489, 492, 493, 495, 496, 498, 499, 500, 502, 504, 506, 510, 511, 517, 519, 520, 521, 523, 525, 526, 527, 528, 529, 530, 532, 534, 557, 559, 562, 563, 566, 567, 569, 573], "excluded_lines": [62, 63, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 75, 76, 77, 78, 79, 80, 81, 82, 83, 85, 86, 87, 88, 89, 90, 91, 92, 93, 95, 96, 97, 98, 99, 100, 101, 102, 103, 136, 137, 138, 139, 140, 141, 142, 143, 144, 145, 146, 147, 148, 154, 155, 156, 157, 158, 159, 160, 161, 162, 163, 164, 165, 166, 172, 173, 174, 175, 176, 177, 178, 179, 180, 181, 182, 183, 184], "functions": {"PluginInterface.__init__": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 5, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 5, "excluded_lines": 0}, "missing_lines": [56, 57, 58, 59, 60], "excluded_lines": []}, "PluginInterface.initialize": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 10}, "missing_lines": [], "excluded_lines": [64, 65, 66, 67, 68, 69, 70, 71, 72, 73]}, "PluginInterface.activate": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 7}, "missing_lines": [], "excluded_lines": [77, 78, 79, 80, 81, 82, 83]}, "PluginInterface.deactivate": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 7}, "missing_lines": [], "excluded_lines": [87, 88, 89, 90, 91, 92, 93]}, "PluginInterface.cleanup": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 7}, "missing_lines": [], "excluded_lines": [97, 98, 99, 100, 101, 102, 103]}, "PluginInterface.get_menu_items": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [112], "excluded_lines": []}, "PluginInterface.get_toolbar_items": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [121], "excluded_lines": []}, "PluginInterface.get_config_widget": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [130], "excluded_lines": []}, "AnalysisPlugin.analyze_data": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 11}, "missing_lines": [], "excluded_lines": [138, 139, 140, 141, 142, 143, 144, 145, 146, 147, 148]}, "VisualizationPlugin.create_visualization": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 11}, "missing_lines": [], "excluded_lines": [156, 157, 158, 159, 160, 161, 162, 163, 164, 165, 166]}, "ProcessingPlugin.process_data": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 11}, "missing_lines": [], "excluded_lines": [174, 175, 176, 177, 178, 179, 180, 181, 182, 183, 184]}, "PluginManager.__init__": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 9, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 9, "excluded_lines": 0}, "missing_lines": [194, 195, 196, 197, 198, 201, 210, 215, 217], "excluded_lines": []}, "PluginManager.set_gui_reference": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [221], "excluded_lines": []}, "PluginManager.discover_plugins": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 17, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 17, "excluded_lines": 0}, "missing_lines": [225, 227, 228, 229, 231, 234, 235, 236, 237, 239, 240, 241, 242, 243, 247, 248, 250], "excluded_lines": []}, "PluginManager._load_plugin_metadata": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 15, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 15, "excluded_lines": 0}, "missing_lines": [254, 255, 256, 259, 267, 268, 269, 272, 275, 289, 290, 292, 294, 295, 296], "excluded_lines": []}, "PluginManager.load_plugin": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 35, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 35, "excluded_lines": 0}, "missing_lines": [309, 310, 311, 313, 314, 315, 316, 318, 319, 320, 322, 324, 325, 326, 329, 330, 331, 334, 335, 338, 339, 340, 341, 344, 347, 348, 349, 350, 353, 354, 356, 357, 359, 360, 361], "excluded_lines": []}, "PluginManager._check_dependencies": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 5, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 5, "excluded_lines": 0}, "missing_lines": [365, 366, 368, 369, 370], "excluded_lines": []}, "PluginManager._find_plugin_class": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 6, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 6, "excluded_lines": 0}, "missing_lines": [376, 382, 384, 385, 390, 392], "excluded_lines": []}, "PluginManager.activate_plugin": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 19, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 19, "excluded_lines": 0}, "missing_lines": [404, 405, 406, 408, 410, 411, 412, 414, 415, 416, 417, 418, 421, 422, 424, 425, 427, 428, 429], "excluded_lines": []}, "PluginManager.deactivate_plugin": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 16, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 16, "excluded_lines": 0}, "missing_lines": [441, 442, 443, 445, 447, 448, 449, 450, 451, 454, 455, 457, 458, 460, 461, 462], "excluded_lines": []}, "PluginManager.unload_plugin": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 19, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 19, "excluded_lines": 0}, "missing_lines": [475, 476, 477, 479, 480, 481, 483, 485, 486, 487, 488, 489, 492, 493, 495, 496, 498, 499, 500], "excluded_lines": []}, "PluginManager.get_plugins_by_type": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [504], "excluded_lines": []}, "PluginManager.get_active_plugins_by_type": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 2, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 2, "excluded_lines": 0}, "missing_lines": [510, 511], "excluded_lines": []}, "PluginManager.register_event_hook": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 3, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 3, "excluded_lines": 0}, "missing_lines": [519, 520, 521], "excluded_lines": []}, "PluginManager._trigger_event": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 6, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 6, "excluded_lines": 0}, "missing_lines": [525, 526, 527, 528, 529, 530], "excluded_lines": []}, "PluginManager.get_plugin_status": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [534], "excluded_lines": []}, "PluginManager.shutdown": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 6, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 6, "excluded_lines": 0}, "missing_lines": [559, 562, 563, 566, 567, 569], "excluded_lines": []}, "": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 59, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 59, "excluded_lines": 14}, "missing_lines": [7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 19, 22, 25, 26, 27, 28, 29, 30, 31, 32, 35, 36, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 51, 54, 105, 114, 123, 133, 151, 169, 187, 192, 219, 223, 252, 299, 363, 372, 394, 431, 464, 502, 506, 517, 523, 532, 557, 573], "excluded_lines": [62, 63, 75, 76, 85, 86, 95, 96, 136, 137, 154, 155, 172, 173]}}, "classes": {"PluginType": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "PluginMetadata": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "PluginInterface": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 8, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 8, "excluded_lines": 31}, "missing_lines": [56, 57, 58, 59, 60, 112, 121, 130], "excluded_lines": [64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 77, 78, 79, 80, 81, 82, 83, 87, 88, 89, 90, 91, 92, 93, 97, 98, 99, 100, 101, 102, 103]}, "AnalysisPlugin": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 11}, "missing_lines": [], "excluded_lines": [138, 139, 140, 141, 142, 143, 144, 145, 146, 147, 148]}, "VisualizationPlugin": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 11}, "missing_lines": [], "excluded_lines": [156, 157, 158, 159, 160, 161, 162, 163, 164, 165, 166]}, "ProcessingPlugin": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 11}, "missing_lines": [], "excluded_lines": [174, 175, 176, 177, 178, 179, 180, 181, 182, 183, 184]}, "PluginManager": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 161, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 161, "excluded_lines": 0}, "missing_lines": [194, 195, 196, 197, 198, 201, 210, 215, 217, 221, 225, 227, 228, 229, 231, 234, 235, 236, 237, 239, 240, 241, 242, 243, 247, 248, 250, 254, 255, 256, 259, 267, 268, 269, 272, 275, 289, 290, 292, 294, 295, 296, 309, 310, 311, 313, 314, 315, 316, 318, 319, 320, 322, 324, 325, 326, 329, 330, 331, 334, 335, 338, 339, 340, 341, 344, 347, 348, 349, 350, 353, 354, 356, 357, 359, 360, 361, 365, 366, 368, 369, 370, 376, 382, 384, 385, 390, 392, 404, 405, 406, 408, 410, 411, 412, 414, 415, 416, 417, 418, 421, 422, 424, 425, 427, 428, 429, 441, 442, 443, 445, 447, 448, 449, 450, 451, 454, 455, 457, 458, 460, 461, 462, 475, 476, 477, 479, 480, 481, 483, 485, 486, 487, 488, 489, 492, 493, 495, 496, 498, 499, 500, 504, 510, 511, 519, 520, 521, 525, 526, 527, 528, 529, 530, 534, 559, 562, 563, 566, 567, 569], "excluded_lines": []}, "": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 59, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 59, "excluded_lines": 14}, "missing_lines": [7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 19, 22, 25, 26, 27, 28, 29, 30, 31, 32, 35, 36, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 51, 54, 105, 114, 123, 133, 151, 169, 187, 192, 219, 223, 252, 299, 363, 372, 394, 431, 464, 502, 506, 517, 523, 532, 557, 573], "excluded_lines": [62, 63, 75, 76, 85, 86, 95, 96, 136, 137, 154, 155, 172, 173]}}}, "aretomo3_gui/core/realtime_processor.py": {"executed_lines": [2, 12, 17, 18, 19, 20, 21, 22, 23, 24, 26, 27, 28, 29, 31, 34, 35, 36, 38, 39, 40, 41, 42, 43, 44, 45, 46, 49, 50, 51, 53, 54, 55, 56, 57, 58, 59, 62, 63, 65, 67, 68, 69, 70, 72, 86, 130, 131, 134, 135, 136, 137, 138, 139, 141, 143, 144, 145, 146, 149, 150, 151, 152, 153, 156, 157, 160, 161, 164, 167, 168, 169, 171, 173, 174, 175, 176, 177, 178, 182, 208, 221, 238, 255, 301, 317, 331, 335, 339], "summary": {"covered_lines": 84, "num_statements": 195, "percent_covered": 43.07692307692308, "percent_covered_display": "43", "missing_lines": 111, "excluded_lines": 0}, "missing_lines": [74, 75, 77, 80, 81, 84, 88, 89, 91, 92, 93, 95, 96, 97, 98, 99, 101, 103, 104, 105, 108, 109, 110, 111, 113, 114, 116, 118, 120, 121, 124, 125, 127, 180, 184, 185, 186, 188, 189, 192, 195, 200, 201, 202, 203, 205, 206, 210, 211, 214, 215, 218, 219, 223, 225, 229, 230, 232, 233, 235, 236, 240, 242, 243, 245, 248, 250, 251, 252, 253, 257, 258, 259, 260, 262, 263, 265, 267, 269, 272, 273, 274, 275, 276, 278, 279, 281, 282, 286, 288, 289, 290, 292, 294, 295, 298, 299, 304, 307, 308, 309, 310, 313, 315, 319, 320, 323, 329, 333, 337, 341], "excluded_lines": [], "functions": {"RealTimeFileHandler.__init__": {"executed_lines": [67, 68, 69, 70], "summary": {"covered_lines": 4, "num_statements": 4, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "RealTimeFileHandler.on_created": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 6, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 6, "excluded_lines": 0}, "missing_lines": [74, 75, 77, 80, 81, 84], "excluded_lines": []}, "RealTimeFileHandler._wait_and_queue_file": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 27, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 27, "excluded_lines": 0}, "missing_lines": [88, 89, 91, 92, 93, 95, 96, 97, 98, 99, 101, 103, 104, 105, 108, 109, 110, 111, 113, 114, 116, 118, 120, 121, 124, 125, 127], "excluded_lines": []}, "RealTimeProcessor.__init__": {"executed_lines": [143, 144, 145, 146, 149, 150, 151, 152, 153, 156, 157, 160, 161, 164, 167, 168, 169], "summary": {"covered_lines": 17, "num_statements": 17, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "RealTimeProcessor._setup_file_monitoring": {"executed_lines": [173, 174, 175, 176, 177, 178], "summary": {"covered_lines": 6, "num_statements": 7, "percent_covered": 85.71428571428571, "percent_covered_display": "86", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [180], "excluded_lines": []}, "RealTimeProcessor.start_processing": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 13, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 13, "excluded_lines": 0}, "missing_lines": [184, 185, 186, 188, 189, 192, 195, 200, 201, 202, 203, 205, 206], "excluded_lines": []}, "RealTimeProcessor.stop_processing": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 6, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 6, "excluded_lines": 0}, "missing_lines": [210, 211, 214, 215, 218, 219], "excluded_lines": []}, "RealTimeProcessor.queue_file": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 8, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 8, "excluded_lines": 0}, "missing_lines": [223, 225, 229, 230, 232, 233, 235, 236], "excluded_lines": []}, "RealTimeProcessor._processing_worker": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 9, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 9, "excluded_lines": 0}, "missing_lines": [240, 242, 243, 245, 248, 250, 251, 252, 253], "excluded_lines": []}, "RealTimeProcessor._process_job": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 27, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 27, "excluded_lines": 0}, "missing_lines": [257, 258, 259, 260, 262, 263, 265, 267, 269, 272, 273, 274, 275, 276, 278, 279, 281, 282, 286, 288, 289, 290, 292, 294, 295, 298, 299], "excluded_lines": []}, "RealTimeProcessor._run_aretomo3_processing": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 7, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 7, "excluded_lines": 0}, "missing_lines": [304, 307, 308, 309, 310, 313, 315], "excluded_lines": []}, "RealTimeProcessor._update_stats": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 4, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 4, "excluded_lines": 0}, "missing_lines": [319, 320, 323, 329], "excluded_lines": []}, "RealTimeProcessor.get_job_status": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [333], "excluded_lines": []}, "RealTimeProcessor.get_all_jobs": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [337], "excluded_lines": []}, "RealTimeProcessor.get_stats": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [341], "excluded_lines": []}, "": {"executed_lines": [2, 12, 17, 18, 19, 20, 21, 22, 23, 24, 26, 27, 28, 29, 31, 34, 35, 36, 38, 39, 40, 41, 42, 43, 44, 45, 46, 49, 50, 51, 53, 54, 55, 56, 57, 58, 59, 62, 63, 65, 72, 86, 130, 131, 134, 135, 136, 137, 138, 139, 141, 171, 182, 208, 221, 238, 255, 301, 317, 331, 335, 339], "summary": {"covered_lines": 57, "num_statements": 57, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}}, "classes": {"ProcessingJob": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "ProcessingStats": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "RealTimeFileHandler": {"executed_lines": [67, 68, 69, 70], "summary": {"covered_lines": 4, "num_statements": 37, "percent_covered": 10.81081081081081, "percent_covered_display": "11", "missing_lines": 33, "excluded_lines": 0}, "missing_lines": [74, 75, 77, 80, 81, 84, 88, 89, 91, 92, 93, 95, 96, 97, 98, 99, 101, 103, 104, 105, 108, 109, 110, 111, 113, 114, 116, 118, 120, 121, 124, 125, 127], "excluded_lines": []}, "RealTimeProcessor": {"executed_lines": [143, 144, 145, 146, 149, 150, 151, 152, 153, 156, 157, 160, 161, 164, 167, 168, 169, 173, 174, 175, 176, 177, 178], "summary": {"covered_lines": 23, "num_statements": 101, "percent_covered": 22.77227722772277, "percent_covered_display": "23", "missing_lines": 78, "excluded_lines": 0}, "missing_lines": [180, 184, 185, 186, 188, 189, 192, 195, 200, 201, 202, 203, 205, 206, 210, 211, 214, 215, 218, 219, 223, 225, 229, 230, 232, 233, 235, 236, 240, 242, 243, 245, 248, 250, 251, 252, 253, 257, 258, 259, 260, 262, 263, 265, 267, 269, 272, 273, 274, 275, 276, 278, 279, 281, 282, 286, 288, 289, 290, 292, 294, 295, 298, 299, 304, 307, 308, 309, 310, 313, 315, 319, 320, 323, 329, 333, 337, 341], "excluded_lines": []}, "": {"executed_lines": [2, 12, 17, 18, 19, 20, 21, 22, 23, 24, 26, 27, 28, 29, 31, 34, 35, 36, 38, 39, 40, 41, 42, 43, 44, 45, 46, 49, 50, 51, 53, 54, 55, 56, 57, 58, 59, 62, 63, 65, 72, 86, 130, 131, 134, 135, 136, 137, 138, 139, 141, 171, 182, 208, 221, 238, 255, 301, 317, 331, 335, 339], "summary": {"covered_lines": 57, "num_statements": 57, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}}}, "aretomo3_gui/core/resource_manager.py": {"executed_lines": [2, 25, 26, 29, 30, 31, 32, 33, 34, 35, 36, 39, 40, 43, 49, 56, 57, 72, 74, 91, 107, 122, 140, 160, 180, 181, 197, 203, 212, 246, 288, 329, 374, 375, 378, 395, 412, 413, 417, 421], "summary": {"covered_lines": 36, "num_statements": 138, "percent_covered": 26.08695652173913, "percent_covered_display": "26", "missing_lines": 102, "excluded_lines": 0}, "missing_lines": [81, 82, 83, 84, 85, 99, 101, 102, 103, 104, 105, 113, 114, 115, 116, 129, 130, 132, 133, 137, 138, 149, 150, 152, 153, 157, 158, 170, 171, 172, 199, 200, 201, 210, 232, 233, 234, 235, 236, 237, 238, 239, 240, 258, 259, 260, 261, 262, 263, 264, 269, 272, 273, 274, 275, 276, 277, 278, 281, 282, 305, 306, 307, 309, 310, 312, 313, 315, 316, 317, 318, 319, 321, 322, 323, 324, 326, 327, 345, 346, 347, 349, 351, 354, 357, 358, 359, 361, 362, 364, 365, 366, 389, 390, 391, 392, 406, 407, 408, 409, 419, 423], "excluded_lines": [], "functions": {"ResourceMonitor.__init__": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 5, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 5, "excluded_lines": 0}, "missing_lines": [81, 82, 83, 84, 85], "excluded_lines": []}, "ResourceMonitor.start_monitoring": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 6, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 6, "excluded_lines": 0}, "missing_lines": [99, 101, 102, 103, 104, 105], "excluded_lines": []}, "ResourceMonitor.stop_monitoring": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 4, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 4, "excluded_lines": 0}, "missing_lines": [113, 114, 115, 116], "excluded_lines": []}, "ResourceMonitor.check_memory": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 6, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 6, "excluded_lines": 0}, "missing_lines": [129, 130, 132, 133, 137, 138], "excluded_lines": []}, "ResourceMonitor.check_disk": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 6, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 6, "excluded_lines": 0}, "missing_lines": [149, 150, 152, 153, 157, 158], "excluded_lines": []}, "ResourceMonitor.check_all": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 3, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 3, "excluded_lines": 0}, "missing_lines": [170, 171, 172], "excluded_lines": []}, "FileManager.__init__": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 3, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 3, "excluded_lines": 0}, "missing_lines": [199, 200, 201], "excluded_lines": []}, "FileManager.__del__": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [210], "excluded_lines": []}, "FileManager.create_temp_directory": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 9, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 9, "excluded_lines": 0}, "missing_lines": [232, 233, 234, 235, 236, 237, 238, 239, 240], "excluded_lines": []}, "FileManager.cleanup": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 17, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 17, "excluded_lines": 0}, "missing_lines": [258, 259, 260, 261, 262, 263, 264, 269, 272, 273, 274, 275, 276, 277, 278, 281, 282], "excluded_lines": []}, "FileManager.acquire_lock": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 18, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 18, "excluded_lines": 0}, "missing_lines": [305, 306, 307, 309, 310, 312, 313, 315, 316, 317, 318, 319, 321, 322, 323, 324, 326, 327], "excluded_lines": []}, "FileManager.release_lock": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 14, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 14, "excluded_lines": 0}, "missing_lines": [345, 346, 347, 349, 351, 354, 357, 358, 359, 361, 362, 364, 365, 366], "excluded_lines": []}, "get_file_manager": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 4, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 4, "excluded_lines": 0}, "missing_lines": [389, 390, 391, 392], "excluded_lines": []}, "get_resource_monitor": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 4, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 4, "excluded_lines": 0}, "missing_lines": [406, 407, 408, 409], "excluded_lines": []}, "ResourceManager.__init__": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [419], "excluded_lines": []}, "ResourceManager.__getattr__": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [423], "excluded_lines": []}, "": {"executed_lines": [2, 25, 26, 29, 30, 31, 32, 33, 34, 35, 36, 39, 40, 43, 49, 56, 57, 72, 74, 91, 107, 122, 140, 160, 180, 181, 197, 203, 212, 246, 288, 329, 374, 375, 378, 395, 412, 413, 417, 421], "summary": {"covered_lines": 36, "num_statements": 36, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}}, "classes": {"ResourceMonitor": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 30, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 30, "excluded_lines": 0}, "missing_lines": [81, 82, 83, 84, 85, 99, 101, 102, 103, 104, 105, 113, 114, 115, 116, 129, 130, 132, 133, 137, 138, 149, 150, 152, 153, 157, 158, 170, 171, 172], "excluded_lines": []}, "FileManager": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 62, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 62, "excluded_lines": 0}, "missing_lines": [199, 200, 201, 210, 232, 233, 234, 235, 236, 237, 238, 239, 240, 258, 259, 260, 261, 262, 263, 264, 269, 272, 273, 274, 275, 276, 277, 278, 281, 282, 305, 306, 307, 309, 310, 312, 313, 315, 316, 317, 318, 319, 321, 322, 323, 324, 326, 327, 345, 346, 347, 349, 351, 354, 357, 358, 359, 361, 362, 364, 365, 366], "excluded_lines": []}, "ResourceManager": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 2, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 2, "excluded_lines": 0}, "missing_lines": [419, 423], "excluded_lines": []}, "": {"executed_lines": [2, 25, 26, 29, 30, 31, 32, 33, 34, 35, 36, 39, 40, 43, 49, 56, 57, 72, 74, 91, 107, 122, 140, 160, 180, 181, 197, 203, 212, 246, 288, 329, 374, 375, 378, 395, 412, 413, 417, 421], "summary": {"covered_lines": 36, "num_statements": 44, "percent_covered": 81.81818181818181, "percent_covered_display": "82", "missing_lines": 8, "excluded_lines": 0}, "missing_lines": [389, 390, 391, 392, 406, 407, 408, 409], "excluded_lines": []}}}, "aretomo3_gui/core/results_tracker.py": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 183, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 183, "excluded_lines": 0}, "missing_lines": [7, 8, 9, 10, 11, 12, 13, 14, 16, 19, 20, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 35, 36, 37, 38, 39, 40, 43, 44, 47, 48, 50, 52, 53, 56, 59, 61, 62, 64, 65, 68, 71, 73, 75, 77, 78, 79, 82, 112, 115, 119, 120, 123, 125, 126, 127, 128, 131, 158, 192, 193, 195, 196, 198, 199, 200, 202, 204, 205, 206, 207, 209, 210, 219, 228, 229, 230, 232, 233, 234, 236, 240, 241, 242, 243, 246, 250, 252, 253, 254, 255, 257, 259, 277, 278, 280, 281, 283, 284, 285, 287, 289, 290, 291, 292, 294, 297, 298, 300, 301, 302, 304, 305, 306, 308, 312, 313, 314, 315, 317, 318, 320, 321, 322, 324, 326, 327, 328, 330, 331, 332, 334, 336, 337, 338, 341, 343, 344, 345, 346, 349, 350, 352, 355, 357, 360, 362, 365, 368, 371, 374, 383, 385, 387, 398, 399, 400, 402, 405, 430, 433, 434, 436, 439, 440, 441, 444, 445, 447, 449, 451, 452, 453, 454, 456, 465, 466, 467, 469, 470, 472, 473, 474], "excluded_lines": [], "functions": {"ProcessingResult.__post_init__": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 2, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 2, "excluded_lines": 0}, "missing_lines": [52, 53], "excluded_lines": []}, "ResultsTracker.__init__": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 7, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 7, "excluded_lines": 0}, "missing_lines": [61, 62, 64, 65, 68, 71, 73], "excluded_lines": []}, "ResultsTracker._init_database": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 8, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 8, "excluded_lines": 0}, "missing_lines": [77, 78, 79, 82, 112, 115, 119, 120], "excluded_lines": []}, "ResultsTracker.add_result": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 13, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 13, "excluded_lines": 0}, "missing_lines": [125, 126, 127, 128, 131, 158, 192, 193, 195, 196, 198, 199, 200], "excluded_lines": []}, "ResultsTracker.update_progress": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 13, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 13, "excluded_lines": 0}, "missing_lines": [204, 205, 206, 207, 209, 210, 219, 228, 229, 230, 232, 233, 234], "excluded_lines": []}, "ResultsTracker.complete_job": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 19, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 19, "excluded_lines": 0}, "missing_lines": [240, 241, 242, 243, 246, 250, 252, 253, 254, 255, 257, 259, 277, 278, 280, 281, 283, 284, 285], "excluded_lines": []}, "ResultsTracker.get_result": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 13, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 13, "excluded_lines": 0}, "missing_lines": [289, 290, 291, 292, 294, 297, 298, 300, 301, 302, 304, 305, 306], "excluded_lines": []}, "ResultsTracker.get_all_results": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 20, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 20, "excluded_lines": 0}, "missing_lines": [312, 313, 314, 315, 317, 318, 320, 321, 322, 324, 326, 327, 328, 330, 331, 332, 334, 336, 337, 338], "excluded_lines": []}, "ResultsTracker.get_statistics": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 21, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 21, "excluded_lines": 0}, "missing_lines": [343, 344, 345, 346, 349, 350, 352, 355, 357, 360, 362, 365, 368, 371, 374, 383, 385, 387, 398, 399, 400], "excluded_lines": []}, "ResultsTracker._row_to_result": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 11, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 11, "excluded_lines": 0}, "missing_lines": [405, 430, 433, 434, 436, 439, 440, 441, 444, 445, 447], "excluded_lines": []}, "ResultsTracker.clear_old_results": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 13, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 13, "excluded_lines": 0}, "missing_lines": [451, 452, 453, 454, 456, 465, 466, 467, 469, 470, 472, 473, 474], "excluded_lines": []}, "": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 43, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 43, "excluded_lines": 0}, "missing_lines": [7, 8, 9, 10, 11, 12, 13, 14, 16, 19, 20, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 35, 36, 37, 38, 39, 40, 43, 44, 47, 48, 50, 56, 59, 75, 123, 202, 236, 287, 308, 341, 402, 449], "excluded_lines": []}}, "classes": {"ProcessingResult": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 2, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 2, "excluded_lines": 0}, "missing_lines": [52, 53], "excluded_lines": []}, "ResultsTracker": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 138, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 138, "excluded_lines": 0}, "missing_lines": [61, 62, 64, 65, 68, 71, 73, 77, 78, 79, 82, 112, 115, 119, 120, 125, 126, 127, 128, 131, 158, 192, 193, 195, 196, 198, 199, 200, 204, 205, 206, 207, 209, 210, 219, 228, 229, 230, 232, 233, 234, 240, 241, 242, 243, 246, 250, 252, 253, 254, 255, 257, 259, 277, 278, 280, 281, 283, 284, 285, 289, 290, 291, 292, 294, 297, 298, 300, 301, 302, 304, 305, 306, 312, 313, 314, 315, 317, 318, 320, 321, 322, 324, 326, 327, 328, 330, 331, 332, 334, 336, 337, 338, 343, 344, 345, 346, 349, 350, 352, 355, 357, 360, 362, 365, 368, 371, 374, 383, 385, 387, 398, 399, 400, 405, 430, 433, 434, 436, 439, 440, 441, 444, 445, 447, 451, 452, 453, 454, 456, 465, 466, 467, 469, 470, 472, 473, 474], "excluded_lines": []}, "": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 43, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 43, "excluded_lines": 0}, "missing_lines": [7, 8, 9, 10, 11, 12, 13, 14, 16, 19, 20, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 35, 36, 37, 38, 39, 40, 43, 44, 47, 48, 50, 56, 59, 75, 123, 202, 236, 287, 308, 341, 402, 449], "excluded_lines": []}}}, "aretomo3_gui/core/secure_web_api.py": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 204, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 204, "excluded_lines": 5}, "missing_lines": [7, 8, 9, 10, 11, 12, 13, 14, 15, 17, 18, 19, 20, 21, 22, 24, 27, 28, 31, 34, 35, 36, 37, 38, 39, 40, 41, 42, 45, 46, 49, 50, 53, 56, 58, 59, 60, 61, 64, 65, 66, 71, 72, 73, 74, 76, 78, 80, 82, 84, 86, 87, 89, 90, 91, 93, 95, 96, 97, 98, 99, 101, 103, 111, 115, 117, 118, 123, 124, 125, 126, 127, 128, 129, 131, 133, 134, 136, 139, 140, 142, 145, 146, 149, 150, 155, 157, 160, 163, 166, 167, 169, 171, 172, 173, 174, 175, 176, 179, 182, 184, 185, 186, 187, 188, 190, 192, 200, 204, 207, 212, 222, 223, 225, 226, 232, 235, 236, 242, 243, 244, 245, 246, 251, 254, 255, 256, 257, 261, 263, 266, 267, 268, 270, 271, 272, 274, 278, 282, 284, 288, 291, 295, 298, 299, 300, 302, 309, 310, 312, 314, 315, 319, 320, 322, 323, 329, 330, 333, 335, 340, 341, 343, 351, 352, 354, 356, 357, 361, 362, 363, 364, 368, 369, 370, 371, 379, 381, 382, 383, 389, 390, 393, 394, 395, 396, 401, 402, 404, 405, 408, 409, 410, 411, 416, 417, 418, 422, 424, 426, 428, 431, 432, 434], "excluded_lines": [446, 448, 457, 464, 465], "functions": {"SecurityManager.__init__": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 11, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 11, "excluded_lines": 0}, "missing_lines": [58, 59, 60, 61, 64, 65, 66, 71, 72, 73, 74], "excluded_lines": []}, "SecurityManager._generate_api_key": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [78], "excluded_lines": []}, "SecurityManager.validate_api_key": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [82], "excluded_lines": []}, "SecurityManager.add_api_key": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 5, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 5, "excluded_lines": 0}, "missing_lines": [86, 87, 89, 90, 91], "excluded_lines": []}, "SecurityManager.revoke_api_key": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 5, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 5, "excluded_lines": 0}, "missing_lines": [95, 96, 97, 98, 99], "excluded_lines": []}, "SecurityManager.generate_jwt_token": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 2, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 2, "excluded_lines": 0}, "missing_lines": [103, 111], "excluded_lines": []}, "SecurityManager.validate_jwt_token": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 9, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 9, "excluded_lines": 0}, "missing_lines": [117, 118, 123, 124, 125, 126, 127, 128, 129], "excluded_lines": []}, "SecurityManager.check_rate_limit": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 16, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 16, "excluded_lines": 0}, "missing_lines": [133, 134, 136, 139, 140, 142, 145, 146, 149, 150, 155, 157, 160, 163, 166, 167], "excluded_lines": []}, "SecurityManager.is_safe_path": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 6, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 6, "excluded_lines": 0}, "missing_lines": [171, 172, 173, 174, 175, 176], "excluded_lines": []}, "SecureWebAPI.__init__": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 5, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 5, "excluded_lines": 0}, "missing_lines": [184, 185, 186, 187, 188], "excluded_lines": []}, "SecureWebAPI._create_app": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 2, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 2, "excluded_lines": 0}, "missing_lines": [192, 200], "excluded_lines": []}, "SecureWebAPI._setup_middleware": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 4, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 4, "excluded_lines": 0}, "missing_lines": [207, 212, 222, 223], "excluded_lines": []}, "SecureWebAPI._setup_middleware.security_middleware": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 16, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 16, "excluded_lines": 0}, "missing_lines": [225, 226, 232, 235, 236, 242, 243, 244, 245, 246, 251, 254, 255, 256, 257, 261], "excluded_lines": []}, "SecureWebAPI._get_client_ip": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 7, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 7, "excluded_lines": 0}, "missing_lines": [266, 267, 268, 270, 271, 272, 274], "excluded_lines": []}, "SecureWebAPI._setup_routes": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 14, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 14, "excluded_lines": 0}, "missing_lines": [282, 284, 309, 310, 314, 315, 340, 341, 351, 352, 389, 390, 404, 405], "excluded_lines": []}, "SecureWebAPI._setup_routes.verify_token": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 7, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 7, "excluded_lines": 0}, "missing_lines": [288, 291, 295, 298, 299, 300, 302], "excluded_lines": []}, "SecureWebAPI._setup_routes.health_check": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [312], "excluded_lines": []}, "SecureWebAPI._setup_routes.login": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 8, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 8, "excluded_lines": 0}, "missing_lines": [319, 320, 322, 323, 329, 330, 333, 335], "excluded_lines": []}, "SecureWebAPI._setup_routes.get_status": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [343], "excluded_lines": []}, "SecureWebAPI._setup_routes.list_files": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 15, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 15, "excluded_lines": 0}, "missing_lines": [354, 356, 357, 361, 362, 363, 364, 368, 369, 370, 371, 379, 381, 382, 383], "excluded_lines": []}, "SecureWebAPI._setup_routes.create_api_key": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 6, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 6, "excluded_lines": 0}, "missing_lines": [393, 394, 395, 396, 401, 402], "excluded_lines": []}, "SecureWebAPI._setup_routes.revoke_api_key": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 8, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 8, "excluded_lines": 0}, "missing_lines": [408, 409, 410, 411, 416, 417, 418, 422], "excluded_lines": []}, "SecureWebAPI.run": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 5, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 5, "excluded_lines": 0}, "missing_lines": [426, 428, 431, 432, 434], "excluded_lines": []}, "": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 49, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 49, "excluded_lines": 5}, "missing_lines": [7, 8, 9, 10, 11, 12, 13, 14, 15, 17, 18, 19, 20, 21, 22, 24, 27, 28, 31, 34, 35, 36, 37, 38, 39, 40, 41, 42, 45, 46, 49, 50, 53, 56, 76, 80, 84, 93, 101, 115, 131, 169, 179, 182, 190, 204, 263, 278, 424], "excluded_lines": [446, 448, 457, 464, 465]}}, "classes": {"SecurityConfig": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "RateLimitInfo": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "SecurityManager": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 56, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 56, "excluded_lines": 0}, "missing_lines": [58, 59, 60, 61, 64, 65, 66, 71, 72, 73, 74, 78, 82, 86, 87, 89, 90, 91, 95, 96, 97, 98, 99, 103, 111, 117, 118, 123, 124, 125, 126, 127, 128, 129, 133, 134, 136, 139, 140, 142, 145, 146, 149, 150, 155, 157, 160, 163, 166, 167, 171, 172, 173, 174, 175, 176], "excluded_lines": []}, "SecureWebAPI": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 99, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 99, "excluded_lines": 0}, "missing_lines": [184, 185, 186, 187, 188, 192, 200, 207, 212, 222, 223, 225, 226, 232, 235, 236, 242, 243, 244, 245, 246, 251, 254, 255, 256, 257, 261, 266, 267, 268, 270, 271, 272, 274, 282, 284, 288, 291, 295, 298, 299, 300, 302, 309, 310, 312, 314, 315, 319, 320, 322, 323, 329, 330, 333, 335, 340, 341, 343, 351, 352, 354, 356, 357, 361, 362, 363, 364, 368, 369, 370, 371, 379, 381, 382, 383, 389, 390, 393, 394, 395, 396, 401, 402, 404, 405, 408, 409, 410, 411, 416, 417, 418, 422, 426, 428, 431, 432, 434], "excluded_lines": []}, "": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 49, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 49, "excluded_lines": 5}, "missing_lines": [7, 8, 9, 10, 11, 12, 13, 14, 15, 17, 18, 19, 20, 21, 22, 24, 27, 28, 31, 34, 35, 36, 37, 38, 39, 40, 41, 42, 45, 46, 49, 50, 53, 56, 76, 80, 84, 93, 101, 115, 131, 169, 179, 182, 190, 204, 263, 278, 424], "excluded_lines": [446, 448, 457, 464, 465]}}}, "aretomo3_gui/core/security_framework.py": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 246, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 246, "excluded_lines": 0}, "missing_lines": [7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 18, 19, 20, 21, 23, 26, 27, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 42, 43, 46, 47, 48, 49, 50, 51, 52, 55, 56, 59, 60, 61, 62, 63, 64, 65, 68, 74, 76, 81, 84, 85, 88, 89, 92, 93, 96, 97, 100, 101, 103, 105, 107, 108, 109, 110, 113, 114, 115, 118, 119, 120, 122, 123, 125, 127, 128, 141, 142, 145, 146, 148, 149, 151, 153, 154, 155, 157, 159, 161, 162, 165, 166, 167, 170, 171, 173, 174, 176, 177, 178, 180, 182, 183, 186, 193, 194, 196, 198, 199, 200, 201, 202, 203, 205, 207, 208, 210, 212, 213, 217, 218, 220, 221, 223, 224, 226, 227, 229, 230, 232, 234, 238, 240, 250, 253, 262, 263, 265, 267, 268, 270, 273, 276, 277, 280, 281, 283, 285, 286, 287, 290, 299, 300, 302, 306, 314, 319, 327, 329, 331, 332, 337, 338, 339, 340, 341, 342, 343, 345, 347, 348, 350, 351, 352, 354, 355, 356, 357, 358, 360, 362, 363, 369, 370, 371, 372, 373, 374, 376, 378, 379, 380, 383, 384, 386, 388, 398, 399, 401, 411, 414, 415, 416, 425, 426, 427, 429, 437, 439, 440, 442, 443, 445, 446, 448, 449, 451, 453, 455, 467, 469, 470, 472, 473, 476, 478, 479, 481, 482, 486, 489, 492, 495, 499, 501, 503, 506, 509, 511, 512, 514, 521, 522, 523, 531, 533], "excluded_lines": [], "functions": {"SecurityFramework.__init__": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 13, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 13, "excluded_lines": 0}, "missing_lines": [76, 81, 84, 85, 88, 89, 92, 93, 96, 97, 100, 101, 103], "excluded_lines": []}, "SecurityFramework._load_config": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 12, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 12, "excluded_lines": 0}, "missing_lines": [107, 108, 109, 110, 113, 114, 115, 118, 119, 120, 122, 123], "excluded_lines": []}, "SecurityFramework._save_config": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 8, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 8, "excluded_lines": 0}, "missing_lines": [127, 128, 141, 142, 145, 146, 148, 149], "excluded_lines": []}, "SecurityFramework._initialize_encryption": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 17, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 17, "excluded_lines": 0}, "missing_lines": [153, 154, 155, 157, 159, 161, 162, 165, 166, 167, 170, 171, 173, 174, 176, 177, 178], "excluded_lines": []}, "SecurityFramework.hash_password": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 5, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 5, "excluded_lines": 0}, "missing_lines": [182, 183, 186, 193, 194], "excluded_lines": []}, "SecurityFramework.verify_password": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 6, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 6, "excluded_lines": 0}, "missing_lines": [198, 199, 200, 201, 202, 203], "excluded_lines": []}, "SecurityFramework.validate_password_strength": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 16, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 16, "excluded_lines": 0}, "missing_lines": [207, 208, 210, 212, 213, 217, 218, 220, 221, 223, 224, 226, 227, 229, 230, 232], "excluded_lines": []}, "SecurityFramework.create_session": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 6, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 6, "excluded_lines": 0}, "missing_lines": [238, 240, 250, 253, 262, 263], "excluded_lines": []}, "SecurityFramework.validate_session": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 8, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 8, "excluded_lines": 0}, "missing_lines": [267, 268, 270, 273, 276, 277, 280, 281], "excluded_lines": []}, "SecurityFramework.invalidate_session": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 6, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 6, "excluded_lines": 0}, "missing_lines": [285, 286, 287, 290, 299, 300], "excluded_lines": []}, "SecurityFramework.generate_jwt_token": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 4, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 4, "excluded_lines": 0}, "missing_lines": [306, 314, 319, 327], "excluded_lines": []}, "SecurityFramework.verify_jwt_token": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 9, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 9, "excluded_lines": 0}, "missing_lines": [331, 332, 337, 338, 339, 340, 341, 342, 343], "excluded_lines": []}, "SecurityFramework.encrypt_data": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 10, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 10, "excluded_lines": 0}, "missing_lines": [347, 348, 350, 351, 352, 354, 355, 356, 357, 358], "excluded_lines": []}, "SecurityFramework.decrypt_data": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 8, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 8, "excluded_lines": 0}, "missing_lines": [362, 363, 369, 370, 371, 372, 373, 374], "excluded_lines": []}, "SecurityFramework.check_permission": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 6, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 6, "excluded_lines": 0}, "missing_lines": [378, 379, 380, 383, 384, 386], "excluded_lines": []}, "SecurityFramework._log_audit_event": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 10, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 10, "excluded_lines": 0}, "missing_lines": [398, 399, 401, 411, 414, 415, 416, 425, 426, 427], "excluded_lines": []}, "SecurityFramework.get_audit_events": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 10, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 10, "excluded_lines": 0}, "missing_lines": [437, 439, 440, 442, 443, 445, 446, 448, 449, 451], "excluded_lines": []}, "SecurityFramework.get_security_status": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [455], "excluded_lines": []}, "SecurityFramework.cleanup_expired_sessions": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 9, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 9, "excluded_lines": 0}, "missing_lines": [469, 470, 472, 473, 476, 478, 479, 481, 482], "excluded_lines": []}, "require_permission": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 2, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 2, "excluded_lines": 0}, "missing_lines": [492, 503], "excluded_lines": []}, "require_permission.decorator": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 2, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 2, "excluded_lines": 0}, "missing_lines": [495, 501], "excluded_lines": []}, "require_permission.decorator.wrapper": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [499], "excluded_lines": []}, "secure_file_operation": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 2, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 2, "excluded_lines": 0}, "missing_lines": [509, 533], "excluded_lines": []}, "secure_file_operation.wrapper": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 7, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 7, "excluded_lines": 0}, "missing_lines": [511, 512, 514, 521, 522, 523, 531], "excluded_lines": []}, "": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 68, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 68, "excluded_lines": 0}, "missing_lines": [7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 18, 19, 20, 21, 23, 26, 27, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 42, 43, 46, 47, 48, 49, 50, 51, 52, 55, 56, 59, 60, 61, 62, 63, 64, 65, 68, 74, 105, 125, 151, 180, 196, 205, 234, 265, 283, 302, 329, 345, 360, 376, 388, 429, 453, 467, 486, 489, 506], "excluded_lines": []}}, "classes": {"SecurityConfig": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "UserSession": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "AuditEvent": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "SecurityFramework": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 164, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 164, "excluded_lines": 0}, "missing_lines": [76, 81, 84, 85, 88, 89, 92, 93, 96, 97, 100, 101, 103, 107, 108, 109, 110, 113, 114, 115, 118, 119, 120, 122, 123, 127, 128, 141, 142, 145, 146, 148, 149, 153, 154, 155, 157, 159, 161, 162, 165, 166, 167, 170, 171, 173, 174, 176, 177, 178, 182, 183, 186, 193, 194, 198, 199, 200, 201, 202, 203, 207, 208, 210, 212, 213, 217, 218, 220, 221, 223, 224, 226, 227, 229, 230, 232, 238, 240, 250, 253, 262, 263, 267, 268, 270, 273, 276, 277, 280, 281, 285, 286, 287, 290, 299, 300, 306, 314, 319, 327, 331, 332, 337, 338, 339, 340, 341, 342, 343, 347, 348, 350, 351, 352, 354, 355, 356, 357, 358, 362, 363, 369, 370, 371, 372, 373, 374, 378, 379, 380, 383, 384, 386, 398, 399, 401, 411, 414, 415, 416, 425, 426, 427, 437, 439, 440, 442, 443, 445, 446, 448, 449, 451, 455, 469, 470, 472, 473, 476, 478, 479, 481, 482], "excluded_lines": []}, "": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 82, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 82, "excluded_lines": 0}, "missing_lines": [7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 18, 19, 20, 21, 23, 26, 27, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 42, 43, 46, 47, 48, 49, 50, 51, 52, 55, 56, 59, 60, 61, 62, 63, 64, 65, 68, 74, 105, 125, 151, 180, 196, 205, 234, 265, 283, 302, 329, 345, 360, 376, 388, 429, 453, 467, 486, 489, 492, 495, 499, 501, 503, 506, 509, 511, 512, 514, 521, 522, 523, 531, 533], "excluded_lines": []}}}, "aretomo3_gui/core/session_manager.py": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 174, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 174, "excluded_lines": 0}, "missing_lines": [6, 7, 8, 9, 10, 11, 12, 13, 14, 16, 19, 20, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 42, 45, 47, 52, 54, 55, 56, 57, 58, 61, 63, 65, 73, 74, 76, 96, 97, 98, 100, 101, 103, 105, 106, 107, 108, 109, 111, 112, 114, 115, 117, 118, 120, 121, 122, 124, 126, 127, 128, 130, 131, 132, 135, 137, 138, 139, 142, 144, 145, 147, 148, 149, 151, 153, 154, 155, 157, 158, 159, 160, 162, 164, 166, 167, 168, 170, 172, 173, 175, 194, 196, 197, 199, 201, 202, 203, 204, 206, 208, 210, 212, 214, 215, 224, 225, 233, 235, 237, 238, 240, 241, 244, 246, 248, 250, 251, 252, 254, 256, 258, 259, 260, 261, 262, 263, 264, 265, 267, 269, 270, 271, 273, 274, 275, 276, 277, 278, 279, 280, 283, 285, 287, 288, 290, 293, 298, 301, 303, 305, 319, 321, 322, 324, 325, 326, 327, 329, 332, 333, 334, 336, 337, 340, 342, 343], "excluded_lines": [], "functions": {"SessionManager.__init__": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 9, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 9, "excluded_lines": 0}, "missing_lines": [47, 52, 54, 55, 56, 57, 58, 61, 63], "excluded_lines": []}, "SessionManager.create_new_session": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 8, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 8, "excluded_lines": 0}, "missing_lines": [73, 74, 76, 96, 97, 98, 100, 101], "excluded_lines": []}, "SessionManager.load_session": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 14, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 14, "excluded_lines": 0}, "missing_lines": [105, 106, 107, 108, 109, 111, 112, 114, 115, 117, 118, 120, 121, 122], "excluded_lines": []}, "SessionManager.save_session": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 16, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 16, "excluded_lines": 0}, "missing_lines": [126, 127, 128, 130, 131, 132, 135, 137, 138, 139, 142, 144, 145, 147, 148, 149], "excluded_lines": []}, "SessionManager.update_session": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 12, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 12, "excluded_lines": 0}, "missing_lines": [153, 154, 155, 157, 158, 159, 160, 162, 164, 166, 167, 168], "excluded_lines": []}, "SessionManager.get_session_for_web": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 3, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 3, "excluded_lines": 0}, "missing_lines": [172, 173, 175], "excluded_lines": []}, "SessionManager.get_quality_summary": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 8, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 8, "excluded_lines": 0}, "missing_lines": [196, 197, 199, 201, 202, 203, 204, 206], "excluded_lines": []}, "SessionManager.get_recent_activity": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 7, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 7, "excluded_lines": 0}, "missing_lines": [210, 212, 214, 215, 224, 225, 233], "excluded_lines": []}, "SessionManager.start_auto_save": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 6, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 6, "excluded_lines": 0}, "missing_lines": [237, 238, 240, 241, 244, 246], "excluded_lines": []}, "SessionManager.stop_auto_save": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 4, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 4, "excluded_lines": 0}, "missing_lines": [250, 251, 252, 254], "excluded_lines": []}, "SessionManager._auto_save_worker": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 8, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 8, "excluded_lines": 0}, "missing_lines": [258, 259, 260, 261, 262, 263, 264, 265], "excluded_lines": []}, "SessionManager.load_session_history": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 15, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 15, "excluded_lines": 0}, "missing_lines": [269, 270, 271, 273, 274, 275, 276, 277, 278, 279, 280, 283, 285, 287, 288], "excluded_lines": []}, "SessionManager.update_session_history": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 3, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 3, "excluded_lines": 0}, "missing_lines": [293, 298, 301], "excluded_lines": []}, "SessionManager.get_session_list": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [305], "excluded_lines": []}, "SessionManager.cleanup_old_sessions": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 15, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 15, "excluded_lines": 0}, "missing_lines": [321, 322, 324, 325, 326, 327, 329, 332, 333, 334, 336, 337, 340, 342, 343], "excluded_lines": []}, "": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 45, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 45, "excluded_lines": 0}, "missing_lines": [6, 7, 8, 9, 10, 11, 12, 13, 14, 16, 19, 20, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 42, 45, 65, 103, 124, 151, 170, 194, 208, 235, 248, 256, 267, 290, 303, 319], "excluded_lines": []}}, "classes": {"ProcessingSession": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "SessionManager": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 129, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 129, "excluded_lines": 0}, "missing_lines": [47, 52, 54, 55, 56, 57, 58, 61, 63, 73, 74, 76, 96, 97, 98, 100, 101, 105, 106, 107, 108, 109, 111, 112, 114, 115, 117, 118, 120, 121, 122, 126, 127, 128, 130, 131, 132, 135, 137, 138, 139, 142, 144, 145, 147, 148, 149, 153, 154, 155, 157, 158, 159, 160, 162, 164, 166, 167, 168, 172, 173, 175, 196, 197, 199, 201, 202, 203, 204, 206, 210, 212, 214, 215, 224, 225, 233, 237, 238, 240, 241, 244, 246, 250, 251, 252, 254, 258, 259, 260, 261, 262, 263, 264, 265, 269, 270, 271, 273, 274, 275, 276, 277, 278, 279, 280, 283, 285, 287, 288, 293, 298, 301, 305, 321, 322, 324, 325, 326, 327, 329, 332, 333, 334, 336, 337, 340, 342, 343], "excluded_lines": []}, "": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 45, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 45, "excluded_lines": 0}, "missing_lines": [6, 7, 8, 9, 10, 11, 12, 13, 14, 16, 19, 20, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 42, 45, 65, 103, 124, 151, 170, 194, 208, 235, 248, 256, 267, 290, 303, 319], "excluded_lines": []}}}, "aretomo3_gui/core/system_integration.py": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 136, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 136, "excluded_lines": 0}, "missing_lines": [7, 8, 9, 10, 11, 12, 13, 15, 18, 19, 22, 23, 24, 25, 26, 27, 30, 36, 38, 39, 40, 41, 42, 43, 44, 47, 49, 51, 53, 78, 79, 84, 86, 88, 89, 91, 93, 94, 97, 98, 100, 101, 102, 103, 104, 106, 107, 108, 109, 111, 112, 115, 117, 119, 121, 122, 124, 125, 126, 128, 129, 132, 135, 137, 139, 140, 141, 142, 143, 145, 147, 148, 149, 150, 152, 154, 156, 157, 158, 159, 165, 166, 167, 169, 171, 172, 173, 174, 175, 177, 179, 180, 181, 183, 184, 186, 187, 189, 191, 192, 193, 195, 197, 199, 200, 202, 203, 204, 206, 208, 209, 210, 211, 213, 216, 218, 219, 221, 223, 225, 227, 228, 229, 231, 232, 235, 237, 239, 257, 259, 261, 262, 263, 265, 266, 270], "excluded_lines": [], "functions": {"SystemIntegrationManager.__init__": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 9, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 9, "excluded_lines": 0}, "missing_lines": [38, 39, 40, 41, 42, 43, 44, 47, 49], "excluded_lines": []}, "SystemIntegrationManager._register_components": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 4, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 4, "excluded_lines": 0}, "missing_lines": [53, 78, 79, 84], "excluded_lines": []}, "SystemIntegrationManager._calculate_startup_order": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 7, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 7, "excluded_lines": 0}, "missing_lines": [88, 89, 91, 111, 112, 115, 117], "excluded_lines": []}, "SystemIntegrationManager._calculate_startup_order.visit": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 13, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 13, "excluded_lines": 0}, "missing_lines": [93, 94, 97, 98, 100, 101, 102, 103, 104, 106, 107, 108, 109], "excluded_lines": []}, "SystemIntegrationManager.start_system": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 15, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 15, "excluded_lines": 0}, "missing_lines": [121, 122, 124, 125, 126, 128, 129, 132, 135, 137, 139, 140, 141, 142, 143], "excluded_lines": []}, "SystemIntegrationManager._start_component": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 19, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 19, "excluded_lines": 0}, "missing_lines": [147, 148, 149, 150, 152, 154, 156, 157, 158, 159, 165, 166, 167, 169, 171, 172, 173, 174, 175], "excluded_lines": []}, "SystemIntegrationManager.shutdown_system": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 7, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 7, "excluded_lines": 0}, "missing_lines": [179, 180, 181, 183, 184, 186, 187], "excluded_lines": []}, "SystemIntegrationManager._stop_component": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 10, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 10, "excluded_lines": 0}, "missing_lines": [191, 192, 193, 195, 197, 199, 200, 202, 203, 204], "excluded_lines": []}, "SystemIntegrationManager._monitor_components": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 8, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 8, "excluded_lines": 0}, "missing_lines": [208, 209, 210, 211, 213, 216, 218, 219], "excluded_lines": []}, "SystemIntegrationManager._health_check_component": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 8, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 8, "excluded_lines": 0}, "missing_lines": [223, 225, 227, 228, 229, 231, 232, 235], "excluded_lines": []}, "SystemIntegrationManager.get_system_status": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [239], "excluded_lines": []}, "SystemIntegrationManager.restart_component": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 4, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 4, "excluded_lines": 0}, "missing_lines": [259, 261, 265, 266], "excluded_lines": []}, "SystemIntegrationManager.restart_component._restart": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 2, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 2, "excluded_lines": 0}, "missing_lines": [262, 263], "excluded_lines": []}, "": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 29, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 29, "excluded_lines": 0}, "missing_lines": [7, 8, 9, 10, 11, 12, 13, 15, 18, 19, 22, 23, 24, 25, 26, 27, 30, 36, 51, 86, 119, 145, 177, 189, 206, 221, 237, 257, 270], "excluded_lines": []}}, "classes": {"SystemComponent": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "SystemIntegrationManager": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 107, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 107, "excluded_lines": 0}, "missing_lines": [38, 39, 40, 41, 42, 43, 44, 47, 49, 53, 78, 79, 84, 88, 89, 91, 93, 94, 97, 98, 100, 101, 102, 103, 104, 106, 107, 108, 109, 111, 112, 115, 117, 121, 122, 124, 125, 126, 128, 129, 132, 135, 137, 139, 140, 141, 142, 143, 147, 148, 149, 150, 152, 154, 156, 157, 158, 159, 165, 166, 167, 169, 171, 172, 173, 174, 175, 179, 180, 181, 183, 184, 186, 187, 191, 192, 193, 195, 197, 199, 200, 202, 203, 204, 208, 209, 210, 211, 213, 216, 218, 219, 223, 225, 227, 228, 229, 231, 232, 235, 239, 259, 261, 262, 263, 265, 266], "excluded_lines": []}, "": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 29, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 29, "excluded_lines": 0}, "missing_lines": [7, 8, 9, 10, 11, 12, 13, 15, 18, 19, 22, 23, 24, 25, 26, 27, 30, 36, 51, 86, 119, 145, 177, 189, 206, 221, 237, 257, 270], "excluded_lines": []}}}, "aretomo3_gui/core/system_monitor.py": {"executed_lines": [3, 4, 5, 6, 7, 8, 9, 11, 13, 16, 17, 19, 20, 21, 22, 23, 24, 26, 27, 62, 63, 112, 113, 134, 135, 159, 160, 162, 170, 180, 187, 229], "summary": {"covered_lines": 30, "num_statements": 121, "percent_covered": 24.793388429752067, "percent_covered_display": "25", "missing_lines": 91, "excluded_lines": 0}, "missing_lines": [29, 30, 32, 33, 34, 36, 38, 44, 45, 47, 48, 54, 55, 56, 57, 58, 59, 60, 66, 67, 70, 71, 75, 77, 79, 90, 91, 94, 95, 98, 105, 107, 109, 110, 115, 116, 118, 119, 129, 130, 131, 132, 137, 138, 140, 141, 144, 145, 146, 148, 149, 150, 151, 153, 154, 155, 156, 164, 165, 166, 167, 168, 172, 173, 175, 176, 177, 178, 182, 183, 184, 185, 189, 190, 192, 193, 194, 195, 198, 199, 202, 219, 220, 224, 227, 231, 232, 233, 234, 235, 236], "excluded_lines": [], "functions": {"GPUMonitor._check_gpu_available": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 18, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 18, "excluded_lines": 0}, "missing_lines": [29, 30, 32, 33, 34, 36, 38, 44, 45, 47, 48, 54, 55, 56, 57, 58, 59, 60], "excluded_lines": []}, "GPUMonitor.get_gpu_info": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 16, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 16, "excluded_lines": 0}, "missing_lines": [66, 67, 70, 71, 75, 77, 79, 90, 91, 94, 95, 98, 105, 107, 109, 110], "excluded_lines": []}, "GPUMonitor.get_gpu_memory": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 8, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 8, "excluded_lines": 0}, "missing_lines": [115, 116, 118, 119, 129, 130, 131, 132], "excluded_lines": []}, "GPUMonitor.get_available_gpus": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 15, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 15, "excluded_lines": 0}, "missing_lines": [137, 138, 140, 141, 144, 145, 146, 148, 149, 150, 151, 153, 154, 155, 156], "excluded_lines": []}, "SystemMonitor.__init__": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 5, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 5, "excluded_lines": 0}, "missing_lines": [164, 165, 166, 167, 168], "excluded_lines": []}, "SystemMonitor.start": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 6, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 6, "excluded_lines": 0}, "missing_lines": [172, 173, 175, 176, 177, 178], "excluded_lines": []}, "SystemMonitor.stop": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 4, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 4, "excluded_lines": 0}, "missing_lines": [182, 183, 184, 185], "excluded_lines": []}, "SystemMonitor._monitor_loop": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 13, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 13, "excluded_lines": 0}, "missing_lines": [189, 190, 192, 193, 194, 195, 198, 199, 202, 219, 220, 224, 227], "excluded_lines": []}, "SystemMonitor.get_latest_data": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 6, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 6, "excluded_lines": 0}, "missing_lines": [231, 232, 233, 234, 235, 236], "excluded_lines": []}, "": {"executed_lines": [3, 4, 5, 6, 7, 8, 9, 11, 13, 16, 17, 19, 20, 21, 22, 23, 24, 26, 27, 62, 63, 112, 113, 134, 135, 159, 160, 162, 170, 180, 187, 229], "summary": {"covered_lines": 30, "num_statements": 30, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}}, "classes": {"GPUMonitor": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 57, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 57, "excluded_lines": 0}, "missing_lines": [29, 30, 32, 33, 34, 36, 38, 44, 45, 47, 48, 54, 55, 56, 57, 58, 59, 60, 66, 67, 70, 71, 75, 77, 79, 90, 91, 94, 95, 98, 105, 107, 109, 110, 115, 116, 118, 119, 129, 130, 131, 132, 137, 138, 140, 141, 144, 145, 146, 148, 149, 150, 151, 153, 154, 155, 156], "excluded_lines": []}, "SystemMonitor": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 34, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 34, "excluded_lines": 0}, "missing_lines": [164, 165, 166, 167, 168, 172, 173, 175, 176, 177, 178, 182, 183, 184, 185, 189, 190, 192, 193, 194, 195, 198, 199, 202, 219, 220, 224, 227, 231, 232, 233, 234, 235, 236], "excluded_lines": []}, "": {"executed_lines": [3, 4, 5, 6, 7, 8, 9, 11, 13, 16, 17, 19, 20, 21, 22, 23, 24, 26, 27, 62, 63, 112, 113, 134, 135, 159, 160, 162, 170, 180, 187, 229], "summary": {"covered_lines": 30, "num_statements": 30, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}}}, "aretomo3_gui/core/thread_manager.py": {"executed_lines": [2, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 54, 57, 60, 67, 68, 70, 71, 72, 75, 76, 78, 79, 80, 81, 82, 85, 86, 87, 103, 142, 156, 166, 210, 211, 213, 214, 215, 216, 219, 220, 222, 239, 261, 283, 293, 294, 312, 313, 314, 315, 317, 337, 348, 369, 386, 400, 409, 418, 443, 446], "summary": {"covered_lines": 56, "num_statements": 184, "percent_covered": 30.434782608695652, "percent_covered_display": "30", "missing_lines": 128, "excluded_lines": 0}, "missing_lines": [125, 126, 127, 128, 129, 130, 131, 134, 135, 136, 137, 138, 139, 140, 152, 153, 154, 163, 164, 176, 177, 179, 180, 181, 182, 183, 184, 186, 187, 189, 191, 192, 193, 194, 196, 197, 199, 200, 202, 231, 232, 233, 234, 235, 236, 237, 241, 243, 244, 246, 248, 249, 251, 252, 254, 255, 256, 257, 259, 263, 265, 266, 267, 268, 269, 270, 272, 273, 274, 275, 276, 278, 281, 285, 324, 325, 326, 327, 328, 329, 330, 333, 335, 339, 340, 341, 342, 343, 344, 345, 346, 358, 359, 361, 362, 364, 365, 367, 379, 380, 381, 382, 383, 384, 396, 397, 398, 402, 403, 404, 406, 407, 411, 412, 413, 415, 416, 425, 426, 429, 430, 432, 433, 434, 436, 454, 455, 456], "excluded_lines": [], "functions": {"Task.__init__": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 14, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 14, "excluded_lines": 0}, "missing_lines": [125, 126, 127, 128, 129, 130, 131, 134, 135, 136, 137, 138, 139, 140], "excluded_lines": []}, "Task.__lt__": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 3, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 3, "excluded_lines": 0}, "missing_lines": [152, 153, 154], "excluded_lines": []}, "Task.update_progress": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 2, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 2, "excluded_lines": 0}, "missing_lines": [163, 164], "excluded_lines": []}, "Task.run": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 20, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 20, "excluded_lines": 0}, "missing_lines": [176, 177, 179, 180, 181, 182, 183, 184, 186, 187, 189, 191, 192, 193, 194, 196, 197, 199, 200, 202], "excluded_lines": []}, "WorkerThread.__init__": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 7, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 7, "excluded_lines": 0}, "missing_lines": [231, 232, 233, 234, 235, 236, 237], "excluded_lines": []}, "WorkerThread.run": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 13, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 13, "excluded_lines": 0}, "missing_lines": [241, 243, 244, 246, 248, 249, 251, 252, 254, 255, 256, 257, 259], "excluded_lines": []}, "WorkerThread.process_task": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 14, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 14, "excluded_lines": 0}, "missing_lines": [263, 265, 266, 267, 268, 269, 270, 272, 273, 274, 275, 276, 278, 281], "excluded_lines": []}, "WorkerThread.stop": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [285], "excluded_lines": []}, "ThreadManager.__init__": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 9, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 9, "excluded_lines": 0}, "missing_lines": [324, 325, 326, 327, 328, 329, 330, 333, 335], "excluded_lines": []}, "ThreadManager._start_workers": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 8, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 8, "excluded_lines": 0}, "missing_lines": [339, 340, 341, 342, 343, 344, 345, 346], "excluded_lines": []}, "ThreadManager.submit_task": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 7, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 7, "excluded_lines": 0}, "missing_lines": [358, 359, 361, 362, 364, 365, 367], "excluded_lines": []}, "ThreadManager.cancel_task": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 6, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 6, "excluded_lines": 0}, "missing_lines": [379, 380, 381, 382, 383, 384], "excluded_lines": []}, "ThreadManager.get_task_status": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 3, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 3, "excluded_lines": 0}, "missing_lines": [396, 397, 398], "excluded_lines": []}, "ThreadManager._on_task_completed": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 5, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 5, "excluded_lines": 0}, "missing_lines": [402, 403, 404, 406, 407], "excluded_lines": []}, "ThreadManager._on_task_failed": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 5, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 5, "excluded_lines": 0}, "missing_lines": [411, 412, 413, 415, 416], "excluded_lines": []}, "ThreadManager.shutdown": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 8, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 8, "excluded_lines": 0}, "missing_lines": [425, 426, 429, 430, 432, 433, 434, 436], "excluded_lines": []}, "get_thread_manager": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 3, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 3, "excluded_lines": 0}, "missing_lines": [454, 455, 456], "excluded_lines": []}, "": {"executed_lines": [2, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 54, 57, 60, 67, 68, 70, 71, 72, 75, 76, 78, 79, 80, 81, 82, 85, 86, 87, 103, 142, 156, 166, 210, 211, 213, 214, 215, 216, 219, 220, 222, 239, 261, 283, 293, 294, 312, 313, 314, 315, 317, 337, 348, 369, 386, 400, 409, 418, 443, 446], "summary": {"covered_lines": 56, "num_statements": 56, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}}, "classes": {"TaskPriority": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "TaskStatus": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "Task": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 39, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 39, "excluded_lines": 0}, "missing_lines": [125, 126, 127, 128, 129, 130, 131, 134, 135, 136, 137, 138, 139, 140, 152, 153, 154, 163, 164, 176, 177, 179, 180, 181, 182, 183, 184, 186, 187, 189, 191, 192, 193, 194, 196, 197, 199, 200, 202], "excluded_lines": []}, "WorkerSignals": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "WorkerThread": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 35, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 35, "excluded_lines": 0}, "missing_lines": [231, 232, 233, 234, 235, 236, 237, 241, 243, 244, 246, 248, 249, 251, 252, 254, 255, 256, 257, 259, 263, 265, 266, 267, 268, 269, 270, 272, 273, 274, 275, 276, 278, 281, 285], "excluded_lines": []}, "ThreadManager": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 51, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 51, "excluded_lines": 0}, "missing_lines": [324, 325, 326, 327, 328, 329, 330, 333, 335, 339, 340, 341, 342, 343, 344, 345, 346, 358, 359, 361, 362, 364, 365, 367, 379, 380, 381, 382, 383, 384, 396, 397, 398, 402, 403, 404, 406, 407, 411, 412, 413, 415, 416, 425, 426, 429, 430, 432, 433, 434, 436], "excluded_lines": []}, "": {"executed_lines": [2, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 54, 57, 60, 67, 68, 70, 71, 72, 75, 76, 78, 79, 80, 81, 82, 85, 86, 87, 103, 142, 156, 166, 210, 211, 213, 214, 215, 216, 219, 220, 222, 239, 261, 283, 293, 294, 312, 313, 314, 315, 317, 337, 348, 369, 386, 400, 409, 418, 443, 446], "summary": {"covered_lines": 56, "num_statements": 59, "percent_covered": 94.91525423728814, "percent_covered_display": "95", "missing_lines": 3, "excluded_lines": 0}, "missing_lines": [454, 455, 456], "excluded_lines": []}}}, "aretomo3_gui/core/tilt_series.py": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 10, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 10, "excluded_lines": 0}, "missing_lines": [1, 4, 6, 7, 8, 9, 10, 11, 12, 13], "excluded_lines": [], "functions": {"TiltSeries.__init__": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 8, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 8, "excluded_lines": 0}, "missing_lines": [6, 7, 8, 9, 10, 11, 12, 13], "excluded_lines": []}, "": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 2, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 2, "excluded_lines": 0}, "missing_lines": [1, 4], "excluded_lines": []}}, "classes": {"TiltSeries": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 8, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 8, "excluded_lines": 0}, "missing_lines": [6, 7, 8, 9, 10, 11, 12, 13], "excluded_lines": []}, "": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 2, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 2, "excluded_lines": 0}, "missing_lines": [1, 4], "excluded_lines": []}}}, "aretomo3_gui/data_management/__init__.py": {"executed_lines": [1, 5, 7], "summary": {"covered_lines": 2, "num_statements": 2, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": [], "functions": {"": {"executed_lines": [1, 5, 7], "summary": {"covered_lines": 2, "num_statements": 2, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}}, "classes": {"": {"executed_lines": [1, 5, 7], "summary": {"covered_lines": 2, "num_statements": 2, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}}}, "aretomo3_gui/data_management/data_manager.py": {"executed_lines": [2, 7, 8, 9, 10, 11, 12, 13, 14, 15, 17, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 55, 56, 58, 79, 123, 127, 140, 150, 156, 180, 197, 203, 219, 255, 270, 292, 307, 334, 346], "summary": {"covered_lines": 57, "num_statements": 209, "percent_covered": 27.272727272727273, "percent_covered_display": "27", "missing_lines": 152, "excluded_lines": 0}, "missing_lines": [59, 60, 61, 64, 65, 66, 67, 69, 70, 73, 74, 75, 77, 84, 85, 88, 89, 90, 91, 92, 93, 96, 99, 117, 118, 120, 121, 125, 130, 132, 133, 135, 136, 138, 142, 143, 144, 145, 146, 147, 148, 152, 153, 154, 159, 161, 174, 175, 177, 178, 185, 186, 187, 188, 189, 190, 191, 192, 194, 195, 199, 200, 201, 205, 206, 208, 209, 210, 211, 212, 213, 214, 216, 217, 222, 223, 224, 226, 227, 228, 230, 232, 233, 234, 237, 238, 239, 240, 241, 244, 245, 246, 248, 249, 251, 252, 253, 257, 258, 260, 261, 263, 272, 286, 287, 288, 290, 294, 296, 297, 298, 299, 300, 301, 302, 303, 305, 309, 310, 311, 312, 313, 315, 316, 317, 318, 319, 321, 322, 323, 324, 325, 327, 328, 329, 330, 331, 332, 336, 337, 338, 339, 341, 342, 343, 344, 348, 349, 350, 351, 352, 353], "excluded_lines": [], "functions": {"DataManager.__init__": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 13, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 13, "excluded_lines": 0}, "missing_lines": [59, 60, 61, 64, 65, 66, 67, 69, 70, 73, 74, 75, 77], "excluded_lines": []}, "DataManager.register_dataset": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 14, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 14, "excluded_lines": 0}, "missing_lines": [84, 85, 88, 89, 90, 91, 92, 93, 96, 99, 117, 118, 120, 121], "excluded_lines": []}, "DataManager.get_dataset": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [125], "excluded_lines": []}, "DataManager.list_datasets": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 6, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 6, "excluded_lines": 0}, "missing_lines": [130, 132, 133, 135, 136, 138], "excluded_lines": []}, "DataManager.update_dataset_status": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 7, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 7, "excluded_lines": 0}, "missing_lines": [142, 143, 144, 145, 146, 147, 148], "excluded_lines": []}, "DataManager.add_dataset_tag": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 3, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 3, "excluded_lines": 0}, "missing_lines": [152, 153, 154], "excluded_lines": []}, "DataManager.record_processing_operation": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 6, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 6, "excluded_lines": 0}, "missing_lines": [159, 161, 174, 175, 177, 178], "excluded_lines": []}, "DataManager.complete_processing_operation": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 10, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 10, "excluded_lines": 0}, "missing_lines": [185, 186, 187, 188, 189, 190, 191, 192, 194, 195], "excluded_lines": []}, "DataManager.get_processing_history": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 3, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 3, "excluded_lines": 0}, "missing_lines": [199, 200, 201], "excluded_lines": []}, "DataManager.cleanup_temp_files": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 11, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 11, "excluded_lines": 0}, "missing_lines": [205, 206, 208, 209, 210, 211, 212, 213, 214, 216, 217], "excluded_lines": []}, "DataManager.export_dataset": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 23, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 23, "excluded_lines": 0}, "missing_lines": [222, 223, 224, 226, 227, 228, 230, 232, 233, 234, 237, 238, 239, 240, 241, 244, 245, 246, 248, 249, 251, 252, 253], "excluded_lines": []}, "DataManager.validate_dataset_integrity": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 5, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 5, "excluded_lines": 0}, "missing_lines": [257, 258, 260, 261, 263], "excluded_lines": []}, "DataManager.get_storage_statistics": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 5, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 5, "excluded_lines": 0}, "missing_lines": [272, 286, 287, 288, 290], "excluded_lines": []}, "DataManager._calculate_dataset_checksum": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 10, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 10, "excluded_lines": 0}, "missing_lines": [294, 296, 297, 298, 299, 300, 301, 302, 303, 305], "excluded_lines": []}, "DataManager.load_metadata": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 21, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 21, "excluded_lines": 0}, "missing_lines": [309, 310, 311, 312, 313, 315, 316, 317, 318, 319, 321, 322, 323, 324, 325, 327, 328, 329, 330, 331, 332], "excluded_lines": []}, "DataManager.save_metadata": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 8, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 8, "excluded_lines": 0}, "missing_lines": [336, 337, 338, 339, 341, 342, 343, 344], "excluded_lines": []}, "DataManager.save_processing_log": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 6, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 6, "excluded_lines": 0}, "missing_lines": [348, 349, 350, 351, 352, 353], "excluded_lines": []}, "": {"executed_lines": [2, 7, 8, 9, 10, 11, 12, 13, 14, 15, 17, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 55, 56, 58, 79, 123, 127, 140, 150, 156, 180, 197, 203, 219, 255, 270, 292, 307, 334, 346], "summary": {"covered_lines": 57, "num_statements": 57, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}}, "classes": {"DatasetMetadata": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "ProcessingRecord": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "DataManager": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 152, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 152, "excluded_lines": 0}, "missing_lines": [59, 60, 61, 64, 65, 66, 67, 69, 70, 73, 74, 75, 77, 84, 85, 88, 89, 90, 91, 92, 93, 96, 99, 117, 118, 120, 121, 125, 130, 132, 133, 135, 136, 138, 142, 143, 144, 145, 146, 147, 148, 152, 153, 154, 159, 161, 174, 175, 177, 178, 185, 186, 187, 188, 189, 190, 191, 192, 194, 195, 199, 200, 201, 205, 206, 208, 209, 210, 211, 212, 213, 214, 216, 217, 222, 223, 224, 226, 227, 228, 230, 232, 233, 234, 237, 238, 239, 240, 241, 244, 245, 246, 248, 249, 251, 252, 253, 257, 258, 260, 261, 263, 272, 286, 287, 288, 290, 294, 296, 297, 298, 299, 300, 301, 302, 303, 305, 309, 310, 311, 312, 313, 315, 316, 317, 318, 319, 321, 322, 323, 324, 325, 327, 328, 329, 330, 331, 332, 336, 337, 338, 339, 341, 342, 343, 344, 348, 349, 350, 351, 352, 353], "excluded_lines": []}, "": {"executed_lines": [2, 7, 8, 9, 10, 11, 12, 13, 14, 15, 17, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 55, 56, 58, 79, 123, 127, 140, 150, 156, 180, 197, 203, 219, 255, 270, 292, 307, 334, 346], "summary": {"covered_lines": 57, "num_statements": 57, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}}}, "aretomo3_gui/data_management/manager.py": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 90, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 90, "excluded_lines": 0}, "missing_lines": [6, 7, 8, 9, 10, 11, 12, 13, 16, 17, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 33, 36, 37, 38, 40, 42, 43, 46, 58, 71, 83, 84, 86, 88, 89, 91, 92, 93, 96, 97, 98, 99, 100, 102, 103, 105, 121, 122, 124, 130, 131, 137, 138, 140, 142, 144, 145, 146, 147, 148, 149, 150, 151, 153, 155, 156, 158, 159, 161, 166, 167, 169, 171, 172, 174, 175, 177, 178, 179, 181, 182, 184, 185, 186, 194, 196, 197, 201], "excluded_lines": [], "functions": {"DataManager.__init__": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 2, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 2, "excluded_lines": 0}, "missing_lines": [37, 38], "excluded_lines": []}, "DataManager.init_database": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 7, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 7, "excluded_lines": 0}, "missing_lines": [42, 43, 46, 58, 71, 83, 84], "excluded_lines": []}, "DataManager.create_dataset": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 21, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 21, "excluded_lines": 0}, "missing_lines": [88, 89, 91, 92, 93, 96, 97, 98, 99, 100, 102, 103, 105, 121, 122, 124, 130, 131, 137, 138, 140], "excluded_lines": []}, "DataManager._calculate_checksum": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 8, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 8, "excluded_lines": 0}, "missing_lines": [144, 145, 146, 147, 148, 149, 150, 151], "excluded_lines": []}, "DataManager.add_processing_step": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 7, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 7, "excluded_lines": 0}, "missing_lines": [155, 156, 158, 159, 161, 166, 167], "excluded_lines": []}, "DataManager.search_datasets": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 15, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 15, "excluded_lines": 0}, "missing_lines": [171, 172, 174, 175, 177, 178, 179, 181, 182, 184, 185, 186, 194, 196, 197], "excluded_lines": []}, "": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 30, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 30, "excluded_lines": 0}, "missing_lines": [6, 7, 8, 9, 10, 11, 12, 13, 16, 17, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 33, 36, 40, 86, 142, 153, 169, 201], "excluded_lines": []}}, "classes": {"DatasetMetadata": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "DataManager": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 60, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 60, "excluded_lines": 0}, "missing_lines": [37, 38, 42, 43, 46, 58, 71, 83, 84, 88, 89, 91, 92, 93, 96, 97, 98, 99, 100, 102, 103, 105, 121, 122, 124, 130, 131, 137, 138, 140, 144, 145, 146, 147, 148, 149, 150, 151, 155, 156, 158, 159, 161, 166, 167, 171, 172, 174, 175, 177, 178, 179, 181, 182, 184, 185, 186, 194, 196, 197], "excluded_lines": []}, "": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 30, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 30, "excluded_lines": 0}, "missing_lines": [6, 7, 8, 9, 10, 11, 12, 13, 16, 17, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 33, 36, 40, 86, 142, 153, 169, 201], "excluded_lines": []}}}, "aretomo3_gui/formats/__init__.py": {"executed_lines": [1, 5, 7], "summary": {"covered_lines": 2, "num_statements": 2, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": [], "functions": {"": {"executed_lines": [1, 5, 7], "summary": {"covered_lines": 2, "num_statements": 2, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}}, "classes": {"": {"executed_lines": [1, 5, 7], "summary": {"covered_lines": 2, "num_statements": 2, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}}}, "aretomo3_gui/formats/format_manager.py": {"executed_lines": [2, 7, 8, 9, 10, 11, 12, 13, 15, 18, 19, 20, 25, 26, 27, 32, 33, 34, 39, 40, 41, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71, 74, 75, 77, 78, 82, 83, 87, 88, 93, 94, 96, 99, 134, 168, 169, 171, 174, 210, 241, 242, 244, 247, 287, 321, 322, 324, 325, 326, 329, 330, 332, 334, 336, 337, 338, 339, 340, 341, 343, 345, 378, 379, 381, 400, 414, 424, 428, 436, 441, 459], "summary": {"covered_lines": 80, "num_statements": 242, "percent_covered": 33.05785123966942, "percent_covered_display": "33", "missing_lines": 162, "excluded_lines": 12}, "missing_lines": [21, 22, 23, 28, 29, 30, 35, 36, 37, 42, 43, 44, 97, 101, 102, 104, 105, 106, 108, 128, 130, 131, 132, 136, 137, 139, 140, 142, 143, 145, 157, 158, 159, 160, 162, 164, 165, 172, 176, 177, 179, 180, 181, 183, 199, 200, 201, 202, 204, 206, 207, 208, 212, 213, 215, 216, 217, 228, 229, 230, 231, 232, 233, 235, 237, 238, 245, 249, 250, 252, 253, 255, 256, 258, 260, 261, 262, 263, 265, 267, 281, 283, 284, 285, 289, 290, 292, 293, 294, 305, 306, 307, 308, 309, 310, 311, 313, 315, 317, 318, 383, 385, 386, 389, 390, 391, 392, 393, 394, 395, 396, 398, 402, 404, 405, 406, 407, 408, 409, 410, 412, 416, 418, 419, 420, 422, 426, 430, 431, 432, 433, 434, 438, 439, 444, 445, 447, 448, 449, 450, 451, 452, 453, 454, 455, 457, 461, 469, 470, 471, 472, 474, 475, 477, 478, 479, 480, 481, 482, 483, 485, 487], "excluded_lines": [77, 78, 79, 80, 82, 83, 84, 85, 87, 88, 89, 90], "functions": {"FormatHandler.can_handle": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 2}, "missing_lines": [], "excluded_lines": [79, 80]}, "FormatHandler.read_metadata": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 2}, "missing_lines": [], "excluded_lines": [84, 85]}, "FormatHandler.validate_file": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 2}, "missing_lines": [], "excluded_lines": [89, 90]}, "MRCHandler.can_handle": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [97], "excluded_lines": []}, "MRCHandler.read_metadata": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 10, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 10, "excluded_lines": 0}, "missing_lines": [101, 102, 104, 105, 106, 108, 128, 130, 131, 132], "excluded_lines": []}, "MRCHandler.validate_file": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 14, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 14, "excluded_lines": 0}, "missing_lines": [136, 137, 139, 140, 142, 143, 145, 157, 158, 159, 160, 162, 164, 165], "excluded_lines": []}, "TIFFHandler.can_handle": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [172], "excluded_lines": []}, "TIFFHandler.read_metadata": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 14, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 14, "excluded_lines": 0}, "missing_lines": [176, 177, 179, 180, 181, 183, 199, 200, 201, 202, 204, 206, 207, 208], "excluded_lines": []}, "TIFFHandler.validate_file": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 14, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 14, "excluded_lines": 0}, "missing_lines": [212, 213, 215, 216, 217, 228, 229, 230, 231, 232, 233, 235, 237, 238], "excluded_lines": []}, "HDF5Handler.can_handle": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [245], "excluded_lines": []}, "HDF5Handler.read_metadata": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 13, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 13, "excluded_lines": 0}, "missing_lines": [249, 250, 252, 253, 255, 256, 258, 265, 267, 281, 283, 284, 285], "excluded_lines": []}, "HDF5Handler.read_metadata.find_datasets": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 4, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 4, "excluded_lines": 0}, "missing_lines": [260, 261, 262, 263], "excluded_lines": []}, "HDF5Handler.validate_file": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 10, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 10, "excluded_lines": 0}, "missing_lines": [289, 290, 292, 293, 294, 305, 313, 315, 317, 318], "excluded_lines": []}, "HDF5Handler.validate_file.check_dataset": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 6, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 6, "excluded_lines": 0}, "missing_lines": [306, 307, 308, 309, 310, 311], "excluded_lines": []}, "FormatManager.__init__": {"executed_lines": [325, 326, 329, 330, 332], "summary": {"covered_lines": 5, "num_statements": 5, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "FormatManager._register_handlers": {"executed_lines": [336, 337, 338, 339, 340, 341], "summary": {"covered_lines": 6, "num_statements": 6, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "FormatManager._initialize_format_info": {"executed_lines": [345, 378, 379], "summary": {"covered_lines": 3, "num_statements": 3, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "FormatManager.detect_format": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 12, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 12, "excluded_lines": 0}, "missing_lines": [383, 385, 386, 389, 390, 391, 392, 393, 394, 395, 396, 398], "excluded_lines": []}, "FormatManager.get_file_metadata": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 9, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 9, "excluded_lines": 0}, "missing_lines": [402, 404, 405, 406, 407, 408, 409, 410, 412], "excluded_lines": []}, "FormatManager.validate_file": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 5, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 5, "excluded_lines": 0}, "missing_lines": [416, 418, 419, 420, 422], "excluded_lines": []}, "FormatManager.get_supported_formats": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [426], "excluded_lines": []}, "FormatManager.get_supported_extensions": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 5, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 5, "excluded_lines": 0}, "missing_lines": [430, 431, 432, 433, 434], "excluded_lines": []}, "FormatManager.is_supported": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 2, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 2, "excluded_lines": 0}, "missing_lines": [438, 439], "excluded_lines": []}, "FormatManager.scan_directory": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 12, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 12, "excluded_lines": 0}, "missing_lines": [444, 445, 447, 448, 449, 450, 451, 452, 453, 454, 455, 457], "excluded_lines": []}, "FormatManager.get_format_statistics": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 16, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 16, "excluded_lines": 0}, "missing_lines": [461, 469, 470, 471, 472, 474, 475, 477, 478, 479, 480, 481, 482, 483, 485, 487], "excluded_lines": []}, "": {"executed_lines": [2, 7, 8, 9, 10, 11, 12, 13, 15, 18, 19, 20, 25, 26, 27, 32, 33, 34, 39, 40, 41, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71, 74, 75, 77, 78, 82, 83, 87, 88, 93, 94, 96, 99, 134, 168, 169, 171, 174, 210, 241, 242, 244, 247, 287, 321, 322, 324, 334, 343, 381, 400, 414, 424, 428, 436, 441, 459], "summary": {"covered_lines": 66, "num_statements": 78, "percent_covered": 84.61538461538461, "percent_covered_display": "85", "missing_lines": 12, "excluded_lines": 6}, "missing_lines": [21, 22, 23, 28, 29, 30, 35, 36, 37, 42, 43, 44], "excluded_lines": [77, 78, 82, 83, 87, 88]}}, "classes": {"FormatInfo": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "FileMetadata": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "FormatHandler": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 6}, "missing_lines": [], "excluded_lines": [79, 80, 84, 85, 89, 90]}, "MRCHandler": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 25, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 25, "excluded_lines": 0}, "missing_lines": [97, 101, 102, 104, 105, 106, 108, 128, 130, 131, 132, 136, 137, 139, 140, 142, 143, 145, 157, 158, 159, 160, 162, 164, 165], "excluded_lines": []}, "TIFFHandler": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 29, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 29, "excluded_lines": 0}, "missing_lines": [172, 176, 177, 179, 180, 181, 183, 199, 200, 201, 202, 204, 206, 207, 208, 212, 213, 215, 216, 217, 228, 229, 230, 231, 232, 233, 235, 237, 238], "excluded_lines": []}, "HDF5Handler": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 34, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 34, "excluded_lines": 0}, "missing_lines": [245, 249, 250, 252, 253, 255, 256, 258, 260, 261, 262, 263, 265, 267, 281, 283, 284, 285, 289, 290, 292, 293, 294, 305, 306, 307, 308, 309, 310, 311, 313, 315, 317, 318], "excluded_lines": []}, "FormatManager": {"executed_lines": [325, 326, 329, 330, 332, 336, 337, 338, 339, 340, 341, 345, 378, 379], "summary": {"covered_lines": 14, "num_statements": 76, "percent_covered": 18.42105263157895, "percent_covered_display": "18", "missing_lines": 62, "excluded_lines": 0}, "missing_lines": [383, 385, 386, 389, 390, 391, 392, 393, 394, 395, 396, 398, 402, 404, 405, 406, 407, 408, 409, 410, 412, 416, 418, 419, 420, 422, 426, 430, 431, 432, 433, 434, 438, 439, 444, 445, 447, 448, 449, 450, 451, 452, 453, 454, 455, 457, 461, 469, 470, 471, 472, 474, 475, 477, 478, 479, 480, 481, 482, 483, 485, 487], "excluded_lines": []}, "": {"executed_lines": [2, 7, 8, 9, 10, 11, 12, 13, 15, 18, 19, 20, 25, 26, 27, 32, 33, 34, 39, 40, 41, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71, 74, 75, 77, 78, 82, 83, 87, 88, 93, 94, 96, 99, 134, 168, 169, 171, 174, 210, 241, 242, 244, 247, 287, 321, 322, 324, 334, 343, 381, 400, 414, 424, 428, 436, 441, 459], "summary": {"covered_lines": 66, "num_statements": 78, "percent_covered": 84.61538461538461, "percent_covered_display": "85", "missing_lines": 12, "excluded_lines": 6}, "missing_lines": [21, 22, 23, 28, 29, 30, 35, 36, 37, 42, 43, 44], "excluded_lines": [77, 78, 82, 83, 87, 88]}}}, "aretomo3_gui/formats/manager.py": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 89, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 89, "excluded_lines": 12}, "missing_lines": [6, 7, 8, 9, 10, 11, 12, 13, 16, 35, 38, 40, 41, 42, 57, 59, 61, 62, 63, 64, 65, 67, 69, 70, 73, 76, 78, 80, 81, 84, 85, 88, 89, 92, 93, 94, 96, 98, 100, 101, 102, 103, 104, 106, 108, 109, 112, 115, 117, 118, 121, 122, 123, 124, 133, 134, 135, 136, 138, 140, 142, 144, 146, 147, 150, 153, 154, 165, 167, 168, 170, 171, 173, 174, 176, 178, 179, 181, 182, 184, 185, 187, 189, 192, 193, 195, 197, 199, 203], "excluded_lines": [19, 20, 21, 22, 24, 25, 26, 27, 29, 30, 31, 32], "functions": {"DataFormat.read": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 2}, "missing_lines": [], "excluded_lines": [21, 22]}, "DataFormat.write": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 2}, "missing_lines": [], "excluded_lines": [26, 27]}, "DataFormat.get_metadata": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 2}, "missing_lines": [], "excluded_lines": [31, 32]}, "MRCFormat.read": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 4, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 4, "excluded_lines": 0}, "missing_lines": [40, 41, 42, 57], "excluded_lines": []}, "MRCFormat.write": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 5, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 5, "excluded_lines": 0}, "missing_lines": [61, 62, 63, 64, 65], "excluded_lines": []}, "MRCFormat.get_metadata": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 2, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 2, "excluded_lines": 0}, "missing_lines": [69, 70], "excluded_lines": []}, "HDF5Format.read": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 11, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 11, "excluded_lines": 0}, "missing_lines": [78, 80, 81, 84, 85, 88, 89, 92, 93, 94, 96], "excluded_lines": []}, "HDF5Format.write": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 5, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 5, "excluded_lines": 0}, "missing_lines": [100, 101, 102, 103, 104], "excluded_lines": []}, "HDF5Format.get_metadata": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 2, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 2, "excluded_lines": 0}, "missing_lines": [108, 109], "excluded_lines": []}, "TIFFFormat.read": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 11, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 11, "excluded_lines": 0}, "missing_lines": [117, 118, 121, 122, 123, 124, 133, 134, 135, 136, 138], "excluded_lines": []}, "TIFFFormat.write": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [142], "excluded_lines": []}, "TIFFFormat.get_metadata": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 2, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 2, "excluded_lines": 0}, "missing_lines": [146, 147], "excluded_lines": []}, "FormatManager.__init__": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [154], "excluded_lines": []}, "FormatManager.read_file": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 6, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 6, "excluded_lines": 0}, "missing_lines": [167, 168, 170, 171, 173, 174], "excluded_lines": []}, "FormatManager.write_file": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 6, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 6, "excluded_lines": 0}, "missing_lines": [178, 179, 181, 182, 184, 185], "excluded_lines": []}, "FormatManager.convert_format": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 4, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 4, "excluded_lines": 0}, "missing_lines": [189, 192, 193, 195], "excluded_lines": []}, "FormatManager.get_supported_formats": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [199], "excluded_lines": []}, "": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 28, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 28, "excluded_lines": 6}, "missing_lines": [6, 7, 8, 9, 10, 11, 12, 13, 16, 35, 38, 59, 67, 73, 76, 98, 106, 112, 115, 140, 144, 150, 153, 165, 176, 187, 197, 203], "excluded_lines": [19, 20, 24, 25, 29, 30]}}, "classes": {"DataFormat": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 6}, "missing_lines": [], "excluded_lines": [21, 22, 26, 27, 31, 32]}, "MRCFormat": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 11, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 11, "excluded_lines": 0}, "missing_lines": [40, 41, 42, 57, 61, 62, 63, 64, 65, 69, 70], "excluded_lines": []}, "HDF5Format": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 18, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 18, "excluded_lines": 0}, "missing_lines": [78, 80, 81, 84, 85, 88, 89, 92, 93, 94, 96, 100, 101, 102, 103, 104, 108, 109], "excluded_lines": []}, "TIFFFormat": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 14, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 14, "excluded_lines": 0}, "missing_lines": [117, 118, 121, 122, 123, 124, 133, 134, 135, 136, 138, 142, 146, 147], "excluded_lines": []}, "FormatManager": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 18, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 18, "excluded_lines": 0}, "missing_lines": [154, 167, 168, 170, 171, 173, 174, 178, 179, 181, 182, 184, 185, 189, 192, 193, 195, 199], "excluded_lines": []}, "": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 28, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 28, "excluded_lines": 6}, "missing_lines": [6, 7, 8, 9, 10, 11, 12, 13, 16, 35, 38, 59, 67, 73, 76, 98, 106, 112, 115, 140, 144, 150, 153, 165, 176, 187, 197, 203], "excluded_lines": [19, 20, 24, 25, 29, 30]}}}, "aretomo3_gui/utils/__init__.py": {"executed_lines": [1, 6, 7, 11, 12, 16, 17, 23], "summary": {"covered_lines": 7, "num_statements": 15, "percent_covered": 46.666666666666664, "percent_covered_display": "47", "missing_lines": 8, "excluded_lines": 0}, "missing_lines": [8, 9, 13, 14, 18, 19, 20, 21], "excluded_lines": [], "functions": {"": {"executed_lines": [1, 6, 7, 11, 12, 16, 17, 23], "summary": {"covered_lines": 7, "num_statements": 15, "percent_covered": 46.666666666666664, "percent_covered_display": "47", "missing_lines": 8, "excluded_lines": 0}, "missing_lines": [8, 9, 13, 14, 18, 19, 20, 21], "excluded_lines": []}}, "classes": {"": {"executed_lines": [1, 6, 7, 11, 12, 16, 17, 23], "summary": {"covered_lines": 7, "num_statements": 15, "percent_covered": 46.666666666666664, "percent_covered_display": "47", "missing_lines": 8, "excluded_lines": 0}, "missing_lines": [8, 9, 13, 14, 18, 19, 20, 21], "excluded_lines": []}}}, "aretomo3_gui/utils/aretomo3_parser.py": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 665, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 665, "excluded_lines": 0}, "missing_lines": [7, 8, 9, 10, 11, 12, 14, 15, 17, 21, 24, 26, 27, 28, 30, 32, 33, 36, 39, 50, 51, 53, 54, 55, 57, 59, 69, 70, 71, 72, 73, 74, 76, 78, 80, 82, 83, 85, 86, 89, 90, 92, 95, 97, 98, 111, 112, 114, 116, 118, 120, 123, 126, 128, 131, 132, 133, 134, 135, 136, 139, 141, 158, 162, 163, 165, 167, 169, 171, 172, 174, 175, 178, 179, 181, 182, 184, 185, 187, 188, 190, 195, 197, 199, 202, 204, 205, 208, 209, 211, 212, 213, 214, 215, 216, 217, 219, 220, 223, 224, 225, 226, 227, 228, 229, 230, 232, 233, 234, 235, 236, 237, 238, 239, 240, 241, 242, 244, 246, 249, 253, 276, 280, 281, 283, 285, 287, 289, 290, 292, 293, 295, 297, 299, 300, 302, 303, 305, 307, 309, 310, 311, 312, 314, 315, 317, 321, 323, 325, 328, 331, 333, 336, 337, 338, 339, 340, 341, 342, 343, 346, 348, 373, 377, 378, 380, 382, 384, 386, 387, 388, 389, 391, 394, 396, 397, 399, 400, 402, 404, 408, 411, 418, 419, 420, 421, 422, 423, 424, 425, 427, 429, 431, 434, 444, 445, 446, 447, 449, 451, 453, 454, 455, 456, 461, 462, 463, 465, 467, 468, 469, 470, 471, 472, 473, 475, 477, 478, 479, 480, 485, 486, 487, 489, 491, 494, 503, 504, 505, 506, 507, 508, 509, 511, 513, 517, 518, 520, 521, 523, 525, 534, 535, 536, 537, 539, 540, 541, 542, 543, 547, 551, 553, 555, 557, 558, 559, 560, 561, 588, 591, 592, 593, 594, 595, 609, 612, 613, 614, 615, 616, 635, 638, 648, 649, 651, 657, 661, 662, 665, 666, 667, 668, 669, 670, 672, 675, 676, 677, 678, 679, 680, 682, 685, 686, 687, 688, 689, 690, 692, 694, 695, 696, 697, 698, 699, 700, 701, 703, 705, 707, 708, 709, 711, 713, 714, 716, 718, 720, 722, 724, 725, 726, 727, 730, 731, 732, 733, 736, 737, 738, 739, 742, 743, 744, 746, 748, 749, 752, 756, 758, 759, 760, 761, 763, 764, 769, 770, 773, 774, 776, 784, 792, 793, 794, 797, 804, 811, 812, 813, 814, 815, 818, 819, 820, 827, 828, 829, 831, 832, 833, 834, 835, 838, 839, 840, 841, 843, 844, 845, 848, 852, 853, 855, 863, 872, 873, 874, 875, 876, 877, 878, 881, 882, 883, 884, 894, 895, 896, 897, 900, 905, 906, 907, 908, 909, 910, 911, 912, 914, 915, 917, 918, 919, 920, 923, 925, 930, 932, 935, 936, 937, 939, 941, 943, 944, 948, 950, 951, 952, 953, 955, 956, 959, 960, 963, 964, 967, 968, 969, 971, 974, 977, 978, 979, 980, 981, 984, 985, 986, 987, 989, 990, 998, 999, 1000, 1001, 1002, 1005, 1006, 1007, 1008, 1010, 1011, 1012, 1013, 1014, 1015, 1017, 1018, 1019, 1021, 1023, 1024, 1025, 1026, 1029, 1030, 1031, 1038, 1041, 1042, 1043, 1044, 1045, 1047, 1048, 1050, 1051, 1052, 1053, 1056, 1057, 1058, 1059, 1060, 1064, 1065, 1066, 1068, 1070, 1073, 1078, 1083, 1089, 1090, 1098, 1106, 1115, 1116, 1117, 1118, 1119, 1120, 1121, 1122, 1124, 1127, 1128, 1129, 1131, 1135, 1136, 1137, 1141, 1143, 1144, 1145, 1146, 1148, 1149, 1152, 1153, 1156, 1157, 1158, 1159, 1162, 1166, 1168, 1169, 1170, 1171, 1174, 1177, 1184, 1187, 1188, 1189, 1190, 1192, 1193, 1194, 1195, 1196, 1199, 1200, 1207, 1210, 1211, 1212, 1213, 1214, 1217, 1225, 1229, 1230, 1231, 1232, 1233, 1236, 1239, 1240, 1243, 1244, 1245, 1246, 1247, 1248, 1250, 1251, 1253, 1254, 1255, 1256, 1257, 1260, 1261, 1262, 1263, 1265, 1266, 1268, 1270, 1271, 1273, 1275, 1276, 1277, 1278, 1280, 1281, 1282, 1285, 1286, 1291, 1297, 1305, 1313, 1322, 1323, 1324, 1325, 1326, 1327, 1328, 1329, 1331, 1334, 1335, 1336, 1338, 1340, 1341, 1342, 1346, 1348, 1349, 1350, 1352, 1355, 1356, 1363, 1364, 1365, 1372, 1375, 1376, 1389, 1390, 1391, 1394, 1395, 1398, 1399, 1400, 1401, 1408, 1410, 1419, 1422, 1423, 1424, 1425, 1426, 1432, 1438, 1440, 1443, 1444, 1445, 1447, 1449, 1450, 1451], "excluded_lines": [], "functions": {"AreTomo3ResultsParser.__init__": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 3, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 3, "excluded_lines": 0}, "missing_lines": [26, 27, 28], "excluded_lines": []}, "AreTomo3ResultsParser.parse_all_results": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 9, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 9, "excluded_lines": 0}, "missing_lines": [32, 33, 36, 39, 50, 51, 53, 54, 55], "excluded_lines": []}, "AreTomo3ResultsParser._find_result_files": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 8, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 8, "excluded_lines": 0}, "missing_lines": [59, 69, 70, 71, 72, 73, 74, 76], "excluded_lines": []}, "AreTomo3ResultsParser._parse_motion_files": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 14, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 14, "excluded_lines": 0}, "missing_lines": [80, 82, 83, 85, 86, 89, 90, 92, 95, 97, 98, 111, 112, 114], "excluded_lines": []}, "AreTomo3ResultsParser._parse_aretomo3_motion_csv": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 17, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 17, "excluded_lines": 0}, "missing_lines": [118, 120, 123, 126, 128, 131, 132, 133, 134, 135, 136, 139, 141, 158, 162, 163, 165], "excluded_lines": []}, "AreTomo3ResultsParser._parse_ctf_files": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 14, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 14, "excluded_lines": 0}, "missing_lines": [169, 171, 172, 174, 175, 178, 179, 181, 182, 184, 185, 187, 188, 190], "excluded_lines": []}, "AreTomo3ResultsParser._parse_aretomo3_ctf_txt": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 43, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 43, "excluded_lines": 0}, "missing_lines": [197, 199, 202, 204, 205, 208, 209, 211, 212, 213, 214, 215, 216, 217, 219, 220, 223, 224, 225, 226, 227, 228, 229, 230, 232, 233, 234, 235, 236, 237, 238, 239, 240, 241, 242, 244, 246, 249, 253, 276, 280, 281, 283], "excluded_lines": []}, "AreTomo3ResultsParser._parse_alignment_files": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 20, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 20, "excluded_lines": 0}, "missing_lines": [287, 289, 290, 292, 293, 295, 297, 299, 300, 302, 303, 305, 307, 309, 310, 311, 312, 314, 315, 317], "excluded_lines": []}, "AreTomo3ResultsParser._parse_aretomo3_alignment_csv": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 19, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 19, "excluded_lines": 0}, "missing_lines": [323, 325, 328, 331, 333, 336, 337, 338, 339, 340, 341, 342, 343, 346, 348, 373, 377, 378, 380], "excluded_lines": []}, "AreTomo3ResultsParser._parse_log_files": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 12, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 12, "excluded_lines": 0}, "missing_lines": [384, 386, 387, 388, 389, 391, 394, 396, 397, 399, 400, 402], "excluded_lines": []}, "AreTomo3ResultsParser._extract_motion_data": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 11, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 11, "excluded_lines": 0}, "missing_lines": [408, 411, 418, 419, 420, 421, 422, 423, 424, 425, 427], "excluded_lines": []}, "AreTomo3ResultsParser._extract_ctf_data": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 7, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 7, "excluded_lines": 0}, "missing_lines": [431, 434, 444, 445, 446, 447, 449], "excluded_lines": []}, "AreTomo3ResultsParser._parse_aln_file": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 7, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 7, "excluded_lines": 0}, "missing_lines": [453, 454, 455, 456, 461, 462, 463], "excluded_lines": []}, "AreTomo3ResultsParser._parse_xf_file": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 7, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 7, "excluded_lines": 0}, "missing_lines": [467, 468, 469, 470, 471, 472, 473], "excluded_lines": []}, "AreTomo3ResultsParser._parse_tlt_file": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 7, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 7, "excluded_lines": 0}, "missing_lines": [477, 478, 479, 480, 485, 486, 487], "excluded_lines": []}, "AreTomo3ResultsParser._extract_log_info": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 10, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 10, "excluded_lines": 0}, "missing_lines": [491, 494, 503, 504, 505, 506, 507, 508, 509, 511], "excluded_lines": []}, "AreTomo3ResultsParser._calculate_drift_rate": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 4, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 4, "excluded_lines": 0}, "missing_lines": [517, 518, 520, 521], "excluded_lines": []}, "AreTomo3ResultsParser._generate_summary": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 11, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 11, "excluded_lines": 0}, "missing_lines": [525, 534, 535, 536, 537, 539, 540, 541, 542, 543, 547], "excluded_lines": []}, "AreTomo3ResultsParser.extract_quality_metrics": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 24, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 24, "excluded_lines": 0}, "missing_lines": [553, 555, 557, 558, 559, 560, 561, 588, 591, 592, 593, 594, 595, 609, 612, 613, 614, 615, 616, 635, 638, 648, 649, 651], "excluded_lines": []}, "AreTomo3ResultsParser._calculate_overall_quality": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 36, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 36, "excluded_lines": 0}, "missing_lines": [661, 662, 665, 666, 667, 668, 669, 670, 672, 675, 676, 677, 678, 679, 680, 682, 685, 686, 687, 688, 689, 690, 692, 694, 695, 696, 697, 698, 699, 700, 701, 703, 705, 707, 708, 709], "excluded_lines": []}, "AreTomo3ResultsParser.generate_plots": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 25, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 25, "excluded_lines": 0}, "missing_lines": [713, 714, 716, 718, 720, 722, 724, 725, 726, 727, 730, 731, 732, 733, 736, 737, 738, 739, 742, 743, 744, 746, 748, 749, 752], "excluded_lines": []}, "AreTomo3ResultsParser._generate_motion_plot": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 86, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 86, "excluded_lines": 0}, "missing_lines": [758, 759, 760, 761, 763, 764, 769, 770, 773, 774, 776, 784, 792, 793, 794, 797, 804, 811, 812, 813, 814, 815, 818, 819, 820, 827, 828, 829, 831, 832, 833, 834, 835, 838, 839, 840, 841, 843, 844, 845, 848, 852, 853, 855, 863, 872, 873, 874, 875, 876, 877, 878, 881, 882, 883, 884, 894, 895, 896, 897, 900, 905, 906, 907, 908, 909, 910, 911, 912, 914, 915, 917, 918, 919, 920, 923, 925, 930, 932, 935, 936, 937, 939, 941, 943, 944], "excluded_lines": []}, "AreTomo3ResultsParser._generate_ctf_plot": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 97, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 97, "excluded_lines": 0}, "missing_lines": [950, 951, 952, 953, 955, 956, 959, 960, 963, 964, 967, 968, 969, 971, 974, 977, 978, 979, 980, 981, 984, 985, 986, 987, 989, 990, 998, 999, 1000, 1001, 1002, 1005, 1006, 1007, 1008, 1010, 1011, 1012, 1013, 1014, 1015, 1017, 1018, 1019, 1021, 1023, 1024, 1025, 1026, 1029, 1030, 1031, 1038, 1041, 1042, 1043, 1044, 1045, 1047, 1048, 1050, 1051, 1052, 1053, 1056, 1057, 1058, 1059, 1060, 1064, 1065, 1066, 1068, 1070, 1073, 1078, 1083, 1089, 1090, 1098, 1106, 1115, 1116, 1117, 1118, 1119, 1120, 1121, 1122, 1124, 1127, 1128, 1129, 1131, 1135, 1136, 1137], "excluded_lines": []}, "AreTomo3ResultsParser._generate_alignment_plot": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 100, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 100, "excluded_lines": 0}, "missing_lines": [1143, 1144, 1145, 1146, 1148, 1149, 1152, 1153, 1156, 1157, 1158, 1159, 1162, 1166, 1168, 1169, 1170, 1171, 1174, 1177, 1184, 1187, 1188, 1189, 1190, 1192, 1193, 1194, 1195, 1196, 1199, 1200, 1207, 1210, 1211, 1212, 1213, 1214, 1217, 1225, 1229, 1230, 1231, 1232, 1233, 1236, 1239, 1240, 1243, 1244, 1245, 1246, 1247, 1248, 1250, 1251, 1253, 1254, 1255, 1256, 1257, 1260, 1261, 1262, 1263, 1265, 1266, 1268, 1270, 1271, 1273, 1275, 1276, 1277, 1278, 1280, 1281, 1282, 1285, 1286, 1291, 1297, 1305, 1313, 1322, 1323, 1324, 1325, 1326, 1327, 1328, 1329, 1331, 1334, 1335, 1336, 1338, 1340, 1341, 1342], "excluded_lines": []}, "AreTomo3ResultsParser._generate_summary_plot": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 39, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 39, "excluded_lines": 0}, "missing_lines": [1348, 1349, 1350, 1352, 1355, 1356, 1363, 1364, 1365, 1372, 1375, 1376, 1389, 1390, 1391, 1394, 1395, 1398, 1399, 1400, 1401, 1408, 1410, 1419, 1422, 1423, 1424, 1425, 1426, 1432, 1438, 1440, 1443, 1444, 1445, 1447, 1449, 1450, 1451], "excluded_lines": []}, "": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 35, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 35, "excluded_lines": 0}, "missing_lines": [7, 8, 9, 10, 11, 12, 14, 15, 17, 21, 24, 30, 57, 78, 116, 167, 195, 285, 321, 382, 404, 429, 451, 465, 475, 489, 513, 523, 551, 657, 711, 756, 948, 1141, 1346], "excluded_lines": []}}, "classes": {"AreTomo3ResultsParser": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 630, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 630, "excluded_lines": 0}, "missing_lines": [26, 27, 28, 32, 33, 36, 39, 50, 51, 53, 54, 55, 59, 69, 70, 71, 72, 73, 74, 76, 80, 82, 83, 85, 86, 89, 90, 92, 95, 97, 98, 111, 112, 114, 118, 120, 123, 126, 128, 131, 132, 133, 134, 135, 136, 139, 141, 158, 162, 163, 165, 169, 171, 172, 174, 175, 178, 179, 181, 182, 184, 185, 187, 188, 190, 197, 199, 202, 204, 205, 208, 209, 211, 212, 213, 214, 215, 216, 217, 219, 220, 223, 224, 225, 226, 227, 228, 229, 230, 232, 233, 234, 235, 236, 237, 238, 239, 240, 241, 242, 244, 246, 249, 253, 276, 280, 281, 283, 287, 289, 290, 292, 293, 295, 297, 299, 300, 302, 303, 305, 307, 309, 310, 311, 312, 314, 315, 317, 323, 325, 328, 331, 333, 336, 337, 338, 339, 340, 341, 342, 343, 346, 348, 373, 377, 378, 380, 384, 386, 387, 388, 389, 391, 394, 396, 397, 399, 400, 402, 408, 411, 418, 419, 420, 421, 422, 423, 424, 425, 427, 431, 434, 444, 445, 446, 447, 449, 453, 454, 455, 456, 461, 462, 463, 467, 468, 469, 470, 471, 472, 473, 477, 478, 479, 480, 485, 486, 487, 491, 494, 503, 504, 505, 506, 507, 508, 509, 511, 517, 518, 520, 521, 525, 534, 535, 536, 537, 539, 540, 541, 542, 543, 547, 553, 555, 557, 558, 559, 560, 561, 588, 591, 592, 593, 594, 595, 609, 612, 613, 614, 615, 616, 635, 638, 648, 649, 651, 661, 662, 665, 666, 667, 668, 669, 670, 672, 675, 676, 677, 678, 679, 680, 682, 685, 686, 687, 688, 689, 690, 692, 694, 695, 696, 697, 698, 699, 700, 701, 703, 705, 707, 708, 709, 713, 714, 716, 718, 720, 722, 724, 725, 726, 727, 730, 731, 732, 733, 736, 737, 738, 739, 742, 743, 744, 746, 748, 749, 752, 758, 759, 760, 761, 763, 764, 769, 770, 773, 774, 776, 784, 792, 793, 794, 797, 804, 811, 812, 813, 814, 815, 818, 819, 820, 827, 828, 829, 831, 832, 833, 834, 835, 838, 839, 840, 841, 843, 844, 845, 848, 852, 853, 855, 863, 872, 873, 874, 875, 876, 877, 878, 881, 882, 883, 884, 894, 895, 896, 897, 900, 905, 906, 907, 908, 909, 910, 911, 912, 914, 915, 917, 918, 919, 920, 923, 925, 930, 932, 935, 936, 937, 939, 941, 943, 944, 950, 951, 952, 953, 955, 956, 959, 960, 963, 964, 967, 968, 969, 971, 974, 977, 978, 979, 980, 981, 984, 985, 986, 987, 989, 990, 998, 999, 1000, 1001, 1002, 1005, 1006, 1007, 1008, 1010, 1011, 1012, 1013, 1014, 1015, 1017, 1018, 1019, 1021, 1023, 1024, 1025, 1026, 1029, 1030, 1031, 1038, 1041, 1042, 1043, 1044, 1045, 1047, 1048, 1050, 1051, 1052, 1053, 1056, 1057, 1058, 1059, 1060, 1064, 1065, 1066, 1068, 1070, 1073, 1078, 1083, 1089, 1090, 1098, 1106, 1115, 1116, 1117, 1118, 1119, 1120, 1121, 1122, 1124, 1127, 1128, 1129, 1131, 1135, 1136, 1137, 1143, 1144, 1145, 1146, 1148, 1149, 1152, 1153, 1156, 1157, 1158, 1159, 1162, 1166, 1168, 1169, 1170, 1171, 1174, 1177, 1184, 1187, 1188, 1189, 1190, 1192, 1193, 1194, 1195, 1196, 1199, 1200, 1207, 1210, 1211, 1212, 1213, 1214, 1217, 1225, 1229, 1230, 1231, 1232, 1233, 1236, 1239, 1240, 1243, 1244, 1245, 1246, 1247, 1248, 1250, 1251, 1253, 1254, 1255, 1256, 1257, 1260, 1261, 1262, 1263, 1265, 1266, 1268, 1270, 1271, 1273, 1275, 1276, 1277, 1278, 1280, 1281, 1282, 1285, 1286, 1291, 1297, 1305, 1313, 1322, 1323, 1324, 1325, 1326, 1327, 1328, 1329, 1331, 1334, 1335, 1336, 1338, 1340, 1341, 1342, 1348, 1349, 1350, 1352, 1355, 1356, 1363, 1364, 1365, 1372, 1375, 1376, 1389, 1390, 1391, 1394, 1395, 1398, 1399, 1400, 1401, 1408, 1410, 1419, 1422, 1423, 1424, 1425, 1426, 1432, 1438, 1440, 1443, 1444, 1445, 1447, 1449, 1450, 1451], "excluded_lines": []}, "": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 35, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 35, "excluded_lines": 0}, "missing_lines": [7, 8, 9, 10, 11, 12, 14, 15, 17, 21, 24, 30, 57, 78, 116, 167, 195, 285, 321, 382, 404, 429, 451, 465, 475, 489, 513, 523, 551, 657, 711, 756, 948, 1141, 1346], "excluded_lines": []}}}, "aretomo3_gui/utils/documentation_generator.py": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 219, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 219, "excluded_lines": 3}, "missing_lines": [7, 8, 9, 10, 11, 12, 13, 14, 15, 17, 20, 21, 24, 25, 26, 27, 28, 29, 35, 41, 43, 44, 47, 48, 49, 50, 53, 57, 59, 61, 63, 65, 66, 67, 68, 69, 72, 74, 75, 77, 78, 79, 81, 83, 85, 88, 89, 90, 93, 94, 95, 97, 99, 101, 103, 104, 105, 107, 108, 109, 110, 111, 112, 114, 116, 120, 121, 122, 125, 128, 129, 130, 133, 134, 139, 140, 141, 142, 143, 144, 147, 148, 149, 151, 153, 154, 155, 157, 161, 162, 163, 165, 170, 171, 172, 173, 174, 175, 176, 178, 180, 184, 185, 186, 189, 191, 193, 197, 199, 202, 203, 206, 207, 208, 209, 210, 213, 214, 216, 218, 220, 223, 224, 226, 228, 231, 233, 234, 240, 241, 244, 246, 249, 250, 251, 253, 255, 256, 258, 262, 265, 266, 269, 270, 272, 273, 276, 277, 279, 280, 283, 284, 286, 292, 294, 296, 332, 333, 334, 339, 340, 341, 343, 344, 347, 348, 349, 351, 355, 357, 359, 421, 422, 424, 428, 430, 432, 502, 503, 505, 508, 510, 544, 545, 547, 549, 551, 552, 554, 555, 557, 559, 561, 562, 564, 565, 567, 569, 571, 572, 574, 575, 577, 579, 581, 584, 585, 588, 589, 592, 593, 596, 597, 599, 603, 606, 608, 609, 611], "excluded_lines": [614, 616, 617], "functions": {"DocumentationGenerator.__init__": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 8, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 8, "excluded_lines": 0}, "missing_lines": [43, 44, 47, 48, 49, 50, 53, 57], "excluded_lines": []}, "DocumentationGenerator.generate_full_documentation": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 13, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 13, "excluded_lines": 0}, "missing_lines": [61, 63, 65, 66, 67, 68, 69, 72, 74, 75, 77, 78, 79], "excluded_lines": []}, "DocumentationGenerator._generate_api_documentation": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 9, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 9, "excluded_lines": 0}, "missing_lines": [83, 85, 88, 89, 90, 93, 94, 95, 97], "excluded_lines": []}, "DocumentationGenerator._scan_python_modules": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 11, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 11, "excluded_lines": 0}, "missing_lines": [101, 103, 104, 105, 107, 108, 109, 110, 111, 112, 114], "excluded_lines": []}, "DocumentationGenerator._extract_module_documentation": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 22, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 22, "excluded_lines": 0}, "missing_lines": [120, 121, 122, 125, 128, 129, 130, 133, 134, 139, 140, 141, 142, 143, 144, 147, 148, 149, 151, 153, 154, 155], "excluded_lines": []}, "DocumentationGenerator._extract_class_documentation": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 12, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 12, "excluded_lines": 0}, "missing_lines": [161, 162, 163, 165, 170, 171, 172, 173, 174, 175, 176, 178], "excluded_lines": []}, "DocumentationGenerator._extract_function_documentation": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 6, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 6, "excluded_lines": 0}, "missing_lines": [184, 185, 186, 189, 191, 193], "excluded_lines": []}, "DocumentationGenerator._get_function_signature": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 11, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 11, "excluded_lines": 0}, "missing_lines": [199, 202, 203, 206, 207, 208, 209, 210, 213, 214, 216], "excluded_lines": []}, "DocumentationGenerator._generate_parameter_documentation": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 18, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 18, "excluded_lines": 0}, "missing_lines": [220, 223, 224, 226, 228, 231, 233, 234, 240, 241, 244, 246, 249, 250, 251, 253, 255, 256], "excluded_lines": []}, "DocumentationGenerator._format_parameter_documentation": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 14, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 14, "excluded_lines": 0}, "missing_lines": [262, 265, 266, 269, 270, 272, 273, 276, 277, 279, 280, 283, 284, 286], "excluded_lines": []}, "DocumentationGenerator._generate_workflow_documentation": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 14, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 14, "excluded_lines": 0}, "missing_lines": [294, 296, 332, 333, 334, 339, 340, 341, 343, 344, 347, 348, 349, 351], "excluded_lines": []}, "DocumentationGenerator._generate_user_guide": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 5, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 5, "excluded_lines": 0}, "missing_lines": [357, 359, 421, 422, 424], "excluded_lines": []}, "DocumentationGenerator._generate_developer_guide": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 5, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 5, "excluded_lines": 0}, "missing_lines": [430, 432, 502, 503, 505], "excluded_lines": []}, "DocumentationGenerator._generate_index": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 4, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 4, "excluded_lines": 0}, "missing_lines": [510, 544, 545, 547], "excluded_lines": []}, "DocumentationGenerator._format_api_documentation": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 5, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 5, "excluded_lines": 0}, "missing_lines": [551, 552, 554, 555, 557], "excluded_lines": []}, "DocumentationGenerator._format_parameter_sections": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 5, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 5, "excluded_lines": 0}, "missing_lines": [561, 562, 564, 565, 567], "excluded_lines": []}, "DocumentationGenerator._format_workflow_sections": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 5, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 5, "excluded_lines": 0}, "missing_lines": [571, 572, 574, 575, 577], "excluded_lines": []}, "DocumentationGenerator._format_section": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 10, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 10, "excluded_lines": 0}, "missing_lines": [581, 584, 585, 588, 589, 592, 593, 596, 597, 599], "excluded_lines": []}, "generate_documentation": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 3, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 3, "excluded_lines": 0}, "missing_lines": [608, 609, 611], "excluded_lines": []}, "": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 39, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 39, "excluded_lines": 3}, "missing_lines": [7, 8, 9, 10, 11, 12, 13, 14, 15, 17, 20, 21, 24, 25, 26, 27, 28, 29, 35, 41, 59, 81, 99, 116, 157, 180, 197, 218, 258, 292, 355, 428, 508, 549, 559, 569, 579, 603, 606], "excluded_lines": [614, 616, 617]}}, "classes": {"DocumentationSection": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "DocumentationGenerator": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 177, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 177, "excluded_lines": 0}, "missing_lines": [43, 44, 47, 48, 49, 50, 53, 57, 61, 63, 65, 66, 67, 68, 69, 72, 74, 75, 77, 78, 79, 83, 85, 88, 89, 90, 93, 94, 95, 97, 101, 103, 104, 105, 107, 108, 109, 110, 111, 112, 114, 120, 121, 122, 125, 128, 129, 130, 133, 134, 139, 140, 141, 142, 143, 144, 147, 148, 149, 151, 153, 154, 155, 161, 162, 163, 165, 170, 171, 172, 173, 174, 175, 176, 178, 184, 185, 186, 189, 191, 193, 199, 202, 203, 206, 207, 208, 209, 210, 213, 214, 216, 220, 223, 224, 226, 228, 231, 233, 234, 240, 241, 244, 246, 249, 250, 251, 253, 255, 256, 262, 265, 266, 269, 270, 272, 273, 276, 277, 279, 280, 283, 284, 286, 294, 296, 332, 333, 334, 339, 340, 341, 343, 344, 347, 348, 349, 351, 357, 359, 421, 422, 424, 430, 432, 502, 503, 505, 510, 544, 545, 547, 551, 552, 554, 555, 557, 561, 562, 564, 565, 567, 571, 572, 574, 575, 577, 581, 584, 585, 588, 589, 592, 593, 596, 597, 599], "excluded_lines": []}, "": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 42, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 42, "excluded_lines": 3}, "missing_lines": [7, 8, 9, 10, 11, 12, 13, 14, 15, 17, 20, 21, 24, 25, 26, 27, 28, 29, 35, 41, 59, 81, 99, 116, 157, 180, 197, 218, 258, 292, 355, 428, 508, 549, 559, 569, 579, 603, 606, 608, 609, 611], "excluded_lines": [614, 616, 617]}}}, "aretomo3_gui/utils/eer_reader.py": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 217, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 217, "excluded_lines": 20}, "missing_lines": [9, 10, 11, 12, 13, 15, 16, 18, 19, 20, 21, 23, 24, 26, 27, 28, 29, 31, 34, 37, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 54, 56, 73, 75, 88, 98, 99, 100, 102, 103, 104, 106, 108, 109, 112, 113, 114, 115, 116, 119, 122, 123, 124, 126, 127, 129, 130, 131, 138, 142, 144, 145, 148, 149, 150, 151, 152, 155, 156, 158, 159, 160, 161, 162, 163, 164, 165, 168, 169, 170, 171, 173, 174, 175, 177, 178, 179, 180, 183, 184, 185, 186, 188, 190, 191, 193, 198, 200, 202, 203, 206, 208, 209, 210, 211, 212, 215, 216, 217, 218, 220, 221, 223, 226, 228, 229, 230, 232, 234, 239, 241, 242, 243, 245, 246, 249, 257, 258, 259, 260, 261, 264, 266, 267, 269, 276, 278, 280, 291, 293, 294, 295, 296, 297, 298, 299, 302, 303, 304, 306, 307, 308, 310, 311, 312, 314, 315, 316, 318, 319, 320, 322, 323, 324, 326, 327, 328, 330, 331, 332, 334, 335, 337, 340, 350, 359, 360, 361, 362, 363, 364, 367, 368, 369, 370, 372, 373, 375, 378, 388, 390, 391, 394, 395, 398, 399, 400, 402, 403, 404, 407, 409, 410, 414, 421, 422, 423, 424, 427, 434, 435, 436, 437], "excluded_lines": [441, 442, 444, 445, 446, 448, 450, 451, 454, 455, 456, 457, 459, 460, 461, 462, 463, 464, 465, 466], "functions": {"EERMetadata.__init__": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 14, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 14, "excluded_lines": 0}, "missing_lines": [39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52], "excluded_lines": []}, "EERMetadata.to_dict": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [56], "excluded_lines": []}, "EERMetadata.__str__": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [75], "excluded_lines": []}, "read_eer_metadata": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 23, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 23, "excluded_lines": 0}, "missing_lines": [98, 99, 100, 102, 103, 104, 106, 108, 109, 112, 113, 114, 115, 116, 119, 122, 123, 124, 126, 127, 129, 130, 131], "excluded_lines": []}, "_extract_tiff_metadata": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 37, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 37, "excluded_lines": 0}, "missing_lines": [142, 144, 145, 148, 149, 150, 151, 152, 155, 156, 158, 159, 160, 161, 162, 163, 164, 165, 168, 169, 170, 171, 173, 174, 175, 177, 178, 179, 180, 183, 184, 185, 186, 188, 190, 191, 193], "excluded_lines": []}, "_extract_xml_metadata": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 16, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 16, "excluded_lines": 0}, "missing_lines": [200, 202, 203, 206, 208, 209, 210, 211, 212, 215, 216, 217, 218, 220, 221, 223], "excluded_lines": []}, "_parse_xml_metadata": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 16, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 16, "excluded_lines": 0}, "missing_lines": [228, 229, 230, 232, 234, 239, 249, 257, 258, 259, 260, 261, 264, 266, 267, 269], "excluded_lines": []}, "_parse_xml_metadata.get_nested_value": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 5, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 5, "excluded_lines": 0}, "missing_lines": [241, 242, 243, 245, 246], "excluded_lines": []}, "_extract_acquisition_data": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 30, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 30, "excluded_lines": 0}, "missing_lines": [278, 280, 291, 302, 303, 304, 306, 307, 308, 310, 311, 312, 314, 315, 316, 318, 319, 320, 322, 323, 324, 326, 327, 328, 330, 331, 332, 334, 335, 337], "excluded_lines": []}, "_extract_acquisition_data.find_value": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 7, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 7, "excluded_lines": 0}, "missing_lines": [293, 294, 295, 296, 297, 298, 299], "excluded_lines": []}, "get_eer_info": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 14, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 14, "excluded_lines": 0}, "missing_lines": [350, 359, 360, 361, 362, 363, 364, 367, 368, 369, 370, 372, 373, 375], "excluded_lines": []}, "is_eer_file": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 14, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 14, "excluded_lines": 0}, "missing_lines": [388, 390, 391, 394, 395, 398, 399, 400, 402, 403, 404, 407, 409, 410], "excluded_lines": []}, "read_eer_dimensions": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 4, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 4, "excluded_lines": 0}, "missing_lines": [421, 422, 423, 424], "excluded_lines": []}, "read_eer_pixel_size": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 4, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 4, "excluded_lines": 0}, "missing_lines": [434, 435, 436, 437], "excluded_lines": []}, "": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 31, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 31, "excluded_lines": 20}, "missing_lines": [9, 10, 11, 12, 13, 15, 16, 18, 19, 20, 21, 23, 24, 26, 27, 28, 29, 31, 34, 37, 54, 73, 88, 138, 198, 226, 276, 340, 378, 414, 427], "excluded_lines": [441, 442, 444, 445, 446, 448, 450, 451, 454, 455, 456, 457, 459, 460, 461, 462, 463, 464, 465, 466]}}, "classes": {"EERMetadata": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 16, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 16, "excluded_lines": 0}, "missing_lines": [39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 56, 75], "excluded_lines": []}, "": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 201, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 201, "excluded_lines": 20}, "missing_lines": [9, 10, 11, 12, 13, 15, 16, 18, 19, 20, 21, 23, 24, 26, 27, 28, 29, 31, 34, 37, 54, 73, 88, 98, 99, 100, 102, 103, 104, 106, 108, 109, 112, 113, 114, 115, 116, 119, 122, 123, 124, 126, 127, 129, 130, 131, 138, 142, 144, 145, 148, 149, 150, 151, 152, 155, 156, 158, 159, 160, 161, 162, 163, 164, 165, 168, 169, 170, 171, 173, 174, 175, 177, 178, 179, 180, 183, 184, 185, 186, 188, 190, 191, 193, 198, 200, 202, 203, 206, 208, 209, 210, 211, 212, 215, 216, 217, 218, 220, 221, 223, 226, 228, 229, 230, 232, 234, 239, 241, 242, 243, 245, 246, 249, 257, 258, 259, 260, 261, 264, 266, 267, 269, 276, 278, 280, 291, 293, 294, 295, 296, 297, 298, 299, 302, 303, 304, 306, 307, 308, 310, 311, 312, 314, 315, 316, 318, 319, 320, 322, 323, 324, 326, 327, 328, 330, 331, 332, 334, 335, 337, 340, 350, 359, 360, 361, 362, 363, 364, 367, 368, 369, 370, 372, 373, 375, 378, 388, 390, 391, 394, 395, 398, 399, 400, 402, 403, 404, 407, 409, 410, 414, 421, 422, 423, 424, 427, 434, 435, 436, 437], "excluded_lines": [441, 442, 444, 445, 446, 448, 450, 451, 454, 455, 456, 457, 459, 460, 461, 462, 463, 464, 465, 466]}}}, "aretomo3_gui/utils/eer_reader_new.py": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 200, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 200, "excluded_lines": 9}, "missing_lines": [9, 10, 11, 13, 15, 16, 17, 19, 20, 21, 22, 24, 27, 30, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 51, 53, 74, 77, 80, 88, 90, 91, 92, 97, 99, 101, 103, 113, 114, 115, 120, 133, 134, 135, 137, 138, 139, 141, 142, 143, 144, 145, 147, 149, 150, 152, 153, 155, 156, 157, 158, 159, 160, 161, 162, 164, 165, 167, 171, 172, 173, 174, 176, 186, 191, 204, 205, 207, 210, 211, 212, 215, 218, 220, 221, 222, 223, 226, 227, 230, 231, 233, 234, 237, 241, 246, 247, 250, 251, 254, 255, 258, 259, 262, 263, 266, 267, 268, 269, 272, 273, 274, 275, 278, 279, 282, 283, 286, 287, 290, 291, 293, 295, 296, 298, 301, 311, 312, 321, 322, 323, 326, 327, 329, 330, 333, 334, 337, 338, 340, 341, 342, 344, 346, 347, 348, 356, 363, 364, 366, 367, 368, 369, 371, 372, 373, 374, 375, 377, 378, 379, 380, 383, 386, 391, 392, 393, 394, 396, 397, 399, 402, 403, 404, 407, 408, 410, 414, 417, 420, 421, 422, 425, 427, 430, 432, 435, 437, 440, 442, 445, 447], "excluded_lines": [451, 452, 454, 455, 456, 457, 459, 460, 461], "functions": {"EERHeader.__init__": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 18, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 18, "excluded_lines": 0}, "missing_lines": [32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49], "excluded_lines": []}, "EERHeader.to_dict": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [53], "excluded_lines": []}, "EERReader.__init__": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 4, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 4, "excluded_lines": 0}, "missing_lines": [90, 91, 92, 97], "excluded_lines": []}, "EERReader.is_available": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [101], "excluded_lines": []}, "EERReader.can_read_file": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 3, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 3, "excluded_lines": 0}, "missing_lines": [113, 114, 115], "excluded_lines": []}, "EERReader.read_eer_metadata": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 31, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 31, "excluded_lines": 0}, "missing_lines": [133, 134, 135, 137, 138, 139, 141, 142, 143, 144, 145, 147, 149, 150, 152, 153, 155, 156, 157, 158, 159, 160, 161, 162, 164, 165, 167, 171, 172, 173, 174], "excluded_lines": []}, "EERReader.read_metadata": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [186], "excluded_lines": []}, "EERReader.read_eer_header": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 50, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 50, "excluded_lines": 0}, "missing_lines": [204, 205, 207, 210, 211, 212, 215, 218, 220, 221, 222, 223, 226, 227, 230, 231, 233, 234, 237, 241, 246, 247, 250, 251, 254, 255, 258, 259, 262, 263, 266, 267, 268, 269, 272, 273, 274, 275, 278, 279, 282, 283, 286, 287, 290, 291, 293, 295, 296, 298], "excluded_lines": []}, "EERReader.get_eer_info": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 20, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 20, "excluded_lines": 0}, "missing_lines": [311, 312, 321, 322, 323, 326, 327, 329, 330, 333, 334, 337, 338, 340, 341, 342, 344, 346, 347, 348], "excluded_lines": []}, "EERReader.print_eer_metadata": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 30, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 30, "excluded_lines": 0}, "missing_lines": [363, 364, 366, 367, 368, 369, 371, 372, 373, 374, 375, 377, 378, 379, 380, 383, 386, 391, 392, 393, 394, 396, 397, 399, 402, 403, 404, 407, 408, 410], "excluded_lines": []}, "get_eer_reader": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 3, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 3, "excluded_lines": 0}, "missing_lines": [420, 421, 422], "excluded_lines": []}, "read_eer_header": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [427], "excluded_lines": []}, "read_eer_metadata": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [432], "excluded_lines": []}, "get_eer_info": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [437], "excluded_lines": []}, "is_eer_supported": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [442], "excluded_lines": []}, "print_eer_metadata": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [447], "excluded_lines": []}, "": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 33, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 33, "excluded_lines": 9}, "missing_lines": [9, 10, 11, 13, 15, 16, 17, 19, 20, 21, 22, 24, 27, 30, 51, 74, 77, 80, 88, 99, 103, 120, 176, 191, 301, 356, 414, 417, 425, 430, 435, 440, 445], "excluded_lines": [451, 452, 454, 455, 456, 457, 459, 460, 461]}}, "classes": {"EERHeader": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 19, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 19, "excluded_lines": 0}, "missing_lines": [32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 53], "excluded_lines": []}, "EERReaderError": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "EERReader": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 140, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 140, "excluded_lines": 0}, "missing_lines": [90, 91, 92, 97, 101, 113, 114, 115, 133, 134, 135, 137, 138, 139, 141, 142, 143, 144, 145, 147, 149, 150, 152, 153, 155, 156, 157, 158, 159, 160, 161, 162, 164, 165, 167, 171, 172, 173, 174, 186, 204, 205, 207, 210, 211, 212, 215, 218, 220, 221, 222, 223, 226, 227, 230, 231, 233, 234, 237, 241, 246, 247, 250, 251, 254, 255, 258, 259, 262, 263, 266, 267, 268, 269, 272, 273, 274, 275, 278, 279, 282, 283, 286, 287, 290, 291, 293, 295, 296, 298, 311, 312, 321, 322, 323, 326, 327, 329, 330, 333, 334, 337, 338, 340, 341, 342, 344, 346, 347, 348, 363, 364, 366, 367, 368, 369, 371, 372, 373, 374, 375, 377, 378, 379, 380, 383, 386, 391, 392, 393, 394, 396, 397, 399, 402, 403, 404, 407, 408, 410], "excluded_lines": []}, "": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 41, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 41, "excluded_lines": 9}, "missing_lines": [9, 10, 11, 13, 15, 16, 17, 19, 20, 21, 22, 24, 27, 30, 51, 74, 77, 80, 88, 99, 103, 120, 176, 191, 301, 356, 414, 417, 420, 421, 422, 425, 427, 430, 432, 435, 437, 440, 442, 445, 447], "excluded_lines": [451, 452, 454, 455, 456, 457, 459, 460, 461]}}}, "aretomo3_gui/utils/export_functions.py": {"executed_lines": [1, 2, 3, 4, 5, 7, 8, 9, 10, 11, 13, 16, 66, 163, 232, 333, 403, 430, 446, 459, 478], "summary": {"covered_lines": 21, "num_statements": 277, "percent_covered": 7.581227436823105, "percent_covered_display": "8", "missing_lines": 256, "excluded_lines": 0}, "missing_lines": [18, 19, 22, 23, 24, 27, 29, 31, 33, 34, 37, 40, 43, 46, 47, 50, 53, 55, 56, 58, 59, 60, 61, 68, 69, 72, 73, 74, 75, 79, 80, 81, 82, 83, 85, 87, 90, 91, 92, 93, 96, 97, 98, 99, 103, 106, 108, 124, 126, 127, 134, 135, 136, 144, 145, 151, 152, 154, 155, 156, 157, 165, 166, 170, 171, 172, 173, 174, 177, 178, 179, 180, 183, 184, 185, 187, 188, 189, 190, 191, 192, 193, 194, 195, 196, 197, 198, 199, 200, 203, 204, 205, 207, 210, 214, 218, 220, 222, 223, 224, 225, 234, 235, 238, 240, 242, 243, 244, 245, 246, 248, 250, 253, 256, 258, 259, 260, 261, 265, 268, 269, 270, 271, 272, 275, 276, 277, 278, 279, 282, 283, 284, 287, 288, 291, 292, 293, 296, 299, 300, 301, 306, 307, 310, 311, 314, 315, 316, 318, 319, 320, 322, 323, 325, 326, 327, 328, 335, 336, 339, 340, 343, 344, 345, 348, 350, 352, 376, 377, 378, 379, 382, 383, 384, 385, 386, 387, 389, 393, 395, 397, 398, 399, 400, 415, 416, 417, 418, 419, 420, 421, 423, 424, 425, 426, 427, 432, 434, 436, 437, 438, 439, 440, 441, 442, 443, 448, 450, 452, 453, 454, 455, 456, 461, 463, 465, 466, 467, 468, 469, 470, 471, 472, 491, 493, 495, 496, 498, 500, 501, 503, 504, 505, 508, 509, 510, 511, 513, 516, 517, 518, 520, 521, 523, 524, 525, 526, 527, 528, 530, 531, 533, 534, 536, 537, 538], "excluded_lines": [], "functions": {"export_to_mrc": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 23, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 23, "excluded_lines": 0}, "missing_lines": [18, 19, 22, 23, 24, 27, 29, 31, 33, 34, 37, 40, 43, 46, 47, 50, 53, 55, 56, 58, 59, 60, 61], "excluded_lines": []}, "export_to_tiff": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 38, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 38, "excluded_lines": 0}, "missing_lines": [68, 69, 72, 73, 74, 75, 79, 80, 81, 82, 83, 85, 87, 90, 91, 92, 93, 96, 97, 98, 99, 103, 106, 108, 124, 126, 127, 134, 135, 136, 144, 145, 151, 152, 154, 155, 156, 157], "excluded_lines": []}, "export_to_relion": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 40, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 40, "excluded_lines": 0}, "missing_lines": [165, 166, 170, 171, 172, 173, 174, 177, 178, 179, 180, 183, 184, 185, 187, 188, 189, 190, 191, 192, 193, 194, 195, 196, 197, 198, 199, 200, 203, 204, 205, 207, 210, 214, 218, 220, 222, 223, 224, 225], "excluded_lines": []}, "export_to_eman": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 56, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 56, "excluded_lines": 0}, "missing_lines": [234, 235, 238, 240, 242, 243, 244, 245, 246, 248, 250, 253, 256, 258, 259, 260, 261, 265, 268, 269, 270, 271, 272, 275, 276, 277, 278, 279, 282, 283, 284, 287, 288, 291, 292, 293, 296, 299, 300, 301, 306, 307, 310, 311, 314, 315, 316, 318, 319, 320, 322, 323, 325, 326, 327, 328], "excluded_lines": []}, "export_to_imagej": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 27, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 27, "excluded_lines": 0}, "missing_lines": [335, 336, 339, 340, 343, 344, 345, 348, 350, 352, 376, 377, 378, 379, 382, 383, 384, 385, 386, 387, 389, 393, 395, 397, 398, 399, 400], "excluded_lines": []}, "export_results": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 12, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 12, "excluded_lines": 0}, "missing_lines": [415, 416, 417, 418, 419, 420, 421, 423, 424, 425, 426, 427], "excluded_lines": []}, "_export_mrc": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 10, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 10, "excluded_lines": 0}, "missing_lines": [432, 434, 436, 437, 438, 439, 440, 441, 442, 443], "excluded_lines": []}, "_export_tiff": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 7, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 7, "excluded_lines": 0}, "missing_lines": [448, 450, 452, 453, 454, 455, 456], "excluded_lines": []}, "_export_hdf": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 10, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 10, "excluded_lines": 0}, "missing_lines": [461, 463, 465, 466, 467, 468, 469, 470, 471, 472], "excluded_lines": []}, "export_to_csv": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 33, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 33, "excluded_lines": 0}, "missing_lines": [491, 493, 495, 496, 498, 500, 501, 503, 504, 505, 508, 509, 510, 511, 513, 516, 517, 518, 520, 521, 523, 524, 525, 526, 527, 528, 530, 531, 533, 534, 536, 537, 538], "excluded_lines": []}, "": {"executed_lines": [1, 2, 3, 4, 5, 7, 8, 9, 10, 11, 13, 16, 66, 163, 232, 333, 403, 430, 446, 459, 478], "summary": {"covered_lines": 21, "num_statements": 21, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}}, "classes": {"": {"executed_lines": [1, 2, 3, 4, 5, 7, 8, 9, 10, 11, 13, 16, 66, 163, 232, 333, 403, 430, 446, 459, 478], "summary": {"covered_lines": 21, "num_statements": 277, "percent_covered": 7.581227436823105, "percent_covered_display": "8", "missing_lines": 256, "excluded_lines": 0}, "missing_lines": [18, 19, 22, 23, 24, 27, 29, 31, 33, 34, 37, 40, 43, 46, 47, 50, 53, 55, 56, 58, 59, 60, 61, 68, 69, 72, 73, 74, 75, 79, 80, 81, 82, 83, 85, 87, 90, 91, 92, 93, 96, 97, 98, 99, 103, 106, 108, 124, 126, 127, 134, 135, 136, 144, 145, 151, 152, 154, 155, 156, 157, 165, 166, 170, 171, 172, 173, 174, 177, 178, 179, 180, 183, 184, 185, 187, 188, 189, 190, 191, 192, 193, 194, 195, 196, 197, 198, 199, 200, 203, 204, 205, 207, 210, 214, 218, 220, 222, 223, 224, 225, 234, 235, 238, 240, 242, 243, 244, 245, 246, 248, 250, 253, 256, 258, 259, 260, 261, 265, 268, 269, 270, 271, 272, 275, 276, 277, 278, 279, 282, 283, 284, 287, 288, 291, 292, 293, 296, 299, 300, 301, 306, 307, 310, 311, 314, 315, 316, 318, 319, 320, 322, 323, 325, 326, 327, 328, 335, 336, 339, 340, 343, 344, 345, 348, 350, 352, 376, 377, 378, 379, 382, 383, 384, 385, 386, 387, 389, 393, 395, 397, 398, 399, 400, 415, 416, 417, 418, 419, 420, 421, 423, 424, 425, 426, 427, 432, 434, 436, 437, 438, 439, 440, 441, 442, 443, 448, 450, 452, 453, 454, 455, 456, 461, 463, 465, 466, 467, 468, 469, 470, 471, 472, 491, 493, 495, 496, 498, 500, 501, 503, 504, 505, 508, 509, 510, 511, 513, 516, 517, 518, 520, 521, 523, 524, 525, 526, 527, 528, 530, 531, 533, 534, 536, 537, 538], "excluded_lines": []}}}, "aretomo3_gui/utils/file_utils.py": {"executed_lines": [1, 2, 3, 4, 5, 7, 10, 21, 23, 26, 27, 28, 31, 32, 33, 34, 35, 38, 39, 40, 41, 42, 43, 45, 47, 48, 49, 52, 63, 64, 66, 67, 70, 73, 76, 79, 82, 83, 84, 86, 87, 88, 89, 90, 91, 92, 100, 102, 103, 104, 105, 114, 116, 119, 122, 123, 124, 126, 127, 128, 130, 131, 132, 133, 134, 135, 136, 142, 145, 147, 149, 151, 152, 155, 157, 158, 159, 160, 163, 165, 168, 170, 173, 175, 178, 180, 181, 182, 183, 184, 187, 198, 205, 206, 207, 208, 210, 212, 213, 215, 216, 217, 218, 219, 220, 221, 222, 223, 224, 230, 231, 232, 233, 234, 235, 236, 237, 239], "summary": {"covered_lines": 118, "num_statements": 132, "percent_covered": 89.39393939393939, "percent_covered_display": "89", "missing_lines": 14, "excluded_lines": 0}, "missing_lines": [74, 137, 138, 139, 140, 189, 190, 191, 192, 193, 194, 195, 225, 227], "excluded_lines": [], "functions": {"validate_safe_path": {"executed_lines": [21, 23, 26, 27, 28, 31, 32, 33, 34, 35, 38, 39, 40, 41, 42, 43, 45, 47, 48, 49], "summary": {"covered_lines": 20, "num_statements": 20, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "sanitize_filename": {"executed_lines": [63, 64, 66, 67, 70, 73, 76], "summary": {"covered_lines": 7, "num_statements": 8, "percent_covered": 87.5, "percent_covered_display": "88", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [74], "excluded_lines": []}, "get_file_info": {"executed_lines": [82, 83, 84, 86, 87, 88, 89, 90, 91, 92], "summary": {"covered_lines": 10, "num_statements": 10, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "get_file_type": {"executed_lines": [102, 103, 104, 105], "summary": {"covered_lines": 4, "num_statements": 4, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "is_supported_format": {"executed_lines": [116], "summary": {"covered_lines": 1, "num_statements": 1, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "get_directory_contents": {"executed_lines": [122, 123, 124, 126, 127, 128, 130, 131, 132, 133, 134, 135, 136, 142], "summary": {"covered_lines": 14, "num_statements": 18, "percent_covered": 77.77777777777777, "percent_covered_display": "78", "missing_lines": 4, "excluded_lines": 0}, "missing_lines": [137, 138, 139, 140], "excluded_lines": []}, "filter_supported_files": {"executed_lines": [147, 149, 151, 152], "summary": {"covered_lines": 4, "num_statements": 4, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "normalize_path": {"executed_lines": [157, 158, 159, 160], "summary": {"covered_lines": 4, "num_statements": 4, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "get_relative_path": {"executed_lines": [165], "summary": {"covered_lines": 1, "num_statements": 1, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "ensure_directory_exists": {"executed_lines": [170], "summary": {"covered_lines": 1, "num_statements": 1, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "validate_file_permissions": {"executed_lines": [175], "summary": {"covered_lines": 1, "num_statements": 1, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "check_disk_space": {"executed_lines": [180, 181, 182, 183, 184], "summary": {"covered_lines": 5, "num_statements": 5, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "estimate_processing_space": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 7, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 7, "excluded_lines": 0}, "missing_lines": [189, 190, 191, 192, 193, 194, 195], "excluded_lines": []}, "analyze_directory": {"executed_lines": [205, 206, 207, 208, 210, 212, 213, 215, 216, 217, 218, 219, 220, 221, 222, 223, 224, 230, 231, 232, 233, 234, 235, 236, 237, 239], "summary": {"covered_lines": 26, "num_statements": 28, "percent_covered": 92.85714285714286, "percent_covered_display": "93", "missing_lines": 2, "excluded_lines": 0}, "missing_lines": [225, 227], "excluded_lines": []}, "": {"executed_lines": [1, 2, 3, 4, 5, 7, 10, 52, 79, 100, 114, 119, 145, 155, 163, 168, 173, 178, 187, 198], "summary": {"covered_lines": 20, "num_statements": 20, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}}, "classes": {"": {"executed_lines": [1, 2, 3, 4, 5, 7, 10, 21, 23, 26, 27, 28, 31, 32, 33, 34, 35, 38, 39, 40, 41, 42, 43, 45, 47, 48, 49, 52, 63, 64, 66, 67, 70, 73, 76, 79, 82, 83, 84, 86, 87, 88, 89, 90, 91, 92, 100, 102, 103, 104, 105, 114, 116, 119, 122, 123, 124, 126, 127, 128, 130, 131, 132, 133, 134, 135, 136, 142, 145, 147, 149, 151, 152, 155, 157, 158, 159, 160, 163, 165, 168, 170, 173, 175, 178, 180, 181, 182, 183, 184, 187, 198, 205, 206, 207, 208, 210, 212, 213, 215, 216, 217, 218, 219, 220, 221, 222, 223, 224, 230, 231, 232, 233, 234, 235, 236, 237, 239], "summary": {"covered_lines": 118, "num_statements": 132, "percent_covered": 89.39393939393939, "percent_covered_display": "89", "missing_lines": 14, "excluded_lines": 0}, "missing_lines": [74, 137, 138, 139, 140, 189, 190, 191, 192, 193, 194, 195, 225, 227], "excluded_lines": []}}}, "aretomo3_gui/utils/mdoc_parser.py": {"executed_lines": [2, 7, 8, 9, 10, 11, 13, 16, 17, 19, 23, 29, 66, 116, 188, 224, 250, 268, 294, 309], "summary": {"covered_lines": 18, "num_statements": 107, "percent_covered": 16.822429906542055, "percent_covered_display": "17", "missing_lines": 89, "excluded_lines": 0}, "missing_lines": [21, 25, 26, 27, 39, 41, 42, 43, 46, 47, 48, 51, 59, 60, 62, 63, 64, 69, 74, 94, 95, 96, 97, 98, 102, 103, 105, 106, 107, 108, 109, 110, 111, 112, 113, 119, 122, 124, 125, 128, 158, 159, 160, 161, 162, 166, 167, 169, 170, 171, 179, 180, 181, 182, 183, 184, 186, 190, 191, 194, 195, 196, 197, 198, 199, 201, 202, 203, 204, 205, 206, 207, 208, 210, 226, 233, 234, 248, 252, 276, 277, 280, 291, 304, 305, 306, 319, 320, 321], "excluded_lines": [], "functions": {"MDOCParser.__init__": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [21], "excluded_lines": []}, "MDOCParser.reset": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 3, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 3, "excluded_lines": 0}, "missing_lines": [25, 26, 27], "excluded_lines": []}, "MDOCParser.parse_mdoc_file": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 13, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 13, "excluded_lines": 0}, "missing_lines": [39, 41, 42, 43, 46, 47, 48, 51, 59, 60, 62, 63, 64], "excluded_lines": []}, "MDOCParser._parse_global_parameters": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 18, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 18, "excluded_lines": 0}, "missing_lines": [69, 74, 94, 95, 96, 97, 98, 102, 103, 105, 106, 107, 108, 109, 110, 111, 112, 113], "excluded_lines": []}, "MDOCParser._parse_sections": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 22, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 22, "excluded_lines": 0}, "missing_lines": [119, 122, 124, 125, 128, 158, 159, 160, 161, 162, 166, 167, 169, 170, 171, 179, 180, 181, 182, 183, 184, 186], "excluded_lines": []}, "MDOCParser._extract_tilt_series_data": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 17, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 17, "excluded_lines": 0}, "missing_lines": [190, 191, 194, 195, 196, 197, 198, 199, 201, 202, 203, 204, 205, 206, 207, 208, 210], "excluded_lines": []}, "MDOCParser._generate_summary": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 4, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 4, "excluded_lines": 0}, "missing_lines": [226, 233, 234, 248], "excluded_lines": []}, "MDOCParser._create_error_result": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [252], "excluded_lines": []}, "parse_mdoc": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 4, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 4, "excluded_lines": 0}, "missing_lines": [276, 277, 280, 291], "excluded_lines": []}, "extract_tilt_angles_from_mdoc": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 3, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 3, "excluded_lines": 0}, "missing_lines": [304, 305, 306], "excluded_lines": []}, "get_mdoc_summary": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 3, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 3, "excluded_lines": 0}, "missing_lines": [319, 320, 321], "excluded_lines": []}, "": {"executed_lines": [2, 7, 8, 9, 10, 11, 13, 16, 17, 19, 23, 29, 66, 116, 188, 224, 250, 268, 294, 309], "summary": {"covered_lines": 18, "num_statements": 18, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}}, "classes": {"MDOCParser": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 79, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 79, "excluded_lines": 0}, "missing_lines": [21, 25, 26, 27, 39, 41, 42, 43, 46, 47, 48, 51, 59, 60, 62, 63, 64, 69, 74, 94, 95, 96, 97, 98, 102, 103, 105, 106, 107, 108, 109, 110, 111, 112, 113, 119, 122, 124, 125, 128, 158, 159, 160, 161, 162, 166, 167, 169, 170, 171, 179, 180, 181, 182, 183, 184, 186, 190, 191, 194, 195, 196, 197, 198, 199, 201, 202, 203, 204, 205, 206, 207, 208, 210, 226, 233, 234, 248, 252], "excluded_lines": []}, "": {"executed_lines": [2, 7, 8, 9, 10, 11, 13, 16, 17, 19, 23, 29, 66, 116, 188, 224, 250, 268, 294, 309], "summary": {"covered_lines": 18, "num_statements": 28, "percent_covered": 64.28571428571429, "percent_covered_display": "64", "missing_lines": 10, "excluded_lines": 0}, "missing_lines": [276, 277, 280, 291, 304, 305, 306, 319, 320, 321], "excluded_lines": []}}}, "aretomo3_gui/utils/pdf_report_generator.py": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 183, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 183, "excluded_lines": 0}, "missing_lines": [7, 8, 9, 10, 11, 13, 15, 16, 17, 18, 19, 20, 21, 32, 33, 34, 36, 37, 38, 39, 41, 42, 43, 45, 48, 51, 53, 54, 55, 57, 59, 62, 73, 84, 94, 104, 114, 123, 124, 125, 127, 129, 130, 133, 134, 135, 138, 148, 151, 154, 157, 158, 163, 164, 169, 170, 177, 180, 183, 185, 186, 188, 189, 190, 192, 195, 196, 197, 200, 203, 204, 207, 208, 209, 210, 211, 214, 215, 222, 223, 225, 227, 230, 233, 234, 235, 236, 237, 240, 241, 242, 243, 244, 247, 248, 249, 250, 251, 254, 255, 270, 271, 273, 275, 280, 281, 282, 285, 286, 287, 288, 289, 291, 295, 298, 300, 302, 305, 306, 307, 310, 311, 312, 313, 314, 316, 318, 321, 323, 327, 330, 331, 332, 335, 336, 337, 338, 339, 341, 343, 346, 348, 350, 352, 355, 356, 357, 360, 365, 366, 367, 368, 373, 374, 375, 376, 379, 380, 381, 384, 385, 387, 391, 392, 396, 397, 398, 400, 402, 405, 408, 409, 410, 411, 412, 413, 415, 417, 419, 420, 421, 422, 424], "excluded_lines": [], "functions": {"TomogramPDFReportGenerator.__init__": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 3, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 3, "excluded_lines": 0}, "missing_lines": [53, 54, 55], "excluded_lines": []}, "TomogramPDFReportGenerator.setup_styles": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 6, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 6, "excluded_lines": 0}, "missing_lines": [59, 62, 73, 84, 94, 104], "excluded_lines": []}, "TomogramPDFReportGenerator.generate_report": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 27, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 27, "excluded_lines": 0}, "missing_lines": [123, 124, 125, 127, 129, 130, 133, 134, 135, 138, 148, 151, 154, 157, 158, 163, 164, 169, 170, 177, 180, 183, 185, 186, 188, 189, 190], "excluded_lines": []}, "TomogramPDFReportGenerator.add_title_page": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 15, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 15, "excluded_lines": 0}, "missing_lines": [195, 196, 197, 200, 203, 204, 207, 208, 209, 210, 211, 214, 215, 222, 223], "excluded_lines": []}, "TomogramPDFReportGenerator.add_executive_summary": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 21, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 21, "excluded_lines": 0}, "missing_lines": [227, 230, 233, 234, 235, 236, 237, 240, 241, 242, 243, 244, 247, 248, 249, 250, 251, 254, 255, 270, 271], "excluded_lines": []}, "TomogramPDFReportGenerator.add_motion_analysis": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 12, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 12, "excluded_lines": 0}, "missing_lines": [275, 280, 281, 282, 285, 286, 287, 288, 289, 291, 295, 298], "excluded_lines": []}, "TomogramPDFReportGenerator.add_ctf_analysis": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 12, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 12, "excluded_lines": 0}, "missing_lines": [302, 305, 306, 307, 310, 311, 312, 313, 314, 316, 318, 321], "excluded_lines": []}, "TomogramPDFReportGenerator.add_alignment_analysis": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 12, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 12, "excluded_lines": 0}, "missing_lines": [327, 330, 331, 332, 335, 336, 337, 338, 339, 341, 343, 346], "excluded_lines": []}, "TomogramPDFReportGenerator.add_recommendations": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 25, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 25, "excluded_lines": 0}, "missing_lines": [350, 352, 355, 356, 357, 360, 365, 366, 367, 368, 373, 374, 375, 376, 379, 380, 381, 384, 385, 387, 391, 392, 396, 397, 398], "excluded_lines": []}, "TomogramPDFReportGenerator.add_technical_details": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 9, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 9, "excluded_lines": 0}, "missing_lines": [402, 405, 408, 409, 410, 411, 412, 413, 415], "excluded_lines": []}, "TomogramPDFReportGenerator.get_quality_style": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 5, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 5, "excluded_lines": 0}, "missing_lines": [419, 420, 421, 422, 424], "excluded_lines": []}, "": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 36, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 36, "excluded_lines": 0}, "missing_lines": [7, 8, 9, 10, 11, 13, 15, 16, 17, 18, 19, 20, 21, 32, 33, 34, 36, 37, 38, 39, 41, 42, 43, 45, 48, 51, 57, 114, 192, 225, 273, 300, 323, 348, 400, 417], "excluded_lines": []}}, "classes": {"TomogramPDFReportGenerator": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 147, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 147, "excluded_lines": 0}, "missing_lines": [53, 54, 55, 59, 62, 73, 84, 94, 104, 123, 124, 125, 127, 129, 130, 133, 134, 135, 138, 148, 151, 154, 157, 158, 163, 164, 169, 170, 177, 180, 183, 185, 186, 188, 189, 190, 195, 196, 197, 200, 203, 204, 207, 208, 209, 210, 211, 214, 215, 222, 223, 227, 230, 233, 234, 235, 236, 237, 240, 241, 242, 243, 244, 247, 248, 249, 250, 251, 254, 255, 270, 271, 275, 280, 281, 282, 285, 286, 287, 288, 289, 291, 295, 298, 302, 305, 306, 307, 310, 311, 312, 313, 314, 316, 318, 321, 327, 330, 331, 332, 335, 336, 337, 338, 339, 341, 343, 346, 350, 352, 355, 356, 357, 360, 365, 366, 367, 368, 373, 374, 375, 376, 379, 380, 381, 384, 385, 387, 391, 392, 396, 397, 398, 402, 405, 408, 409, 410, 411, 412, 413, 415, 419, 420, 421, 422, 424], "excluded_lines": []}, "": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 36, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 36, "excluded_lines": 0}, "missing_lines": [7, 8, 9, 10, 11, 13, 15, 16, 17, 18, 19, 20, 21, 32, 33, 34, 36, 37, 38, 39, 41, 42, 43, 45, 48, 51, 57, 114, 192, 225, 273, 300, 323, 348, 400, 417], "excluded_lines": []}}}, "aretomo3_gui/utils/utils.py": {"executed_lines": [1, 5, 6, 7, 8, 9, 11, 12, 13, 15, 18, 19, 21, 28, 35, 40, 50, 61, 85, 96, 110, 122], "summary": {"covered_lines": 20, "num_statements": 85, "percent_covered": 23.529411764705884, "percent_covered_display": "24", "missing_lines": 65, "excluded_lines": 0}, "missing_lines": [23, 24, 25, 26, 30, 31, 32, 33, 37, 42, 46, 47, 52, 53, 54, 55, 56, 57, 58, 63, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 75, 76, 77, 78, 79, 82, 87, 88, 89, 90, 91, 92, 93, 98, 99, 101, 102, 103, 104, 105, 107, 112, 113, 114, 115, 116, 118, 119, 124, 127, 129, 131, 132, 133], "excluded_lines": [], "functions": {"ProgressDialog.__init__": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 4, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 4, "excluded_lines": 0}, "missing_lines": [23, 24, 25, 26], "excluded_lines": []}, "ProgressDialog.update": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 4, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 4, "excluded_lines": 0}, "missing_lines": [30, 31, 32, 33], "excluded_lines": []}, "ProgressDialog.close": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [37], "excluded_lines": []}, "save_window_state": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 3, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 3, "excluded_lines": 0}, "missing_lines": [42, 46, 47], "excluded_lines": []}, "load_window_state": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 7, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 7, "excluded_lines": 0}, "missing_lines": [52, 53, 54, 55, 56, 57, 58], "excluded_lines": []}, "apply_dark_mode": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 18, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 18, "excluded_lines": 0}, "missing_lines": [63, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 75, 76, 77, 78, 79, 82], "excluded_lines": []}, "export_plot": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 7, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 7, "excluded_lines": 0}, "missing_lines": [87, 88, 89, 90, 91, 92, 93], "excluded_lines": []}, "format_file_size": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 8, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 8, "excluded_lines": 0}, "missing_lines": [98, 99, 101, 102, 103, 104, 105, 107], "excluded_lines": []}, "format_duration": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 7, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 7, "excluded_lines": 0}, "missing_lines": [112, 113, 114, 115, 116, 118, 119], "excluded_lines": []}, "safe_filename": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 6, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 6, "excluded_lines": 0}, "missing_lines": [124, 127, 129, 131, 132, 133], "excluded_lines": []}, "": {"executed_lines": [1, 5, 6, 7, 8, 9, 11, 12, 13, 15, 18, 19, 21, 28, 35, 40, 50, 61, 85, 96, 110, 122], "summary": {"covered_lines": 20, "num_statements": 20, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}}, "classes": {"ProgressDialog": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 9, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 9, "excluded_lines": 0}, "missing_lines": [23, 24, 25, 26, 30, 31, 32, 33, 37], "excluded_lines": []}, "": {"executed_lines": [1, 5, 6, 7, 8, 9, 11, 12, 13, 15, 18, 19, 21, 28, 35, 40, 50, 61, 85, 96, 110, 122], "summary": {"covered_lines": 20, "num_statements": 76, "percent_covered": 26.31578947368421, "percent_covered_display": "26", "missing_lines": 56, "excluded_lines": 0}, "missing_lines": [42, 46, 47, 52, 53, 54, 55, 56, 57, 58, 63, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 75, 76, 77, 78, 79, 82, 87, 88, 89, 90, 91, 92, 93, 98, 99, 101, 102, 103, 104, 105, 107, 112, 113, 114, 115, 116, 118, 119, 124, 127, 129, 131, 132, 133], "excluded_lines": []}}}, "aretomo3_gui/utils/warning_suppression.py": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 29, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 29, "excluded_lines": 0}, "missing_lines": [6, 7, 8, 10, 14, 17, 20, 23, 26, 28, 29, 32, 33, 36, 39, 41, 44, 45, 47, 51, 52, 53, 56, 59, 60, 63, 64, 65, 66], "excluded_lines": [], "functions": {"suppress_common_warnings": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 14, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 14, "excluded_lines": 0}, "missing_lines": [14, 17, 20, 23, 26, 28, 29, 32, 33, 36, 39, 41, 44, 45], "excluded_lines": []}, "setup_professional_environment": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 6, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 6, "excluded_lines": 0}, "missing_lines": [51, 52, 53, 56, 59, 60], "excluded_lines": []}, "": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 9, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 9, "excluded_lines": 0}, "missing_lines": [6, 7, 8, 10, 47, 63, 64, 65, 66], "excluded_lines": []}}, "classes": {"": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 29, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 29, "excluded_lines": 0}, "missing_lines": [6, 7, 8, 10, 14, 17, 20, 23, 26, 28, 29, 32, 33, 36, 39, 41, 44, 45, 47, 51, 52, 53, 56, 59, 60, 63, 64, 65, 66], "excluded_lines": []}}}}, "totals": {"covered_lines": 1052, "num_statements": 9177, "percent_covered": 11.463441211724964, "percent_covered_display": "11", "missing_lines": 8125, "excluded_lines": 145}}
# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
pip-wheel-metadata/
share/python-wheels/
*.egg-info/
.installed.cfg
*.egg

# Virtual environments
venv/
env/
ENV/
at3gui_env/
AT3GUI/

# IDE
.vscode/
.idea/
*.swp
*.swo

# OS
.DS_Store
Thumbs.db

# Logs
*.log
logs/

# Temporary files
*.tmp
*.temp
temp/
tmp/

# Backup files
*_backup/
backup_*/
reorganization_backup/
cleanup_backup_*/

# Test artifacts
.pytest_cache/
.coverage
htmlcov/

# Build artifacts
build/
dist/
*.egg-info/

# Project specific
BATCH_PROCESSING_INVESTIGATION.py
VENV_COMPREHENSIVE_TEST.py
*_INVESTIGATION.py
*_TEST_REPORT.json
<!DOCTYPE html>
<html lang="en">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
    <title>Coverage report</title>
    <link rel="icon" sizes="32x32" href="favicon_32_cb_58284776.png">
    <link rel="stylesheet" href="style_cb_8e611ae1.css" type="text/css">
    <script src="coverage_html_cb_6fb7b396.js" defer></script>
</head>
<body class="indexfile">
<header>
    <div class="content">
        <h1>Coverage report:
            <span class="pc_cov">4%</span>
        </h1>
        <aside id="help_panel_wrapper">
            <input id="help_panel_state" type="checkbox">
            <label for="help_panel_state">
                <img id="keyboard_icon" src="keybd_closed_cb_ce680311.png" alt="Show/hide keyboard shortcuts">
            </label>
            <div id="help_panel">
                <p class="legend">Shortcuts on this page</p>
                <div class="keyhelp">
                    <p>
                        <kbd>f</kbd>
                        <kbd>s</kbd>
                        <kbd>m</kbd>
                        <kbd>x</kbd>
                        <kbd>b</kbd>
                        <kbd>p</kbd>
                        <kbd>c</kbd>
                        &nbsp; change column sorting
                    </p>
                    <p>
                        <kbd>[</kbd>
                        <kbd>]</kbd>
                        &nbsp; prev/next file
                    </p>
                    <p>
                        <kbd>?</kbd> &nbsp; show/hide this help
                    </p>
                </div>
            </div>
        </aside>
        <form id="filter_container">
            <input id="filter" type="text" value="" placeholder="filter...">
            <div>
                <input id="hide100" type="checkbox" >
                <label for="hide100">hide covered</label>
            </div>
        </form>
        <h2>
                <a class="button current">Files</a>
                <a class="button" href="function_index.html">Functions</a>
                <a class="button" href="class_index.html">Classes</a>
        </h2>
        <p class="text">
            <a class="nav" href="https://coverage.readthedocs.io/en/7.8.0">coverage.py v7.8.0</a>,
            created at 2025-06-08 22:21 -0400
        </p>
    </div>
</header>
<main id="index">
    <table class="index" data-sortable>
        <thead>
            <tr class="tablehead" title="Click to sort">
                <th id="file" class="name left" aria-sort="none" data-shortcut="f">File<span class="arrows"></span></th>
                <th id="statements" aria-sort="none" data-default-sort-order="descending" data-shortcut="s">statements<span class="arrows"></span></th>
                <th id="missing" aria-sort="none" data-default-sort-order="descending" data-shortcut="m">missing<span class="arrows"></span></th>
                <th id="excluded" aria-sort="none" data-default-sort-order="descending" data-shortcut="x">excluded<span class="arrows"></span></th>
                <th id="branches" aria-sort="none" data-default-sort-order="descending" data-shortcut="b">branches<span class="arrows"></span></th>
                <th id="partial" aria-sort="none" data-default-sort-order="descending" data-shortcut="p">partial<span class="arrows"></span></th>
                <th id="coverage" class="right" aria-sort="none" data-shortcut="c">coverage<span class="arrows"></span></th>
            </tr>
        </thead>
        <tbody>
            <tr class="region">
                <td class="name left"><a href="z_3caff36e279812ae___init___py.html">aretomo3_gui/__init__.py</a></td>
                <td>14</td>
                <td>7</td>
                <td>0</td>
                <td>2</td>
                <td>1</td>
                <td class="right" data-ratio="8 16">50%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_3caff36e279812ae___main___py.html">aretomo3_gui/__main__.py</a></td>
                <td>0</td>
                <td>0</td>
                <td>3</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a233f4e704dc3d95___init___py.html">aretomo3_gui/analysis/__init__.py</a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a233f4e704dc3d95_aretomo3_output_analyzer_py.html">aretomo3_gui/analysis/aretomo3_output_analyzer.py</a></td>
                <td>159</td>
                <td>159</td>
                <td>0</td>
                <td>70</td>
                <td>0</td>
                <td class="right" data-ratio="0 229">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a233f4e704dc3d95_auto_plot_generator_py.html">aretomo3_gui/analysis/auto_plot_generator.py</a></td>
                <td>398</td>
                <td>398</td>
                <td>0</td>
                <td>118</td>
                <td>0</td>
                <td class="right" data-ratio="0 516">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b8fe8d00152a262b___init___py.html">aretomo3_gui/analysis/ctf_analysis/__init__.py</a></td>
                <td>6</td>
                <td>6</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 6">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b8fe8d00152a262b_ctf_dashboard_py.html">aretomo3_gui/analysis/ctf_analysis/ctf_dashboard.py</a></td>
                <td>209</td>
                <td>209</td>
                <td>2</td>
                <td>44</td>
                <td>0</td>
                <td class="right" data-ratio="0 253">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b8fe8d00152a262b_ctf_parser_py.html">aretomo3_gui/analysis/ctf_analysis/ctf_parser.py</a></td>
                <td>222</td>
                <td>222</td>
                <td>2</td>
                <td>76</td>
                <td>0</td>
                <td class="right" data-ratio="0 298">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b8fe8d00152a262b_ctf_quality_py.html">aretomo3_gui/analysis/ctf_analysis/ctf_quality.py</a></td>
                <td>195</td>
                <td>195</td>
                <td>2</td>
                <td>56</td>
                <td>0</td>
                <td class="right" data-ratio="0 251">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b8fe8d00152a262b_ctf_utils_py.html">aretomo3_gui/analysis/ctf_analysis/ctf_utils.py</a></td>
                <td>212</td>
                <td>212</td>
                <td>2</td>
                <td>78</td>
                <td>0</td>
                <td class="right" data-ratio="0 290">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b8fe8d00152a262b_ctf_visualizer_py.html">aretomo3_gui/analysis/ctf_analysis/ctf_visualizer.py</a></td>
                <td>348</td>
                <td>348</td>
                <td>2</td>
                <td>78</td>
                <td>0</td>
                <td class="right" data-ratio="0 426">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a233f4e704dc3d95_interactive_plots_py.html">aretomo3_gui/analysis/interactive_plots.py</a></td>
                <td>132</td>
                <td>132</td>
                <td>0</td>
                <td>44</td>
                <td>0</td>
                <td class="right" data-ratio="0 176">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2e874a7ec04b2056___init___py.html">aretomo3_gui/analysis/motion_analysis/__init__.py</a></td>
                <td>5</td>
                <td>5</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 5">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2e874a7ec04b2056_motion_parser_py.html">aretomo3_gui/analysis/motion_analysis/motion_parser.py</a></td>
                <td>203</td>
                <td>203</td>
                <td>2</td>
                <td>64</td>
                <td>0</td>
                <td class="right" data-ratio="0 267">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2e874a7ec04b2056_motion_visualizer_py.html">aretomo3_gui/analysis/motion_analysis/motion_visualizer.py</a></td>
                <td>231</td>
                <td>231</td>
                <td>2</td>
                <td>48</td>
                <td>0</td>
                <td class="right" data-ratio="0 279">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a233f4e704dc3d95_realtime_monitor_py.html">aretomo3_gui/analysis/realtime_monitor.py</a></td>
                <td>194</td>
                <td>194</td>
                <td>0</td>
                <td>54</td>
                <td>0</td>
                <td class="right" data-ratio="0 248">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_1fe0bc49e225fa3c___init___py.html">aretomo3_gui/analytics/__init__.py</a></td>
                <td>2</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="2 2">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_1fe0bc49e225fa3c_advanced_analytics_py.html">aretomo3_gui/analytics/advanced_analytics.py</a></td>
                <td>268</td>
                <td>31</td>
                <td>0</td>
                <td>66</td>
                <td>8</td>
                <td class="right" data-ratio="295 334">88%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_1fe0bc49e225fa3c_analyzer_py.html">aretomo3_gui/analytics/analyzer.py</a></td>
                <td>141</td>
                <td>141</td>
                <td>0</td>
                <td>28</td>
                <td>0</td>
                <td class="right" data-ratio="0 169">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_3caff36e279812ae_cli_py.html">aretomo3_gui/cli.py</a></td>
                <td>91</td>
                <td>91</td>
                <td>2</td>
                <td>24</td>
                <td>0</td>
                <td class="right" data-ratio="0 115">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f984836ddf12effb___init___py.html">aretomo3_gui/core/__init__.py</a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f984836ddf12effb_advanced_logging_py.html">aretomo3_gui/core/advanced_logging.py</a></td>
                <td>190</td>
                <td>190</td>
                <td>0</td>
                <td>28</td>
                <td>0</td>
                <td class="right" data-ratio="0 218">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f99fa31c6596b435___init___py.html">aretomo3_gui/core/automation/__init__.py</a></td>
                <td>3</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="3 3">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f99fa31c6596b435_parameter_optimizer_py.html">aretomo3_gui/core/automation/parameter_optimizer.py</a></td>
                <td>21</td>
                <td>17</td>
                <td>0</td>
                <td>10</td>
                <td>0</td>
                <td class="right" data-ratio="4 31">13%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f99fa31c6596b435_quality_predictor_py.html">aretomo3_gui/core/automation/quality_predictor.py</a></td>
                <td>15</td>
                <td>10</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="5 15">33%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f99fa31c6596b435_workflow_manager_py.html">aretomo3_gui/core/automation/workflow_manager.py</a></td>
                <td>272</td>
                <td>181</td>
                <td>0</td>
                <td>86</td>
                <td>0</td>
                <td class="right" data-ratio="91 358">25%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f984836ddf12effb_backup_system_py.html">aretomo3_gui/core/backup_system.py</a></td>
                <td>252</td>
                <td>252</td>
                <td>0</td>
                <td>86</td>
                <td>0</td>
                <td class="right" data-ratio="0 338">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_61fb54b6db731140___init___py.html">aretomo3_gui/core/config/__init__.py</a></td>
                <td>3</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="3 3">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_61fb54b6db731140_config_py.html">aretomo3_gui/core/config/config.py</a></td>
                <td>12</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="12 12">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_61fb54b6db731140_config_manager_py.html">aretomo3_gui/core/config/config_manager.py</a></td>
                <td>145</td>
                <td>102</td>
                <td>0</td>
                <td>20</td>
                <td>0</td>
                <td class="right" data-ratio="43 165">26%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_61fb54b6db731140_config_validation_py.html">aretomo3_gui/core/config/config_validation.py</a></td>
                <td>106</td>
                <td>106</td>
                <td>0</td>
                <td>50</td>
                <td>0</td>
                <td class="right" data-ratio="0 156">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_61fb54b6db731140_template_manager_py.html">aretomo3_gui/core/config/template_manager.py</a></td>
                <td>52</td>
                <td>52</td>
                <td>0</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 56">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f984836ddf12effb_config_manager_py.html">aretomo3_gui/core/config_manager.py</a></td>
                <td>237</td>
                <td>152</td>
                <td>0</td>
                <td>62</td>
                <td>4</td>
                <td class="right" data-ratio="91 299">30%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f984836ddf12effb_continue_mode_manager_py.html">aretomo3_gui/core/continue_mode_manager.py</a></td>
                <td>243</td>
                <td>191</td>
                <td>0</td>
                <td>52</td>
                <td>0</td>
                <td class="right" data-ratio="52 295">18%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f984836ddf12effb_data_validation_py.html">aretomo3_gui/core/data_validation.py</a></td>
                <td>206</td>
                <td>206</td>
                <td>0</td>
                <td>68</td>
                <td>0</td>
                <td class="right" data-ratio="0 274">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f984836ddf12effb_database_manager_py.html">aretomo3_gui/core/database_manager.py</a></td>
                <td>237</td>
                <td>237</td>
                <td>0</td>
                <td>44</td>
                <td>0</td>
                <td class="right" data-ratio="0 281">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f984836ddf12effb_dependency_check_py.html">aretomo3_gui/core/dependency_check.py</a></td>
                <td>78</td>
                <td>78</td>
                <td>3</td>
                <td>28</td>
                <td>0</td>
                <td class="right" data-ratio="0 106">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f984836ddf12effb_enhanced_database_manager_py.html">aretomo3_gui/core/enhanced_database_manager.py</a></td>
                <td>207</td>
                <td>207</td>
                <td>0</td>
                <td>40</td>
                <td>0</td>
                <td class="right" data-ratio="0 247">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f984836ddf12effb_enhanced_parameters_py.html">aretomo3_gui/core/enhanced_parameters.py</a></td>
                <td>154</td>
                <td>154</td>
                <td>0</td>
                <td>46</td>
                <td>0</td>
                <td class="right" data-ratio="0 200">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f984836ddf12effb_enhanced_realtime_processor_py.html">aretomo3_gui/core/enhanced_realtime_processor.py</a></td>
                <td>118</td>
                <td>118</td>
                <td>0</td>
                <td>30</td>
                <td>0</td>
                <td class="right" data-ratio="0 148">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f984836ddf12effb_error_handling_py.html">aretomo3_gui/core/error_handling.py</a></td>
                <td>108</td>
                <td>72</td>
                <td>0</td>
                <td>30</td>
                <td>2</td>
                <td class="right" data-ratio="38 138">28%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f984836ddf12effb_error_recovery_py.html">aretomo3_gui/core/error_recovery.py</a></td>
                <td>225</td>
                <td>225</td>
                <td>0</td>
                <td>54</td>
                <td>0</td>
                <td class="right" data-ratio="0 279">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f984836ddf12effb_file_organization_py.html">aretomo3_gui/core/file_organization.py</a></td>
                <td>226</td>
                <td>226</td>
                <td>0</td>
                <td>62</td>
                <td>0</td>
                <td class="right" data-ratio="0 288">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f984836ddf12effb_file_watcher_py.html">aretomo3_gui/core/file_watcher.py</a></td>
                <td>99</td>
                <td>99</td>
                <td>0</td>
                <td>32</td>
                <td>0</td>
                <td class="right" data-ratio="0 131">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f984836ddf12effb_logging_config_py.html">aretomo3_gui/core/logging_config.py</a></td>
                <td>106</td>
                <td>106</td>
                <td>3</td>
                <td>12</td>
                <td>0</td>
                <td class="right" data-ratio="0 118">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f984836ddf12effb_memory_manager_py.html">aretomo3_gui/core/memory_manager.py</a></td>
                <td>241</td>
                <td>241</td>
                <td>0</td>
                <td>50</td>
                <td>0</td>
                <td class="right" data-ratio="0 291">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f984836ddf12effb_multi_format_handler_py.html">aretomo3_gui/core/multi_format_handler.py</a></td>
                <td>192</td>
                <td>192</td>
                <td>0</td>
                <td>90</td>
                <td>0</td>
                <td class="right" data-ratio="0 282">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f984836ddf12effb_performance_monitor_py.html">aretomo3_gui/core/performance_monitor.py</a></td>
                <td>175</td>
                <td>175</td>
                <td>0</td>
                <td>36</td>
                <td>0</td>
                <td class="right" data-ratio="0 211">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f984836ddf12effb_performance_optimizer_py.html">aretomo3_gui/core/performance_optimizer.py</a></td>
                <td>261</td>
                <td>261</td>
                <td>0</td>
                <td>42</td>
                <td>0</td>
                <td class="right" data-ratio="0 303">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f984836ddf12effb_plugin_system_py.html">aretomo3_gui/core/plugin_system.py</a></td>
                <td>228</td>
                <td>228</td>
                <td>78</td>
                <td>68</td>
                <td>0</td>
                <td class="right" data-ratio="0 296">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f984836ddf12effb_realtime_processor_py.html">aretomo3_gui/core/realtime_processor.py</a></td>
                <td>195</td>
                <td>111</td>
                <td>0</td>
                <td>30</td>
                <td>1</td>
                <td class="right" data-ratio="87 225">39%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f984836ddf12effb_resource_manager_py.html">aretomo3_gui/core/resource_manager.py</a></td>
                <td>138</td>
                <td>102</td>
                <td>0</td>
                <td>28</td>
                <td>0</td>
                <td class="right" data-ratio="36 166">22%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f984836ddf12effb_results_tracker_py.html">aretomo3_gui/core/results_tracker.py</a></td>
                <td>183</td>
                <td>140</td>
                <td>0</td>
                <td>18</td>
                <td>0</td>
                <td class="right" data-ratio="43 201">21%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f984836ddf12effb_secure_web_api_py.html">aretomo3_gui/core/secure_web_api.py</a></td>
                <td>204</td>
                <td>204</td>
                <td>5</td>
                <td>56</td>
                <td>0</td>
                <td class="right" data-ratio="0 260">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f984836ddf12effb_security_framework_py.html">aretomo3_gui/core/security_framework.py</a></td>
                <td>246</td>
                <td>246</td>
                <td>0</td>
                <td>66</td>
                <td>0</td>
                <td class="right" data-ratio="0 312">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f984836ddf12effb_session_manager_py.html">aretomo3_gui/core/session_manager.py</a></td>
                <td>174</td>
                <td>129</td>
                <td>0</td>
                <td>40</td>
                <td>0</td>
                <td class="right" data-ratio="45 214">21%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f984836ddf12effb_system_integration_py.html">aretomo3_gui/core/system_integration.py</a></td>
                <td>136</td>
                <td>136</td>
                <td>0</td>
                <td>36</td>
                <td>0</td>
                <td class="right" data-ratio="0 172">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f984836ddf12effb_system_monitor_py.html">aretomo3_gui/core/system_monitor.py</a></td>
                <td>121</td>
                <td>91</td>
                <td>0</td>
                <td>30</td>
                <td>0</td>
                <td class="right" data-ratio="30 151">20%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f984836ddf12effb_thread_manager_py.html">aretomo3_gui/core/thread_manager.py</a></td>
                <td>184</td>
                <td>128</td>
                <td>0</td>
                <td>32</td>
                <td>0</td>
                <td class="right" data-ratio="56 216">26%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f984836ddf12effb_tilt_series_py.html">aretomo3_gui/core/tilt_series.py</a></td>
                <td>10</td>
                <td>10</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 10">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e475032cb85aca8e___init___py.html">aretomo3_gui/data_management/__init__.py</a></td>
                <td>2</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="2 2">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e475032cb85aca8e_data_manager_py.html">aretomo3_gui/data_management/data_manager.py</a></td>
                <td>209</td>
                <td>152</td>
                <td>0</td>
                <td>54</td>
                <td>0</td>
                <td class="right" data-ratio="57 263">22%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e475032cb85aca8e_manager_py.html">aretomo3_gui/data_management/manager.py</a></td>
                <td>90</td>
                <td>90</td>
                <td>0</td>
                <td>12</td>
                <td>0</td>
                <td class="right" data-ratio="0 102">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e29991547583ad4f___init___py.html">aretomo3_gui/formats/__init__.py</a></td>
                <td>2</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="2 2">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e29991547583ad4f_format_manager_py.html">aretomo3_gui/formats/format_manager.py</a></td>
                <td>242</td>
                <td>162</td>
                <td>12</td>
                <td>66</td>
                <td>3</td>
                <td class="right" data-ratio="85 308">28%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e29991547583ad4f_manager_py.html">aretomo3_gui/formats/manager.py</a></td>
                <td>89</td>
                <td>89</td>
                <td>12</td>
                <td>24</td>
                <td>0</td>
                <td class="right" data-ratio="0 113">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2f7edb285fa49a79___init___py.html">aretomo3_gui/gui/__init__.py</a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2f7edb285fa49a79_advanced_settings_tab_py.html">aretomo3_gui/gui/advanced_settings_tab.py</a></td>
                <td>313</td>
                <td>313</td>
                <td>0</td>
                <td>18</td>
                <td>0</td>
                <td class="right" data-ratio="0 331">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_005df56b57153476___init___py.html">aretomo3_gui/gui/analysis/__init__.py</a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_005df56b57153476_real_time_analyzer_py.html">aretomo3_gui/gui/analysis/real_time_analyzer.py</a></td>
                <td>279</td>
                <td>279</td>
                <td>0</td>
                <td>30</td>
                <td>0</td>
                <td class="right" data-ratio="0 309">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ce4f5229c74c4b5a___init___py.html">aretomo3_gui/gui/components/__init__.py</a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ce4f5229c74c4b5a_menu_manager_py.html">aretomo3_gui/gui/components/menu_manager.py</a></td>
                <td>159</td>
                <td>159</td>
                <td>0</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 163">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ce4f5229c74c4b5a_napari_viewer_py.html">aretomo3_gui/gui/components/napari_viewer.py</a></td>
                <td>158</td>
                <td>158</td>
                <td>0</td>
                <td>20</td>
                <td>0</td>
                <td class="right" data-ratio="0 178">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ce4f5229c74c4b5a_parameter_manager_py.html">aretomo3_gui/gui/components/parameter_manager.py</a></td>
                <td>296</td>
                <td>296</td>
                <td>0</td>
                <td>174</td>
                <td>0</td>
                <td class="right" data-ratio="0 470">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ce4f5229c74c4b5a_session_manager_py.html">aretomo3_gui/gui/components/session_manager.py</a></td>
                <td>155</td>
                <td>155</td>
                <td>0</td>
                <td>78</td>
                <td>0</td>
                <td class="right" data-ratio="0 233">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_3a511341f990a247___init___py.html">aretomo3_gui/gui/embedded_viewers/__init__.py</a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_3a511341f990a247_ctf_viewer_py.html">aretomo3_gui/gui/embedded_viewers/ctf_viewer.py</a></td>
                <td>293</td>
                <td>293</td>
                <td>0</td>
                <td>38</td>
                <td>0</td>
                <td class="right" data-ratio="0 331">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_3a511341f990a247_motion_viewer_py.html">aretomo3_gui/gui/embedded_viewers/motion_viewer.py</a></td>
                <td>359</td>
                <td>359</td>
                <td>0</td>
                <td>58</td>
                <td>0</td>
                <td class="right" data-ratio="0 417">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2f7edb285fa49a79_main_window_py.html">aretomo3_gui/gui/main_window.py</a></td>
                <td>433</td>
                <td>433</td>
                <td>0</td>
                <td>26</td>
                <td>0</td>
                <td class="right" data-ratio="0 459">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2f7edb285fa49a79_minimal_gui_py.html">aretomo3_gui/gui/minimal_gui.py</a></td>
                <td>157</td>
                <td>157</td>
                <td>0</td>
                <td>12</td>
                <td>0</td>
                <td class="right" data-ratio="0 169">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2f7edb285fa49a79_plot_theme_manager_py.html">aretomo3_gui/gui/plot_theme_manager.py</a></td>
                <td>68</td>
                <td>68</td>
                <td>0</td>
                <td>12</td>
                <td>0</td>
                <td class="right" data-ratio="0 80">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2f7edb285fa49a79_rich_main_window_py.html">aretomo3_gui/gui/rich_main_window.py</a></td>
                <td>1655</td>
                <td>1655</td>
                <td>0</td>
                <td>488</td>
                <td>0</td>
                <td class="right" data-ratio="0 2143">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8b5a9328199a4f25___init___py.html">aretomo3_gui/gui/tabs/__init__.py</a></td>
                <td>8</td>
                <td>8</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 8">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8b5a9328199a4f25_analysis_tab_py.html">aretomo3_gui/gui/tabs/analysis_tab.py</a></td>
                <td>52</td>
                <td>52</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 52">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8b5a9328199a4f25_batch_tab_py.html">aretomo3_gui/gui/tabs/batch_tab.py</a></td>
                <td>20</td>
                <td>20</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 20">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8b5a9328199a4f25_ctf_tab_py.html">aretomo3_gui/gui/tabs/ctf_tab.py</a></td>
                <td>97</td>
                <td>97</td>
                <td>0</td>
                <td>8</td>
                <td>0</td>
                <td class="right" data-ratio="0 105">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8b5a9328199a4f25_enhanced_analysis_tab_py.html">aretomo3_gui/gui/tabs/enhanced_analysis_tab.py</a></td>
                <td>1170</td>
                <td>1170</td>
                <td>0</td>
                <td>286</td>
                <td>0</td>
                <td class="right" data-ratio="0 1456">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8b5a9328199a4f25_enhanced_monitor_tab_py.html">aretomo3_gui/gui/tabs/enhanced_monitor_tab.py</a></td>
                <td>160</td>
                <td>160</td>
                <td>0</td>
                <td>16</td>
                <td>0</td>
                <td class="right" data-ratio="0 176">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8b5a9328199a4f25_enhanced_parameters_tab_py.html">aretomo3_gui/gui/tabs/enhanced_parameters_tab.py</a></td>
                <td>1073</td>
                <td>1073</td>
                <td>0</td>
                <td>322</td>
                <td>0</td>
                <td class="right" data-ratio="0 1395">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8b5a9328199a4f25_export_tab_py.html">aretomo3_gui/gui/tabs/export_tab.py</a></td>
                <td>37</td>
                <td>37</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 37">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8b5a9328199a4f25_live_processing_tab_py.html">aretomo3_gui/gui/tabs/live_processing_tab.py</a></td>
                <td>644</td>
                <td>644</td>
                <td>0</td>
                <td>148</td>
                <td>0</td>
                <td class="right" data-ratio="0 792">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8b5a9328199a4f25_log_tab_py.html">aretomo3_gui/gui/tabs/log_tab.py</a></td>
                <td>132</td>
                <td>132</td>
                <td>0</td>
                <td>26</td>
                <td>0</td>
                <td class="right" data-ratio="0 158">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8b5a9328199a4f25_main_tab_py.html">aretomo3_gui/gui/tabs/main_tab.py</a></td>
                <td>305</td>
                <td>305</td>
                <td>0</td>
                <td>18</td>
                <td>0</td>
                <td class="right" data-ratio="0 323">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8b5a9328199a4f25_monitor_tab_py.html">aretomo3_gui/gui/tabs/monitor_tab.py</a></td>
                <td>27</td>
                <td>27</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 27">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8b5a9328199a4f25_napari_viewer_tab_py.html">aretomo3_gui/gui/tabs/napari_viewer_tab.py</a></td>
                <td>183</td>
                <td>183</td>
                <td>0</td>
                <td>24</td>
                <td>0</td>
                <td class="right" data-ratio="0 207">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8b5a9328199a4f25_realtime_analysis_tab_py.html">aretomo3_gui/gui/tabs/realtime_analysis_tab.py</a></td>
                <td>1317</td>
                <td>1317</td>
                <td>0</td>
                <td>476</td>
                <td>0</td>
                <td class="right" data-ratio="0 1793">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8b5a9328199a4f25_reorganized_main_tab_py.html">aretomo3_gui/gui/tabs/reorganized_main_tab.py</a></td>
                <td>593</td>
                <td>593</td>
                <td>0</td>
                <td>168</td>
                <td>0</td>
                <td class="right" data-ratio="0 761">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8b5a9328199a4f25_reorganized_main_tab_backup_py.html">aretomo3_gui/gui/tabs/reorganized_main_tab_backup.py</a></td>
                <td>688</td>
                <td>688</td>
                <td>0</td>
                <td>144</td>
                <td>0</td>
                <td class="right" data-ratio="0 832">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8b5a9328199a4f25_unified_analysis_tab_py.html">aretomo3_gui/gui/tabs/unified_analysis_tab.py</a></td>
                <td>578</td>
                <td>578</td>
                <td>0</td>
                <td>142</td>
                <td>0</td>
                <td class="right" data-ratio="0 720">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8b5a9328199a4f25_unified_live_processing_tab_py.html">aretomo3_gui/gui/tabs/unified_live_processing_tab.py</a></td>
                <td>195</td>
                <td>195</td>
                <td>0</td>
                <td>26</td>
                <td>0</td>
                <td class="right" data-ratio="0 221">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8b5a9328199a4f25_viewer_tab_py.html">aretomo3_gui/gui/tabs/viewer_tab.py</a></td>
                <td>19</td>
                <td>19</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 19">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8b5a9328199a4f25_web_dashboard_tab_py.html">aretomo3_gui/gui/tabs/web_dashboard_tab.py</a></td>
                <td>239</td>
                <td>239</td>
                <td>0</td>
                <td>32</td>
                <td>0</td>
                <td class="right" data-ratio="0 271">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2f7edb285fa49a79_theme_manager_py.html">aretomo3_gui/gui/theme_manager.py</a></td>
                <td>48</td>
                <td>48</td>
                <td>0</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 52">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2f7edb285fa49a79_themes_py.html">aretomo3_gui/gui/themes.py</a></td>
                <td>22</td>
                <td>22</td>
                <td>0</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 28">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_140fbbd1834799a1___init___py.html">aretomo3_gui/gui/viewers/__init__.py</a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_140fbbd1834799a1_analysis_viewer_py.html">aretomo3_gui/gui/viewers/analysis_viewer.py</a></td>
                <td>298</td>
                <td>298</td>
                <td>0</td>
                <td>34</td>
                <td>0</td>
                <td class="right" data-ratio="0 332">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_140fbbd1834799a1_mock_napari_viewer_py.html">aretomo3_gui/gui/viewers/mock_napari_viewer.py</a></td>
                <td>22</td>
                <td>22</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 22">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_140fbbd1834799a1_mrc_viewer_py.html">aretomo3_gui/gui/viewers/mrc_viewer.py</a></td>
                <td>1023</td>
                <td>1023</td>
                <td>0</td>
                <td>206</td>
                <td>0</td>
                <td class="right" data-ratio="0 1229">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_140fbbd1834799a1_napari_mrc_viewer_py.html">aretomo3_gui/gui/viewers/napari_mrc_viewer.py</a></td>
                <td>121</td>
                <td>121</td>
                <td>0</td>
                <td>20</td>
                <td>0</td>
                <td class="right" data-ratio="0 141">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_140fbbd1834799a1_preview_grid_py.html">aretomo3_gui/gui/viewers/preview_grid.py</a></td>
                <td>64</td>
                <td>64</td>
                <td>0</td>
                <td>8</td>
                <td>0</td>
                <td class="right" data-ratio="0 72">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_140fbbd1834799a1_visualization_py.html">aretomo3_gui/gui/viewers/visualization.py</a></td>
                <td>411</td>
                <td>411</td>
                <td>0</td>
                <td>62</td>
                <td>0</td>
                <td class="right" data-ratio="0 473">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_cba28bd5643ea358___init___py.html">aretomo3_gui/gui/widgets/__init__.py</a></td>
                <td>5</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="5 5">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_cba28bd5643ea358_advanced_file_browser_py.html">aretomo3_gui/gui/widgets/advanced_file_browser.py</a></td>
                <td>381</td>
                <td>324</td>
                <td>0</td>
                <td>88</td>
                <td>0</td>
                <td class="right" data-ratio="57 469">12%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_cba28bd5643ea358_advanced_progress_widget_py.html">aretomo3_gui/gui/widgets/advanced_progress_widget.py</a></td>
                <td>286</td>
                <td>286</td>
                <td>0</td>
                <td>70</td>
                <td>0</td>
                <td class="right" data-ratio="0 356">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_cba28bd5643ea358_batch_processing_py.html">aretomo3_gui/gui/widgets/batch_processing.py</a></td>
                <td>561</td>
                <td>520</td>
                <td>0</td>
                <td>174</td>
                <td>0</td>
                <td class="right" data-ratio="41 735">6%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_cba28bd5643ea358_enhanced_progress_visualization_py.html">aretomo3_gui/gui/widgets/enhanced_progress_visualization.py</a></td>
                <td>238</td>
                <td>238</td>
                <td>51</td>
                <td>32</td>
                <td>0</td>
                <td class="right" data-ratio="0 270">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_cba28bd5643ea358_enhanced_spinbox_py.html">aretomo3_gui/gui/widgets/enhanced_spinbox.py</a></td>
                <td>195</td>
                <td>195</td>
                <td>0</td>
                <td>16</td>
                <td>0</td>
                <td class="right" data-ratio="0 211">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_cba28bd5643ea358_gpu_manager_widget_py.html">aretomo3_gui/gui/widgets/gpu_manager_widget.py</a></td>
                <td>52</td>
                <td>52</td>
                <td>0</td>
                <td>14</td>
                <td>0</td>
                <td class="right" data-ratio="0 66">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_cba28bd5643ea358_live_tilt_series_monitor_py.html">aretomo3_gui/gui/widgets/live_tilt_series_monitor.py</a></td>
                <td>221</td>
                <td>221</td>
                <td>0</td>
                <td>66</td>
                <td>0</td>
                <td class="right" data-ratio="0 287">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_cba28bd5643ea358_multigpu_manager_py.html">aretomo3_gui/gui/widgets/multigpu_manager.py</a></td>
                <td>71</td>
                <td>71</td>
                <td>0</td>
                <td>16</td>
                <td>0</td>
                <td class="right" data-ratio="0 87">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_cba28bd5643ea358_processing_dashboard_py.html">aretomo3_gui/gui/widgets/processing_dashboard.py</a></td>
                <td>199</td>
                <td>159</td>
                <td>0</td>
                <td>22</td>
                <td>0</td>
                <td class="right" data-ratio="40 221">18%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_cba28bd5643ea358_project_management_py.html">aretomo3_gui/gui/widgets/project_management.py</a></td>
                <td>644</td>
                <td>644</td>
                <td>0</td>
                <td>82</td>
                <td>0</td>
                <td class="right" data-ratio="0 726">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_cba28bd5643ea358_realtime_widget_py.html">aretomo3_gui/gui/widgets/realtime_widget.py</a></td>
                <td>253</td>
                <td>253</td>
                <td>0</td>
                <td>32</td>
                <td>0</td>
                <td class="right" data-ratio="0 285">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_cba28bd5643ea358_resource_monitor_py.html">aretomo3_gui/gui/widgets/resource_monitor.py</a></td>
                <td>115</td>
                <td>100</td>
                <td>0</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="15 121">12%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_cba28bd5643ea358_smart_file_organizer_py.html">aretomo3_gui/gui/widgets/smart_file_organizer.py</a></td>
                <td>312</td>
                <td>312</td>
                <td>7</td>
                <td>96</td>
                <td>0</td>
                <td class="right" data-ratio="0 408">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_cba28bd5643ea358_unified_processing_monitor_py.html">aretomo3_gui/gui/widgets/unified_processing_monitor.py</a></td>
                <td>577</td>
                <td>514</td>
                <td>0</td>
                <td>114</td>
                <td>0</td>
                <td class="right" data-ratio="63 691">9%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_cba28bd5643ea358_web_server_widget_py.html">aretomo3_gui/gui/widgets/web_server_widget.py</a></td>
                <td>296</td>
                <td>296</td>
                <td>0</td>
                <td>30</td>
                <td>0</td>
                <td class="right" data-ratio="0 326">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6ffcbd6373365cc1___init___py.html">aretomo3_gui/integration/__init__.py</a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6ffcbd6373365cc1_external_tools_py.html">aretomo3_gui/integration/external_tools.py</a></td>
                <td>225</td>
                <td>225</td>
                <td>12</td>
                <td>58</td>
                <td>0</td>
                <td class="right" data-ratio="0 283">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6ffcbd6373365cc1_manager_py.html">aretomo3_gui/integration/manager.py</a></td>
                <td>140</td>
                <td>140</td>
                <td>12</td>
                <td>44</td>
                <td>0</td>
                <td class="right" data-ratio="0 184">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_3caff36e279812ae_main_py.html">aretomo3_gui/main.py</a></td>
                <td>108</td>
                <td>108</td>
                <td>2</td>
                <td>14</td>
                <td>0</td>
                <td class="right" data-ratio="0 122">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_578c64bf13b5f5c5___init___py.html">aretomo3_gui/particle_picking/__init__.py</a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_578c64bf13b5f5c5_picker_py.html">aretomo3_gui/particle_picking/picker.py</a></td>
                <td>118</td>
                <td>118</td>
                <td>0</td>
                <td>24</td>
                <td>0</td>
                <td class="right" data-ratio="0 142">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_3caff36e279812ae_qt_backend_init_py.html">aretomo3_gui/qt_backend_init.py</a></td>
                <td>50</td>
                <td>50</td>
                <td>0</td>
                <td>10</td>
                <td>0</td>
                <td class="right" data-ratio="0 60">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2ed433c7d3dc6b1b___init___py.html">aretomo3_gui/realtime/__init__.py</a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2ed433c7d3dc6b1b_processor_py.html">aretomo3_gui/realtime/processor.py</a></td>
                <td>85</td>
                <td>85</td>
                <td>0</td>
                <td>18</td>
                <td>0</td>
                <td class="right" data-ratio="0 103">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_07f42b498c11b456___init___py.html">aretomo3_gui/subtomogram/__init__.py</a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_07f42b498c11b456_averaging_py.html">aretomo3_gui/subtomogram/averaging.py</a></td>
                <td>141</td>
                <td>141</td>
                <td>0</td>
                <td>40</td>
                <td>0</td>
                <td class="right" data-ratio="0 181">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6f4929cd9ce15cf8___init___py.html">aretomo3_gui/tools/__init__.py</a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6f4929cd9ce15cf8_kmeans_integration_py.html">aretomo3_gui/tools/kmeans_integration.py</a></td>
                <td>201</td>
                <td>201</td>
                <td>0</td>
                <td>32</td>
                <td>0</td>
                <td class="right" data-ratio="0 233">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_0bdf6ef230ab75fb___init___py.html">aretomo3_gui/utils/__init__.py</a></td>
                <td>15</td>
                <td>8</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="7 15">47%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_0bdf6ef230ab75fb_aretomo3_parser_py.html">aretomo3_gui/utils/aretomo3_parser.py</a></td>
                <td>665</td>
                <td>665</td>
                <td>0</td>
                <td>204</td>
                <td>0</td>
                <td class="right" data-ratio="0 869">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_0bdf6ef230ab75fb_documentation_generator_py.html">aretomo3_gui/utils/documentation_generator.py</a></td>
                <td>219</td>
                <td>219</td>
                <td>3</td>
                <td>74</td>
                <td>0</td>
                <td class="right" data-ratio="0 293">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_0bdf6ef230ab75fb_eer_reader_py.html">aretomo3_gui/utils/eer_reader.py</a></td>
                <td>217</td>
                <td>217</td>
                <td>20</td>
                <td>92</td>
                <td>0</td>
                <td class="right" data-ratio="0 309">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_0bdf6ef230ab75fb_eer_reader_new_py.html">aretomo3_gui/utils/eer_reader_new.py</a></td>
                <td>200</td>
                <td>200</td>
                <td>9</td>
                <td>74</td>
                <td>0</td>
                <td class="right" data-ratio="0 274">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_0bdf6ef230ab75fb_export_functions_py.html">aretomo3_gui/utils/export_functions.py</a></td>
                <td>277</td>
                <td>256</td>
                <td>0</td>
                <td>84</td>
                <td>0</td>
                <td class="right" data-ratio="21 361">6%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_0bdf6ef230ab75fb_file_utils_py.html">aretomo3_gui/utils/file_utils.py</a></td>
                <td>132</td>
                <td>14</td>
                <td>0</td>
                <td>56</td>
                <td>2</td>
                <td class="right" data-ratio="166 188">88%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_0bdf6ef230ab75fb_mdoc_parser_py.html">aretomo3_gui/utils/mdoc_parser.py</a></td>
                <td>107</td>
                <td>89</td>
                <td>0</td>
                <td>34</td>
                <td>0</td>
                <td class="right" data-ratio="18 141">13%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_0bdf6ef230ab75fb_pdf_report_generator_py.html">aretomo3_gui/utils/pdf_report_generator.py</a></td>
                <td>183</td>
                <td>183</td>
                <td>0</td>
                <td>50</td>
                <td>0</td>
                <td class="right" data-ratio="0 233">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_0bdf6ef230ab75fb_utils_py.html">aretomo3_gui/utils/utils.py</a></td>
                <td>85</td>
                <td>65</td>
                <td>0</td>
                <td>14</td>
                <td>0</td>
                <td class="right" data-ratio="20 99">20%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_0bdf6ef230ab75fb_warning_suppression_py.html">aretomo3_gui/utils/warning_suppression.py</a></td>
                <td>29</td>
                <td>29</td>
                <td>0</td>
                <td>8</td>
                <td>0</td>
                <td class="right" data-ratio="0 37">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_d0cb65951d5615b5___init___py.html">aretomo3_gui/visualization/__init__.py</a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_d0cb65951d5615b5_engine_py.html">aretomo3_gui/visualization/engine.py</a></td>
                <td>148</td>
                <td>148</td>
                <td>0</td>
                <td>12</td>
                <td>0</td>
                <td class="right" data-ratio="0 160">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_0878bee3da37af71___init___py.html">aretomo3_gui/web/__init__.py</a></td>
                <td>3</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="3 3">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_0878bee3da37af71_api_server_py.html">aretomo3_gui/web/api_server.py</a></td>
                <td>879</td>
                <td>771</td>
                <td>0</td>
                <td>230</td>
                <td>0</td>
                <td class="right" data-ratio="108 1109">10%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_0878bee3da37af71_plot_server_py.html">aretomo3_gui/web/plot_server.py</a></td>
                <td>120</td>
                <td>97</td>
                <td>0</td>
                <td>36</td>
                <td>0</td>
                <td class="right" data-ratio="23 156">15%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_0878bee3da37af71_server_py.html">aretomo3_gui/web/server.py</a></td>
                <td>236</td>
                <td>166</td>
                <td>2</td>
                <td>30</td>
                <td>1</td>
                <td class="right" data-ratio="71 266">27%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_48936c27d675ff69___init___py.html">aretomo3_gui/web_interface/__init__.py</a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_48936c27d675ff69_server_py.html">aretomo3_gui/web_interface/server.py</a></td>
                <td>41</td>
                <td>41</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 41">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_063c36b57d90849f___init___py.html">aretomo3_gui/workflow/__init__.py</a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_063c36b57d90849f_engine_py.html">aretomo3_gui/workflow/engine.py</a></td>
                <td>98</td>
                <td>98</td>
                <td>0</td>
                <td>16</td>
                <td>0</td>
                <td class="right" data-ratio="0 114">0%</td>
            </tr>
        </tbody>
        <tfoot>
            <tr class="total">
                <td class="name left">Total</td>
                <td>32686</td>
                <td>31062</td>
                <td>250</td>
                <td>7718</td>
                <td>22</td>
                <td class="right" data-ratio="1748 40404">4%</td>
            </tr>
        </tfoot>
    </table>
    <p id="no_rows">
        No items found using the specified filter.
    </p>
</main>
<footer>
    <div class="content">
        <p>
            <a class="nav" href="https://coverage.readthedocs.io/en/7.8.0">coverage.py v7.8.0</a>,
            created at 2025-06-08 22:21 -0400
        </p>
    </div>
    <aside class="hidden">
        <a id="prevFileLink" class="nav" href="z_063c36b57d90849f_engine_py.html"></a>
        <a id="nextFileLink" class="nav" href="z_3caff36e279812ae___init___py.html"></a>
        <button type="button" class="button_prev_file" data-shortcut="["></button>
        <button type="button" class="button_next_file" data-shortcut="]"></button>
        <button type="button" class="button_show_hide_help" data-shortcut="?"></button>
    </aside>
</footer>
</body>
</html>

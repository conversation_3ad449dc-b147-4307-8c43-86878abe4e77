#!/usr/bin/env python3
"""
Professional Deployment Structure Setup
Organizes the project for production deployment with proper directory structure.
"""

import os
import shutil
import json
from pathlib import Path
from datetime import datetime

def create_deployment_structure():
    """Create professional deployment directory structure."""
    
    print("🚀 CREATING PROFESSIONAL DEPLOYMENT STRUCTURE")
    print("=" * 60)
    
    # Define the professional structure
    structure = {
        "deployment": {
            "AreTomo3-GUI-Professional-v3.0.0": {
                "src": {
                    "aretomo3_gui": "main_package"
                },
                "bin": {
                    "aretomo3-gui": "executable_script",
                    "aretomo3-gui.bat": "windows_script"
                },
                "config": {
                    "default_config.json": "default_configuration",
                    "logging.conf": "logging_configuration"
                },
                "docs": {
                    "README.md": "main_documentation",
                    "INSTALLATION.md": "installation_guide",
                    "USER_GUIDE.md": "user_guide",
                    "API_REFERENCE.md": "api_documentation",
                    "CHANGELOG.md": "version_history"
                },
                "tests": {
                    "unit": "unit_tests",
                    "integration": "integration_tests",
                    "robustness": "robustness_tests"
                },
                "scripts": {
                    "install.py": "installation_script",
                    "setup.py": "setup_script",
                    "verify_installation.py": "verification_script"
                },
                "requirements": {
                    "requirements.txt": "core_dependencies",
                    "requirements-dev.txt": "development_dependencies",
                    "requirements-optional.txt": "optional_dependencies"
                },
                "examples": {
                    "basic_usage.py": "basic_examples",
                    "advanced_usage.py": "advanced_examples",
                    "api_examples.py": "api_examples"
                },
                "data": {
                    "sample_data": "sample_datasets",
                    "templates": "configuration_templates"
                }
            }
        }
    }
    
    # Create the deployment directory
    deployment_root = Path("deployment/AreTomo3-GUI-Professional-v3.0.0")
    deployment_root.mkdir(parents=True, exist_ok=True)
    
    # Create all directories
    for main_dir, subdirs in structure["deployment"]["AreTomo3-GUI-Professional-v3.0.0"].items():
        main_path = deployment_root / main_dir
        main_path.mkdir(parents=True, exist_ok=True)
        print(f"✅ Created: {main_path}")

        if isinstance(subdirs, dict):
            for subdir in subdirs.keys():
                if not subdir.endswith('.py') and not subdir.endswith('.txt') and not subdir.endswith('.md'):
                    dir_path = deployment_root / main_dir / subdir
                    dir_path.mkdir(parents=True, exist_ok=True)
                    print(f"✅ Created: {dir_path}")
    
    return deployment_root

def copy_source_code(deployment_root):
    """Copy source code to deployment structure."""
    print("\n📦 COPYING SOURCE CODE")
    print("=" * 40)
    
    src_dest = deployment_root / "src"
    
    # Copy main package
    if Path("aretomo3_gui").exists():
        shutil.copytree("aretomo3_gui", src_dest / "aretomo3_gui", dirs_exist_ok=True)
        print("✅ Copied main package: aretomo3_gui")
    
    # Copy essential files to root
    essential_files = [
        "LICENSE",
        "README.md", 
        "pyproject.toml"
    ]
    
    for file in essential_files:
        if Path(file).exists():
            shutil.copy2(file, deployment_root / file)
            print(f"✅ Copied: {file}")

def copy_tests(deployment_root):
    """Copy and organize tests."""
    print("\n🧪 ORGANIZING TESTS")
    print("=" * 30)
    
    tests_dest = deployment_root / "tests"
    
    # Copy comprehensive tests
    test_files = [
        "tests/test_real_file_utils.py",
        "tests/test_real_analytics.py", 
        "tests/test_real_data_management.py",
        "tests/test_real_core.py",
        "tests/test_real_processing.py",
        "tests/test_real_web.py",
        "tests/test_real_gui.py",
        "tests/test_real_integration.py",
        "tests/test_real_analysis.py"
    ]
    
    # Copy to unit tests
    unit_dir = tests_dest / "unit"
    for test_file in test_files:
        if Path(test_file).exists():
            shutil.copy2(test_file, unit_dir / Path(test_file).name)
            print(f"✅ Copied test: {Path(test_file).name}")
    
    # Copy robustness test
    if Path("test_robustness_verification.py").exists():
        shutil.copy2("test_robustness_verification.py", tests_dest / "robustness" / "test_robustness.py")
        print("✅ Copied robustness test")
    
    # Copy test configuration
    if Path("tests/conftest.py").exists():
        shutil.copy2("tests/conftest.py", tests_dest / "conftest.py")
        print("✅ Copied test configuration")

def create_installation_scripts(deployment_root):
    """Create professional installation scripts."""
    print("\n⚙️  CREATING INSTALLATION SCRIPTS")
    print("=" * 40)
    
    scripts_dir = deployment_root / "scripts"
    
    # Create main installation script
    install_script = scripts_dir / "install.py"
    install_script.parent.mkdir(parents=True, exist_ok=True)
    with open(install_script, 'w') as f:
        f.write('''#!/usr/bin/env python3
"""
AreTomo3 GUI Professional Installation Script
Automated installation with dependency management and verification.
"""

import sys
import subprocess
import os
from pathlib import Path

def main():
    print("🚀 AreTomo3 GUI Professional Installation")
    print("=" * 50)
    
    # Check Python version
    if sys.version_info < (3.8):
        print("❌ Python 3.8+ required")
        sys.exit(1)
    
    print("✅ Python version check passed")
    
    # Install package
    try:
        subprocess.run([sys.executable, "-m", "pip", "install", "-e", "."], check=True)
        print("✅ Package installed successfully")
    except subprocess.CalledProcessError:
        print("❌ Installation failed")
        sys.exit(1)
    
    # Run verification
    try:
        subprocess.run([sys.executable, "scripts/verify_installation.py"], check=True)
        print("✅ Installation verified")
    except subprocess.CalledProcessError:
        print("⚠️  Installation verification had issues")
    
    print("\\n🎉 Installation complete!")
    print("Run 'aretomo3-gui' to start the application")

if __name__ == "__main__":
    main()
''')
    
    print("✅ Created installation script")
    
    # Create verification script
    verify_script = scripts_dir / "verify_installation.py"
    with open(verify_script, 'w') as f:
        f.write('''#!/usr/bin/env python3
"""
Installation Verification Script
Verifies that AreTomo3 GUI is properly installed and functional.
"""

import sys
import importlib

def verify_installation():
    print("🔍 VERIFYING INSTALLATION")
    print("=" * 40)
    
    # Test core imports
    try:
        import aretomo3_gui
        print("✅ Core package import successful")
    except ImportError as e:
        print(f"❌ Core package import failed: {e}")
        return False
    
    # Test key components
    components = [
        "aretomo3_gui.utils.file_utils",
        "aretomo3_gui.analytics.advanced_analytics",
        "aretomo3_gui.data_management.data_manager",
        "aretomo3_gui.core.config_manager",
        "aretomo3_gui.web.server"
    ]
    
    for component in components:
        try:
            importlib.import_module(component)
            print(f"✅ {component}")
        except ImportError as e:
            print(f"❌ {component}: {e}")
            return False
    
    print("\\n🎉 Installation verification successful!")
    return True

if __name__ == "__main__":
    success = verify_installation()
    sys.exit(0 if success else 1)
''')
    
    print("✅ Created verification script")

def create_executable_scripts(deployment_root):
    """Create executable scripts."""
    print("\n🔧 CREATING EXECUTABLE SCRIPTS")
    print("=" * 40)
    
    bin_dir = deployment_root / "bin"
    
    # Unix/Linux executable
    unix_script = bin_dir / "aretomo3-gui"
    with open(unix_script, 'w') as f:
        f.write('''#!/usr/bin/env python3
"""
AreTomo3 GUI Launcher Script
"""

import sys
import os
from pathlib import Path

# Add src to path
script_dir = Path(__file__).parent
src_dir = script_dir.parent / "src"
sys.path.insert(0, str(src_dir))

try:
    from aretomo3_gui.main import main
    main()
except ImportError as e:
    print(f"Error importing AreTomo3 GUI: {e}")
    print("Please ensure the package is properly installed")
    sys.exit(1)
except Exception as e:
    print(f"Error running AreTomo3 GUI: {e}")
    sys.exit(1)
''')
    
    # Make executable
    unix_script.chmod(0o755)
    print("✅ Created Unix/Linux launcher")
    
    # Windows batch script
    windows_script = bin_dir / "aretomo3-gui.bat"
    with open(windows_script, 'w') as f:
        f.write('''@echo off
REM AreTomo3 GUI Windows Launcher

set SCRIPT_DIR=%~dp0
set SRC_DIR=%SCRIPT_DIR%..\\src

python -c "import sys; sys.path.insert(0, r'%SRC_DIR%'); from aretomo3_gui.main import main; main()"

if %ERRORLEVEL% neq 0 (
    echo Error running AreTomo3 GUI
    pause
)
''')
    
    print("✅ Created Windows launcher")

def create_requirements_files(deployment_root):
    """Create comprehensive requirements files."""
    print("\n📋 CREATING REQUIREMENTS FILES")
    print("=" * 40)
    
    req_dir = deployment_root / "requirements"
    
    # Core requirements
    core_req = req_dir / "requirements.txt"
    with open(core_req, 'w') as f:
        f.write('''# AreTomo3 GUI Core Dependencies
# GUI Framework
PyQt6>=6.4.0

# Scientific Computing
numpy>=1.21.0
scipy>=1.7.0
matplotlib>=3.5.0

# Image Processing
Pillow>=8.3.0

# Data Handling
pandas>=1.3.0

# Configuration
toml>=0.10.2

# Logging
colorlog>=6.6.0

# Optional but recommended
psutil>=5.8.0
''')
    
    print("✅ Created core requirements")
    
    # Development requirements
    dev_req = req_dir / "requirements-dev.txt"
    with open(dev_req, 'w') as f:
        f.write('''# Development Dependencies
-r requirements.txt

# Testing
pytest>=7.0.0
pytest-cov>=4.0.0
pytest-qt>=4.0.0

# Code Quality
black>=22.0.0
flake8>=4.0.0
mypy>=0.950

# Documentation
sphinx>=4.5.0
sphinx-rtd-theme>=1.0.0
''')
    
    print("✅ Created development requirements")
    
    # Optional requirements
    opt_req = req_dir / "requirements-optional.txt"
    with open(opt_req, 'w') as f:
        f.write('''# Optional Dependencies for Enhanced Features

# Web Interface
Flask>=2.0.0
Flask-CORS>=3.0.0

# Advanced Analytics
scikit-learn>=1.0.0
scikit-image>=0.19.0

# 3D Visualization
napari>=0.4.15

# Configuration Formats
PyYAML>=6.0

# Advanced File Formats
h5py>=3.6.0
mrcfile>=1.3.0

# Performance
numba>=0.56.0

# Database
sqlalchemy>=1.4.0
''')
    
    print("✅ Created optional requirements")

def create_documentation(deployment_root):
    """Create comprehensive documentation."""
    print("\n📚 CREATING DOCUMENTATION")
    print("=" * 35)
    
    docs_dir = deployment_root / "docs"
    
    # Installation guide
    install_guide = docs_dir / "INSTALLATION.md"
    with open(install_guide, 'w') as f:
        f.write('''# AreTomo3 GUI Installation Guide

## Quick Installation

### Prerequisites
- Python 3.8 or higher
- pip package manager

### Automated Installation
```bash
cd AreTomo3-GUI-Professional-v3.0.0
python scripts/install.py
```

### Manual Installation
```bash
# Install core dependencies
pip install -r requirements/requirements.txt

# Install the package
pip install -e .

# Verify installation
python scripts/verify_installation.py
```

## Running the Application

### Command Line
```bash
# Using the launcher script
./bin/aretomo3-gui

# Or directly with Python
python -m aretomo3_gui
```

### Troubleshooting

#### Common Issues
1. **Import Errors**: Ensure all dependencies are installed
2. **Qt Issues**: Install PyQt6 system packages if needed
3. **Permission Errors**: Run with appropriate permissions

#### Getting Help
- Check the logs in the `logs/` directory
- Run the verification script: `python scripts/verify_installation.py`
- Consult the User Guide for detailed usage instructions

## Development Installation

For development work:
```bash
pip install -r requirements/requirements-dev.txt
```

This includes testing and code quality tools.
''')
    
    print("✅ Created installation guide")
    
    # User guide
    user_guide = docs_dir / "USER_GUIDE.md"
    with open(user_guide, 'w') as f:
        f.write('''# AreTomo3 GUI User Guide

## Getting Started

### First Launch
1. Run `aretomo3-gui` from command line
2. The main window will open with tabbed interface
3. Configure your AreTomo3 executable path in Settings

### Basic Workflow
1. **Input**: Select your tilt series data
2. **Parameters**: Configure processing parameters
3. **Processing**: Run AreTomo3 reconstruction
4. **Analysis**: View results and quality metrics
5. **Export**: Save results and reports

### Key Features

#### Real-time Processing
- Monitor processing progress in real-time
- Live quality assessment
- Automatic error detection

#### Advanced Analytics
- Motion correction analysis
- CTF estimation quality
- 3D reconstruction metrics

#### Web Interface
- Browser-based dashboard
- Remote monitoring capabilities
- API for automation

### Tips and Best Practices
- Always verify input data quality first
- Use appropriate pixel size settings
- Monitor system resources during processing
- Regular backup of important results

For detailed feature documentation, see the API Reference.
''')
    
    print("✅ Created user guide")

def create_deployment_info(deployment_root):
    """Create deployment information file."""
    print("\n📄 CREATING DEPLOYMENT INFO")
    print("=" * 35)
    
    info = {
        "name": "AreTomo3-GUI-Professional",
        "version": "3.0.0",
        "build_date": datetime.now().isoformat(),
        "description": "Professional AreTomo3 GUI with comprehensive features",
        "features": [
            "Real-time processing monitoring",
            "Advanced analytics and quality assessment", 
            "Web-based dashboard",
            "Comprehensive testing (3000+ lines)",
            "Robust error handling",
            "Professional deployment structure"
        ],
        "requirements": {
            "python": ">=3.8",
            "core_dependencies": "See requirements/requirements.txt",
            "optional_dependencies": "See requirements/requirements-optional.txt"
        },
        "installation": {
            "automated": "python scripts/install.py",
            "manual": "pip install -e .",
            "verification": "python scripts/verify_installation.py"
        },
        "usage": {
            "gui": "./bin/aretomo3-gui",
            "cli": "python -m aretomo3_gui",
            "web": "Access web interface at http://localhost:8000"
        }
    }
    
    info_file = deployment_root / "DEPLOYMENT_INFO.json"
    with open(info_file, 'w') as f:
        json.dump(info, f, indent=2)
    
    print("✅ Created deployment info")

def main():
    """Main deployment setup function."""
    try:
        # Create structure
        deployment_root = create_deployment_structure()
        
        # Copy source code
        copy_source_code(deployment_root)
        
        # Copy tests
        copy_tests(deployment_root)
        
        # Create scripts
        create_installation_scripts(deployment_root)
        create_executable_scripts(deployment_root)
        
        # Create requirements
        create_requirements_files(deployment_root)
        
        # Create documentation
        create_documentation(deployment_root)
        
        # Create deployment info
        create_deployment_info(deployment_root)
        
        print(f"\n🎉 DEPLOYMENT STRUCTURE CREATED SUCCESSFULLY!")
        print(f"📁 Location: {deployment_root}")
        print(f"🚀 Ready for professional deployment!")
        
        return deployment_root
        
    except Exception as e:
        print(f"❌ Error creating deployment structure: {e}")
        return None

if __name__ == "__main__":
    main()

#!/usr/bin/env python3
"""
Test Embedded CTF and Motion Viewer Integration
Verify that the embedded viewers are properly integrated into the main GUI
"""

import sys
import os
from pathlib import Path

# Add the project root to Python path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def test_imports():
    """Test that all embedded viewer components can be imported."""
    print("🧪 Testing Embedded Viewer Imports")
    
    try:
        # Test embedded viewer imports
        from aretomo3_gui.gui.embedded_viewers.ctf_viewer import EmbeddedCTFViewer
        print("✅ EmbeddedCTFViewer imported successfully")
        
        from aretomo3_gui.gui.embedded_viewers.motion_viewer import EmbeddedMotionViewer
        print("✅ EmbeddedMotionViewer imported successfully")
        
        from aretomo3_gui.gui.tabs.embedded_viewer_tab import EmbeddedViewerTab
        print("✅ EmbeddedViewerTab imported successfully")
        
        return True
        
    except ImportError as e:
        print(f"❌ Import failed: {e}")
        return False


def test_main_window_integration():
    """Test that the main window can integrate the embedded viewers."""
    print("\n🧪 Testing Main Window Integration")
    
    try:
        # Import PyQt6 components
        from PyQt6.QtWidgets import QApplication
        from PyQt6.QtCore import Qt
        
        # Create QApplication if it doesn't exist
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)
            
        # Import and test main window
        from aretomo3_gui.gui.main_window import AreTomoGUI
        
        # Create main window instance
        main_window = AreTomoGUI()
        print("✅ Main window created successfully")
        
        # Check if embedded viewer tab exists
        tab_widget = main_window.tab_widget
        tab_found = False
        
        for i in range(tab_widget.count()):
            tab_text = tab_widget.tabText(i)
            if "CTF & Motion" in tab_text:
                tab_found = True
                print(f"✅ Embedded viewer tab found: '{tab_text}'")
                break
                
        if not tab_found:
            print("❌ Embedded viewer tab not found in main window")
            return False
            
        # Test tab switching
        if hasattr(main_window, 'embedded_viewer_tab'):
            print("✅ Embedded viewer tab attribute exists")
        else:
            print("⚠️  Embedded viewer tab attribute not found (may be in fallback mode)")
            
        return True
        
    except Exception as e:
        print(f"❌ Main window integration failed: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_viewer_functionality():
    """Test basic functionality of embedded viewers."""
    print("\n🧪 Testing Viewer Functionality")
    
    try:
        from PyQt6.QtWidgets import QApplication
        from aretomo3_gui.gui.embedded_viewers.ctf_viewer import EmbeddedCTFViewer
        from aretomo3_gui.gui.embedded_viewers.motion_viewer import EmbeddedMotionViewer
        
        # Create QApplication if it doesn't exist
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)
            
        # Test CTF viewer
        ctf_viewer = EmbeddedCTFViewer()
        print("✅ CTF viewer created successfully")
        
        # Test basic CTF viewer methods
        if hasattr(ctf_viewer, 'load_ctf_data'):
            print("✅ CTF viewer has load_ctf_data method")
        if hasattr(ctf_viewer, 'update_display'):
            print("✅ CTF viewer has update_display method")
            
        # Test Motion viewer
        motion_viewer = EmbeddedMotionViewer()
        print("✅ Motion viewer created successfully")
        
        # Test basic motion viewer methods
        if hasattr(motion_viewer, 'load_motion_data'):
            print("✅ Motion viewer has load_motion_data method")
        if hasattr(motion_viewer, 'update_display'):
            print("✅ Motion viewer has update_display method")
            
        return True
        
    except Exception as e:
        print(f"❌ Viewer functionality test failed: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_file_structure():
    """Test that all required files exist."""
    print("\n🧪 Testing File Structure")
    
    required_files = [
        "aretomo3_gui/gui/embedded_viewers/__init__.py",
        "aretomo3_gui/gui/embedded_viewers/ctf_viewer.py",
        "aretomo3_gui/gui/embedded_viewers/motion_viewer.py",
        "aretomo3_gui/gui/tabs/embedded_viewer_tab.py"
    ]
    
    all_exist = True
    for file_path in required_files:
        full_path = project_root / file_path
        if full_path.exists():
            print(f"✅ {file_path}")
        else:
            print(f"❌ {file_path} - NOT FOUND")
            all_exist = False
            
    return all_exist


def main():
    """Run all integration tests."""
    print("🚀 Testing Embedded CTF & Motion Viewer Integration")
    print("=" * 60)
    
    tests = [
        ("File Structure", test_file_structure),
        ("Component Imports", test_imports),
        ("Main Window Integration", test_main_window_integration),
        ("Viewer Functionality", test_viewer_functionality)
    ]
    
    results = []
    
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name} failed with exception: {e}")
            results.append((test_name, False))
    
    # Summary
    print("\n" + "=" * 60)
    print("🏁 INTEGRATION TEST SUMMARY")
    print("=" * 60)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✅ PASSED" if result else "❌ FAILED"
        print(f"{test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\nOverall: {passed}/{total} tests passed")
    
    if passed == total:
        print("\n🎉 ALL TESTS PASSED!")
        print("✅ Embedded CTF & Motion viewers are fully integrated!")
        print("\n📋 What's Available:")
        print("  • Embedded CTF Analysis Viewer")
        print("  • Embedded Motion Correction Viewer")
        print("  • Combined Analysis Tab")
        print("  • Integrated into Main GUI")
        print("  • Real-time data loading and analysis")
        print("  • Professional visualization")
        
        print("\n🚀 Next Steps:")
        print("  • Run the main GUI: python -m aretomo3_gui")
        print("  • Navigate to 'CTF & Motion Analysis' tab")
        print("  • Load your CTF and motion data")
        print("  • Enjoy the embedded viewers!")
        
    else:
        print(f"\n⚠️  {total - passed} tests failed")
        print("Please check the error messages above for details.")
        
    return passed == total


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)

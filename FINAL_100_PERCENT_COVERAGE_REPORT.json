{"timestamp": "2025-06-08T22:08:58.975598", "test_results": {"tests/test_real_file_utils.py": {"status": "PARTIAL", "passed": 0, "failed": 0}, "tests/test_real_analytics.py": {"status": "PARTIAL", "passed": 0, "failed": 0}, "tests/test_real_data_management.py": {"status": "PARTIAL", "passed": 0, "failed": 0}}, "coverage_results": {"total_coverage": 2.833017193905648, "lines_covered": 926, "lines_total": 32686, "missing_lines": 31760}, "summary": {"final_coverage": 4.002079002079002, "target_achieved": false, "improvement_from_1_1_percent": 2.902079002079002}}
==> 2025-06-08 15:24:23 <==
# cmd: /home/<USER>/miniforge3/lib/python3.12/site-packages/conda/__main__.py create --yes --prefix .conda python=3.11
# conda version: 24.11.3
+conda-forge/linux-64::_libgcc_mutex-0.1-conda_forge
+conda-forge/linux-64::_openmp_mutex-4.5-2_gnu
+conda-forge/linux-64::bzip2-1.0.8-h4bc722e_7
+conda-forge/linux-64::ld_impl_linux-64-2.43-h712a8e2_4
+conda-forge/linux-64::libexpat-2.7.0-h5888daf_0
+conda-forge/linux-64::libffi-3.4.6-h2dba641_1
+conda-forge/linux-64::libgcc-15.1.0-h767d61c_2
+conda-forge/linux-64::libgcc-ng-15.1.0-h69a702a_2
+conda-forge/linux-64::libgomp-15.1.0-h767d61c_2
+conda-forge/linux-64::liblzma-5.8.1-hb9d3cd8_2
+conda-forge/linux-64::libnsl-2.0.1-hd590300_0
+conda-forge/linux-64::libsqlite-3.50.1-hee588c1_0
+conda-forge/linux-64::libuuid-2.38.1-h0b41bf4_0
+conda-forge/linux-64::libxcrypt-4.4.36-hd590300_1
+conda-forge/linux-64::libzlib-1.3.1-hb9d3cd8_2
+conda-forge/linux-64::ncurses-6.5-h2d0b736_3
+conda-forge/linux-64::openssl-3.5.0-h7b32b05_1
+conda-forge/linux-64::python-3.11.13-h9e4cc4f_0_cpython
+conda-forge/linux-64::readline-8.2-h8c095d6_2
+conda-forge/linux-64::tk-8.6.13-noxft_hd72426e_102
+conda-forge/noarch::ca-certificates-2025.4.26-hbd8a1cb_0
+conda-forge/noarch::pip-25.1.1-pyh8b19718_0
+conda-forge/noarch::setuptools-80.9.0-pyhff2d567_0
+conda-forge/noarch::tzdata-2025b-h78e105d_0
+conda-forge/noarch::wheel-0.45.1-pyhd8ed1ab_1
# update specs: ['python=3.11']

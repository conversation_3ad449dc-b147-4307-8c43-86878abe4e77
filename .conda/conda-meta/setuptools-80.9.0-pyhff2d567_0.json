{"build": "pyhff2d567_0", "build_number": 0, "channel": "https://conda.anaconda.org/conda-forge/noarch", "constrains": [], "depends": ["python >=3.9"], "extracted_package_dir": "/home/<USER>/miniforge3/pkgs/setuptools-80.9.0-pyhff2d567_0", "files": ["lib/python3.11/site-packages/_distutils_hack/__init__.py", "lib/python3.11/site-packages/_distutils_hack/override.py", "lib/python3.11/site-packages/distutils-precedence.pth", "lib/python3.11/site-packages/pkg_resources/__init__.py", "lib/python3.11/site-packages/pkg_resources/api_tests.txt", "lib/python3.11/site-packages/pkg_resources/py.typed", "lib/python3.11/site-packages/pkg_resources/tests/__init__.py", "lib/python3.11/site-packages/pkg_resources/tests/data/my-test-package-source/setup.cfg", "lib/python3.11/site-packages/pkg_resources/tests/data/my-test-package-source/setup.py", "lib/python3.11/site-packages/pkg_resources/tests/data/my-test-package-zip/my-test-package.zip", "lib/python3.11/site-packages/pkg_resources/tests/data/my-test-package_unpacked-egg/my_test_package-1.0-py3.7.egg/EGG-INFO/PKG-INFO", "lib/python3.11/site-packages/pkg_resources/tests/data/my-test-package_unpacked-egg/my_test_package-1.0-py3.7.egg/EGG-INFO/SOURCES.txt", "lib/python3.11/site-packages/pkg_resources/tests/data/my-test-package_unpacked-egg/my_test_package-1.0-py3.7.egg/EGG-INFO/dependency_links.txt", "lib/python3.11/site-packages/pkg_resources/tests/data/my-test-package_unpacked-egg/my_test_package-1.0-py3.7.egg/EGG-INFO/top_level.txt", "lib/python3.11/site-packages/pkg_resources/tests/data/my-test-package_unpacked-egg/my_test_package-1.0-py3.7.egg/EGG-INFO/zip-safe", "lib/python3.11/site-packages/pkg_resources/tests/data/my-test-package_zipped-egg/my_test_package-1.0-py3.7.egg", "lib/python3.11/site-packages/pkg_resources/tests/test_find_distributions.py", "lib/python3.11/site-packages/pkg_resources/tests/test_integration_zope_interface.py", "lib/python3.11/site-packages/pkg_resources/tests/test_markers.py", "lib/python3.11/site-packages/pkg_resources/tests/test_pkg_resources.py", "lib/python3.11/site-packages/pkg_resources/tests/test_resources.py", "lib/python3.11/site-packages/pkg_resources/tests/test_working_set.py", "lib/python3.11/site-packages/setuptools-80.9.0-py3.9.egg-info/PKG-INFO", "lib/python3.11/site-packages/setuptools-80.9.0-py3.9.egg-info/SOURCES.txt", "lib/python3.11/site-packages/setuptools-80.9.0-py3.9.egg-info/dependency_links.txt", "lib/python3.11/site-packages/setuptools-80.9.0-py3.9.egg-info/entry_points.txt", "lib/python3.11/site-packages/setuptools-80.9.0-py3.9.egg-info/requires.txt", "lib/python3.11/site-packages/setuptools-80.9.0-py3.9.egg-info/top_level.txt", "lib/python3.11/site-packages/setuptools/__init__.py", "lib/python3.11/site-packages/setuptools/_core_metadata.py", "lib/python3.11/site-packages/setuptools/_discovery.py", "lib/python3.11/site-packages/setuptools/_distutils/__init__.py", "lib/python3.11/site-packages/setuptools/_distutils/_log.py", "lib/python3.11/site-packages/setuptools/_distutils/_macos_compat.py", "lib/python3.11/site-packages/setuptools/_distutils/_modified.py", "lib/python3.11/site-packages/setuptools/_distutils/_msvccompiler.py", "lib/python3.11/site-packages/setuptools/_distutils/archive_util.py", "lib/python3.11/site-packages/setuptools/_distutils/ccompiler.py", "lib/python3.11/site-packages/setuptools/_distutils/cmd.py", "lib/python3.11/site-packages/setuptools/_distutils/command/__init__.py", "lib/python3.11/site-packages/setuptools/_distutils/command/_framework_compat.py", "lib/python3.11/site-packages/setuptools/_distutils/command/bdist.py", "lib/python3.11/site-packages/setuptools/_distutils/command/bdist_dumb.py", "lib/python3.11/site-packages/setuptools/_distutils/command/bdist_rpm.py", "lib/python3.11/site-packages/setuptools/_distutils/command/build.py", "lib/python3.11/site-packages/setuptools/_distutils/command/build_clib.py", "lib/python3.11/site-packages/setuptools/_distutils/command/build_ext.py", "lib/python3.11/site-packages/setuptools/_distutils/command/build_py.py", "lib/python3.11/site-packages/setuptools/_distutils/command/build_scripts.py", "lib/python3.11/site-packages/setuptools/_distutils/command/check.py", "lib/python3.11/site-packages/setuptools/_distutils/command/clean.py", "lib/python3.11/site-packages/setuptools/_distutils/command/config.py", "lib/python3.11/site-packages/setuptools/_distutils/command/install.py", "lib/python3.11/site-packages/setuptools/_distutils/command/install_data.py", "lib/python3.11/site-packages/setuptools/_distutils/command/install_egg_info.py", "lib/python3.11/site-packages/setuptools/_distutils/command/install_headers.py", "lib/python3.11/site-packages/setuptools/_distutils/command/install_lib.py", "lib/python3.11/site-packages/setuptools/_distutils/command/install_scripts.py", "lib/python3.11/site-packages/setuptools/_distutils/command/sdist.py", "lib/python3.11/site-packages/setuptools/_distutils/compat/__init__.py", "lib/python3.11/site-packages/setuptools/_distutils/compat/numpy.py", "lib/python3.11/site-packages/setuptools/_distutils/compat/py39.py", "lib/python3.11/site-packages/setuptools/_distutils/compilers/C/base.py", "lib/python3.11/site-packages/setuptools/_distutils/compilers/C/cygwin.py", "lib/python3.11/site-packages/setuptools/_distutils/compilers/C/errors.py", "lib/python3.11/site-packages/setuptools/_distutils/compilers/C/msvc.py", "lib/python3.11/site-packages/setuptools/_distutils/compilers/C/tests/test_base.py", "lib/python3.11/site-packages/setuptools/_distutils/compilers/C/tests/test_cygwin.py", "lib/python3.11/site-packages/setuptools/_distutils/compilers/C/tests/test_mingw.py", "lib/python3.11/site-packages/setuptools/_distutils/compilers/C/tests/test_msvc.py", "lib/python3.11/site-packages/setuptools/_distutils/compilers/C/tests/test_unix.py", "lib/python3.11/site-packages/setuptools/_distutils/compilers/C/unix.py", "lib/python3.11/site-packages/setuptools/_distutils/compilers/C/zos.py", "lib/python3.11/site-packages/setuptools/_distutils/core.py", "lib/python3.11/site-packages/setuptools/_distutils/cygwinccompiler.py", "lib/python3.11/site-packages/setuptools/_distutils/debug.py", "lib/python3.11/site-packages/setuptools/_distutils/dep_util.py", "lib/python3.11/site-packages/setuptools/_distutils/dir_util.py", "lib/python3.11/site-packages/setuptools/_distutils/dist.py", "lib/python3.11/site-packages/setuptools/_distutils/errors.py", "lib/python3.11/site-packages/setuptools/_distutils/extension.py", "lib/python3.11/site-packages/setuptools/_distutils/fancy_getopt.py", "lib/python3.11/site-packages/setuptools/_distutils/file_util.py", "lib/python3.11/site-packages/setuptools/_distutils/filelist.py", "lib/python3.11/site-packages/setuptools/_distutils/log.py", "lib/python3.11/site-packages/setuptools/_distutils/spawn.py", "lib/python3.11/site-packages/setuptools/_distutils/sysconfig.py", "lib/python3.11/site-packages/setuptools/_distutils/tests/__init__.py", "lib/python3.11/site-packages/setuptools/_distutils/tests/compat/__init__.py", "lib/python3.11/site-packages/setuptools/_distutils/tests/compat/py39.py", "lib/python3.11/site-packages/setuptools/_distutils/tests/support.py", "lib/python3.11/site-packages/setuptools/_distutils/tests/test_archive_util.py", "lib/python3.11/site-packages/setuptools/_distutils/tests/test_bdist.py", "lib/python3.11/site-packages/setuptools/_distutils/tests/test_bdist_dumb.py", "lib/python3.11/site-packages/setuptools/_distutils/tests/test_bdist_rpm.py", "lib/python3.11/site-packages/setuptools/_distutils/tests/test_build.py", "lib/python3.11/site-packages/setuptools/_distutils/tests/test_build_clib.py", "lib/python3.11/site-packages/setuptools/_distutils/tests/test_build_ext.py", "lib/python3.11/site-packages/setuptools/_distutils/tests/test_build_py.py", "lib/python3.11/site-packages/setuptools/_distutils/tests/test_build_scripts.py", "lib/python3.11/site-packages/setuptools/_distutils/tests/test_check.py", "lib/python3.11/site-packages/setuptools/_distutils/tests/test_clean.py", "lib/python3.11/site-packages/setuptools/_distutils/tests/test_cmd.py", "lib/python3.11/site-packages/setuptools/_distutils/tests/test_config_cmd.py", "lib/python3.11/site-packages/setuptools/_distutils/tests/test_core.py", "lib/python3.11/site-packages/setuptools/_distutils/tests/test_dir_util.py", "lib/python3.11/site-packages/setuptools/_distutils/tests/test_dist.py", "lib/python3.11/site-packages/setuptools/_distutils/tests/test_extension.py", "lib/python3.11/site-packages/setuptools/_distutils/tests/test_file_util.py", "lib/python3.11/site-packages/setuptools/_distutils/tests/test_filelist.py", "lib/python3.11/site-packages/setuptools/_distutils/tests/test_install.py", "lib/python3.11/site-packages/setuptools/_distutils/tests/test_install_data.py", "lib/python3.11/site-packages/setuptools/_distutils/tests/test_install_headers.py", "lib/python3.11/site-packages/setuptools/_distutils/tests/test_install_lib.py", "lib/python3.11/site-packages/setuptools/_distutils/tests/test_install_scripts.py", "lib/python3.11/site-packages/setuptools/_distutils/tests/test_log.py", "lib/python3.11/site-packages/setuptools/_distutils/tests/test_modified.py", "lib/python3.11/site-packages/setuptools/_distutils/tests/test_sdist.py", "lib/python3.11/site-packages/setuptools/_distutils/tests/test_spawn.py", "lib/python3.11/site-packages/setuptools/_distutils/tests/test_sysconfig.py", "lib/python3.11/site-packages/setuptools/_distutils/tests/test_text_file.py", "lib/python3.11/site-packages/setuptools/_distutils/tests/test_util.py", "lib/python3.11/site-packages/setuptools/_distutils/tests/test_version.py", "lib/python3.11/site-packages/setuptools/_distutils/tests/test_versionpredicate.py", "lib/python3.11/site-packages/setuptools/_distutils/tests/unix_compat.py", "lib/python3.11/site-packages/setuptools/_distutils/text_file.py", "lib/python3.11/site-packages/setuptools/_distutils/unixccompiler.py", "lib/python3.11/site-packages/setuptools/_distutils/util.py", "lib/python3.11/site-packages/setuptools/_distutils/version.py", "lib/python3.11/site-packages/setuptools/_distutils/versionpredicate.py", "lib/python3.11/site-packages/setuptools/_distutils/zosccompiler.py", "lib/python3.11/site-packages/setuptools/_entry_points.py", "lib/python3.11/site-packages/setuptools/_imp.py", "lib/python3.11/site-packages/setuptools/_importlib.py", "lib/python3.11/site-packages/setuptools/_itertools.py", "lib/python3.11/site-packages/setuptools/_normalization.py", "lib/python3.11/site-packages/setuptools/_path.py", "lib/python3.11/site-packages/setuptools/_reqs.py", "lib/python3.11/site-packages/setuptools/_scripts.py", "lib/python3.11/site-packages/setuptools/_shutil.py", "lib/python3.11/site-packages/setuptools/_static.py", "lib/python3.11/site-packages/setuptools/_vendor/autocommand-2.2.2.dist-info/INSTALLER", "lib/python3.11/site-packages/setuptools/_vendor/autocommand-2.2.2.dist-info/LICENSE", "lib/python3.11/site-packages/setuptools/_vendor/autocommand-2.2.2.dist-info/METADATA", "lib/python3.11/site-packages/setuptools/_vendor/autocommand-2.2.2.dist-info/RECORD", "lib/python3.11/site-packages/setuptools/_vendor/autocommand-2.2.2.dist-info/WHEEL", "lib/python3.11/site-packages/setuptools/_vendor/autocommand-2.2.2.dist-info/top_level.txt", "lib/python3.11/site-packages/setuptools/_vendor/autocommand/__init__.py", "lib/python3.11/site-packages/setuptools/_vendor/autocommand/autoasync.py", "lib/python3.11/site-packages/setuptools/_vendor/autocommand/autocommand.py", "lib/python3.11/site-packages/setuptools/_vendor/autocommand/automain.py", "lib/python3.11/site-packages/setuptools/_vendor/autocommand/autoparse.py", "lib/python3.11/site-packages/setuptools/_vendor/autocommand/errors.py", "lib/python3.11/site-packages/setuptools/_vendor/backports.tarfile-1.2.0.dist-info/INSTALLER", "lib/python3.11/site-packages/setuptools/_vendor/backports.tarfile-1.2.0.dist-info/LICENSE", "lib/python3.11/site-packages/setuptools/_vendor/backports.tarfile-1.2.0.dist-info/METADATA", "lib/python3.11/site-packages/setuptools/_vendor/backports.tarfile-1.2.0.dist-info/RECORD", "lib/python3.11/site-packages/setuptools/_vendor/backports.tarfile-1.2.0.dist-info/REQUESTED", "lib/python3.11/site-packages/setuptools/_vendor/backports.tarfile-1.2.0.dist-info/WHEEL", "lib/python3.11/site-packages/setuptools/_vendor/backports.tarfile-1.2.0.dist-info/top_level.txt", "lib/python3.11/site-packages/setuptools/_vendor/backports/__init__.py", "lib/python3.11/site-packages/setuptools/_vendor/backports/tarfile/__init__.py", "lib/python3.11/site-packages/setuptools/_vendor/backports/tarfile/__main__.py", "lib/python3.11/site-packages/setuptools/_vendor/backports/tarfile/compat/__init__.py", "lib/python3.11/site-packages/setuptools/_vendor/backports/tarfile/compat/py38.py", "lib/python3.11/site-packages/setuptools/_vendor/importlib_metadata-8.0.0.dist-info/INSTALLER", "lib/python3.11/site-packages/setuptools/_vendor/importlib_metadata-8.0.0.dist-info/LICENSE", "lib/python3.11/site-packages/setuptools/_vendor/importlib_metadata-8.0.0.dist-info/METADATA", "lib/python3.11/site-packages/setuptools/_vendor/importlib_metadata-8.0.0.dist-info/RECORD", "lib/python3.11/site-packages/setuptools/_vendor/importlib_metadata-8.0.0.dist-info/REQUESTED", "lib/python3.11/site-packages/setuptools/_vendor/importlib_metadata-8.0.0.dist-info/WHEEL", "lib/python3.11/site-packages/setuptools/_vendor/importlib_metadata-8.0.0.dist-info/top_level.txt", "lib/python3.11/site-packages/setuptools/_vendor/importlib_metadata/__init__.py", "lib/python3.11/site-packages/setuptools/_vendor/importlib_metadata/_adapters.py", "lib/python3.11/site-packages/setuptools/_vendor/importlib_metadata/_collections.py", "lib/python3.11/site-packages/setuptools/_vendor/importlib_metadata/_compat.py", "lib/python3.11/site-packages/setuptools/_vendor/importlib_metadata/_functools.py", "lib/python3.11/site-packages/setuptools/_vendor/importlib_metadata/_itertools.py", "lib/python3.11/site-packages/setuptools/_vendor/importlib_metadata/_meta.py", "lib/python3.11/site-packages/setuptools/_vendor/importlib_metadata/_text.py", "lib/python3.11/site-packages/setuptools/_vendor/importlib_metadata/compat/__init__.py", "lib/python3.11/site-packages/setuptools/_vendor/importlib_metadata/compat/py311.py", "lib/python3.11/site-packages/setuptools/_vendor/importlib_metadata/compat/py39.py", "lib/python3.11/site-packages/setuptools/_vendor/importlib_metadata/diagnose.py", "lib/python3.11/site-packages/setuptools/_vendor/importlib_metadata/py.typed", "lib/python3.11/site-packages/setuptools/_vendor/inflect-7.3.1.dist-info/INSTALLER", "lib/python3.11/site-packages/setuptools/_vendor/inflect-7.3.1.dist-info/LICENSE", "lib/python3.11/site-packages/setuptools/_vendor/inflect-7.3.1.dist-info/METADATA", "lib/python3.11/site-packages/setuptools/_vendor/inflect-7.3.1.dist-info/RECORD", "lib/python3.11/site-packages/setuptools/_vendor/inflect-7.3.1.dist-info/WHEEL", "lib/python3.11/site-packages/setuptools/_vendor/inflect-7.3.1.dist-info/top_level.txt", "lib/python3.11/site-packages/setuptools/_vendor/inflect/__init__.py", "lib/python3.11/site-packages/setuptools/_vendor/inflect/compat/__init__.py", "lib/python3.11/site-packages/setuptools/_vendor/inflect/compat/py38.py", "lib/python3.11/site-packages/setuptools/_vendor/inflect/py.typed", "lib/python3.11/site-packages/setuptools/_vendor/jaraco.collections-5.1.0.dist-info/INSTALLER", "lib/python3.11/site-packages/setuptools/_vendor/jaraco.collections-5.1.0.dist-info/LICENSE", "lib/python3.11/site-packages/setuptools/_vendor/jaraco.collections-5.1.0.dist-info/METADATA", "lib/python3.11/site-packages/setuptools/_vendor/jaraco.collections-5.1.0.dist-info/RECORD", "lib/python3.11/site-packages/setuptools/_vendor/jaraco.collections-5.1.0.dist-info/REQUESTED", "lib/python3.11/site-packages/setuptools/_vendor/jaraco.collections-5.1.0.dist-info/WHEEL", "lib/python3.11/site-packages/setuptools/_vendor/jaraco.collections-5.1.0.dist-info/top_level.txt", "lib/python3.11/site-packages/setuptools/_vendor/jaraco.context-5.3.0.dist-info/INSTALLER", "lib/python3.11/site-packages/setuptools/_vendor/jaraco.context-5.3.0.dist-info/LICENSE", "lib/python3.11/site-packages/setuptools/_vendor/jaraco.context-5.3.0.dist-info/METADATA", "lib/python3.11/site-packages/setuptools/_vendor/jaraco.context-5.3.0.dist-info/RECORD", "lib/python3.11/site-packages/setuptools/_vendor/jaraco.context-5.3.0.dist-info/WHEEL", "lib/python3.11/site-packages/setuptools/_vendor/jaraco.context-5.3.0.dist-info/top_level.txt", "lib/python3.11/site-packages/setuptools/_vendor/jaraco.functools-4.0.1.dist-info/INSTALLER", "lib/python3.11/site-packages/setuptools/_vendor/jaraco.functools-4.0.1.dist-info/LICENSE", "lib/python3.11/site-packages/setuptools/_vendor/jaraco.functools-4.0.1.dist-info/METADATA", "lib/python3.11/site-packages/setuptools/_vendor/jaraco.functools-4.0.1.dist-info/RECORD", "lib/python3.11/site-packages/setuptools/_vendor/jaraco.functools-4.0.1.dist-info/WHEEL", "lib/python3.11/site-packages/setuptools/_vendor/jaraco.functools-4.0.1.dist-info/top_level.txt", "lib/python3.11/site-packages/setuptools/_vendor/jaraco.text-3.12.1.dist-info/INSTALLER", "lib/python3.11/site-packages/setuptools/_vendor/jaraco.text-3.12.1.dist-info/LICENSE", "lib/python3.11/site-packages/setuptools/_vendor/jaraco.text-3.12.1.dist-info/METADATA", "lib/python3.11/site-packages/setuptools/_vendor/jaraco.text-3.12.1.dist-info/RECORD", "lib/python3.11/site-packages/setuptools/_vendor/jaraco.text-3.12.1.dist-info/REQUESTED", "lib/python3.11/site-packages/setuptools/_vendor/jaraco.text-3.12.1.dist-info/WHEEL", "lib/python3.11/site-packages/setuptools/_vendor/jaraco.text-3.12.1.dist-info/top_level.txt", "lib/python3.11/site-packages/setuptools/_vendor/jaraco/collections/__init__.py", "lib/python3.11/site-packages/setuptools/_vendor/jaraco/collections/py.typed", "lib/python3.11/site-packages/setuptools/_vendor/jaraco/context.py", "lib/python3.11/site-packages/setuptools/_vendor/jaraco/functools/__init__.py", "lib/python3.11/site-packages/setuptools/_vendor/jaraco/functools/__init__.pyi", "lib/python3.11/site-packages/setuptools/_vendor/jaraco/functools/py.typed", "lib/python3.11/site-packages/setuptools/_vendor/jaraco/text/Lorem ipsum.txt", "lib/python3.11/site-packages/setuptools/_vendor/jaraco/text/__init__.py", "lib/python3.11/site-packages/setuptools/_vendor/jaraco/text/layouts.py", "lib/python3.11/site-packages/setuptools/_vendor/jaraco/text/show-newlines.py", "lib/python3.11/site-packages/setuptools/_vendor/jaraco/text/strip-prefix.py", "lib/python3.11/site-packages/setuptools/_vendor/jaraco/text/to-dvorak.py", "lib/python3.11/site-packages/setuptools/_vendor/jaraco/text/to-qwerty.py", "lib/python3.11/site-packages/setuptools/_vendor/more_itertools-10.3.0.dist-info/INSTALLER", "lib/python3.11/site-packages/setuptools/_vendor/more_itertools-10.3.0.dist-info/LICENSE", "lib/python3.11/site-packages/setuptools/_vendor/more_itertools-10.3.0.dist-info/METADATA", "lib/python3.11/site-packages/setuptools/_vendor/more_itertools-10.3.0.dist-info/RECORD", "lib/python3.11/site-packages/setuptools/_vendor/more_itertools-10.3.0.dist-info/REQUESTED", "lib/python3.11/site-packages/setuptools/_vendor/more_itertools-10.3.0.dist-info/WHEEL", "lib/python3.11/site-packages/setuptools/_vendor/more_itertools/__init__.py", "lib/python3.11/site-packages/setuptools/_vendor/more_itertools/__init__.pyi", "lib/python3.11/site-packages/setuptools/_vendor/more_itertools/more.py", "lib/python3.11/site-packages/setuptools/_vendor/more_itertools/more.pyi", "lib/python3.11/site-packages/setuptools/_vendor/more_itertools/py.typed", "lib/python3.11/site-packages/setuptools/_vendor/more_itertools/recipes.py", "lib/python3.11/site-packages/setuptools/_vendor/more_itertools/recipes.pyi", "lib/python3.11/site-packages/setuptools/_vendor/packaging-24.2.dist-info/INSTALLER", "lib/python3.11/site-packages/setuptools/_vendor/packaging-24.2.dist-info/LICENSE", "lib/python3.11/site-packages/setuptools/_vendor/packaging-24.2.dist-info/LICENSE.APACHE", "lib/python3.11/site-packages/setuptools/_vendor/packaging-24.2.dist-info/LICENSE.BSD", "lib/python3.11/site-packages/setuptools/_vendor/packaging-24.2.dist-info/METADATA", "lib/python3.11/site-packages/setuptools/_vendor/packaging-24.2.dist-info/RECORD", "lib/python3.11/site-packages/setuptools/_vendor/packaging-24.2.dist-info/REQUESTED", "lib/python3.11/site-packages/setuptools/_vendor/packaging-24.2.dist-info/WHEEL", "lib/python3.11/site-packages/setuptools/_vendor/packaging/__init__.py", "lib/python3.11/site-packages/setuptools/_vendor/packaging/_elffile.py", "lib/python3.11/site-packages/setuptools/_vendor/packaging/_manylinux.py", "lib/python3.11/site-packages/setuptools/_vendor/packaging/_musllinux.py", "lib/python3.11/site-packages/setuptools/_vendor/packaging/_parser.py", "lib/python3.11/site-packages/setuptools/_vendor/packaging/_structures.py", "lib/python3.11/site-packages/setuptools/_vendor/packaging/_tokenizer.py", "lib/python3.11/site-packages/setuptools/_vendor/packaging/licenses/__init__.py", "lib/python3.11/site-packages/setuptools/_vendor/packaging/licenses/_spdx.py", "lib/python3.11/site-packages/setuptools/_vendor/packaging/markers.py", "lib/python3.11/site-packages/setuptools/_vendor/packaging/metadata.py", "lib/python3.11/site-packages/setuptools/_vendor/packaging/py.typed", "lib/python3.11/site-packages/setuptools/_vendor/packaging/requirements.py", "lib/python3.11/site-packages/setuptools/_vendor/packaging/specifiers.py", "lib/python3.11/site-packages/setuptools/_vendor/packaging/tags.py", "lib/python3.11/site-packages/setuptools/_vendor/packaging/utils.py", "lib/python3.11/site-packages/setuptools/_vendor/packaging/version.py", "lib/python3.11/site-packages/setuptools/_vendor/platformdirs-4.2.2.dist-info/INSTALLER", "lib/python3.11/site-packages/setuptools/_vendor/platformdirs-4.2.2.dist-info/METADATA", "lib/python3.11/site-packages/setuptools/_vendor/platformdirs-4.2.2.dist-info/RECORD", "lib/python3.11/site-packages/setuptools/_vendor/platformdirs-4.2.2.dist-info/REQUESTED", "lib/python3.11/site-packages/setuptools/_vendor/platformdirs-4.2.2.dist-info/WHEEL", "lib/python3.11/site-packages/setuptools/_vendor/platformdirs-4.2.2.dist-info/licenses/LICENSE", "lib/python3.11/site-packages/setuptools/_vendor/platformdirs/__init__.py", "lib/python3.11/site-packages/setuptools/_vendor/platformdirs/__main__.py", "lib/python3.11/site-packages/setuptools/_vendor/platformdirs/android.py", "lib/python3.11/site-packages/setuptools/_vendor/platformdirs/api.py", "lib/python3.11/site-packages/setuptools/_vendor/platformdirs/macos.py", "lib/python3.11/site-packages/setuptools/_vendor/platformdirs/py.typed", "lib/python3.11/site-packages/setuptools/_vendor/platformdirs/unix.py", "lib/python3.11/site-packages/setuptools/_vendor/platformdirs/version.py", "lib/python3.11/site-packages/setuptools/_vendor/platformdirs/windows.py", "lib/python3.11/site-packages/setuptools/_vendor/tomli-2.0.1.dist-info/INSTALLER", "lib/python3.11/site-packages/setuptools/_vendor/tomli-2.0.1.dist-info/LICENSE", "lib/python3.11/site-packages/setuptools/_vendor/tomli-2.0.1.dist-info/METADATA", "lib/python3.11/site-packages/setuptools/_vendor/tomli-2.0.1.dist-info/RECORD", "lib/python3.11/site-packages/setuptools/_vendor/tomli-2.0.1.dist-info/REQUESTED", "lib/python3.11/site-packages/setuptools/_vendor/tomli-2.0.1.dist-info/WHEEL", "lib/python3.11/site-packages/setuptools/_vendor/tomli/__init__.py", "lib/python3.11/site-packages/setuptools/_vendor/tomli/_parser.py", "lib/python3.11/site-packages/setuptools/_vendor/tomli/_re.py", "lib/python3.11/site-packages/setuptools/_vendor/tomli/_types.py", "lib/python3.11/site-packages/setuptools/_vendor/tomli/py.typed", "lib/python3.11/site-packages/setuptools/_vendor/typeguard-4.3.0.dist-info/INSTALLER", "lib/python3.11/site-packages/setuptools/_vendor/typeguard-4.3.0.dist-info/LICENSE", "lib/python3.11/site-packages/setuptools/_vendor/typeguard-4.3.0.dist-info/METADATA", "lib/python3.11/site-packages/setuptools/_vendor/typeguard-4.3.0.dist-info/RECORD", "lib/python3.11/site-packages/setuptools/_vendor/typeguard-4.3.0.dist-info/WHEEL", "lib/python3.11/site-packages/setuptools/_vendor/typeguard-4.3.0.dist-info/entry_points.txt", "lib/python3.11/site-packages/setuptools/_vendor/typeguard-4.3.0.dist-info/top_level.txt", "lib/python3.11/site-packages/setuptools/_vendor/typeguard/__init__.py", "lib/python3.11/site-packages/setuptools/_vendor/typeguard/_checkers.py", "lib/python3.11/site-packages/setuptools/_vendor/typeguard/_config.py", "lib/python3.11/site-packages/setuptools/_vendor/typeguard/_decorators.py", "lib/python3.11/site-packages/setuptools/_vendor/typeguard/_exceptions.py", "lib/python3.11/site-packages/setuptools/_vendor/typeguard/_functions.py", "lib/python3.11/site-packages/setuptools/_vendor/typeguard/_importhook.py", "lib/python3.11/site-packages/setuptools/_vendor/typeguard/_memo.py", "lib/python3.11/site-packages/setuptools/_vendor/typeguard/_pytest_plugin.py", "lib/python3.11/site-packages/setuptools/_vendor/typeguard/_suppression.py", "lib/python3.11/site-packages/setuptools/_vendor/typeguard/_transformer.py", "lib/python3.11/site-packages/setuptools/_vendor/typeguard/_union_transformer.py", "lib/python3.11/site-packages/setuptools/_vendor/typeguard/_utils.py", "lib/python3.11/site-packages/setuptools/_vendor/typeguard/py.typed", "lib/python3.11/site-packages/setuptools/_vendor/typing_extensions-4.12.2.dist-info/INSTALLER", "lib/python3.11/site-packages/setuptools/_vendor/typing_extensions-4.12.2.dist-info/LICENSE", "lib/python3.11/site-packages/setuptools/_vendor/typing_extensions-4.12.2.dist-info/METADATA", "lib/python3.11/site-packages/setuptools/_vendor/typing_extensions-4.12.2.dist-info/RECORD", "lib/python3.11/site-packages/setuptools/_vendor/typing_extensions-4.12.2.dist-info/WHEEL", "lib/python3.11/site-packages/setuptools/_vendor/typing_extensions.py", "lib/python3.11/site-packages/setuptools/_vendor/wheel-0.45.1.dist-info/INSTALLER", "lib/python3.11/site-packages/setuptools/_vendor/wheel-0.45.1.dist-info/LICENSE.txt", "lib/python3.11/site-packages/setuptools/_vendor/wheel-0.45.1.dist-info/METADATA", "lib/python3.11/site-packages/setuptools/_vendor/wheel-0.45.1.dist-info/RECORD", "lib/python3.11/site-packages/setuptools/_vendor/wheel-0.45.1.dist-info/REQUESTED", "lib/python3.11/site-packages/setuptools/_vendor/wheel-0.45.1.dist-info/WHEEL", "lib/python3.11/site-packages/setuptools/_vendor/wheel-0.45.1.dist-info/entry_points.txt", "lib/python3.11/site-packages/setuptools/_vendor/wheel/__init__.py", "lib/python3.11/site-packages/setuptools/_vendor/wheel/__main__.py", "lib/python3.11/site-packages/setuptools/_vendor/wheel/_bdist_wheel.py", "lib/python3.11/site-packages/setuptools/_vendor/wheel/_setuptools_logging.py", "lib/python3.11/site-packages/setuptools/_vendor/wheel/bdist_wheel.py", "lib/python3.11/site-packages/setuptools/_vendor/wheel/cli/__init__.py", "lib/python3.11/site-packages/setuptools/_vendor/wheel/cli/convert.py", "lib/python3.11/site-packages/setuptools/_vendor/wheel/cli/pack.py", "lib/python3.11/site-packages/setuptools/_vendor/wheel/cli/tags.py", "lib/python3.11/site-packages/setuptools/_vendor/wheel/cli/unpack.py", "lib/python3.11/site-packages/setuptools/_vendor/wheel/macosx_libfile.py", "lib/python3.11/site-packages/setuptools/_vendor/wheel/metadata.py", "lib/python3.11/site-packages/setuptools/_vendor/wheel/util.py", "lib/python3.11/site-packages/setuptools/_vendor/wheel/vendored/__init__.py", "lib/python3.11/site-packages/setuptools/_vendor/wheel/vendored/packaging/LICENSE", "lib/python3.11/site-packages/setuptools/_vendor/wheel/vendored/packaging/LICENSE.APACHE", "lib/python3.11/site-packages/setuptools/_vendor/wheel/vendored/packaging/LICENSE.BSD", "lib/python3.11/site-packages/setuptools/_vendor/wheel/vendored/packaging/__init__.py", "lib/python3.11/site-packages/setuptools/_vendor/wheel/vendored/packaging/_elffile.py", "lib/python3.11/site-packages/setuptools/_vendor/wheel/vendored/packaging/_manylinux.py", "lib/python3.11/site-packages/setuptools/_vendor/wheel/vendored/packaging/_musllinux.py", "lib/python3.11/site-packages/setuptools/_vendor/wheel/vendored/packaging/_parser.py", "lib/python3.11/site-packages/setuptools/_vendor/wheel/vendored/packaging/_structures.py", "lib/python3.11/site-packages/setuptools/_vendor/wheel/vendored/packaging/_tokenizer.py", "lib/python3.11/site-packages/setuptools/_vendor/wheel/vendored/packaging/markers.py", "lib/python3.11/site-packages/setuptools/_vendor/wheel/vendored/packaging/requirements.py", "lib/python3.11/site-packages/setuptools/_vendor/wheel/vendored/packaging/specifiers.py", "lib/python3.11/site-packages/setuptools/_vendor/wheel/vendored/packaging/tags.py", "lib/python3.11/site-packages/setuptools/_vendor/wheel/vendored/packaging/utils.py", "lib/python3.11/site-packages/setuptools/_vendor/wheel/vendored/packaging/version.py", "lib/python3.11/site-packages/setuptools/_vendor/wheel/vendored/vendor.txt", "lib/python3.11/site-packages/setuptools/_vendor/wheel/wheelfile.py", "lib/python3.11/site-packages/setuptools/_vendor/zipp-3.19.2.dist-info/INSTALLER", "lib/python3.11/site-packages/setuptools/_vendor/zipp-3.19.2.dist-info/LICENSE", "lib/python3.11/site-packages/setuptools/_vendor/zipp-3.19.2.dist-info/METADATA", "lib/python3.11/site-packages/setuptools/_vendor/zipp-3.19.2.dist-info/RECORD", "lib/python3.11/site-packages/setuptools/_vendor/zipp-3.19.2.dist-info/REQUESTED", "lib/python3.11/site-packages/setuptools/_vendor/zipp-3.19.2.dist-info/WHEEL", "lib/python3.11/site-packages/setuptools/_vendor/zipp-3.19.2.dist-info/top_level.txt", "lib/python3.11/site-packages/setuptools/_vendor/zipp/__init__.py", "lib/python3.11/site-packages/setuptools/_vendor/zipp/compat/__init__.py", "lib/python3.11/site-packages/setuptools/_vendor/zipp/compat/py310.py", "lib/python3.11/site-packages/setuptools/_vendor/zipp/glob.py", "lib/python3.11/site-packages/setuptools/archive_util.py", "lib/python3.11/site-packages/setuptools/build_meta.py", "lib/python3.11/site-packages/setuptools/cli-32.exe", "lib/python3.11/site-packages/setuptools/cli-64.exe", "lib/python3.11/site-packages/setuptools/cli-arm64.exe", "lib/python3.11/site-packages/setuptools/cli.exe", "lib/python3.11/site-packages/setuptools/command/__init__.py", "lib/python3.11/site-packages/setuptools/command/_requirestxt.py", "lib/python3.11/site-packages/setuptools/command/alias.py", "lib/python3.11/site-packages/setuptools/command/bdist_egg.py", "lib/python3.11/site-packages/setuptools/command/bdist_rpm.py", "lib/python3.11/site-packages/setuptools/command/bdist_wheel.py", "lib/python3.11/site-packages/setuptools/command/build.py", "lib/python3.11/site-packages/setuptools/command/build_clib.py", "lib/python3.11/site-packages/setuptools/command/build_ext.py", "lib/python3.11/site-packages/setuptools/command/build_py.py", "lib/python3.11/site-packages/setuptools/command/develop.py", "lib/python3.11/site-packages/setuptools/command/dist_info.py", "lib/python3.11/site-packages/setuptools/command/easy_install.py", "lib/python3.11/site-packages/setuptools/command/editable_wheel.py", "lib/python3.11/site-packages/setuptools/command/egg_info.py", "lib/python3.11/site-packages/setuptools/command/install.py", "lib/python3.11/site-packages/setuptools/command/install_egg_info.py", "lib/python3.11/site-packages/setuptools/command/install_lib.py", "lib/python3.11/site-packages/setuptools/command/install_scripts.py", "lib/python3.11/site-packages/setuptools/command/launcher manifest.xml", "lib/python3.11/site-packages/setuptools/command/rotate.py", "lib/python3.11/site-packages/setuptools/command/saveopts.py", "lib/python3.11/site-packages/setuptools/command/sdist.py", "lib/python3.11/site-packages/setuptools/command/setopt.py", "lib/python3.11/site-packages/setuptools/command/test.py", "lib/python3.11/site-packages/setuptools/compat/__init__.py", "lib/python3.11/site-packages/setuptools/compat/py310.py", "lib/python3.11/site-packages/setuptools/compat/py311.py", "lib/python3.11/site-packages/setuptools/compat/py312.py", "lib/python3.11/site-packages/setuptools/compat/py39.py", "lib/python3.11/site-packages/setuptools/config/NOTICE", "lib/python3.11/site-packages/setuptools/config/__init__.py", "lib/python3.11/site-packages/setuptools/config/_apply_pyprojecttoml.py", "lib/python3.11/site-packages/setuptools/config/_validate_pyproject/NOTICE", "lib/python3.11/site-packages/setuptools/config/_validate_pyproject/__init__.py", "lib/python3.11/site-packages/setuptools/config/_validate_pyproject/error_reporting.py", "lib/python3.11/site-packages/setuptools/config/_validate_pyproject/extra_validations.py", "lib/python3.11/site-packages/setuptools/config/_validate_pyproject/fastjsonschema_exceptions.py", "lib/python3.11/site-packages/setuptools/config/_validate_pyproject/fastjsonschema_validations.py", "lib/python3.11/site-packages/setuptools/config/_validate_pyproject/formats.py", "lib/python3.11/site-packages/setuptools/config/distutils.schema.json", "lib/python3.11/site-packages/setuptools/config/expand.py", "lib/python3.11/site-packages/setuptools/config/pyprojecttoml.py", "lib/python3.11/site-packages/setuptools/config/setupcfg.py", "lib/python3.11/site-packages/setuptools/config/setuptools.schema.json", "lib/python3.11/site-packages/setuptools/depends.py", "lib/python3.11/site-packages/setuptools/discovery.py", "lib/python3.11/site-packages/setuptools/dist.py", "lib/python3.11/site-packages/setuptools/errors.py", "lib/python3.11/site-packages/setuptools/extension.py", "lib/python3.11/site-packages/setuptools/glob.py", "lib/python3.11/site-packages/setuptools/gui-32.exe", "lib/python3.11/site-packages/setuptools/gui-64.exe", "lib/python3.11/site-packages/setuptools/gui-arm64.exe", "lib/python3.11/site-packages/setuptools/gui.exe", "lib/python3.11/site-packages/setuptools/installer.py", "lib/python3.11/site-packages/setuptools/launch.py", "lib/python3.11/site-packages/setuptools/logging.py", "lib/python3.11/site-packages/setuptools/modified.py", "lib/python3.11/site-packages/setuptools/monkey.py", "lib/python3.11/site-packages/setuptools/msvc.py", "lib/python3.11/site-packages/setuptools/namespaces.py", "lib/python3.11/site-packages/setuptools/script (dev).tmpl", "lib/python3.11/site-packages/setuptools/script.tmpl", "lib/python3.11/site-packages/setuptools/tests/__init__.py", "lib/python3.11/site-packages/setuptools/tests/compat/__init__.py", "lib/python3.11/site-packages/setuptools/tests/compat/py39.py", "lib/python3.11/site-packages/setuptools/tests/config/__init__.py", "lib/python3.11/site-packages/setuptools/tests/config/downloads/__init__.py", "lib/python3.11/site-packages/setuptools/tests/config/downloads/preload.py", "lib/python3.11/site-packages/setuptools/tests/config/setupcfg_examples.txt", "lib/python3.11/site-packages/setuptools/tests/config/test_apply_pyprojecttoml.py", "lib/python3.11/site-packages/setuptools/tests/config/test_expand.py", "lib/python3.11/site-packages/setuptools/tests/config/test_pyprojecttoml.py", "lib/python3.11/site-packages/setuptools/tests/config/test_pyprojecttoml_dynamic_deps.py", "lib/python3.11/site-packages/setuptools/tests/config/test_setupcfg.py", "lib/python3.11/site-packages/setuptools/tests/contexts.py", "lib/python3.11/site-packages/setuptools/tests/environment.py", "lib/python3.11/site-packages/setuptools/tests/fixtures.py", "lib/python3.11/site-packages/setuptools/tests/indexes/test_links_priority/external.html", "lib/python3.11/site-packages/setuptools/tests/indexes/test_links_priority/simple/foobar/index.html", "lib/python3.11/site-packages/setuptools/tests/integration/__init__.py", "lib/python3.11/site-packages/setuptools/tests/integration/helpers.py", "lib/python3.11/site-packages/setuptools/tests/integration/test_pbr.py", "lib/python3.11/site-packages/setuptools/tests/integration/test_pip_install_sdist.py", "lib/python3.11/site-packages/setuptools/tests/mod_with_constant.py", "lib/python3.11/site-packages/setuptools/tests/namespaces.py", "lib/python3.11/site-packages/setuptools/tests/script-with-bom.py", "lib/python3.11/site-packages/setuptools/tests/test_archive_util.py", "lib/python3.11/site-packages/setuptools/tests/test_bdist_deprecations.py", "lib/python3.11/site-packages/setuptools/tests/test_bdist_egg.py", "lib/python3.11/site-packages/setuptools/tests/test_bdist_wheel.py", "lib/python3.11/site-packages/setuptools/tests/test_build.py", "lib/python3.11/site-packages/setuptools/tests/test_build_clib.py", "lib/python3.11/site-packages/setuptools/tests/test_build_ext.py", "lib/python3.11/site-packages/setuptools/tests/test_build_meta.py", "lib/python3.11/site-packages/setuptools/tests/test_build_py.py", "lib/python3.11/site-packages/setuptools/tests/test_config_discovery.py", "lib/python3.11/site-packages/setuptools/tests/test_core_metadata.py", "lib/python3.11/site-packages/setuptools/tests/test_depends.py", "lib/python3.11/site-packages/setuptools/tests/test_develop.py", "lib/python3.11/site-packages/setuptools/tests/test_dist.py", "lib/python3.11/site-packages/setuptools/tests/test_dist_info.py", "lib/python3.11/site-packages/setuptools/tests/test_distutils_adoption.py", "lib/python3.11/site-packages/setuptools/tests/test_editable_install.py", "lib/python3.11/site-packages/setuptools/tests/test_egg_info.py", "lib/python3.11/site-packages/setuptools/tests/test_extern.py", "lib/python3.11/site-packages/setuptools/tests/test_find_packages.py", "lib/python3.11/site-packages/setuptools/tests/test_find_py_modules.py", "lib/python3.11/site-packages/setuptools/tests/test_glob.py", "lib/python3.11/site-packages/setuptools/tests/test_install_scripts.py", "lib/python3.11/site-packages/setuptools/tests/test_logging.py", "lib/python3.11/site-packages/setuptools/tests/test_manifest.py", "lib/python3.11/site-packages/setuptools/tests/test_namespaces.py", "lib/python3.11/site-packages/setuptools/tests/test_scripts.py", "lib/python3.11/site-packages/setuptools/tests/test_sdist.py", "lib/python3.11/site-packages/setuptools/tests/test_setopt.py", "lib/python3.11/site-packages/setuptools/tests/test_setuptools.py", "lib/python3.11/site-packages/setuptools/tests/test_shutil_wrapper.py", "lib/python3.11/site-packages/setuptools/tests/test_unicode_utils.py", "lib/python3.11/site-packages/setuptools/tests/test_virtualenv.py", "lib/python3.11/site-packages/setuptools/tests/test_warnings.py", "lib/python3.11/site-packages/setuptools/tests/test_wheel.py", "lib/python3.11/site-packages/setuptools/tests/test_windows_wrappers.py", "lib/python3.11/site-packages/setuptools/tests/text.py", "lib/python3.11/site-packages/setuptools/tests/textwrap.py", "lib/python3.11/site-packages/setuptools/unicode_utils.py", "lib/python3.11/site-packages/setuptools/version.py", "lib/python3.11/site-packages/setuptools/warnings.py", "lib/python3.11/site-packages/setuptools/wheel.py", "lib/python3.11/site-packages/setuptools/windows_support.py", "lib/python3.11/site-packages/_distutils_hack/__pycache__/__init__.cpython-311.pyc", "lib/python3.11/site-packages/_distutils_hack/__pycache__/override.cpython-311.pyc", "lib/python3.11/site-packages/pkg_resources/__pycache__/__init__.cpython-311.pyc", "lib/python3.11/site-packages/pkg_resources/tests/__pycache__/__init__.cpython-311.pyc", "lib/python3.11/site-packages/pkg_resources/tests/data/my-test-package-source/__pycache__/setup.cpython-311.pyc", "lib/python3.11/site-packages/pkg_resources/tests/__pycache__/test_find_distributions.cpython-311.pyc", "lib/python3.11/site-packages/pkg_resources/tests/__pycache__/test_integration_zope_interface.cpython-311.pyc", "lib/python3.11/site-packages/pkg_resources/tests/__pycache__/test_markers.cpython-311.pyc", "lib/python3.11/site-packages/pkg_resources/tests/__pycache__/test_pkg_resources.cpython-311.pyc", "lib/python3.11/site-packages/pkg_resources/tests/__pycache__/test_resources.cpython-311.pyc", "lib/python3.11/site-packages/pkg_resources/tests/__pycache__/test_working_set.cpython-311.pyc", "lib/python3.11/site-packages/setuptools/__pycache__/__init__.cpython-311.pyc", "lib/python3.11/site-packages/setuptools/__pycache__/_core_metadata.cpython-311.pyc", "lib/python3.11/site-packages/setuptools/__pycache__/_discovery.cpython-311.pyc", "lib/python3.11/site-packages/setuptools/_distutils/__pycache__/__init__.cpython-311.pyc", "lib/python3.11/site-packages/setuptools/_distutils/__pycache__/_log.cpython-311.pyc", "lib/python3.11/site-packages/setuptools/_distutils/__pycache__/_macos_compat.cpython-311.pyc", "lib/python3.11/site-packages/setuptools/_distutils/__pycache__/_modified.cpython-311.pyc", "lib/python3.11/site-packages/setuptools/_distutils/__pycache__/_msvccompiler.cpython-311.pyc", "lib/python3.11/site-packages/setuptools/_distutils/__pycache__/archive_util.cpython-311.pyc", "lib/python3.11/site-packages/setuptools/_distutils/__pycache__/ccompiler.cpython-311.pyc", "lib/python3.11/site-packages/setuptools/_distutils/__pycache__/cmd.cpython-311.pyc", "lib/python3.11/site-packages/setuptools/_distutils/command/__pycache__/__init__.cpython-311.pyc", "lib/python3.11/site-packages/setuptools/_distutils/command/__pycache__/_framework_compat.cpython-311.pyc", "lib/python3.11/site-packages/setuptools/_distutils/command/__pycache__/bdist.cpython-311.pyc", "lib/python3.11/site-packages/setuptools/_distutils/command/__pycache__/bdist_dumb.cpython-311.pyc", "lib/python3.11/site-packages/setuptools/_distutils/command/__pycache__/bdist_rpm.cpython-311.pyc", "lib/python3.11/site-packages/setuptools/_distutils/command/__pycache__/build.cpython-311.pyc", "lib/python3.11/site-packages/setuptools/_distutils/command/__pycache__/build_clib.cpython-311.pyc", "lib/python3.11/site-packages/setuptools/_distutils/command/__pycache__/build_ext.cpython-311.pyc", "lib/python3.11/site-packages/setuptools/_distutils/command/__pycache__/build_py.cpython-311.pyc", "lib/python3.11/site-packages/setuptools/_distutils/command/__pycache__/build_scripts.cpython-311.pyc", "lib/python3.11/site-packages/setuptools/_distutils/command/__pycache__/check.cpython-311.pyc", "lib/python3.11/site-packages/setuptools/_distutils/command/__pycache__/clean.cpython-311.pyc", "lib/python3.11/site-packages/setuptools/_distutils/command/__pycache__/config.cpython-311.pyc", "lib/python3.11/site-packages/setuptools/_distutils/command/__pycache__/install.cpython-311.pyc", "lib/python3.11/site-packages/setuptools/_distutils/command/__pycache__/install_data.cpython-311.pyc", "lib/python3.11/site-packages/setuptools/_distutils/command/__pycache__/install_egg_info.cpython-311.pyc", "lib/python3.11/site-packages/setuptools/_distutils/command/__pycache__/install_headers.cpython-311.pyc", "lib/python3.11/site-packages/setuptools/_distutils/command/__pycache__/install_lib.cpython-311.pyc", "lib/python3.11/site-packages/setuptools/_distutils/command/__pycache__/install_scripts.cpython-311.pyc", "lib/python3.11/site-packages/setuptools/_distutils/command/__pycache__/sdist.cpython-311.pyc", "lib/python3.11/site-packages/setuptools/_distutils/compat/__pycache__/__init__.cpython-311.pyc", "lib/python3.11/site-packages/setuptools/_distutils/compat/__pycache__/numpy.cpython-311.pyc", "lib/python3.11/site-packages/setuptools/_distutils/compat/__pycache__/py39.cpython-311.pyc", "lib/python3.11/site-packages/setuptools/_distutils/compilers/C/__pycache__/base.cpython-311.pyc", "lib/python3.11/site-packages/setuptools/_distutils/compilers/C/__pycache__/cygwin.cpython-311.pyc", "lib/python3.11/site-packages/setuptools/_distutils/compilers/C/__pycache__/errors.cpython-311.pyc", "lib/python3.11/site-packages/setuptools/_distutils/compilers/C/__pycache__/msvc.cpython-311.pyc", "lib/python3.11/site-packages/setuptools/_distutils/compilers/C/tests/__pycache__/test_base.cpython-311.pyc", "lib/python3.11/site-packages/setuptools/_distutils/compilers/C/tests/__pycache__/test_cygwin.cpython-311.pyc", "lib/python3.11/site-packages/setuptools/_distutils/compilers/C/tests/__pycache__/test_mingw.cpython-311.pyc", "lib/python3.11/site-packages/setuptools/_distutils/compilers/C/tests/__pycache__/test_msvc.cpython-311.pyc", "lib/python3.11/site-packages/setuptools/_distutils/compilers/C/tests/__pycache__/test_unix.cpython-311.pyc", "lib/python3.11/site-packages/setuptools/_distutils/compilers/C/__pycache__/unix.cpython-311.pyc", "lib/python3.11/site-packages/setuptools/_distutils/compilers/C/__pycache__/zos.cpython-311.pyc", "lib/python3.11/site-packages/setuptools/_distutils/__pycache__/core.cpython-311.pyc", "lib/python3.11/site-packages/setuptools/_distutils/__pycache__/cygwinccompiler.cpython-311.pyc", "lib/python3.11/site-packages/setuptools/_distutils/__pycache__/debug.cpython-311.pyc", "lib/python3.11/site-packages/setuptools/_distutils/__pycache__/dep_util.cpython-311.pyc", "lib/python3.11/site-packages/setuptools/_distutils/__pycache__/dir_util.cpython-311.pyc", "lib/python3.11/site-packages/setuptools/_distutils/__pycache__/dist.cpython-311.pyc", "lib/python3.11/site-packages/setuptools/_distutils/__pycache__/errors.cpython-311.pyc", "lib/python3.11/site-packages/setuptools/_distutils/__pycache__/extension.cpython-311.pyc", "lib/python3.11/site-packages/setuptools/_distutils/__pycache__/fancy_getopt.cpython-311.pyc", "lib/python3.11/site-packages/setuptools/_distutils/__pycache__/file_util.cpython-311.pyc", "lib/python3.11/site-packages/setuptools/_distutils/__pycache__/filelist.cpython-311.pyc", "lib/python3.11/site-packages/setuptools/_distutils/__pycache__/log.cpython-311.pyc", "lib/python3.11/site-packages/setuptools/_distutils/__pycache__/spawn.cpython-311.pyc", "lib/python3.11/site-packages/setuptools/_distutils/__pycache__/sysconfig.cpython-311.pyc", "lib/python3.11/site-packages/setuptools/_distutils/tests/__pycache__/__init__.cpython-311.pyc", "lib/python3.11/site-packages/setuptools/_distutils/tests/compat/__pycache__/__init__.cpython-311.pyc", "lib/python3.11/site-packages/setuptools/_distutils/tests/compat/__pycache__/py39.cpython-311.pyc", "lib/python3.11/site-packages/setuptools/_distutils/tests/__pycache__/support.cpython-311.pyc", "lib/python3.11/site-packages/setuptools/_distutils/tests/__pycache__/test_archive_util.cpython-311.pyc", "lib/python3.11/site-packages/setuptools/_distutils/tests/__pycache__/test_bdist.cpython-311.pyc", "lib/python3.11/site-packages/setuptools/_distutils/tests/__pycache__/test_bdist_dumb.cpython-311.pyc", "lib/python3.11/site-packages/setuptools/_distutils/tests/__pycache__/test_bdist_rpm.cpython-311.pyc", "lib/python3.11/site-packages/setuptools/_distutils/tests/__pycache__/test_build.cpython-311.pyc", "lib/python3.11/site-packages/setuptools/_distutils/tests/__pycache__/test_build_clib.cpython-311.pyc", "lib/python3.11/site-packages/setuptools/_distutils/tests/__pycache__/test_build_ext.cpython-311.pyc", "lib/python3.11/site-packages/setuptools/_distutils/tests/__pycache__/test_build_py.cpython-311.pyc", "lib/python3.11/site-packages/setuptools/_distutils/tests/__pycache__/test_build_scripts.cpython-311.pyc", "lib/python3.11/site-packages/setuptools/_distutils/tests/__pycache__/test_check.cpython-311.pyc", "lib/python3.11/site-packages/setuptools/_distutils/tests/__pycache__/test_clean.cpython-311.pyc", "lib/python3.11/site-packages/setuptools/_distutils/tests/__pycache__/test_cmd.cpython-311.pyc", "lib/python3.11/site-packages/setuptools/_distutils/tests/__pycache__/test_config_cmd.cpython-311.pyc", "lib/python3.11/site-packages/setuptools/_distutils/tests/__pycache__/test_core.cpython-311.pyc", "lib/python3.11/site-packages/setuptools/_distutils/tests/__pycache__/test_dir_util.cpython-311.pyc", "lib/python3.11/site-packages/setuptools/_distutils/tests/__pycache__/test_dist.cpython-311.pyc", "lib/python3.11/site-packages/setuptools/_distutils/tests/__pycache__/test_extension.cpython-311.pyc", "lib/python3.11/site-packages/setuptools/_distutils/tests/__pycache__/test_file_util.cpython-311.pyc", "lib/python3.11/site-packages/setuptools/_distutils/tests/__pycache__/test_filelist.cpython-311.pyc", "lib/python3.11/site-packages/setuptools/_distutils/tests/__pycache__/test_install.cpython-311.pyc", "lib/python3.11/site-packages/setuptools/_distutils/tests/__pycache__/test_install_data.cpython-311.pyc", "lib/python3.11/site-packages/setuptools/_distutils/tests/__pycache__/test_install_headers.cpython-311.pyc", "lib/python3.11/site-packages/setuptools/_distutils/tests/__pycache__/test_install_lib.cpython-311.pyc", "lib/python3.11/site-packages/setuptools/_distutils/tests/__pycache__/test_install_scripts.cpython-311.pyc", "lib/python3.11/site-packages/setuptools/_distutils/tests/__pycache__/test_log.cpython-311.pyc", "lib/python3.11/site-packages/setuptools/_distutils/tests/__pycache__/test_modified.cpython-311.pyc", "lib/python3.11/site-packages/setuptools/_distutils/tests/__pycache__/test_sdist.cpython-311.pyc", "lib/python3.11/site-packages/setuptools/_distutils/tests/__pycache__/test_spawn.cpython-311.pyc", "lib/python3.11/site-packages/setuptools/_distutils/tests/__pycache__/test_sysconfig.cpython-311.pyc", "lib/python3.11/site-packages/setuptools/_distutils/tests/__pycache__/test_text_file.cpython-311.pyc", "lib/python3.11/site-packages/setuptools/_distutils/tests/__pycache__/test_util.cpython-311.pyc", "lib/python3.11/site-packages/setuptools/_distutils/tests/__pycache__/test_version.cpython-311.pyc", "lib/python3.11/site-packages/setuptools/_distutils/tests/__pycache__/test_versionpredicate.cpython-311.pyc", "lib/python3.11/site-packages/setuptools/_distutils/tests/__pycache__/unix_compat.cpython-311.pyc", "lib/python3.11/site-packages/setuptools/_distutils/__pycache__/text_file.cpython-311.pyc", "lib/python3.11/site-packages/setuptools/_distutils/__pycache__/unixccompiler.cpython-311.pyc", "lib/python3.11/site-packages/setuptools/_distutils/__pycache__/util.cpython-311.pyc", "lib/python3.11/site-packages/setuptools/_distutils/__pycache__/version.cpython-311.pyc", "lib/python3.11/site-packages/setuptools/_distutils/__pycache__/versionpredicate.cpython-311.pyc", "lib/python3.11/site-packages/setuptools/_distutils/__pycache__/zosccompiler.cpython-311.pyc", "lib/python3.11/site-packages/setuptools/__pycache__/_entry_points.cpython-311.pyc", "lib/python3.11/site-packages/setuptools/__pycache__/_imp.cpython-311.pyc", "lib/python3.11/site-packages/setuptools/__pycache__/_importlib.cpython-311.pyc", "lib/python3.11/site-packages/setuptools/__pycache__/_itertools.cpython-311.pyc", "lib/python3.11/site-packages/setuptools/__pycache__/_normalization.cpython-311.pyc", "lib/python3.11/site-packages/setuptools/__pycache__/_path.cpython-311.pyc", "lib/python3.11/site-packages/setuptools/__pycache__/_reqs.cpython-311.pyc", "lib/python3.11/site-packages/setuptools/__pycache__/_scripts.cpython-311.pyc", "lib/python3.11/site-packages/setuptools/__pycache__/_shutil.cpython-311.pyc", "lib/python3.11/site-packages/setuptools/__pycache__/_static.cpython-311.pyc", "lib/python3.11/site-packages/setuptools/_vendor/autocommand/__pycache__/__init__.cpython-311.pyc", "lib/python3.11/site-packages/setuptools/_vendor/autocommand/__pycache__/autoasync.cpython-311.pyc", "lib/python3.11/site-packages/setuptools/_vendor/autocommand/__pycache__/autocommand.cpython-311.pyc", "lib/python3.11/site-packages/setuptools/_vendor/autocommand/__pycache__/automain.cpython-311.pyc", "lib/python3.11/site-packages/setuptools/_vendor/autocommand/__pycache__/autoparse.cpython-311.pyc", "lib/python3.11/site-packages/setuptools/_vendor/autocommand/__pycache__/errors.cpython-311.pyc", "lib/python3.11/site-packages/setuptools/_vendor/backports/__pycache__/__init__.cpython-311.pyc", "lib/python3.11/site-packages/setuptools/_vendor/backports/tarfile/__pycache__/__init__.cpython-311.pyc", "lib/python3.11/site-packages/setuptools/_vendor/backports/tarfile/__pycache__/__main__.cpython-311.pyc", "lib/python3.11/site-packages/setuptools/_vendor/backports/tarfile/compat/__pycache__/__init__.cpython-311.pyc", "lib/python3.11/site-packages/setuptools/_vendor/backports/tarfile/compat/__pycache__/py38.cpython-311.pyc", "lib/python3.11/site-packages/setuptools/_vendor/importlib_metadata/__pycache__/__init__.cpython-311.pyc", "lib/python3.11/site-packages/setuptools/_vendor/importlib_metadata/__pycache__/_adapters.cpython-311.pyc", "lib/python3.11/site-packages/setuptools/_vendor/importlib_metadata/__pycache__/_collections.cpython-311.pyc", "lib/python3.11/site-packages/setuptools/_vendor/importlib_metadata/__pycache__/_compat.cpython-311.pyc", "lib/python3.11/site-packages/setuptools/_vendor/importlib_metadata/__pycache__/_functools.cpython-311.pyc", "lib/python3.11/site-packages/setuptools/_vendor/importlib_metadata/__pycache__/_itertools.cpython-311.pyc", "lib/python3.11/site-packages/setuptools/_vendor/importlib_metadata/__pycache__/_meta.cpython-311.pyc", "lib/python3.11/site-packages/setuptools/_vendor/importlib_metadata/__pycache__/_text.cpython-311.pyc", "lib/python3.11/site-packages/setuptools/_vendor/importlib_metadata/compat/__pycache__/__init__.cpython-311.pyc", "lib/python3.11/site-packages/setuptools/_vendor/importlib_metadata/compat/__pycache__/py311.cpython-311.pyc", "lib/python3.11/site-packages/setuptools/_vendor/importlib_metadata/compat/__pycache__/py39.cpython-311.pyc", "lib/python3.11/site-packages/setuptools/_vendor/importlib_metadata/__pycache__/diagnose.cpython-311.pyc", "lib/python3.11/site-packages/setuptools/_vendor/inflect/__pycache__/__init__.cpython-311.pyc", "lib/python3.11/site-packages/setuptools/_vendor/inflect/compat/__pycache__/__init__.cpython-311.pyc", "lib/python3.11/site-packages/setuptools/_vendor/inflect/compat/__pycache__/py38.cpython-311.pyc", "lib/python3.11/site-packages/setuptools/_vendor/jaraco/collections/__pycache__/__init__.cpython-311.pyc", "lib/python3.11/site-packages/setuptools/_vendor/jaraco/__pycache__/context.cpython-311.pyc", "lib/python3.11/site-packages/setuptools/_vendor/jaraco/functools/__pycache__/__init__.cpython-311.pyc", "lib/python3.11/site-packages/setuptools/_vendor/jaraco/text/__pycache__/__init__.cpython-311.pyc", "lib/python3.11/site-packages/setuptools/_vendor/jaraco/text/__pycache__/layouts.cpython-311.pyc", "lib/python3.11/site-packages/setuptools/_vendor/jaraco/text/__pycache__/show-newlines.cpython-311.pyc", "lib/python3.11/site-packages/setuptools/_vendor/jaraco/text/__pycache__/strip-prefix.cpython-311.pyc", "lib/python3.11/site-packages/setuptools/_vendor/jaraco/text/__pycache__/to-dvorak.cpython-311.pyc", "lib/python3.11/site-packages/setuptools/_vendor/jaraco/text/__pycache__/to-qwerty.cpython-311.pyc", "lib/python3.11/site-packages/setuptools/_vendor/more_itertools/__pycache__/__init__.cpython-311.pyc", "lib/python3.11/site-packages/setuptools/_vendor/more_itertools/__pycache__/more.cpython-311.pyc", "lib/python3.11/site-packages/setuptools/_vendor/more_itertools/__pycache__/recipes.cpython-311.pyc", "lib/python3.11/site-packages/setuptools/_vendor/packaging/__pycache__/__init__.cpython-311.pyc", "lib/python3.11/site-packages/setuptools/_vendor/packaging/__pycache__/_elffile.cpython-311.pyc", "lib/python3.11/site-packages/setuptools/_vendor/packaging/__pycache__/_manylinux.cpython-311.pyc", "lib/python3.11/site-packages/setuptools/_vendor/packaging/__pycache__/_musllinux.cpython-311.pyc", "lib/python3.11/site-packages/setuptools/_vendor/packaging/__pycache__/_parser.cpython-311.pyc", "lib/python3.11/site-packages/setuptools/_vendor/packaging/__pycache__/_structures.cpython-311.pyc", "lib/python3.11/site-packages/setuptools/_vendor/packaging/__pycache__/_tokenizer.cpython-311.pyc", "lib/python3.11/site-packages/setuptools/_vendor/packaging/licenses/__pycache__/__init__.cpython-311.pyc", "lib/python3.11/site-packages/setuptools/_vendor/packaging/licenses/__pycache__/_spdx.cpython-311.pyc", "lib/python3.11/site-packages/setuptools/_vendor/packaging/__pycache__/markers.cpython-311.pyc", "lib/python3.11/site-packages/setuptools/_vendor/packaging/__pycache__/metadata.cpython-311.pyc", "lib/python3.11/site-packages/setuptools/_vendor/packaging/__pycache__/requirements.cpython-311.pyc", "lib/python3.11/site-packages/setuptools/_vendor/packaging/__pycache__/specifiers.cpython-311.pyc", "lib/python3.11/site-packages/setuptools/_vendor/packaging/__pycache__/tags.cpython-311.pyc", "lib/python3.11/site-packages/setuptools/_vendor/packaging/__pycache__/utils.cpython-311.pyc", "lib/python3.11/site-packages/setuptools/_vendor/packaging/__pycache__/version.cpython-311.pyc", "lib/python3.11/site-packages/setuptools/_vendor/platformdirs/__pycache__/__init__.cpython-311.pyc", "lib/python3.11/site-packages/setuptools/_vendor/platformdirs/__pycache__/__main__.cpython-311.pyc", "lib/python3.11/site-packages/setuptools/_vendor/platformdirs/__pycache__/android.cpython-311.pyc", "lib/python3.11/site-packages/setuptools/_vendor/platformdirs/__pycache__/api.cpython-311.pyc", "lib/python3.11/site-packages/setuptools/_vendor/platformdirs/__pycache__/macos.cpython-311.pyc", "lib/python3.11/site-packages/setuptools/_vendor/platformdirs/__pycache__/unix.cpython-311.pyc", "lib/python3.11/site-packages/setuptools/_vendor/platformdirs/__pycache__/version.cpython-311.pyc", "lib/python3.11/site-packages/setuptools/_vendor/platformdirs/__pycache__/windows.cpython-311.pyc", "lib/python3.11/site-packages/setuptools/_vendor/tomli/__pycache__/__init__.cpython-311.pyc", "lib/python3.11/site-packages/setuptools/_vendor/tomli/__pycache__/_parser.cpython-311.pyc", "lib/python3.11/site-packages/setuptools/_vendor/tomli/__pycache__/_re.cpython-311.pyc", "lib/python3.11/site-packages/setuptools/_vendor/tomli/__pycache__/_types.cpython-311.pyc", "lib/python3.11/site-packages/setuptools/_vendor/typeguard/__pycache__/__init__.cpython-311.pyc", "lib/python3.11/site-packages/setuptools/_vendor/typeguard/__pycache__/_checkers.cpython-311.pyc", "lib/python3.11/site-packages/setuptools/_vendor/typeguard/__pycache__/_config.cpython-311.pyc", "lib/python3.11/site-packages/setuptools/_vendor/typeguard/__pycache__/_decorators.cpython-311.pyc", "lib/python3.11/site-packages/setuptools/_vendor/typeguard/__pycache__/_exceptions.cpython-311.pyc", "lib/python3.11/site-packages/setuptools/_vendor/typeguard/__pycache__/_functions.cpython-311.pyc", "lib/python3.11/site-packages/setuptools/_vendor/typeguard/__pycache__/_importhook.cpython-311.pyc", "lib/python3.11/site-packages/setuptools/_vendor/typeguard/__pycache__/_memo.cpython-311.pyc", "lib/python3.11/site-packages/setuptools/_vendor/typeguard/__pycache__/_pytest_plugin.cpython-311.pyc", "lib/python3.11/site-packages/setuptools/_vendor/typeguard/__pycache__/_suppression.cpython-311.pyc", "lib/python3.11/site-packages/setuptools/_vendor/typeguard/__pycache__/_transformer.cpython-311.pyc", "lib/python3.11/site-packages/setuptools/_vendor/typeguard/__pycache__/_union_transformer.cpython-311.pyc", "lib/python3.11/site-packages/setuptools/_vendor/typeguard/__pycache__/_utils.cpython-311.pyc", "lib/python3.11/site-packages/setuptools/_vendor/__pycache__/typing_extensions.cpython-311.pyc", "lib/python3.11/site-packages/setuptools/_vendor/wheel/__pycache__/__init__.cpython-311.pyc", "lib/python3.11/site-packages/setuptools/_vendor/wheel/__pycache__/__main__.cpython-311.pyc", "lib/python3.11/site-packages/setuptools/_vendor/wheel/__pycache__/_bdist_wheel.cpython-311.pyc", "lib/python3.11/site-packages/setuptools/_vendor/wheel/__pycache__/_setuptools_logging.cpython-311.pyc", "lib/python3.11/site-packages/setuptools/_vendor/wheel/__pycache__/bdist_wheel.cpython-311.pyc", "lib/python3.11/site-packages/setuptools/_vendor/wheel/cli/__pycache__/__init__.cpython-311.pyc", "lib/python3.11/site-packages/setuptools/_vendor/wheel/cli/__pycache__/convert.cpython-311.pyc", "lib/python3.11/site-packages/setuptools/_vendor/wheel/cli/__pycache__/pack.cpython-311.pyc", "lib/python3.11/site-packages/setuptools/_vendor/wheel/cli/__pycache__/tags.cpython-311.pyc", "lib/python3.11/site-packages/setuptools/_vendor/wheel/cli/__pycache__/unpack.cpython-311.pyc", "lib/python3.11/site-packages/setuptools/_vendor/wheel/__pycache__/macosx_libfile.cpython-311.pyc", "lib/python3.11/site-packages/setuptools/_vendor/wheel/__pycache__/metadata.cpython-311.pyc", "lib/python3.11/site-packages/setuptools/_vendor/wheel/__pycache__/util.cpython-311.pyc", "lib/python3.11/site-packages/setuptools/_vendor/wheel/vendored/__pycache__/__init__.cpython-311.pyc", "lib/python3.11/site-packages/setuptools/_vendor/wheel/vendored/packaging/__pycache__/__init__.cpython-311.pyc", "lib/python3.11/site-packages/setuptools/_vendor/wheel/vendored/packaging/__pycache__/_elffile.cpython-311.pyc", "lib/python3.11/site-packages/setuptools/_vendor/wheel/vendored/packaging/__pycache__/_manylinux.cpython-311.pyc", "lib/python3.11/site-packages/setuptools/_vendor/wheel/vendored/packaging/__pycache__/_musllinux.cpython-311.pyc", "lib/python3.11/site-packages/setuptools/_vendor/wheel/vendored/packaging/__pycache__/_parser.cpython-311.pyc", "lib/python3.11/site-packages/setuptools/_vendor/wheel/vendored/packaging/__pycache__/_structures.cpython-311.pyc", "lib/python3.11/site-packages/setuptools/_vendor/wheel/vendored/packaging/__pycache__/_tokenizer.cpython-311.pyc", "lib/python3.11/site-packages/setuptools/_vendor/wheel/vendored/packaging/__pycache__/markers.cpython-311.pyc", "lib/python3.11/site-packages/setuptools/_vendor/wheel/vendored/packaging/__pycache__/requirements.cpython-311.pyc", "lib/python3.11/site-packages/setuptools/_vendor/wheel/vendored/packaging/__pycache__/specifiers.cpython-311.pyc", "lib/python3.11/site-packages/setuptools/_vendor/wheel/vendored/packaging/__pycache__/tags.cpython-311.pyc", "lib/python3.11/site-packages/setuptools/_vendor/wheel/vendored/packaging/__pycache__/utils.cpython-311.pyc", "lib/python3.11/site-packages/setuptools/_vendor/wheel/vendored/packaging/__pycache__/version.cpython-311.pyc", "lib/python3.11/site-packages/setuptools/_vendor/wheel/__pycache__/wheelfile.cpython-311.pyc", "lib/python3.11/site-packages/setuptools/_vendor/zipp/__pycache__/__init__.cpython-311.pyc", "lib/python3.11/site-packages/setuptools/_vendor/zipp/compat/__pycache__/__init__.cpython-311.pyc", "lib/python3.11/site-packages/setuptools/_vendor/zipp/compat/__pycache__/py310.cpython-311.pyc", "lib/python3.11/site-packages/setuptools/_vendor/zipp/__pycache__/glob.cpython-311.pyc", "lib/python3.11/site-packages/setuptools/__pycache__/archive_util.cpython-311.pyc", "lib/python3.11/site-packages/setuptools/__pycache__/build_meta.cpython-311.pyc", "lib/python3.11/site-packages/setuptools/command/__pycache__/__init__.cpython-311.pyc", "lib/python3.11/site-packages/setuptools/command/__pycache__/_requirestxt.cpython-311.pyc", "lib/python3.11/site-packages/setuptools/command/__pycache__/alias.cpython-311.pyc", "lib/python3.11/site-packages/setuptools/command/__pycache__/bdist_egg.cpython-311.pyc", "lib/python3.11/site-packages/setuptools/command/__pycache__/bdist_rpm.cpython-311.pyc", "lib/python3.11/site-packages/setuptools/command/__pycache__/bdist_wheel.cpython-311.pyc", "lib/python3.11/site-packages/setuptools/command/__pycache__/build.cpython-311.pyc", "lib/python3.11/site-packages/setuptools/command/__pycache__/build_clib.cpython-311.pyc", "lib/python3.11/site-packages/setuptools/command/__pycache__/build_ext.cpython-311.pyc", "lib/python3.11/site-packages/setuptools/command/__pycache__/build_py.cpython-311.pyc", "lib/python3.11/site-packages/setuptools/command/__pycache__/develop.cpython-311.pyc", "lib/python3.11/site-packages/setuptools/command/__pycache__/dist_info.cpython-311.pyc", "lib/python3.11/site-packages/setuptools/command/__pycache__/easy_install.cpython-311.pyc", "lib/python3.11/site-packages/setuptools/command/__pycache__/editable_wheel.cpython-311.pyc", "lib/python3.11/site-packages/setuptools/command/__pycache__/egg_info.cpython-311.pyc", "lib/python3.11/site-packages/setuptools/command/__pycache__/install.cpython-311.pyc", "lib/python3.11/site-packages/setuptools/command/__pycache__/install_egg_info.cpython-311.pyc", "lib/python3.11/site-packages/setuptools/command/__pycache__/install_lib.cpython-311.pyc", "lib/python3.11/site-packages/setuptools/command/__pycache__/install_scripts.cpython-311.pyc", "lib/python3.11/site-packages/setuptools/command/__pycache__/rotate.cpython-311.pyc", "lib/python3.11/site-packages/setuptools/command/__pycache__/saveopts.cpython-311.pyc", "lib/python3.11/site-packages/setuptools/command/__pycache__/sdist.cpython-311.pyc", "lib/python3.11/site-packages/setuptools/command/__pycache__/setopt.cpython-311.pyc", "lib/python3.11/site-packages/setuptools/command/__pycache__/test.cpython-311.pyc", "lib/python3.11/site-packages/setuptools/compat/__pycache__/__init__.cpython-311.pyc", "lib/python3.11/site-packages/setuptools/compat/__pycache__/py310.cpython-311.pyc", "lib/python3.11/site-packages/setuptools/compat/__pycache__/py311.cpython-311.pyc", "lib/python3.11/site-packages/setuptools/compat/__pycache__/py312.cpython-311.pyc", "lib/python3.11/site-packages/setuptools/compat/__pycache__/py39.cpython-311.pyc", "lib/python3.11/site-packages/setuptools/config/__pycache__/__init__.cpython-311.pyc", "lib/python3.11/site-packages/setuptools/config/__pycache__/_apply_pyprojecttoml.cpython-311.pyc", "lib/python3.11/site-packages/setuptools/config/_validate_pyproject/__pycache__/__init__.cpython-311.pyc", "lib/python3.11/site-packages/setuptools/config/_validate_pyproject/__pycache__/error_reporting.cpython-311.pyc", "lib/python3.11/site-packages/setuptools/config/_validate_pyproject/__pycache__/extra_validations.cpython-311.pyc", "lib/python3.11/site-packages/setuptools/config/_validate_pyproject/__pycache__/fastjsonschema_exceptions.cpython-311.pyc", "lib/python3.11/site-packages/setuptools/config/_validate_pyproject/__pycache__/fastjsonschema_validations.cpython-311.pyc", "lib/python3.11/site-packages/setuptools/config/_validate_pyproject/__pycache__/formats.cpython-311.pyc", "lib/python3.11/site-packages/setuptools/config/__pycache__/expand.cpython-311.pyc", "lib/python3.11/site-packages/setuptools/config/__pycache__/pyprojecttoml.cpython-311.pyc", "lib/python3.11/site-packages/setuptools/config/__pycache__/setupcfg.cpython-311.pyc", "lib/python3.11/site-packages/setuptools/__pycache__/depends.cpython-311.pyc", "lib/python3.11/site-packages/setuptools/__pycache__/discovery.cpython-311.pyc", "lib/python3.11/site-packages/setuptools/__pycache__/dist.cpython-311.pyc", "lib/python3.11/site-packages/setuptools/__pycache__/errors.cpython-311.pyc", "lib/python3.11/site-packages/setuptools/__pycache__/extension.cpython-311.pyc", "lib/python3.11/site-packages/setuptools/__pycache__/glob.cpython-311.pyc", "lib/python3.11/site-packages/setuptools/__pycache__/installer.cpython-311.pyc", "lib/python3.11/site-packages/setuptools/__pycache__/launch.cpython-311.pyc", "lib/python3.11/site-packages/setuptools/__pycache__/logging.cpython-311.pyc", "lib/python3.11/site-packages/setuptools/__pycache__/modified.cpython-311.pyc", "lib/python3.11/site-packages/setuptools/__pycache__/monkey.cpython-311.pyc", "lib/python3.11/site-packages/setuptools/__pycache__/msvc.cpython-311.pyc", "lib/python3.11/site-packages/setuptools/__pycache__/namespaces.cpython-311.pyc", "lib/python3.11/site-packages/setuptools/tests/__pycache__/__init__.cpython-311.pyc", "lib/python3.11/site-packages/setuptools/tests/compat/__pycache__/__init__.cpython-311.pyc", "lib/python3.11/site-packages/setuptools/tests/compat/__pycache__/py39.cpython-311.pyc", "lib/python3.11/site-packages/setuptools/tests/config/__pycache__/__init__.cpython-311.pyc", "lib/python3.11/site-packages/setuptools/tests/config/downloads/__pycache__/__init__.cpython-311.pyc", "lib/python3.11/site-packages/setuptools/tests/config/downloads/__pycache__/preload.cpython-311.pyc", "lib/python3.11/site-packages/setuptools/tests/config/__pycache__/test_apply_pyprojecttoml.cpython-311.pyc", "lib/python3.11/site-packages/setuptools/tests/config/__pycache__/test_expand.cpython-311.pyc", "lib/python3.11/site-packages/setuptools/tests/config/__pycache__/test_pyprojecttoml.cpython-311.pyc", "lib/python3.11/site-packages/setuptools/tests/config/__pycache__/test_pyprojecttoml_dynamic_deps.cpython-311.pyc", "lib/python3.11/site-packages/setuptools/tests/config/__pycache__/test_setupcfg.cpython-311.pyc", "lib/python3.11/site-packages/setuptools/tests/__pycache__/contexts.cpython-311.pyc", "lib/python3.11/site-packages/setuptools/tests/__pycache__/environment.cpython-311.pyc", "lib/python3.11/site-packages/setuptools/tests/__pycache__/fixtures.cpython-311.pyc", "lib/python3.11/site-packages/setuptools/tests/integration/__pycache__/__init__.cpython-311.pyc", "lib/python3.11/site-packages/setuptools/tests/integration/__pycache__/helpers.cpython-311.pyc", "lib/python3.11/site-packages/setuptools/tests/integration/__pycache__/test_pbr.cpython-311.pyc", "lib/python3.11/site-packages/setuptools/tests/integration/__pycache__/test_pip_install_sdist.cpython-311.pyc", "lib/python3.11/site-packages/setuptools/tests/__pycache__/mod_with_constant.cpython-311.pyc", "lib/python3.11/site-packages/setuptools/tests/__pycache__/namespaces.cpython-311.pyc", "lib/python3.11/site-packages/setuptools/tests/__pycache__/script-with-bom.cpython-311.pyc", "lib/python3.11/site-packages/setuptools/tests/__pycache__/test_archive_util.cpython-311.pyc", "lib/python3.11/site-packages/setuptools/tests/__pycache__/test_bdist_deprecations.cpython-311.pyc", "lib/python3.11/site-packages/setuptools/tests/__pycache__/test_bdist_egg.cpython-311.pyc", "lib/python3.11/site-packages/setuptools/tests/__pycache__/test_bdist_wheel.cpython-311.pyc", "lib/python3.11/site-packages/setuptools/tests/__pycache__/test_build.cpython-311.pyc", "lib/python3.11/site-packages/setuptools/tests/__pycache__/test_build_clib.cpython-311.pyc", "lib/python3.11/site-packages/setuptools/tests/__pycache__/test_build_ext.cpython-311.pyc", "lib/python3.11/site-packages/setuptools/tests/__pycache__/test_build_meta.cpython-311.pyc", "lib/python3.11/site-packages/setuptools/tests/__pycache__/test_build_py.cpython-311.pyc", "lib/python3.11/site-packages/setuptools/tests/__pycache__/test_config_discovery.cpython-311.pyc", "lib/python3.11/site-packages/setuptools/tests/__pycache__/test_core_metadata.cpython-311.pyc", "lib/python3.11/site-packages/setuptools/tests/__pycache__/test_depends.cpython-311.pyc", "lib/python3.11/site-packages/setuptools/tests/__pycache__/test_develop.cpython-311.pyc", "lib/python3.11/site-packages/setuptools/tests/__pycache__/test_dist.cpython-311.pyc", "lib/python3.11/site-packages/setuptools/tests/__pycache__/test_dist_info.cpython-311.pyc", "lib/python3.11/site-packages/setuptools/tests/__pycache__/test_distutils_adoption.cpython-311.pyc", "lib/python3.11/site-packages/setuptools/tests/__pycache__/test_editable_install.cpython-311.pyc", "lib/python3.11/site-packages/setuptools/tests/__pycache__/test_egg_info.cpython-311.pyc", "lib/python3.11/site-packages/setuptools/tests/__pycache__/test_extern.cpython-311.pyc", "lib/python3.11/site-packages/setuptools/tests/__pycache__/test_find_packages.cpython-311.pyc", "lib/python3.11/site-packages/setuptools/tests/__pycache__/test_find_py_modules.cpython-311.pyc", "lib/python3.11/site-packages/setuptools/tests/__pycache__/test_glob.cpython-311.pyc", "lib/python3.11/site-packages/setuptools/tests/__pycache__/test_install_scripts.cpython-311.pyc", "lib/python3.11/site-packages/setuptools/tests/__pycache__/test_logging.cpython-311.pyc", "lib/python3.11/site-packages/setuptools/tests/__pycache__/test_manifest.cpython-311.pyc", "lib/python3.11/site-packages/setuptools/tests/__pycache__/test_namespaces.cpython-311.pyc", "lib/python3.11/site-packages/setuptools/tests/__pycache__/test_scripts.cpython-311.pyc", "lib/python3.11/site-packages/setuptools/tests/__pycache__/test_sdist.cpython-311.pyc", "lib/python3.11/site-packages/setuptools/tests/__pycache__/test_setopt.cpython-311.pyc", "lib/python3.11/site-packages/setuptools/tests/__pycache__/test_setuptools.cpython-311.pyc", "lib/python3.11/site-packages/setuptools/tests/__pycache__/test_shutil_wrapper.cpython-311.pyc", "lib/python3.11/site-packages/setuptools/tests/__pycache__/test_unicode_utils.cpython-311.pyc", "lib/python3.11/site-packages/setuptools/tests/__pycache__/test_virtualenv.cpython-311.pyc", "lib/python3.11/site-packages/setuptools/tests/__pycache__/test_warnings.cpython-311.pyc", "lib/python3.11/site-packages/setuptools/tests/__pycache__/test_wheel.cpython-311.pyc", "lib/python3.11/site-packages/setuptools/tests/__pycache__/test_windows_wrappers.cpython-311.pyc", "lib/python3.11/site-packages/setuptools/tests/__pycache__/text.cpython-311.pyc", "lib/python3.11/site-packages/setuptools/tests/__pycache__/textwrap.cpython-311.pyc", "lib/python3.11/site-packages/setuptools/__pycache__/unicode_utils.cpython-311.pyc", "lib/python3.11/site-packages/setuptools/__pycache__/version.cpython-311.pyc", "lib/python3.11/site-packages/setuptools/__pycache__/warnings.cpython-311.pyc", "lib/python3.11/site-packages/setuptools/__pycache__/wheel.cpython-311.pyc", "lib/python3.11/site-packages/setuptools/__pycache__/windows_support.cpython-311.pyc"], "fn": "setuptools-80.9.0-pyhff2d567_0.conda", "license": "MIT", "link": {"source": "/home/<USER>/miniforge3/pkgs/setuptools-80.9.0-pyhff2d567_0", "type": 1}, "md5": "4de79c071274a53dcaf2a8c749d1499e", "name": "setuptools", "noarch": "python", "package_tarball_full_path": "/home/<USER>/miniforge3/pkgs/setuptools-80.9.0-pyhff2d567_0.conda", "package_type": "noarch_python", "paths_data": {"paths": [{"_path": "site-packages/_distutils_hack/__init__.py", "path_type": "hardlink", "sha256": "df81e6bcba34ee3e3952f776551fb669143b9490fdd6c4caeb32609f97e985b4", "sha256_in_prefix": "df81e6bcba34ee3e3952f776551fb669143b9490fdd6c4caeb32609f97e985b4", "size_in_bytes": 6755}, {"_path": "site-packages/_distutils_hack/override.py", "path_type": "hardlink", "sha256": "12efecf8d17a5486780aa774b5b6c0e70b56932d8864f35df1eb7a18bb759b3a", "sha256_in_prefix": "12efecf8d17a5486780aa774b5b6c0e70b56932d8864f35df1eb7a18bb759b3a", "size_in_bytes": 44}, {"_path": "site-packages/distutils-precedence.pth", "path_type": "hardlink", "sha256": "2638ce9e2500e572a5e0de7faed6661eb569d1b696fcba07b0dd223da5f5d224", "sha256_in_prefix": "2638ce9e2500e572a5e0de7faed6661eb569d1b696fcba07b0dd223da5f5d224", "size_in_bytes": 151}, {"_path": "site-packages/pkg_resources/__init__.py", "path_type": "hardlink", "sha256": "bb1ad698a177971b06e2aea88c796ee2d07c8fc2b0f63ab1fd234cc8328fe6ae", "sha256_in_prefix": "bb1ad698a177971b06e2aea88c796ee2d07c8fc2b0f63ab1fd234cc8328fe6ae", "size_in_bytes": 126219}, {"_path": "site-packages/pkg_resources/api_tests.txt", "path_type": "hardlink", "sha256": "5c476fcb88a01c7aeadaa34734c1e795f3ba5d240a36a3b22c76e5e907297c02", "sha256_in_prefix": "5c476fcb88a01c7aeadaa34734c1e795f3ba5d240a36a3b22c76e5e907297c02", "size_in_bytes": 12595}, {"_path": "site-packages/pkg_resources/py.typed", "path_type": "hardlink", "sha256": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "sha256_in_prefix": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "size_in_bytes": 0}, {"_path": "site-packages/pkg_resources/tests/__init__.py", "path_type": "hardlink", "sha256": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "sha256_in_prefix": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "size_in_bytes": 0}, {"_path": "site-packages/pkg_resources/tests/data/my-test-package-source/setup.cfg", "path_type": "hardlink", "sha256": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "sha256_in_prefix": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "size_in_bytes": 0}, {"_path": "site-packages/pkg_resources/tests/data/my-test-package-source/setup.py", "path_type": "hardlink", "sha256": "d55a1b84065b31beccf667e16ff344f0fc03b2fba4a162ecf5a5004b4a5885ef", "sha256_in_prefix": "d55a1b84065b31beccf667e16ff344f0fc03b2fba4a162ecf5a5004b4a5885ef", "size_in_bytes": 105}, {"_path": "site-packages/pkg_resources/tests/data/my-test-package-zip/my-test-package.zip", "path_type": "hardlink", "sha256": "01845c437f4655e3cf9cc4fc4e49cfd607431f22675e1b611129a90239f34822", "sha256_in_prefix": "01845c437f4655e3cf9cc4fc4e49cfd607431f22675e1b611129a90239f34822", "size_in_bytes": 1809}, {"_path": "site-packages/pkg_resources/tests/data/my-test-package_unpacked-egg/my_test_package-1.0-py3.7.egg/EGG-INFO/PKG-INFO", "path_type": "hardlink", "sha256": "26f5aff48a363c0b98c04130d9f056e1073962f75b92c729297d6498bceca079", "sha256_in_prefix": "26f5aff48a363c0b98c04130d9f056e1073962f75b92c729297d6498bceca079", "size_in_bytes": 187}, {"_path": "site-packages/pkg_resources/tests/data/my-test-package_unpacked-egg/my_test_package-1.0-py3.7.egg/EGG-INFO/SOURCES.txt", "path_type": "hardlink", "sha256": "e029641fc793a2f66b755ac916c56ec5d6cc105fbe941552b8aa270c03c4e497", "sha256_in_prefix": "e029641fc793a2f66b755ac916c56ec5d6cc105fbe941552b8aa270c03c4e497", "size_in_bytes": 208}, {"_path": "site-packages/pkg_resources/tests/data/my-test-package_unpacked-egg/my_test_package-1.0-py3.7.egg/EGG-INFO/dependency_links.txt", "path_type": "hardlink", "sha256": "01ba4719c80b6fe911b091a7c05124b64eeece964e09c058ef8f9805daca546b", "sha256_in_prefix": "01ba4719c80b6fe911b091a7c05124b64eeece964e09c058ef8f9805daca546b", "size_in_bytes": 1}, {"_path": "site-packages/pkg_resources/tests/data/my-test-package_unpacked-egg/my_test_package-1.0-py3.7.egg/EGG-INFO/top_level.txt", "path_type": "hardlink", "sha256": "01ba4719c80b6fe911b091a7c05124b64eeece964e09c058ef8f9805daca546b", "sha256_in_prefix": "01ba4719c80b6fe911b091a7c05124b64eeece964e09c058ef8f9805daca546b", "size_in_bytes": 1}, {"_path": "site-packages/pkg_resources/tests/data/my-test-package_unpacked-egg/my_test_package-1.0-py3.7.egg/EGG-INFO/zip-safe", "path_type": "hardlink", "sha256": "01ba4719c80b6fe911b091a7c05124b64eeece964e09c058ef8f9805daca546b", "sha256_in_prefix": "01ba4719c80b6fe911b091a7c05124b64eeece964e09c058ef8f9805daca546b", "size_in_bytes": 1}, {"_path": "site-packages/pkg_resources/tests/data/my-test-package_zipped-egg/my_test_package-1.0-py3.7.egg", "path_type": "hardlink", "sha256": "65394c1b18d11a2283364880d9cef98db407d93588b5e3f4d22ac5f60bdccdba", "sha256_in_prefix": "65394c1b18d11a2283364880d9cef98db407d93588b5e3f4d22ac5f60bdccdba", "size_in_bytes": 843}, {"_path": "site-packages/pkg_resources/tests/test_find_distributions.py", "path_type": "hardlink", "sha256": "53dd5ca2fe4bd423802162cdab75f2e29954eff327384d56b5732eea2576c1a3", "sha256_in_prefix": "53dd5ca2fe4bd423802162cdab75f2e29954eff327384d56b5732eea2576c1a3", "size_in_bytes": 1972}, {"_path": "site-packages/pkg_resources/tests/test_integration_zope_interface.py", "path_type": "hardlink", "sha256": "9f35682b9e7b29940dd15dc3210d6c55e6823a0b782a997e08e0c05ac3bba667", "sha256_in_prefix": "9f35682b9e7b29940dd15dc3210d6c55e6823a0b782a997e08e0c05ac3bba667", "size_in_bytes": 1652}, {"_path": "site-packages/pkg_resources/tests/test_markers.py", "path_type": "hardlink", "sha256": "d28aca83b50c0dfedf9ee350bd130e73e105f4400ffc94d09e4e26b4681b5b9d", "sha256_in_prefix": "d28aca83b50c0dfedf9ee350bd130e73e105f4400ffc94d09e4e26b4681b5b9d", "size_in_bytes": 241}, {"_path": "site-packages/pkg_resources/tests/test_pkg_resources.py", "path_type": "hardlink", "sha256": "e4cb786c94212c22fc8fc702e3a52fdf6369d977354d3c4b19ac087c44f9e459", "sha256_in_prefix": "e4cb786c94212c22fc8fc702e3a52fdf6369d977354d3c4b19ac087c44f9e459", "size_in_bytes": 17111}, {"_path": "site-packages/pkg_resources/tests/test_resources.py", "path_type": "hardlink", "sha256": "2b42ea300506a5143da546fd2b4bf223b19eb2fb6542f4c7d3be26f84d95425a", "sha256_in_prefix": "2b42ea300506a5143da546fd2b4bf223b19eb2fb6542f4c7d3be26f84d95425a", "size_in_bytes": 31252}, {"_path": "site-packages/pkg_resources/tests/test_working_set.py", "path_type": "hardlink", "sha256": "951b46256222c52c123126e31e047178911088b3115dccf7c7324bdaa2fb7976", "sha256_in_prefix": "951b46256222c52c123126e31e047178911088b3115dccf7c7324bdaa2fb7976", "size_in_bytes": 8602}, {"_path": "site-packages/setuptools-80.9.0-py3.9.egg-info/PKG-INFO", "path_type": "hardlink", "sha256": "7f890ca8dbc16b63b568c89312ed6a7f9f0a79d0b2b7f3c83bf796ac3d930615", "sha256_in_prefix": "7f890ca8dbc16b63b568c89312ed6a7f9f0a79d0b2b7f3c83bf796ac3d930615", "size_in_bytes": 6572}, {"_path": "site-packages/setuptools-80.9.0-py3.9.egg-info/SOURCES.txt", "path_type": "hardlink", "sha256": "c57197a8903ee39f4f7f2c7887eaf4e1d508c4f533976705810175f5f4b9ff1b", "sha256_in_prefix": "c57197a8903ee39f4f7f2c7887eaf4e1d508c4f533976705810175f5f4b9ff1b", "size_in_bytes": 24260}, {"_path": "site-packages/setuptools-80.9.0-py3.9.egg-info/dependency_links.txt", "path_type": "hardlink", "sha256": "01ba4719c80b6fe911b091a7c05124b64eeece964e09c058ef8f9805daca546b", "sha256_in_prefix": "01ba4719c80b6fe911b091a7c05124b64eeece964e09c058ef8f9805daca546b", "size_in_bytes": 1}, {"_path": "site-packages/setuptools-80.9.0-py3.9.egg-info/entry_points.txt", "path_type": "hardlink", "sha256": "ce482d8697ff15af4d544f69e85293dd793d0d1d5f680711538728820b15ee30", "sha256_in_prefix": "ce482d8697ff15af4d544f69e85293dd793d0d1d5f680711538728820b15ee30", "size_in_bytes": 2449}, {"_path": "site-packages/setuptools-80.9.0-py3.9.egg-info/requires.txt", "path_type": "hardlink", "sha256": "cd186b9559d56bb22f53a7730a6ffdd8b0c2a342bbf97705c98b547553d584e3", "sha256_in_prefix": "cd186b9559d56bb22f53a7730a6ffdd8b0c2a342bbf97705c98b547553d584e3", "size_in_bytes": 1231}, {"_path": "site-packages/setuptools-80.9.0-py3.9.egg-info/top_level.txt", "path_type": "hardlink", "sha256": "77dc8bdfdbff5bbaa62830d21fab13e1b1348ff2ecd4cdcfd7ad4e1a076c9b88", "sha256_in_prefix": "77dc8bdfdbff5bbaa62830d21fab13e1b1348ff2ecd4cdcfd7ad4e1a076c9b88", "size_in_bytes": 41}, {"_path": "site-packages/setuptools/__init__.py", "path_type": "hardlink", "sha256": "eddb9a7016889b1ceb51ad0e821233f25560689d5f230efeb8bdafd7abd8fd21", "sha256_in_prefix": "eddb9a7016889b1ceb51ad0e821233f25560689d5f230efeb8bdafd7abd8fd21", "size_in_bytes": 9004}, {"_path": "site-packages/setuptools/_core_metadata.py", "path_type": "hardlink", "sha256": "4fb4e3a7e592a0df3cd5a75ebf7475c335c23e79031ea6c2d8c83294dd728d2f", "sha256_in_prefix": "4fb4e3a7e592a0df3cd5a75ebf7475c335c23e79031ea6c2d8c83294dd728d2f", "size_in_bytes": 11978}, {"_path": "site-packages/setuptools/_discovery.py", "path_type": "hardlink", "sha256": "1f112981843ccd468b38e4a9f09200e32d8c76f9fdcac571b293d3fa9a5fccce", "sha256_in_prefix": "1f112981843ccd468b38e4a9f09200e32d8c76f9fdcac571b293d3fa9a5fccce", "size_in_bytes": 836}, {"_path": "site-packages/setuptools/_distutils/__init__.py", "path_type": "hardlink", "sha256": "c4662e856c0b1b4ec9d10e3d0559c48cfcbac320dc77abde24c0c95fb9639723", "sha256_in_prefix": "c4662e856c0b1b4ec9d10e3d0559c48cfcbac320dc77abde24c0c95fb9639723", "size_in_bytes": 359}, {"_path": "site-packages/setuptools/_distutils/_log.py", "path_type": "hardlink", "sha256": "8be94d4d37174bc4e65884c9e833831afb56e73e6d31ab6d250efa87cad9c505", "sha256_in_prefix": "8be94d4d37174bc4e65884c9e833831afb56e73e6d31ab6d250efa87cad9c505", "size_in_bytes": 42}, {"_path": "site-packages/setuptools/_distutils/_macos_compat.py", "path_type": "hardlink", "sha256": "273506845e04e722084c76d468fa1b6445a318776badc355eb7cfce92e118c17", "sha256_in_prefix": "273506845e04e722084c76d468fa1b6445a318776badc355eb7cfce92e118c17", "size_in_bytes": 239}, {"_path": "site-packages/setuptools/_distutils/_modified.py", "path_type": "hardlink", "sha256": "445d67d427b1c83615de5bc66de5d2d2cf9708955ba0338851b03cc0442a6136", "sha256_in_prefix": "445d67d427b1c83615de5bc66de5d2d2cf9708955ba0338851b03cc0442a6136", "size_in_bytes": 3211}, {"_path": "site-packages/setuptools/_distutils/_msvccompiler.py", "path_type": "hardlink", "sha256": "f4f49f487c6f2671e740be92ab3e17733ee2681213eb6a7a061790cc6b12970a", "sha256_in_prefix": "f4f49f487c6f2671e740be92ab3e17733ee2681213eb6a7a061790cc6b12970a", "size_in_bytes": 335}, {"_path": "site-packages/setuptools/_distutils/archive_util.py", "path_type": "hardlink", "sha256": "430db3f8fb7e355f2535442bce3b375c31960961cc3e7a872f2b7c4e20f65c40", "sha256_in_prefix": "430db3f8fb7e355f2535442bce3b375c31960961cc3e7a872f2b7c4e20f65c40", "size_in_bytes": 8884}, {"_path": "site-packages/setuptools/_distutils/ccompiler.py", "path_type": "hardlink", "sha256": "14a563ab3189edcf85b68b8d8e12e268c3e6e4b28c6471c0aee5dff0b536d7a7", "sha256_in_prefix": "14a563ab3189edcf85b68b8d8e12e268c3e6e4b28c6471c0aee5dff0b536d7a7", "size_in_bytes": 524}, {"_path": "site-packages/setuptools/_distutils/cmd.py", "path_type": "hardlink", "sha256": "857b5a45a1fb4019df34e22a12f0ade3b8b06730fd315bc176185d41cb47b313", "sha256_in_prefix": "857b5a45a1fb4019df34e22a12f0ade3b8b06730fd315bc176185d41cb47b313", "size_in_bytes": 22186}, {"_path": "site-packages/setuptools/_distutils/command/__init__.py", "path_type": "hardlink", "sha256": "19f140cdb06a935ab1487e0175a2a2a0a4b88514670f8e01026c0437ce42e2ef", "sha256_in_prefix": "19f140cdb06a935ab1487e0175a2a2a0a4b88514670f8e01026c0437ce42e2ef", "size_in_bytes": 386}, {"_path": "site-packages/setuptools/_distutils/command/_framework_compat.py", "path_type": "hardlink", "sha256": "d2265d4896331915820afcd10ca13e474fbfc9a018bc531dd729576f67985ee8", "sha256_in_prefix": "d2265d4896331915820afcd10ca13e474fbfc9a018bc531dd729576f67985ee8", "size_in_bytes": 1609}, {"_path": "site-packages/setuptools/_distutils/command/bdist.py", "path_type": "hardlink", "sha256": "8d6b64eb547b7d635450dc49574b614d9cd4e67f342f7032d7069288ff6488b0", "sha256_in_prefix": "8d6b64eb547b7d635450dc49574b614d9cd4e67f342f7032d7069288ff6488b0", "size_in_bytes": 5854}, {"_path": "site-packages/setuptools/_distutils/command/bdist_dumb.py", "path_type": "hardlink", "sha256": "1f1d6302aa19371608cb83794cbcd4a7a2797e2f0bb35f29cbb5252cd1613b61", "sha256_in_prefix": "1f1d6302aa19371608cb83794cbcd4a7a2797e2f0bb35f29cbb5252cd1613b61", "size_in_bytes": 4631}, {"_path": "site-packages/setuptools/_distutils/command/bdist_rpm.py", "path_type": "hardlink", "sha256": "9f17175efe5aec1fb59ed5aee036c6982b444b810120dac968141c44d0180892", "sha256_in_prefix": "9f17175efe5aec1fb59ed5aee036c6982b444b810120dac968141c44d0180892", "size_in_bytes": 21785}, {"_path": "site-packages/setuptools/_distutils/command/build.py", "path_type": "hardlink", "sha256": "4a91e56a07f488d68a572221c437e13c567c5f5f8b0163824000b2fb2b762b4c", "sha256_in_prefix": "4a91e56a07f488d68a572221c437e13c567c5f5f8b0163824000b2fb2b762b4c", "size_in_bytes": 5923}, {"_path": "site-packages/setuptools/_distutils/command/build_clib.py", "path_type": "hardlink", "sha256": "68ca997147c26ce02eff1afe03d896f90f58647ce90c62d14decce80c4099924", "sha256_in_prefix": "68ca997147c26ce02eff1afe03d896f90f58647ce90c62d14decce80c4099924", "size_in_bytes": 7777}, {"_path": "site-packages/setuptools/_distutils/command/build_ext.py", "path_type": "hardlink", "sha256": "cebaecbbd1d79f357a6d761b26e6422b84b05593232a7978a46d68ddb35cc6d7", "sha256_in_prefix": "cebaecbbd1d79f357a6d761b26e6422b84b05593232a7978a46d68ddb35cc6d7", "size_in_bytes": 32710}, {"_path": "site-packages/setuptools/_distutils/command/build_py.py", "path_type": "hardlink", "sha256": "55fabe20d7a6a0c6e0e9fd34dc14f2fd47e9f1b8ce661985221a4a31c7d72e0b", "sha256_in_prefix": "55fabe20d7a6a0c6e0e9fd34dc14f2fd47e9f1b8ce661985221a4a31c7d72e0b", "size_in_bytes": 16696}, {"_path": "site-packages/setuptools/_distutils/command/build_scripts.py", "path_type": "hardlink", "sha256": "7a630438e36434b3c2f803232b2e39524b14958d70934e9eb0e193869c227483", "sha256_in_prefix": "7a630438e36434b3c2f803232b2e39524b14958d70934e9eb0e193869c227483", "size_in_bytes": 5135}, {"_path": "site-packages/setuptools/_distutils/command/check.py", "path_type": "hardlink", "sha256": "ca835ed8c3d8e0971333baf0a0841d7d9ef9ab9462d39f08d9ca22f86abd0a33", "sha256_in_prefix": "ca835ed8c3d8e0971333baf0a0841d7d9ef9ab9462d39f08d9ca22f86abd0a33", "size_in_bytes": 4946}, {"_path": "site-packages/setuptools/_distutils/command/clean.py", "path_type": "hardlink", "sha256": "75001a70e69bc015d4f49a19fb5185bacab778596d0da7972454989dca866ef1", "sha256_in_prefix": "75001a70e69bc015d4f49a19fb5185bacab778596d0da7972454989dca866ef1", "size_in_bytes": 2644}, {"_path": "site-packages/setuptools/_distutils/command/config.py", "path_type": "hardlink", "sha256": "aabadfcfa34440e4666ea898d9796f08359886c6cbcb165726c51129f60dfe4c", "sha256_in_prefix": "aabadfcfa34440e4666ea898d9796f08359886c6cbcb165726c51129f60dfe4c", "size_in_bytes": 12724}, {"_path": "site-packages/setuptools/_distutils/command/install.py", "path_type": "hardlink", "sha256": "f897a707e9ae6b885cd9123ff96f05f4f9cffc9f8e6853bb1343c918ac4ba35a", "sha256_in_prefix": "f897a707e9ae6b885cd9123ff96f05f4f9cffc9f8e6853bb1343c918ac4ba35a", "size_in_bytes": 30072}, {"_path": "site-packages/setuptools/_distutils/command/install_data.py", "path_type": "hardlink", "sha256": "1b306551658ab9b4d82653fe2f46ae52b8aaf5c2fee5128e728c874edb4a8f44", "sha256_in_prefix": "1b306551658ab9b4d82653fe2f46ae52b8aaf5c2fee5128e728c874edb4a8f44", "size_in_bytes": 2875}, {"_path": "site-packages/setuptools/_distutils/command/install_egg_info.py", "path_type": "hardlink", "sha256": "7df88ba14d62bd027cab6fd62fb6728196d470eb207452ca2fba2d1082565a42", "sha256_in_prefix": "7df88ba14d62bd027cab6fd62fb6728196d470eb207452ca2fba2d1082565a42", "size_in_bytes": 2868}, {"_path": "site-packages/setuptools/_distutils/command/install_headers.py", "path_type": "hardlink", "sha256": "e5c88a0a3f1cdd72ac60d29d91d32f9f2a5a50229ca1608379e6628f77c3f99e", "sha256_in_prefix": "e5c88a0a3f1cdd72ac60d29d91d32f9f2a5a50229ca1608379e6628f77c3f99e", "size_in_bytes": 1272}, {"_path": "site-packages/setuptools/_distutils/command/install_lib.py", "path_type": "hardlink", "sha256": "dacf7e9b9f9bd6a2a6e75176f250792f7f59eafbff187325bfd74d052ba9a24d", "sha256_in_prefix": "dacf7e9b9f9bd6a2a6e75176f250792f7f59eafbff187325bfd74d052ba9a24d", "size_in_bytes": 8588}, {"_path": "site-packages/setuptools/_distutils/command/install_scripts.py", "path_type": "hardlink", "sha256": "334a4f7626aa07b4c69aa4ccba3a4619e88bd08abf0937868cc16dae60e6c333", "sha256_in_prefix": "334a4f7626aa07b4c69aa4ccba3a4619e88bd08abf0937868cc16dae60e6c333", "size_in_bytes": 2002}, {"_path": "site-packages/setuptools/_distutils/command/sdist.py", "path_type": "hardlink", "sha256": "711205e87b75849e9ac8e38557270c14150dc63a3de1efeb1583f1e078bc0217", "sha256_in_prefix": "711205e87b75849e9ac8e38557270c14150dc63a3de1efeb1583f1e078bc0217", "size_in_bytes": 19151}, {"_path": "site-packages/setuptools/_distutils/compat/__init__.py", "path_type": "hardlink", "sha256": "276d1a5c68c9f3a460e35c452c85a57160a067d79d31d27dbef74d110f3bbcf4", "sha256_in_prefix": "276d1a5c68c9f3a460e35c452c85a57160a067d79d31d27dbef74d110f3bbcf4", "size_in_bytes": 522}, {"_path": "site-packages/setuptools/_distutils/compat/numpy.py", "path_type": "hardlink", "sha256": "505827799c3dc3dee0e1cfb21a80083b22f150e590f9f3d122185f32ceff3ae7", "sha256_in_prefix": "505827799c3dc3dee0e1cfb21a80083b22f150e590f9f3d122185f32ceff3ae7", "size_in_bytes": 167}, {"_path": "site-packages/setuptools/_distutils/compat/py39.py", "path_type": "hardlink", "sha256": "84eb03ea5c192ea66832769c349dcfea7500f8b250844a55b584f3547d28f7a3", "sha256_in_prefix": "84eb03ea5c192ea66832769c349dcfea7500f8b250844a55b584f3547d28f7a3", "size_in_bytes": 1964}, {"_path": "site-packages/setuptools/_distutils/compilers/C/base.py", "path_type": "hardlink", "sha256": "5d1d6b0424ad0aabaa9bb40e6170f8d7e2dfbec15c3e91b1af0c5e5f32729ffc", "sha256_in_prefix": "5d1d6b0424ad0aabaa9bb40e6170f8d7e2dfbec15c3e91b1af0c5e5f32729ffc", "size_in_bytes": 54876}, {"_path": "site-packages/setuptools/_distutils/compilers/C/cygwin.py", "path_type": "hardlink", "sha256": "0d49704126f9e5a8fb39d72671d76b98299512311ac48889e611d43b71813cdb", "sha256_in_prefix": "0d49704126f9e5a8fb39d72671d76b98299512311ac48889e611d43b71813cdb", "size_in_bytes": 11844}, {"_path": "site-packages/setuptools/_distutils/compilers/C/errors.py", "path_type": "hardlink", "sha256": "b0a395cc96a331498d75fcb0a3d50cfd0406b0a15c7250e1b48e5394289730b7", "sha256_in_prefix": "b0a395cc96a331498d75fcb0a3d50cfd0406b0a15c7250e1b48e5394289730b7", "size_in_bytes": 573}, {"_path": "site-packages/setuptools/_distutils/compilers/C/msvc.py", "path_type": "hardlink", "sha256": "3bcce8e8d2830300aebf917414f65e02ec986fb0055c82ede4db676463e5c8d8", "sha256_in_prefix": "3bcce8e8d2830300aebf917414f65e02ec986fb0055c82ede4db676463e5c8d8", "size_in_bytes": 21802}, {"_path": "site-packages/setuptools/_distutils/compilers/C/tests/test_base.py", "path_type": "hardlink", "sha256": "add847739e9b857b66e4d9cdf41487c2be9cebd52accc22d650ce5c3602c74c7", "sha256_in_prefix": "add847739e9b857b66e4d9cdf41487c2be9cebd52accc22d650ce5c3602c74c7", "size_in_bytes": 2706}, {"_path": "site-packages/setuptools/_distutils/compilers/C/tests/test_cygwin.py", "path_type": "hardlink", "sha256": "5205765605178f756e95c6c373450159f132243c78dad812c12e0bcc78b1de66", "sha256_in_prefix": "5205765605178f756e95c6c373450159f132243c78dad812c12e0bcc78b1de66", "size_in_bytes": 2701}, {"_path": "site-packages/setuptools/_distutils/compilers/C/tests/test_mingw.py", "path_type": "hardlink", "sha256": "8429b0cb2c084a9468c8ec926c51c12f84e9ad6455d265160ca98e2cef170571", "sha256_in_prefix": "8429b0cb2c084a9468c8ec926c51c12f84e9ad6455d265160ca98e2cef170571", "size_in_bytes": 1900}, {"_path": "site-packages/setuptools/_distutils/compilers/C/tests/test_msvc.py", "path_type": "hardlink", "sha256": "0e51a3999d660523172209a5bbcd0129ced5f8424e66e62e730270161e5d9f6f", "sha256_in_prefix": "0e51a3999d660523172209a5bbcd0129ced5f8424e66e62e730270161e5d9f6f", "size_in_bytes": 4151}, {"_path": "site-packages/setuptools/_distutils/compilers/C/tests/test_unix.py", "path_type": "hardlink", "sha256": "03269d5b0d5f47e51e0e5d93bc86d807338954792913fa11450dc94c95aa6840", "sha256_in_prefix": "03269d5b0d5f47e51e0e5d93bc86d807338954792913fa11450dc94c95aa6840", "size_in_bytes": 14642}, {"_path": "site-packages/setuptools/_distutils/compilers/C/unix.py", "path_type": "hardlink", "sha256": "607fb2f60fe98c1163689c8725090310143bab80f6d08dbe71624d760c3e621a", "sha256_in_prefix": "607fb2f60fe98c1163689c8725090310143bab80f6d08dbe71624d760c3e621a", "size_in_bytes": 16531}, {"_path": "site-packages/setuptools/_distutils/compilers/C/zos.py", "path_type": "hardlink", "sha256": "be735e58b45991d224759f98c819cbf2275351f7023a7d2d2cc5b938127449c5", "sha256_in_prefix": "be735e58b45991d224759f98c819cbf2275351f7023a7d2d2cc5b938127449c5", "size_in_bytes": 6586}, {"_path": "site-packages/setuptools/_distutils/core.py", "path_type": "hardlink", "sha256": "1841ca6850b8f13de8fbf4a61f8f3ae06bcacb1d4881b542e884883d5971edae", "sha256_in_prefix": "1841ca6850b8f13de8fbf4a61f8f3ae06bcacb1d4881b542e884883d5971edae", "size_in_bytes": 9364}, {"_path": "site-packages/setuptools/_distutils/cygwinccompiler.py", "path_type": "hardlink", "sha256": "986fdc53c4956786a60ff56d179bc7e815cfd3e920846b033db0d25eb43deb77", "sha256_in_prefix": "986fdc53c4956786a60ff56d179bc7e815cfd3e920846b033db0d25eb43deb77", "size_in_bytes": 594}, {"_path": "site-packages/setuptools/_distutils/debug.py", "path_type": "hardlink", "sha256": "37a32b4c0a8aea5f52564ead5b0791d74f0f33c3a5eea3657f257e9c770b86c6", "sha256_in_prefix": "37a32b4c0a8aea5f52564ead5b0791d74f0f33c3a5eea3657f257e9c770b86c6", "size_in_bytes": 139}, {"_path": "site-packages/setuptools/_distutils/dep_util.py", "path_type": "hardlink", "sha256": "c4def9a7a6691e13221c473eae92f65e29494329c79c336269f1ed79a678b635", "sha256_in_prefix": "c4def9a7a6691e13221c473eae92f65e29494329c79c336269f1ed79a678b635", "size_in_bytes": 349}, {"_path": "site-packages/setuptools/_distutils/dir_util.py", "path_type": "hardlink", "sha256": "0d73d495f5551ac83d07e26083802dfe3f53eef33ad0e8303579101ea4e8efe2", "sha256_in_prefix": "0d73d495f5551ac83d07e26083802dfe3f53eef33ad0e8303579101ea4e8efe2", "size_in_bytes": 7236}, {"_path": "site-packages/setuptools/_distutils/dist.py", "path_type": "hardlink", "sha256": "816e7df1413458c9335d0437d4dafef0becc3f0d2820ecf9392491cd8665c2b3", "sha256_in_prefix": "816e7df1413458c9335d0437d4dafef0becc3f0d2820ecf9392491cd8665c2b3", "size_in_bytes": 55794}, {"_path": "site-packages/setuptools/_distutils/errors.py", "path_type": "hardlink", "sha256": "3cf136a03461e72f50d5b78a2bdae176f0da0b34218b81c25ece0a72a7ea8196", "sha256_in_prefix": "3cf136a03461e72f50d5b78a2bdae176f0da0b34218b81c25ece0a72a7ea8196", "size_in_bytes": 3092}, {"_path": "site-packages/setuptools/_distutils/extension.py", "path_type": "hardlink", "sha256": "168caee2050b70faa6d7f53dceb6181f1364e0daa0957bf5adbb0e93f42b49db", "sha256_in_prefix": "168caee2050b70faa6d7f53dceb6181f1364e0daa0957bf5adbb0e93f42b49db", "size_in_bytes": 11155}, {"_path": "site-packages/setuptools/_distutils/fancy_getopt.py", "path_type": "hardlink", "sha256": "3e374ef9b5825b48a657f50df8c184c3d47618fd8e884f291e32138264c06374", "sha256_in_prefix": "3e374ef9b5825b48a657f50df8c184c3d47618fd8e884f291e32138264c06374", "size_in_bytes": 17895}, {"_path": "site-packages/setuptools/_distutils/file_util.py", "path_type": "hardlink", "sha256": "60540bfe90f784bb8447d208fc7ebe8430bf608184a2891d778c1e74bba24d6d", "sha256_in_prefix": "60540bfe90f784bb8447d208fc7ebe8430bf608184a2891d778c1e74bba24d6d", "size_in_bytes": 7978}, {"_path": "site-packages/setuptools/_distutils/filelist.py", "path_type": "hardlink", "sha256": "30179244998f70a983bfca28660494e018903d9d0a870bfc97f8e10f9d17c9c2", "sha256_in_prefix": "30179244998f70a983bfca28660494e018903d9d0a870bfc97f8e10f9d17c9c2", "size_in_bytes": 15337}, {"_path": "site-packages/setuptools/_distutils/log.py", "path_type": "hardlink", "sha256": "57206ce63ef3e3e2ba5d310405385473d1f2329a0f2c6b50a4446a6f3e72970c", "sha256_in_prefix": "57206ce63ef3e3e2ba5d310405385473d1f2329a0f2c6b50a4446a6f3e72970c", "size_in_bytes": 1200}, {"_path": "site-packages/setuptools/_distutils/spawn.py", "path_type": "hardlink", "sha256": "cec78287db0489fca9d08e5583bd7d24d2004a544e2767a15ea4271e5a6df3d4", "sha256_in_prefix": "cec78287db0489fca9d08e5583bd7d24d2004a544e2767a15ea4271e5a6df3d4", "size_in_bytes": 4086}, {"_path": "site-packages/setuptools/_distutils/sysconfig.py", "path_type": "hardlink", "sha256": "29e23c3876ccb84cc727c4347017b3f4a667cbc891cba67a634024333d6396c5", "sha256_in_prefix": "29e23c3876ccb84cc727c4347017b3f4a667cbc891cba67a634024333d6396c5", "size_in_bytes": 19728}, {"_path": "site-packages/setuptools/_distutils/tests/__init__.py", "path_type": "hardlink", "sha256": "8fe2283d912d42fdc438fbaa353c1a96be862f2463cc20be38e68dbd9ce61ec2", "sha256_in_prefix": "8fe2283d912d42fdc438fbaa353c1a96be862f2463cc20be38e68dbd9ce61ec2", "size_in_bytes": 1485}, {"_path": "site-packages/setuptools/_distutils/tests/compat/__init__.py", "path_type": "hardlink", "sha256": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "sha256_in_prefix": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "size_in_bytes": 0}, {"_path": "site-packages/setuptools/_distutils/tests/compat/py39.py", "path_type": "hardlink", "sha256": "b741814ccfb7d235fef7309f93094d045b73cda6de9b1eb4eb9989d1df7f551c", "sha256_in_prefix": "b741814ccfb7d235fef7309f93094d045b73cda6de9b1eb4eb9989d1df7f551c", "size_in_bytes": 1026}, {"_path": "site-packages/setuptools/_distutils/tests/support.py", "path_type": "hardlink", "sha256": "b63b18b32c6fa532b836b902b1e876ba3bc320657431ffdbe522397cfd93d323", "sha256_in_prefix": "b63b18b32c6fa532b836b902b1e876ba3bc320657431ffdbe522397cfd93d323", "size_in_bytes": 4099}, {"_path": "site-packages/setuptools/_distutils/tests/test_archive_util.py", "path_type": "hardlink", "sha256": "8e8ce2992c0f045f89a097cdfef0da895199a7ae8135c5991a1df81655b9ec34", "sha256_in_prefix": "8e8ce2992c0f045f89a097cdfef0da895199a7ae8135c5991a1df81655b9ec34", "size_in_bytes": 11787}, {"_path": "site-packages/setuptools/_distutils/tests/test_bdist.py", "path_type": "hardlink", "sha256": "c4d1f152c2e51ec6504709332dbfe2483db8b3ef4c93e357d9f7c15b03b23f27", "sha256_in_prefix": "c4d1f152c2e51ec6504709332dbfe2483db8b3ef4c93e357d9f7c15b03b23f27", "size_in_bytes": 1396}, {"_path": "site-packages/setuptools/_distutils/tests/test_bdist_dumb.py", "path_type": "hardlink", "sha256": "405d393073613ce759ca1f3c5e9c3c2ac3bae2cee9445925f0a2fe4685785cad", "sha256_in_prefix": "405d393073613ce759ca1f3c5e9c3c2ac3bae2cee9445925f0a2fe4685785cad", "size_in_bytes": 2247}, {"_path": "site-packages/setuptools/_distutils/tests/test_bdist_rpm.py", "path_type": "hardlink", "sha256": "1dd9bea705a0c9aa067466c470665af1c461194e39cbc8072bcba639a9d38e29", "sha256_in_prefix": "1dd9bea705a0c9aa067466c470665af1c461194e39cbc8072bcba639a9d38e29", "size_in_bytes": 3932}, {"_path": "site-packages/setuptools/_distutils/tests/test_build.py", "path_type": "hardlink", "sha256": "2496395e9399728db9658d29b2dc65fa223c987b163f4ba37f9a3c68eb6e6586", "sha256_in_prefix": "2496395e9399728db9658d29b2dc65fa223c987b163f4ba37f9a3c68eb6e6586", "size_in_bytes": 1742}, {"_path": "site-packages/setuptools/_distutils/tests/test_build_clib.py", "path_type": "hardlink", "sha256": "328d5915be02d555c160e1af9da965c0ded80a74edaf6e1a90b0cef198b80ac6", "sha256_in_prefix": "328d5915be02d555c160e1af9da965c0ded80a74edaf6e1a90b0cef198b80ac6", "size_in_bytes": 4331}, {"_path": "site-packages/setuptools/_distutils/tests/test_build_ext.py", "path_type": "hardlink", "sha256": "4053bda98561596749bb5ec75dce31f513272d99619349401e2f47569a5bb97e", "sha256_in_prefix": "4053bda98561596749bb5ec75dce31f513272d99619349401e2f47569a5bb97e", "size_in_bytes": 22545}, {"_path": "site-packages/setuptools/_distutils/tests/test_build_py.py", "path_type": "hardlink", "sha256": "36c7e646ba2338705734ca9647f9a9e60e0f2d3823843ee264551f7c664521dc", "sha256_in_prefix": "36c7e646ba2338705734ca9647f9a9e60e0f2d3823843ee264551f7c664521dc", "size_in_bytes": 6882}, {"_path": "site-packages/setuptools/_distutils/tests/test_build_scripts.py", "path_type": "hardlink", "sha256": "703f85472fa85f9e6c5d15f9133e7140269e1eb59a8f229ce17bb0bf67dee3cc", "sha256_in_prefix": "703f85472fa85f9e6c5d15f9133e7140269e1eb59a8f229ce17bb0bf67dee3cc", "size_in_bytes": 2880}, {"_path": "site-packages/setuptools/_distutils/tests/test_check.py", "path_type": "hardlink", "sha256": "847495d3ba9fed8a12c46b136dbb1443db6cb19cf945135d6eb635b364b06852", "sha256_in_prefix": "847495d3ba9fed8a12c46b136dbb1443db6cb19cf945135d6eb635b364b06852", "size_in_bytes": 6226}, {"_path": "site-packages/setuptools/_distutils/tests/test_clean.py", "path_type": "hardlink", "sha256": "84f1fa8df22918552bbd66c5d6dc6488d55235a031b76c2ae578d5e3df733b81", "sha256_in_prefix": "84f1fa8df22918552bbd66c5d6dc6488d55235a031b76c2ae578d5e3df733b81", "size_in_bytes": 1240}, {"_path": "site-packages/setuptools/_distutils/tests/test_cmd.py", "path_type": "hardlink", "sha256": "6e0441efd9a2b6838a4753a2c991e70a882f1b1b77a56931793a880b4e254164", "sha256_in_prefix": "6e0441efd9a2b6838a4753a2c991e70a882f1b1b77a56931793a880b4e254164", "size_in_bytes": 3254}, {"_path": "site-packages/setuptools/_distutils/tests/test_config_cmd.py", "path_type": "hardlink", "sha256": "66ce965f421fc43be6b82d7d5f3b953676029d3afd63e865ef74c09834813786", "sha256_in_prefix": "66ce965f421fc43be6b82d7d5f3b953676029d3afd63e865ef74c09834813786", "size_in_bytes": 2664}, {"_path": "site-packages/setuptools/_distutils/tests/test_core.py", "path_type": "hardlink", "sha256": "2fb5ca540c5af8c1a8019780368a67b8af5f44a9de621912429830f1742f705f", "sha256_in_prefix": "2fb5ca540c5af8c1a8019780368a67b8af5f44a9de621912429830f1742f705f", "size_in_bytes": 3829}, {"_path": "site-packages/setuptools/_distutils/tests/test_dir_util.py", "path_type": "hardlink", "sha256": "13ce250be938ae2554c1447259a43426ac76ba2dbe8a8fb446e25adcceea909b", "sha256_in_prefix": "13ce250be938ae2554c1447259a43426ac76ba2dbe8a8fb446e25adcceea909b", "size_in_bytes": 4500}, {"_path": "site-packages/setuptools/_distutils/tests/test_dist.py", "path_type": "hardlink", "sha256": "6bac257397d025de6a43a1ce9ddcdcba93618d3c6f8fafbf24bb69b98bda3f53", "sha256_in_prefix": "6bac257397d025de6a43a1ce9ddcdcba93618d3c6f8fafbf24bb69b98bda3f53", "size_in_bytes": 18793}, {"_path": "site-packages/setuptools/_distutils/tests/test_extension.py", "path_type": "hardlink", "sha256": "f987a32e0642bb2705ace05deb8a551f426fc0c73d3708731ef431bef8d71ea9", "sha256_in_prefix": "f987a32e0642bb2705ace05deb8a551f426fc0c73d3708731ef431bef8d71ea9", "size_in_bytes": 3670}, {"_path": "site-packages/setuptools/_distutils/tests/test_file_util.py", "path_type": "hardlink", "sha256": "962be39e5dc592295096b076ac574542af67be3115647ca73726b46dfceffdbe", "sha256_in_prefix": "962be39e5dc592295096b076ac574542af67be3115647ca73726b46dfceffdbe", "size_in_bytes": 3522}, {"_path": "site-packages/setuptools/_distutils/tests/test_filelist.py", "path_type": "hardlink", "sha256": "ac9c24a8251f9060e05a50f6d93a33b13f3271bba930707c0d7a93873c13d53e", "sha256_in_prefix": "ac9c24a8251f9060e05a50f6d93a33b13f3271bba930707c0d7a93873c13d53e", "size_in_bytes": 10766}, {"_path": "site-packages/setuptools/_distutils/tests/test_install.py", "path_type": "hardlink", "sha256": "4df081d32921231c9d202d90e12b93019cd21efb5e30782b04bf708684a02bd4", "sha256_in_prefix": "4df081d32921231c9d202d90e12b93019cd21efb5e30782b04bf708684a02bd4", "size_in_bytes": 8618}, {"_path": "site-packages/setuptools/_distutils/tests/test_install_data.py", "path_type": "hardlink", "sha256": "bcaab72bdee4d210409ce837f279b011d7fb7040d5afdad357209e2689606f80", "sha256_in_prefix": "bcaab72bdee4d210409ce837f279b011d7fb7040d5afdad357209e2689606f80", "size_in_bytes": 2464}, {"_path": "site-packages/setuptools/_distutils/tests/test_install_headers.py", "path_type": "hardlink", "sha256": "3d5018a68fed625f7cd107fae033ce9a64afc9e7c81dd534e9fed5b09799ca41", "sha256_in_prefix": "3d5018a68fed625f7cd107fae033ce9a64afc9e7c81dd534e9fed5b09799ca41", "size_in_bytes": 936}, {"_path": "site-packages/setuptools/_distutils/tests/test_install_lib.py", "path_type": "hardlink", "sha256": "aab8ba465fa668d4d0acd0d5f036de5cd974863b1f4482a2238adf64bae65812", "sha256_in_prefix": "aab8ba465fa668d4d0acd0d5f036de5cd974863b1f4482a2238adf64bae65812", "size_in_bytes": 3612}, {"_path": "site-packages/setuptools/_distutils/tests/test_install_scripts.py", "path_type": "hardlink", "sha256": "284defd1c0e4156fbdd083880fe3a665918cda6872f99904bae5bb5174b6487c", "sha256_in_prefix": "284defd1c0e4156fbdd083880fe3a665918cda6872f99904bae5bb5174b6487c", "size_in_bytes": 1600}, {"_path": "site-packages/setuptools/_distutils/tests/test_log.py", "path_type": "hardlink", "sha256": "8ac16d3ae7e5a02c84759690395edc554af8e86c2d755323e37986041e571fb9", "sha256_in_prefix": "8ac16d3ae7e5a02c84759690395edc554af8e86c2d755323e37986041e571fb9", "size_in_bytes": 323}, {"_path": "site-packages/setuptools/_distutils/tests/test_modified.py", "path_type": "hardlink", "sha256": "875fbe6ce5a6b49a356e9555eae4617674bd6ebef508188d0ccd4c0f0486a6e8", "sha256_in_prefix": "875fbe6ce5a6b49a356e9555eae4617674bd6ebef508188d0ccd4c0f0486a6e8", "size_in_bytes": 4221}, {"_path": "site-packages/setuptools/_distutils/tests/test_sdist.py", "path_type": "hardlink", "sha256": "71fcd4865080e35f3ed6f1fdb5adc806cdba73f8d405b909a0538ae469c0c8d9", "sha256_in_prefix": "71fcd4865080e35f3ed6f1fdb5adc806cdba73f8d405b909a0538ae469c0c8d9", "size_in_bytes": 15062}, {"_path": "site-packages/setuptools/_distutils/tests/test_spawn.py", "path_type": "hardlink", "sha256": "792f30f43edb4f1c852d2c916a12567ae87c29cd45f11596898fdd486e41e417", "sha256_in_prefix": "792f30f43edb4f1c852d2c916a12567ae87c29cd45f11596898fdd486e41e417", "size_in_bytes": 4803}, {"_path": "site-packages/setuptools/_distutils/tests/test_sysconfig.py", "path_type": "hardlink", "sha256": "97133c2ec522d53a268c35781e860af2ee6752806478d2fad14abc3d8d437305", "sha256_in_prefix": "97133c2ec522d53a268c35781e860af2ee6752806478d2fad14abc3d8d437305", "size_in_bytes": 11986}, {"_path": "site-packages/setuptools/_distutils/tests/test_text_file.py", "path_type": "hardlink", "sha256": "59059207901f7410d968c03c045822a493e7b096ffd9228c7cbf747d291156dc", "sha256_in_prefix": "59059207901f7410d968c03c045822a493e7b096ffd9228c7cbf747d291156dc", "size_in_bytes": 3460}, {"_path": "site-packages/setuptools/_distutils/tests/test_util.py", "path_type": "hardlink", "sha256": "1fdce5678cf8561e137e33580c1b313fbc20b902e9c427c963239c9b5c995377", "sha256_in_prefix": "1fdce5678cf8561e137e33580c1b313fbc20b902e9c427c963239c9b5c995377", "size_in_bytes": 7988}, {"_path": "site-packages/setuptools/_distutils/tests/test_version.py", "path_type": "hardlink", "sha256": "6a17e0fe63fcc11cb5b20c18fbf3f1e61ae381febfba94c8a670a0a51e325919", "sha256_in_prefix": "6a17e0fe63fcc11cb5b20c18fbf3f1e61ae381febfba94c8a670a0a51e325919", "size_in_bytes": 2750}, {"_path": "site-packages/setuptools/_distutils/tests/test_versionpredicate.py", "path_type": "hardlink", "sha256": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "sha256_in_prefix": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "size_in_bytes": 0}, {"_path": "site-packages/setuptools/_distutils/tests/unix_compat.py", "path_type": "hardlink", "sha256": "cfea29e82da255d5f56aae4120147b72a3b18a3284f7b6a537026a1cd74ba682", "sha256_in_prefix": "cfea29e82da255d5f56aae4120147b72a3b18a3284f7b6a537026a1cd74ba682", "size_in_bytes": 386}, {"_path": "site-packages/setuptools/_distutils/text_file.py", "path_type": "hardlink", "sha256": "cf876438906bf41a362c6d1336a9bcb03eb72c411a29248fd09d1b581ac51b77", "sha256_in_prefix": "cf876438906bf41a362c6d1336a9bcb03eb72c411a29248fd09d1b581ac51b77", "size_in_bytes": 12101}, {"_path": "site-packages/setuptools/_distutils/unixccompiler.py", "path_type": "hardlink", "sha256": "d5b5c9587e1f8aefc0d967eb887cdff3cc639654135e79e352465d44ab3d7165", "sha256_in_prefix": "d5b5c9587e1f8aefc0d967eb887cdff3cc639654135e79e352465d44ab3d7165", "size_in_bytes": 212}, {"_path": "site-packages/setuptools/_distutils/util.py", "path_type": "hardlink", "sha256": "3637e7aa4eb4ccc7648808d19c6713597dede3dfa86c76a93a56cdbf2225d362", "sha256_in_prefix": "3637e7aa4eb4ccc7648808d19c6713597dede3dfa86c76a93a56cdbf2225d362", "size_in_bytes": 18094}, {"_path": "site-packages/setuptools/_distutils/version.py", "path_type": "hardlink", "sha256": "bc8993e7e1025e4436d6828bd17605893a8ae8dc8cd3d729cc136803fdf80905", "sha256_in_prefix": "bc8993e7e1025e4436d6828bd17605893a8ae8dc8cd3d729cc136803fdf80905", "size_in_bytes": 12619}, {"_path": "site-packages/setuptools/_distutils/versionpredicate.py", "path_type": "hardlink", "sha256": "a81590eb04e3d76383cada13988c9d79f218da36f8b98d6c75b81bb8b9fe2093", "sha256_in_prefix": "a81590eb04e3d76383cada13988c9d79f218da36f8b98d6c75b81bb8b9fe2093", "size_in_bytes": 5205}, {"_path": "site-packages/setuptools/_distutils/zosccompiler.py", "path_type": "hardlink", "sha256": "b2f7625d9da475cc0aac929f8548626f4df2f20cfb68664aba45c7dc8ed89017", "sha256_in_prefix": "b2f7625d9da475cc0aac929f8548626f4df2f20cfb68664aba45c7dc8ed89017", "size_in_bytes": 58}, {"_path": "site-packages/setuptools/_entry_points.py", "path_type": "hardlink", "sha256": "d744e36f37c676a5861f4ea542e3cf1830dc8be3a75e8233adfa4859b6b9019c", "sha256_in_prefix": "d744e36f37c676a5861f4ea542e3cf1830dc8be3a75e8233adfa4859b6b9019c", "size_in_bytes": 2468}, {"_path": "site-packages/setuptools/_imp.py", "path_type": "hardlink", "sha256": "618d448d910dfb4cd8722d5cc4ed7407f69d0043abee2f1e2bc26809cf487ab7", "sha256_in_prefix": "618d448d910dfb4cd8722d5cc4ed7407f69d0043abee2f1e2bc26809cf487ab7", "size_in_bytes": 2435}, {"_path": "site-packages/setuptools/_importlib.py", "path_type": "hardlink", "sha256": "68a22370ad07297373d83f974ebc5a8b11a7fe3b9390e3709aeddd72178c385d", "sha256_in_prefix": "68a22370ad07297373d83f974ebc5a8b11a7fe3b9390e3709aeddd72178c385d", "size_in_bytes": 223}, {"_path": "site-packages/setuptools/_itertools.py", "path_type": "hardlink", "sha256": "8d645fb08ae90bb9b2a28cf78435118fd1adbe9b3065e2978361da926121363a", "sha256_in_prefix": "8d645fb08ae90bb9b2a28cf78435118fd1adbe9b3065e2978361da926121363a", "size_in_bytes": 657}, {"_path": "site-packages/setuptools/_normalization.py", "path_type": "hardlink", "sha256": "f92c5d862b16dd6d492b3a8a6317775de1f2463223dc9f4455192ddda2fc9ca6", "sha256_in_prefix": "f92c5d862b16dd6d492b3a8a6317775de1f2463223dc9f4455192ddda2fc9ca6", "size_in_bytes": 5747}, {"_path": "site-packages/setuptools/_path.py", "path_type": "hardlink", "sha256": "d81bf5abdfc7adde288b32b0707de9bffd3961547a99ea2f404e99b720f8e722", "sha256_in_prefix": "d81bf5abdfc7adde288b32b0707de9bffd3961547a99ea2f404e99b720f8e722", "size_in_bytes": 2976}, {"_path": "site-packages/setuptools/_reqs.py", "path_type": "hardlink", "sha256": "4515fea98b33fdfcba2bae97721087708b332b76d202d53a68ed6cdd968b575e", "sha256_in_prefix": "4515fea98b33fdfcba2bae97721087708b332b76d202d53a68ed6cdd968b575e", "size_in_bytes": 1380}, {"_path": "site-packages/setuptools/_scripts.py", "path_type": "hardlink", "sha256": "e53ac858354eb8eddc45c7f371901404a3d11fb2b8b2f4117502d928a883d5b4", "sha256_in_prefix": "e53ac858354eb8eddc45c7f371901404a3d11fb2b8b2f4117502d928a883d5b4", "size_in_bytes": 11247}, {"_path": "site-packages/setuptools/_shutil.py", "path_type": "hardlink", "sha256": "21043581c3d7e17ff03e29581891b10a1c8c0aa1b8dd539e8e84194c8ac24d8f", "sha256_in_prefix": "21043581c3d7e17ff03e29581891b10a1c8c0aa1b8dd539e8e84194c8ac24d8f", "size_in_bytes": 1578}, {"_path": "site-packages/setuptools/_static.py", "path_type": "hardlink", "sha256": "19347bf60112175fc968ae2dacb9290eb12e09e12d3e5c105b4311bfb54d417e", "sha256_in_prefix": "19347bf60112175fc968ae2dacb9290eb12e09e12d3e5c105b4311bfb54d417e", "size_in_bytes": 4855}, {"_path": "site-packages/setuptools/_vendor/autocommand-2.2.2.dist-info/INSTALLER", "path_type": "hardlink", "sha256": "d0edee15f91b406f3f99726e44eb990be6e34fd0345b52b910c568e0eef6a2a8", "sha256_in_prefix": "d0edee15f91b406f3f99726e44eb990be6e34fd0345b52b910c568e0eef6a2a8", "size_in_bytes": 5}, {"_path": "site-packages/setuptools/_vendor/autocommand-2.2.2.dist-info/LICENSE", "path_type": "hardlink", "sha256": "ade78d04982d69972d444a8e14a94f87a2334dd3855cc80348ea8e240aa0df2d", "sha256_in_prefix": "ade78d04982d69972d444a8e14a94f87a2334dd3855cc80348ea8e240aa0df2d", "size_in_bytes": 7634}, {"_path": "site-packages/setuptools/_vendor/autocommand-2.2.2.dist-info/METADATA", "path_type": "hardlink", "sha256": "3800d9b91dceea2065a6ed6279383362e97ac38b8e56b9343f404ee531860099", "sha256_in_prefix": "3800d9b91dceea2065a6ed6279383362e97ac38b8e56b9343f404ee531860099", "size_in_bytes": 15006}, {"_path": "site-packages/setuptools/_vendor/autocommand-2.2.2.dist-info/RECORD", "path_type": "hardlink", "sha256": "822bba66b41526fa547186b80221f85da50d652bee5493dbfe5d14085112f0c3", "sha256_in_prefix": "822bba66b41526fa547186b80221f85da50d652bee5493dbfe5d14085112f0c3", "size_in_bytes": 1308}, {"_path": "site-packages/setuptools/_vendor/autocommand-2.2.2.dist-info/WHEEL", "path_type": "hardlink", "sha256": "db07a93359e4e034b8785a58ad6d534ea3dca0635f1e184efe2e66e1c3a299ba", "sha256_in_prefix": "db07a93359e4e034b8785a58ad6d534ea3dca0635f1e184efe2e66e1c3a299ba", "size_in_bytes": 92}, {"_path": "site-packages/setuptools/_vendor/autocommand-2.2.2.dist-info/top_level.txt", "path_type": "hardlink", "sha256": "0337e180a292f04740c16513485f2681e5506d7398f64a241c1ea44aac30aaed", "sha256_in_prefix": "0337e180a292f04740c16513485f2681e5506d7398f64a241c1ea44aac30aaed", "size_in_bytes": 12}, {"_path": "site-packages/setuptools/_vendor/autocommand/__init__.py", "path_type": "hardlink", "sha256": "ce4a39467be896f6fe5178c2c7fd80acf4c6056c142b9418e0b21020a611ec0b", "sha256_in_prefix": "ce4a39467be896f6fe5178c2c7fd80acf4c6056c142b9418e0b21020a611ec0b", "size_in_bytes": 1037}, {"_path": "site-packages/setuptools/_vendor/autocommand/autoasync.py", "path_type": "hardlink", "sha256": "00c772af1352e29a9625f3ffc6ea0e70898e1d60fea93ef1d3ac2628dd55a7e5", "sha256_in_prefix": "00c772af1352e29a9625f3ffc6ea0e70898e1d60fea93ef1d3ac2628dd55a7e5", "size_in_bytes": 5680}, {"_path": "site-packages/setuptools/_vendor/autocommand/autocommand.py", "path_type": "hardlink", "sha256": "866904990ef61ed2f9e609d44558c33a7b1f62519de652d76ef4f8286e3de90c", "sha256_in_prefix": "866904990ef61ed2f9e609d44558c33a7b1f62519de652d76ef4f8286e3de90c", "size_in_bytes": 2505}, {"_path": "site-packages/setuptools/_vendor/autocommand/automain.py", "path_type": "hardlink", "sha256": "0366fc8bbe7833173f0e353d585afabea6035a5873d1c9fc9a2bbc77c12cc55f", "sha256_in_prefix": "0366fc8bbe7833173f0e353d585afabea6035a5873d1c9fc9a2bbc77c12cc55f", "size_in_bytes": 2076}, {"_path": "site-packages/setuptools/_vendor/autocommand/autoparse.py", "path_type": "hardlink", "sha256": "5955a66493dc6f350a5cfe34ada430ff41c3f2a3c9d95f551b57851669a7171c", "sha256_in_prefix": "5955a66493dc6f350a5cfe34ada430ff41c3f2a3c9d95f551b57851669a7171c", "size_in_bytes": 11642}, {"_path": "site-packages/setuptools/_vendor/autocommand/errors.py", "path_type": "hardlink", "sha256": "eda6b7ae887d1deaddea720aa501cd584b25584f28abb1a21d8554b91a8e4670", "sha256_in_prefix": "eda6b7ae887d1deaddea720aa501cd584b25584f28abb1a21d8554b91a8e4670", "size_in_bytes": 886}, {"_path": "site-packages/setuptools/_vendor/backports.tarfile-1.2.0.dist-info/INSTALLER", "path_type": "hardlink", "sha256": "d0edee15f91b406f3f99726e44eb990be6e34fd0345b52b910c568e0eef6a2a8", "sha256_in_prefix": "d0edee15f91b406f3f99726e44eb990be6e34fd0345b52b910c568e0eef6a2a8", "size_in_bytes": 5}, {"_path": "site-packages/setuptools/_vendor/backports.tarfile-1.2.0.dist-info/LICENSE", "path_type": "hardlink", "sha256": "86da0f01aeae46348a3c3d465195dc1ceccde79f79e87769a64b8da04b2a4741", "sha256_in_prefix": "86da0f01aeae46348a3c3d465195dc1ceccde79f79e87769a64b8da04b2a4741", "size_in_bytes": 1023}, {"_path": "site-packages/setuptools/_vendor/backports.tarfile-1.2.0.dist-info/METADATA", "path_type": "hardlink", "sha256": "8215c54ead77d9dc5a108a25c6bdc72b5999aa6f62c9499a440359412afa5a51", "sha256_in_prefix": "8215c54ead77d9dc5a108a25c6bdc72b5999aa6f62c9499a440359412afa5a51", "size_in_bytes": 2020}, {"_path": "site-packages/setuptools/_vendor/backports.tarfile-1.2.0.dist-info/RECORD", "path_type": "hardlink", "sha256": "258a1f1c849e1175069a55a5d6ce357afdd04e34cd5de27093e4acec7a9d2ce1", "sha256_in_prefix": "258a1f1c849e1175069a55a5d6ce357afdd04e34cd5de27093e4acec7a9d2ce1", "size_in_bytes": 1360}, {"_path": "site-packages/setuptools/_vendor/backports.tarfile-1.2.0.dist-info/REQUESTED", "path_type": "hardlink", "sha256": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "sha256_in_prefix": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "size_in_bytes": 0}, {"_path": "site-packages/setuptools/_vendor/backports.tarfile-1.2.0.dist-info/WHEEL", "path_type": "hardlink", "sha256": "189eedfe4581172c1b6a02b97a8f48a14c0b5baa3239e4ca990fbd8871553714", "sha256_in_prefix": "189eedfe4581172c1b6a02b97a8f48a14c0b5baa3239e4ca990fbd8871553714", "size_in_bytes": 92}, {"_path": "site-packages/setuptools/_vendor/backports.tarfile-1.2.0.dist-info/top_level.txt", "path_type": "hardlink", "sha256": "7068da2cc3a8051d452b4029a23b73595995893b49ec91882bf1f05e212cbed5", "sha256_in_prefix": "7068da2cc3a8051d452b4029a23b73595995893b49ec91882bf1f05e212cbed5", "size_in_bytes": 10}, {"_path": "site-packages/setuptools/_vendor/backports/__init__.py", "path_type": "hardlink", "sha256": "88e10cc2794e4567b374ef3edafc4120f491dfb0fb2468e5b99f1fe79bf3c65b", "sha256_in_prefix": "88e10cc2794e4567b374ef3edafc4120f491dfb0fb2468e5b99f1fe79bf3c65b", "size_in_bytes": 81}, {"_path": "site-packages/setuptools/_vendor/backports/tarfile/__init__.py", "path_type": "hardlink", "sha256": "3f07f6a9421f0744a89493c229cc77bf3dd412efda89db38838b007f1cbde2a8", "sha256_in_prefix": "3f07f6a9421f0744a89493c229cc77bf3dd412efda89db38838b007f1cbde2a8", "size_in_bytes": 108491}, {"_path": "site-packages/setuptools/_vendor/backports/tarfile/__main__.py", "path_type": "hardlink", "sha256": "630da8193d5a7ebcf6781b24cdd3d82fc45e07fde5880a6684590dd846c399ce", "sha256_in_prefix": "630da8193d5a7ebcf6781b24cdd3d82fc45e07fde5880a6684590dd846c399ce", "size_in_bytes": 59}, {"_path": "site-packages/setuptools/_vendor/backports/tarfile/compat/__init__.py", "path_type": "hardlink", "sha256": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "sha256_in_prefix": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "size_in_bytes": 0}, {"_path": "site-packages/setuptools/_vendor/backports/tarfile/compat/py38.py", "path_type": "hardlink", "sha256": "898932b7f82f5a32f31944c90fd4ee4df30c8ce93e7abb17666465bd060ddaa1", "sha256_in_prefix": "898932b7f82f5a32f31944c90fd4ee4df30c8ce93e7abb17666465bd060ddaa1", "size_in_bytes": 568}, {"_path": "site-packages/setuptools/_vendor/importlib_metadata-8.0.0.dist-info/INSTALLER", "path_type": "hardlink", "sha256": "d0edee15f91b406f3f99726e44eb990be6e34fd0345b52b910c568e0eef6a2a8", "sha256_in_prefix": "d0edee15f91b406f3f99726e44eb990be6e34fd0345b52b910c568e0eef6a2a8", "size_in_bytes": 5}, {"_path": "site-packages/setuptools/_vendor/importlib_metadata-8.0.0.dist-info/LICENSE", "path_type": "hardlink", "sha256": "cfc7749b96f63bd31c3c42b5c471bf756814053e847c10f3eb003417bc523d30", "sha256_in_prefix": "cfc7749b96f63bd31c3c42b5c471bf756814053e847c10f3eb003417bc523d30", "size_in_bytes": 11358}, {"_path": "site-packages/setuptools/_vendor/importlib_metadata-8.0.0.dist-info/METADATA", "path_type": "hardlink", "sha256": "6a7b90effee1e09d5b484cdf7232016a43e2d9cc9543bcbb8e494b1ec05e1f59", "sha256_in_prefix": "6a7b90effee1e09d5b484cdf7232016a43e2d9cc9543bcbb8e494b1ec05e1f59", "size_in_bytes": 4648}, {"_path": "site-packages/setuptools/_vendor/importlib_metadata-8.0.0.dist-info/RECORD", "path_type": "hardlink", "sha256": "0d8d3c6eeb9ebbe86cac7d60861552433c329da9ea51248b61d02be2e5e64030", "sha256_in_prefix": "0d8d3c6eeb9ebbe86cac7d60861552433c329da9ea51248b61d02be2e5e64030", "size_in_bytes": 2518}, {"_path": "site-packages/setuptools/_vendor/importlib_metadata-8.0.0.dist-info/REQUESTED", "path_type": "hardlink", "sha256": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "sha256_in_prefix": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "size_in_bytes": 0}, {"_path": "site-packages/setuptools/_vendor/importlib_metadata-8.0.0.dist-info/WHEEL", "path_type": "hardlink", "sha256": "9a0b8c95618c5fe5479cca4a3a38d089d228d6cb1194216ee1ae26069cf5b363", "sha256_in_prefix": "9a0b8c95618c5fe5479cca4a3a38d089d228d6cb1194216ee1ae26069cf5b363", "size_in_bytes": 91}, {"_path": "site-packages/setuptools/_vendor/importlib_metadata-8.0.0.dist-info/top_level.txt", "path_type": "hardlink", "sha256": "08eddf0fdcb29403625e4acca38a872d5fe6a972f6b02e4914a82dd725804fe0", "sha256_in_prefix": "08eddf0fdcb29403625e4acca38a872d5fe6a972f6b02e4914a82dd725804fe0", "size_in_bytes": 19}, {"_path": "site-packages/setuptools/_vendor/importlib_metadata/__init__.py", "path_type": "hardlink", "sha256": "b59341fb6de1f018b18bdb82ad0aa3f587f469e0bef89a2c772dc8651210781d", "sha256_in_prefix": "b59341fb6de1f018b18bdb82ad0aa3f587f469e0bef89a2c772dc8651210781d", "size_in_bytes": 33798}, {"_path": "site-packages/setuptools/_vendor/importlib_metadata/_adapters.py", "path_type": "hardlink", "sha256": "ac88564f006f600d5b57b8bee457d55f7f2a1170d35c5792e5c6f9c49b4fde4b", "sha256_in_prefix": "ac88564f006f600d5b57b8bee457d55f7f2a1170d35c5792e5c6f9c49b4fde4b", "size_in_bytes": 2317}, {"_path": "site-packages/setuptools/_vendor/importlib_metadata/_collections.py", "path_type": "hardlink", "sha256": "089d0e4c21c88d6034648552e2fa0e440b27d91e11d9c40112d3ec6442690126", "sha256_in_prefix": "089d0e4c21c88d6034648552e2fa0e440b27d91e11d9c40112d3ec6442690126", "size_in_bytes": 743}, {"_path": "site-packages/setuptools/_vendor/importlib_metadata/_compat.py", "path_type": "hardlink", "sha256": "ef740aacdf4a368699ce16d7e723c20996be15a00bc177bc4cf94347bd898015", "sha256_in_prefix": "ef740aacdf4a368699ce16d7e723c20996be15a00bc177bc4cf94347bd898015", "size_in_bytes": 1314}, {"_path": "site-packages/setuptools/_vendor/importlib_metadata/_functools.py", "path_type": "hardlink", "sha256": "3ec636fb8aeb297e1155e442d681a9d65075a660bd78a37cf3f7fe6c3f6e3a80", "sha256_in_prefix": "3ec636fb8aeb297e1155e442d681a9d65075a660bd78a37cf3f7fe6c3f6e3a80", "size_in_bytes": 2895}, {"_path": "site-packages/setuptools/_vendor/importlib_metadata/_itertools.py", "path_type": "hardlink", "sha256": "72faffdaff0145bc5c225e71e6575fa9d1e3848f188bcb3cca4e741bf9e6ea34", "sha256_in_prefix": "72faffdaff0145bc5c225e71e6575fa9d1e3848f188bcb3cca4e741bf9e6ea34", "size_in_bytes": 2068}, {"_path": "site-packages/setuptools/_vendor/importlib_metadata/_meta.py", "path_type": "hardlink", "sha256": "9f167b0bc19595c04500a5b254e9ff767ee8b7fb7005c6e6d4d9af8c87ad0472", "sha256_in_prefix": "9f167b0bc19595c04500a5b254e9ff767ee8b7fb7005c6e6d4d9af8c87ad0472", "size_in_bytes": 1801}, {"_path": "site-packages/setuptools/_vendor/importlib_metadata/_text.py", "path_type": "hardlink", "sha256": "1c2b0592c66924b7933f734493f9e0ac079755146d4ebb7287d78e001a113f80", "sha256_in_prefix": "1c2b0592c66924b7933f734493f9e0ac079755146d4ebb7287d78e001a113f80", "size_in_bytes": 2166}, {"_path": "site-packages/setuptools/_vendor/importlib_metadata/compat/__init__.py", "path_type": "hardlink", "sha256": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "sha256_in_prefix": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "size_in_bytes": 0}, {"_path": "site-packages/setuptools/_vendor/importlib_metadata/compat/py311.py", "path_type": "hardlink", "sha256": "baa9be2beba88728f5d38d931f86bd12bfc8e68efaebb0efba5703fa00bf7d20", "sha256_in_prefix": "baa9be2beba88728f5d38d931f86bd12bfc8e68efaebb0efba5703fa00bf7d20", "size_in_bytes": 608}, {"_path": "site-packages/setuptools/_vendor/importlib_metadata/compat/py39.py", "path_type": "hardlink", "sha256": "70f90cbfafb48a52bed09c3f4e49f4c586ce28965ce1624a407a30d1cd93e38c", "sha256_in_prefix": "70f90cbfafb48a52bed09c3f4e49f4c586ce28965ce1624a407a30d1cd93e38c", "size_in_bytes": 1102}, {"_path": "site-packages/setuptools/_vendor/importlib_metadata/diagnose.py", "path_type": "hardlink", "sha256": "9e4491322a309669212d884a86f0a0f60966b7fd750a8c7e1262f311ba984daf", "sha256_in_prefix": "9e4491322a309669212d884a86f0a0f60966b7fd750a8c7e1262f311ba984daf", "size_in_bytes": 379}, {"_path": "site-packages/setuptools/_vendor/importlib_metadata/py.typed", "path_type": "hardlink", "sha256": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "sha256_in_prefix": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "size_in_bytes": 0}, {"_path": "site-packages/setuptools/_vendor/inflect-7.3.1.dist-info/INSTALLER", "path_type": "hardlink", "sha256": "d0edee15f91b406f3f99726e44eb990be6e34fd0345b52b910c568e0eef6a2a8", "sha256_in_prefix": "d0edee15f91b406f3f99726e44eb990be6e34fd0345b52b910c568e0eef6a2a8", "size_in_bytes": 5}, {"_path": "site-packages/setuptools/_vendor/inflect-7.3.1.dist-info/LICENSE", "path_type": "hardlink", "sha256": "86da0f01aeae46348a3c3d465195dc1ceccde79f79e87769a64b8da04b2a4741", "sha256_in_prefix": "86da0f01aeae46348a3c3d465195dc1ceccde79f79e87769a64b8da04b2a4741", "size_in_bytes": 1023}, {"_path": "site-packages/setuptools/_vendor/inflect-7.3.1.dist-info/METADATA", "path_type": "hardlink", "sha256": "66030d634580651b3e53cc19895d9231f8d22aa06b327817c8332cfc20303308", "sha256_in_prefix": "66030d634580651b3e53cc19895d9231f8d22aa06b327817c8332cfc20303308", "size_in_bytes": 21079}, {"_path": "site-packages/setuptools/_vendor/inflect-7.3.1.dist-info/RECORD", "path_type": "hardlink", "sha256": "5d7834ac1ba2612c6801050fde57a7b98b0f36acf88c3c2d4f376fd8911b3091", "sha256_in_prefix": "5d7834ac1ba2612c6801050fde57a7b98b0f36acf88c3c2d4f376fd8911b3091", "size_in_bytes": 943}, {"_path": "site-packages/setuptools/_vendor/inflect-7.3.1.dist-info/WHEEL", "path_type": "hardlink", "sha256": "cb8997f92397e1f6089289ec0060393743b2fbcfe0238157c391cd235c6abd68", "sha256_in_prefix": "cb8997f92397e1f6089289ec0060393743b2fbcfe0238157c391cd235c6abd68", "size_in_bytes": 91}, {"_path": "site-packages/setuptools/_vendor/inflect-7.3.1.dist-info/top_level.txt", "path_type": "hardlink", "sha256": "9b9dae8dda75d02a93ea38755d0c594fa9049ed727bfeed397b52218d4f35990", "sha256_in_prefix": "9b9dae8dda75d02a93ea38755d0c594fa9049ed727bfeed397b52218d4f35990", "size_in_bytes": 8}, {"_path": "site-packages/setuptools/_vendor/inflect/__init__.py", "path_type": "hardlink", "sha256": "271cb51c95d9899f3990778b021926bf3e84313745a817be76ebeddf847a20e7", "sha256_in_prefix": "271cb51c95d9899f3990778b021926bf3e84313745a817be76ebeddf847a20e7", "size_in_bytes": 103796}, {"_path": "site-packages/setuptools/_vendor/inflect/compat/__init__.py", "path_type": "hardlink", "sha256": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "sha256_in_prefix": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "size_in_bytes": 0}, {"_path": "site-packages/setuptools/_vendor/inflect/compat/py38.py", "path_type": "hardlink", "sha256": "a0e6d57d59d65fdfcea673ae1099f59856c9c55870c91e5ea5b8933570c36aca", "sha256_in_prefix": "a0e6d57d59d65fdfcea673ae1099f59856c9c55870c91e5ea5b8933570c36aca", "size_in_bytes": 160}, {"_path": "site-packages/setuptools/_vendor/inflect/py.typed", "path_type": "hardlink", "sha256": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "sha256_in_prefix": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "size_in_bytes": 0}, {"_path": "site-packages/setuptools/_vendor/jaraco.collections-5.1.0.dist-info/INSTALLER", "path_type": "hardlink", "sha256": "d0edee15f91b406f3f99726e44eb990be6e34fd0345b52b910c568e0eef6a2a8", "sha256_in_prefix": "d0edee15f91b406f3f99726e44eb990be6e34fd0345b52b910c568e0eef6a2a8", "size_in_bytes": 5}, {"_path": "site-packages/setuptools/_vendor/jaraco.collections-5.1.0.dist-info/LICENSE", "path_type": "hardlink", "sha256": "86da0f01aeae46348a3c3d465195dc1ceccde79f79e87769a64b8da04b2a4741", "sha256_in_prefix": "86da0f01aeae46348a3c3d465195dc1ceccde79f79e87769a64b8da04b2a4741", "size_in_bytes": 1023}, {"_path": "site-packages/setuptools/_vendor/jaraco.collections-5.1.0.dist-info/METADATA", "path_type": "hardlink", "sha256": "20c51a96236c0395f53b1f4c5d458e6a0721e51e16c1bff733b7aba76f5d06d8", "sha256_in_prefix": "20c51a96236c0395f53b1f4c5d458e6a0721e51e16c1bff733b7aba76f5d06d8", "size_in_bytes": 3933}, {"_path": "site-packages/setuptools/_vendor/jaraco.collections-5.1.0.dist-info/RECORD", "path_type": "hardlink", "sha256": "1e9b62bd70e4a5fa26e9594cbb80860ffeca3debfee8773daefa774cd259ca06", "sha256_in_prefix": "1e9b62bd70e4a5fa26e9594cbb80860ffeca3debfee8773daefa774cd259ca06", "size_in_bytes": 873}, {"_path": "site-packages/setuptools/_vendor/jaraco.collections-5.1.0.dist-info/REQUESTED", "path_type": "hardlink", "sha256": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "sha256_in_prefix": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "size_in_bytes": 0}, {"_path": "site-packages/setuptools/_vendor/jaraco.collections-5.1.0.dist-info/WHEEL", "path_type": "hardlink", "sha256": "31d8bd3c3370119a6d3a34e551c02d87b5c90c5b4aac761a40c3ee9597810a24", "sha256_in_prefix": "31d8bd3c3370119a6d3a34e551c02d87b5c90c5b4aac761a40c3ee9597810a24", "size_in_bytes": 91}, {"_path": "site-packages/setuptools/_vendor/jaraco.collections-5.1.0.dist-info/top_level.txt", "path_type": "hardlink", "sha256": "d099cddcb7d71f82c845f5cbf9014e18227341664edc42f1e11d5dfe5a2ea103", "sha256_in_prefix": "d099cddcb7d71f82c845f5cbf9014e18227341664edc42f1e11d5dfe5a2ea103", "size_in_bytes": 7}, {"_path": "site-packages/setuptools/_vendor/jaraco.context-5.3.0.dist-info/INSTALLER", "path_type": "hardlink", "sha256": "d0edee15f91b406f3f99726e44eb990be6e34fd0345b52b910c568e0eef6a2a8", "sha256_in_prefix": "d0edee15f91b406f3f99726e44eb990be6e34fd0345b52b910c568e0eef6a2a8", "size_in_bytes": 5}, {"_path": "site-packages/setuptools/_vendor/jaraco.context-5.3.0.dist-info/LICENSE", "path_type": "hardlink", "sha256": "86da0f01aeae46348a3c3d465195dc1ceccde79f79e87769a64b8da04b2a4741", "sha256_in_prefix": "86da0f01aeae46348a3c3d465195dc1ceccde79f79e87769a64b8da04b2a4741", "size_in_bytes": 1023}, {"_path": "site-packages/setuptools/_vendor/jaraco.context-5.3.0.dist-info/METADATA", "path_type": "hardlink", "sha256": "c43b60b897a3d2d37d8845c252fc44261d9aef171e21154111a9012d2afffed6", "sha256_in_prefix": "c43b60b897a3d2d37d8845c252fc44261d9aef171e21154111a9012d2afffed6", "size_in_bytes": 4020}, {"_path": "site-packages/setuptools/_vendor/jaraco.context-5.3.0.dist-info/RECORD", "path_type": "hardlink", "sha256": "55197b88a78443297bb2d827a75baae740b33896251d872835d4b4c75ec2f57e", "sha256_in_prefix": "55197b88a78443297bb2d827a75baae740b33896251d872835d4b4c75ec2f57e", "size_in_bytes": 641}, {"_path": "site-packages/setuptools/_vendor/jaraco.context-5.3.0.dist-info/WHEEL", "path_type": "hardlink", "sha256": "189eedfe4581172c1b6a02b97a8f48a14c0b5baa3239e4ca990fbd8871553714", "sha256_in_prefix": "189eedfe4581172c1b6a02b97a8f48a14c0b5baa3239e4ca990fbd8871553714", "size_in_bytes": 92}, {"_path": "site-packages/setuptools/_vendor/jaraco.context-5.3.0.dist-info/top_level.txt", "path_type": "hardlink", "sha256": "d099cddcb7d71f82c845f5cbf9014e18227341664edc42f1e11d5dfe5a2ea103", "sha256_in_prefix": "d099cddcb7d71f82c845f5cbf9014e18227341664edc42f1e11d5dfe5a2ea103", "size_in_bytes": 7}, {"_path": "site-packages/setuptools/_vendor/jaraco.functools-4.0.1.dist-info/INSTALLER", "path_type": "hardlink", "sha256": "d0edee15f91b406f3f99726e44eb990be6e34fd0345b52b910c568e0eef6a2a8", "sha256_in_prefix": "d0edee15f91b406f3f99726e44eb990be6e34fd0345b52b910c568e0eef6a2a8", "size_in_bytes": 5}, {"_path": "site-packages/setuptools/_vendor/jaraco.functools-4.0.1.dist-info/LICENSE", "path_type": "hardlink", "sha256": "86da0f01aeae46348a3c3d465195dc1ceccde79f79e87769a64b8da04b2a4741", "sha256_in_prefix": "86da0f01aeae46348a3c3d465195dc1ceccde79f79e87769a64b8da04b2a4741", "size_in_bytes": 1023}, {"_path": "site-packages/setuptools/_vendor/jaraco.functools-4.0.1.dist-info/METADATA", "path_type": "hardlink", "sha256": "8b86946900d7fa38dd1102b9c1ebe17a0cb1f09c8b7e29f61f2bda4a4dc51eca", "sha256_in_prefix": "8b86946900d7fa38dd1102b9c1ebe17a0cb1f09c8b7e29f61f2bda4a4dc51eca", "size_in_bytes": 2891}, {"_path": "site-packages/setuptools/_vendor/jaraco.functools-4.0.1.dist-info/RECORD", "path_type": "hardlink", "sha256": "632aa7c04f7c4bcc01c027af5b9bc76fe8958f4a181035b957a3bd3014ba248b", "sha256_in_prefix": "632aa7c04f7c4bcc01c027af5b9bc76fe8958f4a181035b957a3bd3014ba248b", "size_in_bytes": 843}, {"_path": "site-packages/setuptools/_vendor/jaraco.functools-4.0.1.dist-info/WHEEL", "path_type": "hardlink", "sha256": "189eedfe4581172c1b6a02b97a8f48a14c0b5baa3239e4ca990fbd8871553714", "sha256_in_prefix": "189eedfe4581172c1b6a02b97a8f48a14c0b5baa3239e4ca990fbd8871553714", "size_in_bytes": 92}, {"_path": "site-packages/setuptools/_vendor/jaraco.functools-4.0.1.dist-info/top_level.txt", "path_type": "hardlink", "sha256": "d099cddcb7d71f82c845f5cbf9014e18227341664edc42f1e11d5dfe5a2ea103", "sha256_in_prefix": "d099cddcb7d71f82c845f5cbf9014e18227341664edc42f1e11d5dfe5a2ea103", "size_in_bytes": 7}, {"_path": "site-packages/setuptools/_vendor/jaraco.text-3.12.1.dist-info/INSTALLER", "path_type": "hardlink", "sha256": "d0edee15f91b406f3f99726e44eb990be6e34fd0345b52b910c568e0eef6a2a8", "sha256_in_prefix": "d0edee15f91b406f3f99726e44eb990be6e34fd0345b52b910c568e0eef6a2a8", "size_in_bytes": 5}, {"_path": "site-packages/setuptools/_vendor/jaraco.text-3.12.1.dist-info/LICENSE", "path_type": "hardlink", "sha256": "86da0f01aeae46348a3c3d465195dc1ceccde79f79e87769a64b8da04b2a4741", "sha256_in_prefix": "86da0f01aeae46348a3c3d465195dc1ceccde79f79e87769a64b8da04b2a4741", "size_in_bytes": 1023}, {"_path": "site-packages/setuptools/_vendor/jaraco.text-3.12.1.dist-info/METADATA", "path_type": "hardlink", "sha256": "03359d9ba56231f0ce3e840c7cb5a7db380141218949ccaa78ddbd4dcb965d52", "sha256_in_prefix": "03359d9ba56231f0ce3e840c7cb5a7db380141218949ccaa78ddbd4dcb965d52", "size_in_bytes": 3658}, {"_path": "site-packages/setuptools/_vendor/jaraco.text-3.12.1.dist-info/RECORD", "path_type": "hardlink", "sha256": "816d945741dca246099388ca3eed74fc0667acbaa36f70b559b2494c3979b1f6", "sha256_in_prefix": "816d945741dca246099388ca3eed74fc0667acbaa36f70b559b2494c3979b1f6", "size_in_bytes": 1500}, {"_path": "site-packages/setuptools/_vendor/jaraco.text-3.12.1.dist-info/REQUESTED", "path_type": "hardlink", "sha256": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "sha256_in_prefix": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "size_in_bytes": 0}, {"_path": "site-packages/setuptools/_vendor/jaraco.text-3.12.1.dist-info/WHEEL", "path_type": "hardlink", "sha256": "189eedfe4581172c1b6a02b97a8f48a14c0b5baa3239e4ca990fbd8871553714", "sha256_in_prefix": "189eedfe4581172c1b6a02b97a8f48a14c0b5baa3239e4ca990fbd8871553714", "size_in_bytes": 92}, {"_path": "site-packages/setuptools/_vendor/jaraco.text-3.12.1.dist-info/top_level.txt", "path_type": "hardlink", "sha256": "d099cddcb7d71f82c845f5cbf9014e18227341664edc42f1e11d5dfe5a2ea103", "sha256_in_prefix": "d099cddcb7d71f82c845f5cbf9014e18227341664edc42f1e11d5dfe5a2ea103", "size_in_bytes": 7}, {"_path": "site-packages/setuptools/_vendor/jaraco/collections/__init__.py", "path_type": "hardlink", "sha256": "3dcd7e4aa8d69bcd5a7753f4f86b6da64c0efcb5a59da63a814abc81c2a1dafd", "sha256_in_prefix": "3dcd7e4aa8d69bcd5a7753f4f86b6da64c0efcb5a59da63a814abc81c2a1dafd", "size_in_bytes": 26640}, {"_path": "site-packages/setuptools/_vendor/jaraco/collections/py.typed", "path_type": "hardlink", "sha256": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "sha256_in_prefix": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "size_in_bytes": 0}, {"_path": "site-packages/setuptools/_vendor/jaraco/context.py", "path_type": "hardlink", "sha256": "444a0b2310e43b931f118a30b7f5a8ba9342468c414b9bfb617d8f6d6f2521cb", "sha256_in_prefix": "444a0b2310e43b931f118a30b7f5a8ba9342468c414b9bfb617d8f6d6f2521cb", "size_in_bytes": 9552}, {"_path": "site-packages/setuptools/_vendor/jaraco/functools/__init__.py", "path_type": "hardlink", "sha256": "844009692dae49946e17f258e02c421c8621efd669c5a3e9f4e887cabf44275c", "sha256_in_prefix": "844009692dae49946e17f258e02c421c8621efd669c5a3e9f4e887cabf44275c", "size_in_bytes": 16642}, {"_path": "site-packages/setuptools/_vendor/jaraco/functools/__init__.pyi", "path_type": "hardlink", "sha256": "824dddb201f3a3917f53be07cc0be9362bc500f0a43c9d5bdbec8277ad9d7e7c", "sha256_in_prefix": "824dddb201f3a3917f53be07cc0be9362bc500f0a43c9d5bdbec8277ad9d7e7c", "size_in_bytes": 3878}, {"_path": "site-packages/setuptools/_vendor/jaraco/functools/py.typed", "path_type": "hardlink", "sha256": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "sha256_in_prefix": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "size_in_bytes": 0}, {"_path": "site-packages/setuptools/_vendor/jaraco/text/Lorem ipsum.txt", "path_type": "hardlink", "sha256": "37fedcffbf73c4eb9f058f47677cb33203a436ff9390e4d38a8e01c9dad28e0b", "sha256_in_prefix": "37fedcffbf73c4eb9f058f47677cb33203a436ff9390e4d38a8e01c9dad28e0b", "size_in_bytes": 1335}, {"_path": "site-packages/setuptools/_vendor/jaraco/text/__init__.py", "path_type": "hardlink", "sha256": "636614a9747fa2b5280da6384a43d17ba65985be4750707f021f5108db15ca1a", "sha256_in_prefix": "636614a9747fa2b5280da6384a43d17ba65985be4750707f021f5108db15ca1a", "size_in_bytes": 16250}, {"_path": "site-packages/setuptools/_vendor/jaraco/text/layouts.py", "path_type": "hardlink", "sha256": "1d30bc6924cb67bb978a9c8e5daa51302d79f23b9e7232ba455c22b5f999f7fc", "sha256_in_prefix": "1d30bc6924cb67bb978a9c8e5daa51302d79f23b9e7232ba455c22b5f999f7fc", "size_in_bytes": 643}, {"_path": "site-packages/setuptools/_vendor/jaraco/text/show-newlines.py", "path_type": "hardlink", "sha256": "58641aeb97bc97285bf762d438ba959fa29a0ada1e560570b3a5ad49198b93ac", "sha256_in_prefix": "58641aeb97bc97285bf762d438ba959fa29a0ada1e560570b3a5ad49198b93ac", "size_in_bytes": 904}, {"_path": "site-packages/setuptools/_vendor/jaraco/text/strip-prefix.py", "path_type": "hardlink", "sha256": "35f55757c255368ea7a9cb980127cc57bff2e04a3cccc42a942386bc09d1215c", "sha256_in_prefix": "35f55757c255368ea7a9cb980127cc57bff2e04a3cccc42a942386bc09d1215c", "size_in_bytes": 412}, {"_path": "site-packages/setuptools/_vendor/jaraco/text/to-dvorak.py", "path_type": "hardlink", "sha256": "d5235c6d2b2f212a575e0f8b9f26c81c763e45414e42bdfacdc1e4635a5616ac", "sha256_in_prefix": "d5235c6d2b2f212a575e0f8b9f26c81c763e45414e42bdfacdc1e4635a5616ac", "size_in_bytes": 119}, {"_path": "site-packages/setuptools/_vendor/jaraco/text/to-qwerty.py", "path_type": "hardlink", "sha256": "b3850c4149cfc059ff741e6e642dbb06eac7390718a277417f322954be69133c", "sha256_in_prefix": "b3850c4149cfc059ff741e6e642dbb06eac7390718a277417f322954be69133c", "size_in_bytes": 119}, {"_path": "site-packages/setuptools/_vendor/more_itertools-10.3.0.dist-info/INSTALLER", "path_type": "hardlink", "sha256": "d0edee15f91b406f3f99726e44eb990be6e34fd0345b52b910c568e0eef6a2a8", "sha256_in_prefix": "d0edee15f91b406f3f99726e44eb990be6e34fd0345b52b910c568e0eef6a2a8", "size_in_bytes": 5}, {"_path": "site-packages/setuptools/_vendor/more_itertools-10.3.0.dist-info/LICENSE", "path_type": "hardlink", "sha256": "09f1c8c9e941af3e584d59641ea9b87d83c0cb0fd007eb5ef391a7e2643c1a46", "sha256_in_prefix": "09f1c8c9e941af3e584d59641ea9b87d83c0cb0fd007eb5ef391a7e2643c1a46", "size_in_bytes": 1053}, {"_path": "site-packages/setuptools/_vendor/more_itertools-10.3.0.dist-info/METADATA", "path_type": "hardlink", "sha256": "0453bdd0ef9f2cd89540ca63ee8212e73b73809514419dd3037d8fe471f737e0", "sha256_in_prefix": "0453bdd0ef9f2cd89540ca63ee8212e73b73809514419dd3037d8fe471f737e0", "size_in_bytes": 36293}, {"_path": "site-packages/setuptools/_vendor/more_itertools-10.3.0.dist-info/RECORD", "path_type": "hardlink", "sha256": "77c8e73e018dc0fd7e9ed6c80b05a4404545f641fb085220ce42b368b59aa3d3", "sha256_in_prefix": "77c8e73e018dc0fd7e9ed6c80b05a4404545f641fb085220ce42b368b59aa3d3", "size_in_bytes": 1259}, {"_path": "site-packages/setuptools/_vendor/more_itertools-10.3.0.dist-info/REQUESTED", "path_type": "hardlink", "sha256": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "sha256_in_prefix": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "size_in_bytes": 0}, {"_path": "site-packages/setuptools/_vendor/more_itertools-10.3.0.dist-info/WHEEL", "path_type": "hardlink", "sha256": "ad282afc9a4717d7c7475971e77ab083fd7ed8bca9644fea99cb976d552af78f", "sha256_in_prefix": "ad282afc9a4717d7c7475971e77ab083fd7ed8bca9644fea99cb976d552af78f", "size_in_bytes": 81}, {"_path": "site-packages/setuptools/_vendor/more_itertools/__init__.py", "path_type": "hardlink", "sha256": "76d01b1a34c39a7fe08625396177e1c83cb4a610255d576de649590397d46be4", "sha256_in_prefix": "76d01b1a34c39a7fe08625396177e1c83cb4a610255d576de649590397d46be4", "size_in_bytes": 149}, {"_path": "site-packages/setuptools/_vendor/more_itertools/__init__.pyi", "path_type": "hardlink", "sha256": "e41dde4f338dd4106e38ba1bd6f09f97211bda549deaeb17410f82bfe85791e0", "sha256_in_prefix": "e41dde4f338dd4106e38ba1bd6f09f97211bda549deaeb17410f82bfe85791e0", "size_in_bytes": 43}, {"_path": "site-packages/setuptools/_vendor/more_itertools/more.py", "path_type": "hardlink", "sha256": "d44e64cc59dc44a4c3c34718bf5c915cc80376e9ecb4b41dd504ad7272fa53dd", "sha256_in_prefix": "d44e64cc59dc44a4c3c34718bf5c915cc80376e9ecb4b41dd504ad7272fa53dd", "size_in_bytes": 148370}, {"_path": "site-packages/setuptools/_vendor/more_itertools/more.pyi", "path_type": "hardlink", "sha256": "8975deaade3c3717bc5469885a99155ee2a947615836ebb60d4f2740b5820aed", "sha256_in_prefix": "8975deaade3c3717bc5469885a99155ee2a947615836ebb60d4f2740b5820aed", "size_in_bytes": 21484}, {"_path": "site-packages/setuptools/_vendor/more_itertools/py.typed", "path_type": "hardlink", "sha256": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "sha256_in_prefix": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "size_in_bytes": 0}, {"_path": "site-packages/setuptools/_vendor/more_itertools/recipes.py", "path_type": "hardlink", "sha256": "59e76185f846560aface28bc7c86c62559914f0d1929188b2a76010c626fe276", "sha256_in_prefix": "59e76185f846560aface28bc7c86c62559914f0d1929188b2a76010c626fe276", "size_in_bytes": 28591}, {"_path": "site-packages/setuptools/_vendor/more_itertools/recipes.pyi", "path_type": "hardlink", "sha256": "4ff99d1a970575facfdc94966f0cd83fd272355f86a3eed13dfa717dfb405a50", "sha256_in_prefix": "4ff99d1a970575facfdc94966f0cd83fd272355f86a3eed13dfa717dfb405a50", "size_in_bytes": 4617}, {"_path": "site-packages/setuptools/_vendor/packaging-24.2.dist-info/INSTALLER", "path_type": "hardlink", "sha256": "d0edee15f91b406f3f99726e44eb990be6e34fd0345b52b910c568e0eef6a2a8", "sha256_in_prefix": "d0edee15f91b406f3f99726e44eb990be6e34fd0345b52b910c568e0eef6a2a8", "size_in_bytes": 5}, {"_path": "site-packages/setuptools/_vendor/packaging-24.2.dist-info/LICENSE", "path_type": "hardlink", "sha256": "cad1ef5bd340d73e074ba614d26f7deaca5c7940c3d8c34852e65c4909686c48", "sha256_in_prefix": "cad1ef5bd340d73e074ba614d26f7deaca5c7940c3d8c34852e65c4909686c48", "size_in_bytes": 197}, {"_path": "site-packages/setuptools/_vendor/packaging-24.2.dist-info/LICENSE.APACHE", "path_type": "hardlink", "sha256": "0d542e0c8804e39aa7f37eb00da5a762149dc682d7829451287e11b938e94594", "sha256_in_prefix": "0d542e0c8804e39aa7f37eb00da5a762149dc682d7829451287e11b938e94594", "size_in_bytes": 10174}, {"_path": "site-packages/setuptools/_vendor/packaging-24.2.dist-info/LICENSE.BSD", "path_type": "hardlink", "sha256": "b70e7e9b742f1cc6f948b34c16aa39ffece94196364bc88ff0d2180f0028fac5", "sha256_in_prefix": "b70e7e9b742f1cc6f948b34c16aa39ffece94196364bc88ff0d2180f0028fac5", "size_in_bytes": 1344}, {"_path": "site-packages/setuptools/_vendor/packaging-24.2.dist-info/METADATA", "path_type": "hardlink", "sha256": "a211fceacea4e6621f4316364d2d0b7127c00de3856b8062082f9bc5957ea4db", "sha256_in_prefix": "a211fceacea4e6621f4316364d2d0b7127c00de3856b8062082f9bc5957ea4db", "size_in_bytes": 3204}, {"_path": "site-packages/setuptools/_vendor/packaging-24.2.dist-info/RECORD", "path_type": "hardlink", "sha256": "6380eb5ccd0a63402b7f385b0046b52d814fc16dd612011e2f8882a977be03e3", "sha256_in_prefix": "6380eb5ccd0a63402b7f385b0046b52d814fc16dd612011e2f8882a977be03e3", "size_in_bytes": 2009}, {"_path": "site-packages/setuptools/_vendor/packaging-24.2.dist-info/REQUESTED", "path_type": "hardlink", "sha256": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "sha256_in_prefix": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "size_in_bytes": 0}, {"_path": "site-packages/setuptools/_vendor/packaging-24.2.dist-info/WHEEL", "path_type": "hardlink", "sha256": "0a950253178741b44de54191407611268acee407fe432fdf1cc72d710f034862", "sha256_in_prefix": "0a950253178741b44de54191407611268acee407fe432fdf1cc72d710f034862", "size_in_bytes": 82}, {"_path": "site-packages/setuptools/_vendor/packaging/__init__.py", "path_type": "hardlink", "sha256": "764e136bfbe67552716070dc7f286f40dc3c5773e0481a2628d5ea83e0f62436", "sha256_in_prefix": "764e136bfbe67552716070dc7f286f40dc3c5773e0481a2628d5ea83e0f62436", "size_in_bytes": 494}, {"_path": "site-packages/setuptools/_vendor/packaging/_elffile.py", "path_type": "hardlink", "sha256": "71f940400904db9b738589aafda0a2ef641f6d3fed9fcf75b4fcdfa5b7873b01", "sha256_in_prefix": "71f940400904db9b738589aafda0a2ef641f6d3fed9fcf75b4fcdfa5b7873b01", "size_in_bytes": 3306}, {"_path": "site-packages/setuptools/_vendor/packaging/_manylinux.py", "path_type": "hardlink", "sha256": "be5e4e0a8cf8931f341f9af05ca7975a397d58d2121a6af86604e94cff6553d7", "sha256_in_prefix": "be5e4e0a8cf8931f341f9af05ca7975a397d58d2121a6af86604e94cff6553d7", "size_in_bytes": 9612}, {"_path": "site-packages/setuptools/_vendor/packaging/_musllinux.py", "path_type": "hardlink", "sha256": "a7d66a35888e22d19e7bc29c64578717f61c76157018774aeabfbc9608b1bc64", "sha256_in_prefix": "a7d66a35888e22d19e7bc29c64578717f61c76157018774aeabfbc9608b1bc64", "size_in_bytes": 2694}, {"_path": "site-packages/setuptools/_vendor/packaging/_parser.py", "path_type": "hardlink", "sha256": "b3f4ef4ef0cd2b436b336401dd529385d58533835cd0fe899e439b925dcc8e93", "sha256_in_prefix": "b3f4ef4ef0cd2b436b336401dd529385d58533835cd0fe899e439b925dcc8e93", "size_in_bytes": 10236}, {"_path": "site-packages/setuptools/_vendor/packaging/_structures.py", "path_type": "hardlink", "sha256": "ab77953666d62461bf4b40e2b7f4b7028f2a42acffe4f6135c500a0597b9cabe", "sha256_in_prefix": "ab77953666d62461bf4b40e2b7f4b7028f2a42acffe4f6135c500a0597b9cabe", "size_in_bytes": 1431}, {"_path": "site-packages/setuptools/_vendor/packaging/_tokenizer.py", "path_type": "hardlink", "sha256": "27abf91fb273bdbfa0f35c69ff640008ac0eecbc47400ea292bc8c53bcd7c0df", "sha256_in_prefix": "27abf91fb273bdbfa0f35c69ff640008ac0eecbc47400ea292bc8c53bcd7c0df", "size_in_bytes": 5273}, {"_path": "site-packages/setuptools/_vendor/packaging/licenses/__init__.py", "path_type": "hardlink", "sha256": "d71e4cd671188dc83011b2edd1d5f0cf6ba48ebd7c0e20b30b4b2b690a89f96c", "sha256_in_prefix": "d71e4cd671188dc83011b2edd1d5f0cf6ba48ebd7c0e20b30b4b2b690a89f96c", "size_in_bytes": 5715}, {"_path": "site-packages/setuptools/_vendor/packaging/licenses/_spdx.py", "path_type": "hardlink", "sha256": "a009b5ced3c5c25b2608a7bb94002cbff38839f4b57160eef5b34191ebbeda7b", "sha256_in_prefix": "a009b5ced3c5c25b2608a7bb94002cbff38839f4b57160eef5b34191ebbeda7b", "size_in_bytes": 48398}, {"_path": "site-packages/setuptools/_vendor/packaging/markers.py", "path_type": "hardlink", "sha256": "73cf5337307b65d198864a2f9ba3d89aa1b21f15e561568b5b9f753c750d283f", "sha256_in_prefix": "73cf5337307b65d198864a2f9ba3d89aa1b21f15e561568b5b9f753c750d283f", "size_in_bytes": 10561}, {"_path": "site-packages/setuptools/_vendor/packaging/metadata.py", "path_type": "hardlink", "sha256": "60989b33b1987b8adef3ed1adce9579864be5c38131283b8b6506ddaadb90678", "sha256_in_prefix": "60989b33b1987b8adef3ed1adce9579864be5c38131283b8b6506ddaadb90678", "size_in_bytes": 34762}, {"_path": "site-packages/setuptools/_vendor/packaging/py.typed", "path_type": "hardlink", "sha256": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "sha256_in_prefix": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "size_in_bytes": 0}, {"_path": "site-packages/setuptools/_vendor/packaging/requirements.py", "path_type": "hardlink", "sha256": "818c9148075bac8c8a0d8ebaba02035108d132fc641f600b8a84e65f7b672faa", "sha256_in_prefix": "818c9148075bac8c8a0d8ebaba02035108d132fc641f600b8a84e65f7b672faa", "size_in_bytes": 2947}, {"_path": "site-packages/setuptools/_vendor/packaging/specifiers.py", "path_type": "hardlink", "sha256": "186d703cd31c2f47cc24eebcbc5e77c0a31dc277de84371a23eafd3694df8a50", "sha256_in_prefix": "186d703cd31c2f47cc24eebcbc5e77c0a31dc277de84371a23eafd3694df8a50", "size_in_bytes": 40074}, {"_path": "site-packages/setuptools/_vendor/packaging/tags.py", "path_type": "hardlink", "sha256": "085aab2730337365cd19ec5eac7fff4fe639230abb59bb185ec88b1112d6c58d", "sha256_in_prefix": "085aab2730337365cd19ec5eac7fff4fe639230abb59bb185ec88b1112d6c58d", "size_in_bytes": 21014}, {"_path": "site-packages/setuptools/_vendor/packaging/utils.py", "path_type": "hardlink", "sha256": "d05dc787d385b9182b8538066549792b6d85bf560fdad665d73ff680eea42620", "sha256_in_prefix": "d05dc787d385b9182b8538066549792b6d85bf560fdad665d73ff680eea42620", "size_in_bytes": 5050}, {"_path": "site-packages/setuptools/_vendor/packaging/version.py", "path_type": "hardlink", "sha256": "a257f2ba4fc33db7e5364278c0159eb57435edcef8c770c1e74d5d7a052fec36", "sha256_in_prefix": "a257f2ba4fc33db7e5364278c0159eb57435edcef8c770c1e74d5d7a052fec36", "size_in_bytes": 16676}, {"_path": "site-packages/setuptools/_vendor/platformdirs-4.2.2.dist-info/INSTALLER", "path_type": "hardlink", "sha256": "d0edee15f91b406f3f99726e44eb990be6e34fd0345b52b910c568e0eef6a2a8", "sha256_in_prefix": "d0edee15f91b406f3f99726e44eb990be6e34fd0345b52b910c568e0eef6a2a8", "size_in_bytes": 5}, {"_path": "site-packages/setuptools/_vendor/platformdirs-4.2.2.dist-info/METADATA", "path_type": "hardlink", "sha256": "ce6b227b4d46d4cb57474c2022fe57a557933bb89daf4596bdf9b12ac296b869", "sha256_in_prefix": "ce6b227b4d46d4cb57474c2022fe57a557933bb89daf4596bdf9b12ac296b869", "size_in_bytes": 11429}, {"_path": "site-packages/setuptools/_vendor/platformdirs-4.2.2.dist-info/RECORD", "path_type": "hardlink", "sha256": "4c211d76d42ed40efc3acfcc866d8912a718afbca2b7e51849442366d6e99fe8", "sha256_in_prefix": "4c211d76d42ed40efc3acfcc866d8912a718afbca2b7e51849442366d6e99fe8", "size_in_bytes": 1642}, {"_path": "site-packages/setuptools/_vendor/platformdirs-4.2.2.dist-info/REQUESTED", "path_type": "hardlink", "sha256": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "sha256_in_prefix": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "size_in_bytes": 0}, {"_path": "site-packages/setuptools/_vendor/platformdirs-4.2.2.dist-info/WHEEL", "path_type": "hardlink", "sha256": "cc431c46bf4aaf4df1d68cc6c20e6ff4d4012a7de49dda7a2d2a1295583e8e15", "sha256_in_prefix": "cc431c46bf4aaf4df1d68cc6c20e6ff4d4012a7de49dda7a2d2a1295583e8e15", "size_in_bytes": 87}, {"_path": "site-packages/setuptools/_vendor/platformdirs-4.2.2.dist-info/licenses/LICENSE", "path_type": "hardlink", "sha256": "29e0fd62e929850e86eb28c3fdccf0cefdf4fa94879011cffb3d0d4bed6d4db6", "sha256_in_prefix": "29e0fd62e929850e86eb28c3fdccf0cefdf4fa94879011cffb3d0d4bed6d4db6", "size_in_bytes": 1089}, {"_path": "site-packages/setuptools/_vendor/platformdirs/__init__.py", "path_type": "hardlink", "sha256": "10c184f2a787451f42cc316bf242f7b40f217596678988d705565dd1419358ad", "sha256_in_prefix": "10c184f2a787451f42cc316bf242f7b40f217596678988d705565dd1419358ad", "size_in_bytes": 22225}, {"_path": "site-packages/setuptools/_vendor/platformdirs/__main__.py", "path_type": "hardlink", "sha256": "1e7b14407a6205a893c70726c15c3e9c568f755359b5021d8b57960ed23e3332", "sha256_in_prefix": "1e7b14407a6205a893c70726c15c3e9c568f755359b5021d8b57960ed23e3332", "size_in_bytes": 1493}, {"_path": "site-packages/setuptools/_vendor/platformdirs/android.py", "path_type": "hardlink", "sha256": "c595d8f49778e963acc53d94ebee47b0db4367e210ab170452b04b977858938a", "sha256_in_prefix": "c595d8f49778e963acc53d94ebee47b0db4367e210ab170452b04b977858938a", "size_in_bytes": 9016}, {"_path": "site-packages/setuptools/_vendor/platformdirs/api.py", "path_type": "hardlink", "sha256": "40161d51a736782e76d5e93fcb9dee0f50dcabe9495fc22049155de089c2eae7", "sha256_in_prefix": "40161d51a736782e76d5e93fcb9dee0f50dcabe9495fc22049155de089c2eae7", "size_in_bytes": 8996}, {"_path": "site-packages/setuptools/_vendor/platformdirs/macos.py", "path_type": "hardlink", "sha256": "c1fb6c6ecbeaea767458e4574a20ab64d9111f3fd62ae92d9746ba982ecc1642", "sha256_in_prefix": "c1fb6c6ecbeaea767458e4574a20ab64d9111f3fd62ae92d9746ba982ecc1642", "size_in_bytes": 5580}, {"_path": "site-packages/setuptools/_vendor/platformdirs/py.typed", "path_type": "hardlink", "sha256": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "sha256_in_prefix": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "size_in_bytes": 0}, {"_path": "site-packages/setuptools/_vendor/platformdirs/unix.py", "path_type": "hardlink", "sha256": "09c8bd5aab77e5d00cb20e874fd9d11874815b9a1b6f4a51dc01352499ec0978", "sha256_in_prefix": "09c8bd5aab77e5d00cb20e874fd9d11874815b9a1b6f4a51dc01352499ec0978", "size_in_bytes": 10643}, {"_path": "site-packages/setuptools/_vendor/platformdirs/version.py", "path_type": "hardlink", "sha256": "afb17bead6518e040aceba71fc8d3f64c40e314f8f4bb7869c70fbcc42b7281d", "sha256_in_prefix": "afb17bead6518e040aceba71fc8d3f64c40e314f8f4bb7869c70fbcc42b7281d", "size_in_bytes": 411}, {"_path": "site-packages/setuptools/_vendor/platformdirs/windows.py", "path_type": "hardlink", "sha256": "205a62a21501c313ed0b39722b036dc725b8264f2169ae96f28e7d99fac35d5a", "sha256_in_prefix": "205a62a21501c313ed0b39722b036dc725b8264f2169ae96f28e7d99fac35d5a", "size_in_bytes": 10125}, {"_path": "site-packages/setuptools/_vendor/tomli-2.0.1.dist-info/INSTALLER", "path_type": "hardlink", "sha256": "d0edee15f91b406f3f99726e44eb990be6e34fd0345b52b910c568e0eef6a2a8", "sha256_in_prefix": "d0edee15f91b406f3f99726e44eb990be6e34fd0345b52b910c568e0eef6a2a8", "size_in_bytes": 5}, {"_path": "site-packages/setuptools/_vendor/tomli-2.0.1.dist-info/LICENSE", "path_type": "hardlink", "sha256": "b80816b0d530b8accb4c2211783790984a6e3b61922c2b5ee92f3372ab2742fe", "sha256_in_prefix": "b80816b0d530b8accb4c2211783790984a6e3b61922c2b5ee92f3372ab2742fe", "size_in_bytes": 1072}, {"_path": "site-packages/setuptools/_vendor/tomli-2.0.1.dist-info/METADATA", "path_type": "hardlink", "sha256": "ccf0dc78a98fc0918b5ad67292b1e2c4bed65575a6246cd9d63c914f9942a0f2", "sha256_in_prefix": "ccf0dc78a98fc0918b5ad67292b1e2c4bed65575a6246cd9d63c914f9942a0f2", "size_in_bytes": 8875}, {"_path": "site-packages/setuptools/_vendor/tomli-2.0.1.dist-info/RECORD", "path_type": "hardlink", "sha256": "0cb9f9a451a1e365ac54b4c88662e1da0cb54a72d16a5258fb0abff9d3e1c022", "sha256_in_prefix": "0cb9f9a451a1e365ac54b4c88662e1da0cb54a72d16a5258fb0abff9d3e1c022", "size_in_bytes": 999}, {"_path": "site-packages/setuptools/_vendor/tomli-2.0.1.dist-info/REQUESTED", "path_type": "hardlink", "sha256": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "sha256_in_prefix": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "size_in_bytes": 0}, {"_path": "site-packages/setuptools/_vendor/tomli-2.0.1.dist-info/WHEEL", "path_type": "hardlink", "sha256": "8cf311fc3ce47385f889c42d9b3f35967358fe402c7e883baf2eeaa11bd82d7c", "sha256_in_prefix": "8cf311fc3ce47385f889c42d9b3f35967358fe402c7e883baf2eeaa11bd82d7c", "size_in_bytes": 81}, {"_path": "site-packages/setuptools/_vendor/tomli/__init__.py", "path_type": "hardlink", "sha256": "26153057ae830758381efb7551009531d7c2bbe220015f055e6bc353da27c5de", "sha256_in_prefix": "26153057ae830758381efb7551009531d7c2bbe220015f055e6bc353da27c5de", "size_in_bytes": 396}, {"_path": "site-packages/setuptools/_vendor/tomli/_parser.py", "path_type": "hardlink", "sha256": "83df8435a00b4be07c768918a42bb35056a55a5a20ed3f922183232d9496aed3", "sha256_in_prefix": "83df8435a00b4be07c768918a42bb35056a55a5a20ed3f922183232d9496aed3", "size_in_bytes": 22633}, {"_path": "site-packages/setuptools/_vendor/tomli/_re.py", "path_type": "hardlink", "sha256": "75b8e0e428594f6dca6bdcfd0c73977ddb52a4fc147dd80c5e78fc34ea25cbec", "sha256_in_prefix": "75b8e0e428594f6dca6bdcfd0c73977ddb52a4fc147dd80c5e78fc34ea25cbec", "size_in_bytes": 2943}, {"_path": "site-packages/setuptools/_vendor/tomli/_types.py", "path_type": "hardlink", "sha256": "f864c6d9552a929c7032ace654ee05ef26ca75d21b027b801d77e65907138b74", "sha256_in_prefix": "f864c6d9552a929c7032ace654ee05ef26ca75d21b027b801d77e65907138b74", "size_in_bytes": 254}, {"_path": "site-packages/setuptools/_vendor/tomli/py.typed", "path_type": "hardlink", "sha256": "f0f8f2675695a10a5156fb7bd66bafbaae6a13e8d315990af862c792175e6e67", "sha256_in_prefix": "f0f8f2675695a10a5156fb7bd66bafbaae6a13e8d315990af862c792175e6e67", "size_in_bytes": 26}, {"_path": "site-packages/setuptools/_vendor/typeguard-4.3.0.dist-info/INSTALLER", "path_type": "hardlink", "sha256": "d0edee15f91b406f3f99726e44eb990be6e34fd0345b52b910c568e0eef6a2a8", "sha256_in_prefix": "d0edee15f91b406f3f99726e44eb990be6e34fd0345b52b910c568e0eef6a2a8", "size_in_bytes": 5}, {"_path": "site-packages/setuptools/_vendor/typeguard-4.3.0.dist-info/LICENSE", "path_type": "hardlink", "sha256": "6163f7987dfb38d6bc320ce2b70b2f02b862bc41126516d552ef1cd43247e758", "sha256_in_prefix": "6163f7987dfb38d6bc320ce2b70b2f02b862bc41126516d552ef1cd43247e758", "size_in_bytes": 1130}, {"_path": "site-packages/setuptools/_vendor/typeguard-4.3.0.dist-info/METADATA", "path_type": "hardlink", "sha256": "cf675c1c0a744f08580855390de87cc77d676b312582e8d4cfdb5bb8fd298d21", "sha256_in_prefix": "cf675c1c0a744f08580855390de87cc77d676b312582e8d4cfdb5bb8fd298d21", "size_in_bytes": 3717}, {"_path": "site-packages/setuptools/_vendor/typeguard-4.3.0.dist-info/RECORD", "path_type": "hardlink", "sha256": "48a51959582478352275428ceecd78ef77d79ac9dae796e39a2eaf2540282552", "sha256_in_prefix": "48a51959582478352275428ceecd78ef77d79ac9dae796e39a2eaf2540282552", "size_in_bytes": 2402}, {"_path": "site-packages/setuptools/_vendor/typeguard-4.3.0.dist-info/WHEEL", "path_type": "hardlink", "sha256": "189eedfe4581172c1b6a02b97a8f48a14c0b5baa3239e4ca990fbd8871553714", "sha256_in_prefix": "189eedfe4581172c1b6a02b97a8f48a14c0b5baa3239e4ca990fbd8871553714", "size_in_bytes": 92}, {"_path": "site-packages/setuptools/_vendor/typeguard-4.3.0.dist-info/entry_points.txt", "path_type": "hardlink", "sha256": "aa9ecd43568bb624a0310aa8ea05a57c6a72d08217ce830999e4132e9cea1594", "sha256_in_prefix": "aa9ecd43568bb624a0310aa8ea05a57c6a72d08217ce830999e4132e9cea1594", "size_in_bytes": 48}, {"_path": "site-packages/setuptools/_vendor/typeguard-4.3.0.dist-info/top_level.txt", "path_type": "hardlink", "sha256": "e33dbc021b83a1dc114bf73527f97c1f9d6de50bb07d3b1eb24633971a7a82bb", "sha256_in_prefix": "e33dbc021b83a1dc114bf73527f97c1f9d6de50bb07d3b1eb24633971a7a82bb", "size_in_bytes": 10}, {"_path": "site-packages/setuptools/_vendor/typeguard/__init__.py", "path_type": "hardlink", "sha256": "3a7878c37f1e94f0a3b65714dc963d93787bd0d8ecc5722401f966427f99d056", "sha256_in_prefix": "3a7878c37f1e94f0a3b65714dc963d93787bd0d8ecc5722401f966427f99d056", "size_in_bytes": 2071}, {"_path": "site-packages/setuptools/_vendor/typeguard/_checkers.py", "path_type": "hardlink", "sha256": "251ae02a271d3847c8068344b5e81808422586969df9ad6ed449bb1828f45822", "sha256_in_prefix": "251ae02a271d3847c8068344b5e81808422586969df9ad6ed449bb1828f45822", "size_in_bytes": 31360}, {"_path": "site-packages/setuptools/_vendor/typeguard/_config.py", "path_type": "hardlink", "sha256": "9c8cfc4300dafa814edcbf4ef3feacaf396677df6949bcb6c0e33859bec5fc1d", "sha256_in_prefix": "9c8cfc4300dafa814edcbf4ef3feacaf396677df6949bcb6c0e33859bec5fc1d", "size_in_bytes": 2846}, {"_path": "site-packages/setuptools/_vendor/typeguard/_decorators.py", "path_type": "hardlink", "sha256": "bfa76c21e5af3e113118b3ffc1717e4660d4ca365ffc0936f20ee0049fefd3ed", "sha256_in_prefix": "bfa76c21e5af3e113118b3ffc1717e4660d4ca365ffc0936f20ee0049fefd3ed", "size_in_bytes": 9033}, {"_path": "site-packages/setuptools/_vendor/typeguard/_exceptions.py", "path_type": "hardlink", "sha256": "6483de895f8505de449b0d8469677616f96caf08b8a1cc13d9f54604802d1dc4", "sha256_in_prefix": "6483de895f8505de449b0d8469677616f96caf08b8a1cc13d9f54604802d1dc4", "size_in_bytes": 1121}, {"_path": "site-packages/setuptools/_vendor/typeguard/_functions.py", "path_type": "hardlink", "sha256": "89b81200a6b9a6d226d5e47d0111b4052a3300524f14d01266a84f57241eaa28", "sha256_in_prefix": "89b81200a6b9a6d226d5e47d0111b4052a3300524f14d01266a84f57241eaa28", "size_in_bytes": 10393}, {"_path": "site-packages/setuptools/_vendor/typeguard/_importhook.py", "path_type": "hardlink", "sha256": "ba08c20ef15c756314ed4ba0aa5246f7522954da44231b51afef7db3487593b3", "sha256_in_prefix": "ba08c20ef15c756314ed4ba0aa5246f7522954da44231b51afef7db3487593b3", "size_in_bytes": 6389}, {"_path": "site-packages/setuptools/_vendor/typeguard/_memo.py", "path_type": "hardlink", "sha256": "d63b9057fbf19c3d8960a6d2ade6e242e8f8a0a1f3ea7ebbbfda5803e0822128", "sha256_in_prefix": "d63b9057fbf19c3d8960a6d2ade6e242e8f8a0a1f3ea7ebbbfda5803e0822128", "size_in_bytes": 1303}, {"_path": "site-packages/setuptools/_vendor/typeguard/_pytest_plugin.py", "path_type": "hardlink", "sha256": "f9f712aa4bf9e2b21f205f290dabd8e5840f923e0e5fc18cb7f94bec24120f82", "sha256_in_prefix": "f9f712aa4bf9e2b21f205f290dabd8e5840f923e0e5fc18cb7f94bec24120f82", "size_in_bytes": 4416}, {"_path": "site-packages/setuptools/_vendor/typeguard/_suppression.py", "path_type": "hardlink", "sha256": "5507f3c5cc086eede27f47fb54190a33b86460e03bb4d170f5aee3301b26320e", "sha256_in_prefix": "5507f3c5cc086eede27f47fb54190a33b86460e03bb4d170f5aee3301b26320e", "size_in_bytes": 2266}, {"_path": "site-packages/setuptools/_vendor/typeguard/_transformer.py", "path_type": "hardlink", "sha256": "f476bbfd085dc285278bfea1bdd63e8596ee11eae0a301eb34bdafcc721a9056", "sha256_in_prefix": "f476bbfd085dc285278bfea1bdd63e8596ee11eae0a301eb34bdafcc721a9056", "size_in_bytes": 44937}, {"_path": "site-packages/setuptools/_vendor/typeguard/_union_transformer.py", "path_type": "hardlink", "sha256": "bffe36afbfba1ee457d92a05c27c89f84e4f9715e955e5093c9475f8753da92a", "sha256_in_prefix": "bffe36afbfba1ee457d92a05c27c89f84e4f9715e955e5093c9475f8753da92a", "size_in_bytes": 1354}, {"_path": "site-packages/setuptools/_vendor/typeguard/_utils.py", "path_type": "hardlink", "sha256": "e4784ed6b3e7e5fd4ceb29a454012fed79a5cf5717fa3d0e9d3325c87aaaad1f", "sha256_in_prefix": "e4784ed6b3e7e5fd4ceb29a454012fed79a5cf5717fa3d0e9d3325c87aaaad1f", "size_in_bytes": 5270}, {"_path": "site-packages/setuptools/_vendor/typeguard/py.typed", "path_type": "hardlink", "sha256": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "sha256_in_prefix": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "size_in_bytes": 0}, {"_path": "site-packages/setuptools/_vendor/typing_extensions-4.12.2.dist-info/INSTALLER", "path_type": "hardlink", "sha256": "d0edee15f91b406f3f99726e44eb990be6e34fd0345b52b910c568e0eef6a2a8", "sha256_in_prefix": "d0edee15f91b406f3f99726e44eb990be6e34fd0345b52b910c568e0eef6a2a8", "size_in_bytes": 5}, {"_path": "site-packages/setuptools/_vendor/typing_extensions-4.12.2.dist-info/LICENSE", "path_type": "hardlink", "sha256": "3b2f81fe21d181c499c59a256c8e1968455d6689d269aa85373bfb6af41da3bf", "sha256_in_prefix": "3b2f81fe21d181c499c59a256c8e1968455d6689d269aa85373bfb6af41da3bf", "size_in_bytes": 13936}, {"_path": "site-packages/setuptools/_vendor/typing_extensions-4.12.2.dist-info/METADATA", "path_type": "hardlink", "sha256": "05e51021af1c9d86eb8d6c7e37c4cece733d5065b91a6d8389c5690ed440f16d", "sha256_in_prefix": "05e51021af1c9d86eb8d6c7e37c4cece733d5065b91a6d8389c5690ed440f16d", "size_in_bytes": 3018}, {"_path": "site-packages/setuptools/_vendor/typing_extensions-4.12.2.dist-info/RECORD", "path_type": "hardlink", "sha256": "7710002d81971e632aa6a2fc33dc5d74aaf5d7caae22040a65d3e31503b05ee9", "sha256_in_prefix": "7710002d81971e632aa6a2fc33dc5d74aaf5d7caae22040a65d3e31503b05ee9", "size_in_bytes": 571}, {"_path": "site-packages/setuptools/_vendor/typing_extensions-4.12.2.dist-info/WHEEL", "path_type": "hardlink", "sha256": "1196c6921ec87b83e865f450f08d19b8ff5592537f4ef719e83484e546abe33e", "sha256_in_prefix": "1196c6921ec87b83e865f450f08d19b8ff5592537f4ef719e83484e546abe33e", "size_in_bytes": 81}, {"_path": "site-packages/setuptools/_vendor/typing_extensions.py", "path_type": "hardlink", "sha256": "8307a4a721bd0d51b797158a5f89e2f2eee793759ee6c946f7c980f45dc3250c", "sha256_in_prefix": "8307a4a721bd0d51b797158a5f89e2f2eee793759ee6c946f7c980f45dc3250c", "size_in_bytes": 134451}, {"_path": "site-packages/setuptools/_vendor/wheel-0.45.1.dist-info/INSTALLER", "path_type": "hardlink", "sha256": "d0edee15f91b406f3f99726e44eb990be6e34fd0345b52b910c568e0eef6a2a8", "sha256_in_prefix": "d0edee15f91b406f3f99726e44eb990be6e34fd0345b52b910c568e0eef6a2a8", "size_in_bytes": 5}, {"_path": "site-packages/setuptools/_vendor/wheel-0.45.1.dist-info/LICENSE.txt", "path_type": "hardlink", "sha256": "30c23618679108f3e8ea1d2a658c7ca417bdfc891c98ef1a89fa4ff0c9828654", "sha256_in_prefix": "30c23618679108f3e8ea1d2a658c7ca417bdfc891c98ef1a89fa4ff0c9828654", "size_in_bytes": 1107}, {"_path": "site-packages/setuptools/_vendor/wheel-0.45.1.dist-info/METADATA", "path_type": "hardlink", "sha256": "98acfce07ee6ee3b31272cde21c4d53918936f434f315dfd2af3886211a09a30", "sha256_in_prefix": "98acfce07ee6ee3b31272cde21c4d53918936f434f315dfd2af3886211a09a30", "size_in_bytes": 2313}, {"_path": "site-packages/setuptools/_vendor/wheel-0.45.1.dist-info/RECORD", "path_type": "hardlink", "sha256": "d639f1ac7c993c3715bd42f27c616189b6b86792fdfd1b17afd77170d6e16984", "sha256_in_prefix": "d639f1ac7c993c3715bd42f27c616189b6b86792fdfd1b17afd77170d6e16984", "size_in_bytes": 4900}, {"_path": "site-packages/setuptools/_vendor/wheel-0.45.1.dist-info/REQUESTED", "path_type": "hardlink", "sha256": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "sha256_in_prefix": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "size_in_bytes": 0}, {"_path": "site-packages/setuptools/_vendor/wheel-0.45.1.dist-info/WHEEL", "path_type": "hardlink", "sha256": "0a950253178741b44de54191407611268acee407fe432fdf1cc72d710f034862", "sha256_in_prefix": "0a950253178741b44de54191407611268acee407fe432fdf1cc72d710f034862", "size_in_bytes": 82}, {"_path": "site-packages/setuptools/_vendor/wheel-0.45.1.dist-info/entry_points.txt", "path_type": "hardlink", "sha256": "ad363505b90f1e1906326e10dc5d29233241cd6da4331a06d68ae27dfbc6740d", "sha256_in_prefix": "ad363505b90f1e1906326e10dc5d29233241cd6da4331a06d68ae27dfbc6740d", "size_in_bytes": 104}, {"_path": "site-packages/setuptools/_vendor/wheel/__init__.py", "path_type": "hardlink", "sha256": "9abc4c9ef757002babfcb59e81b51f879839cac599addeb75099fcf74c2f18d9", "sha256_in_prefix": "9abc4c9ef757002babfcb59e81b51f879839cac599addeb75099fcf74c2f18d9", "size_in_bytes": 59}, {"_path": "site-packages/setuptools/_vendor/wheel/__main__.py", "path_type": "hardlink", "sha256": "3643149ee4c219c3a4818d0804b8010950bf04619c58e471d8af236064b5d941", "sha256_in_prefix": "3643149ee4c219c3a4818d0804b8010950bf04619c58e471d8af236064b5d941", "size_in_bytes": 455}, {"_path": "site-packages/setuptools/_vendor/wheel/_bdist_wheel.py", "path_type": "hardlink", "sha256": "520842423487fe955f71987aa118f34b0fd342171fdda9d2c753a488b48bf363", "sha256_in_prefix": "520842423487fe955f71987aa118f34b0fd342171fdda9d2c753a488b48bf363", "size_in_bytes": 21694}, {"_path": "site-packages/setuptools/_vendor/wheel/_setuptools_logging.py", "path_type": "hardlink", "sha256": "fb9282fa59ded2294e5162037ce92a6a951618c15986e2980c86af219881e643", "sha256_in_prefix": "fb9282fa59ded2294e5162037ce92a6a951618c15986e2980c86af219881e643", "size_in_bytes": 781}, {"_path": "site-packages/setuptools/_vendor/wheel/bdist_wheel.py", "path_type": "hardlink", "sha256": "b697fd5ae7e248ed51b84320e683e121f486f0333388267fe26b82285ebd0aaa", "sha256_in_prefix": "b697fd5ae7e248ed51b84320e683e121f486f0333388267fe26b82285ebd0aaa", "size_in_bytes": 1107}, {"_path": "site-packages/setuptools/_vendor/wheel/cli/__init__.py", "path_type": "hardlink", "sha256": "369abafe32a2d3776121c46799bb85870be2549c703b4b5812712158cbfd709a", "sha256_in_prefix": "369abafe32a2d3776121c46799bb85870be2549c703b4b5812712158cbfd709a", "size_in_bytes": 4402}, {"_path": "site-packages/setuptools/_vendor/wheel/cli/convert.py", "path_type": "hardlink", "sha256": "062d27b445dbf674e5942c56793450e23fa73ecdeccd64842a2a46fc68273244", "sha256_in_prefix": "062d27b445dbf674e5942c56793450e23fa73ecdeccd64842a2a46fc68273244", "size_in_bytes": 12634}, {"_path": "site-packages/setuptools/_vendor/wheel/cli/pack.py", "path_type": "hardlink", "sha256": "08015c1dd055ba5bec1d82659dd2953bb28c23d26a053673e628b43cac7108eb", "sha256_in_prefix": "08015c1dd055ba5bec1d82659dd2953bb28c23d26a053673e628b43cac7108eb", "size_in_bytes": 3103}, {"_path": "site-packages/setuptools/_vendor/wheel/cli/tags.py", "path_type": "hardlink", "sha256": "947c3e2da5ab912e49cbfa96730fbaa528de34ceb20230e7a8a2371392534c25", "sha256_in_prefix": "947c3e2da5ab912e49cbfa96730fbaa528de34ceb20230e7a8a2371392534c25", "size_in_bytes": 4760}, {"_path": "site-packages/setuptools/_vendor/wheel/cli/unpack.py", "path_type": "hardlink", "sha256": "63f27bca7c4f4a81454d3ec7d1f3206c195512bc320c670e6e099ee4c06ecf9f", "sha256_in_prefix": "63f27bca7c4f4a81454d3ec7d1f3206c195512bc320c670e6e099ee4c06ecf9f", "size_in_bytes": 1021}, {"_path": "site-packages/setuptools/_vendor/wheel/macosx_libfile.py", "path_type": "hardlink", "sha256": "935c7b084dcb3ed3951aa8fa3574359d319854f69e46b855cd41bf28fab7cc3b", "sha256_in_prefix": "935c7b084dcb3ed3951aa8fa3574359d319854f69e46b855cd41bf28fab7cc3b", "size_in_bytes": 16572}, {"_path": "site-packages/setuptools/_vendor/wheel/metadata.py", "path_type": "hardlink", "sha256": "242e29ee395066ed9b513010d9f7af92a2e383f5fa8273724612e7e8e50ed6d7", "sha256_in_prefix": "242e29ee395066ed9b513010d9f7af92a2e383f5fa8273724612e7e8e50ed6d7", "size_in_bytes": 6171}, {"_path": "site-packages/setuptools/_vendor/wheel/util.py", "path_type": "hardlink", "sha256": "68beda89b1f061481f73c5a5a252f9b577645780dab5b2716476f59301c52405", "sha256_in_prefix": "68beda89b1f061481f73c5a5a252f9b577645780dab5b2716476f59301c52405", "size_in_bytes": 423}, {"_path": "site-packages/setuptools/_vendor/wheel/vendored/__init__.py", "path_type": "hardlink", "sha256": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "sha256_in_prefix": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "size_in_bytes": 0}, {"_path": "site-packages/setuptools/_vendor/wheel/vendored/packaging/LICENSE", "path_type": "hardlink", "sha256": "cad1ef5bd340d73e074ba614d26f7deaca5c7940c3d8c34852e65c4909686c48", "sha256_in_prefix": "cad1ef5bd340d73e074ba614d26f7deaca5c7940c3d8c34852e65c4909686c48", "size_in_bytes": 197}, {"_path": "site-packages/setuptools/_vendor/wheel/vendored/packaging/LICENSE.APACHE", "path_type": "hardlink", "sha256": "0d542e0c8804e39aa7f37eb00da5a762149dc682d7829451287e11b938e94594", "sha256_in_prefix": "0d542e0c8804e39aa7f37eb00da5a762149dc682d7829451287e11b938e94594", "size_in_bytes": 10174}, {"_path": "site-packages/setuptools/_vendor/wheel/vendored/packaging/LICENSE.BSD", "path_type": "hardlink", "sha256": "b70e7e9b742f1cc6f948b34c16aa39ffece94196364bc88ff0d2180f0028fac5", "sha256_in_prefix": "b70e7e9b742f1cc6f948b34c16aa39ffece94196364bc88ff0d2180f0028fac5", "size_in_bytes": 1344}, {"_path": "site-packages/setuptools/_vendor/wheel/vendored/packaging/__init__.py", "path_type": "hardlink", "sha256": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "sha256_in_prefix": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "size_in_bytes": 0}, {"_path": "site-packages/setuptools/_vendor/wheel/vendored/packaging/_elffile.py", "path_type": "hardlink", "sha256": "85b98af0e0fa67b7d8ea1c229c7114703d5bcbb73390688d62eed28671449369", "sha256_in_prefix": "85b98af0e0fa67b7d8ea1c229c7114703d5bcbb73390688d62eed28671449369", "size_in_bytes": 3266}, {"_path": "site-packages/setuptools/_vendor/wheel/vendored/packaging/_manylinux.py", "path_type": "hardlink", "sha256": "3fbb1d479ffb5c1634f4b55860f8479b274c2482303d75ac878a2593be14ba3e", "sha256_in_prefix": "3fbb1d479ffb5c1634f4b55860f8479b274c2482303d75ac878a2593be14ba3e", "size_in_bytes": 9588}, {"_path": "site-packages/setuptools/_vendor/wheel/vendored/packaging/_musllinux.py", "path_type": "hardlink", "sha256": "cf5b3c4e8da1434be99ff77e3b68b9ab11b010af1698694bb7777fdba57b35e6", "sha256_in_prefix": "cf5b3c4e8da1434be99ff77e3b68b9ab11b010af1698694bb7777fdba57b35e6", "size_in_bytes": 2674}, {"_path": "site-packages/setuptools/_vendor/wheel/vendored/packaging/_parser.py", "path_type": "hardlink", "sha256": "e2d4f87a64a5daa4da53b553404d576bda358cc3c2b017b3b18071c8d31437eb", "sha256_in_prefix": "e2d4f87a64a5daa4da53b553404d576bda358cc3c2b017b3b18071c8d31437eb", "size_in_bytes": 10347}, {"_path": "site-packages/setuptools/_vendor/wheel/vendored/packaging/_structures.py", "path_type": "hardlink", "sha256": "ab77953666d62461bf4b40e2b7f4b7028f2a42acffe4f6135c500a0597b9cabe", "sha256_in_prefix": "ab77953666d62461bf4b40e2b7f4b7028f2a42acffe4f6135c500a0597b9cabe", "size_in_bytes": 1431}, {"_path": "site-packages/setuptools/_vendor/wheel/vendored/packaging/_tokenizer.py", "path_type": "hardlink", "sha256": "6a50ad6f05e138502614667a050fb0093485a11009db3fb2b087fbfff31327f9", "sha256_in_prefix": "6a50ad6f05e138502614667a050fb0093485a11009db3fb2b087fbfff31327f9", "size_in_bytes": 5292}, {"_path": "site-packages/setuptools/_vendor/wheel/vendored/packaging/markers.py", "path_type": "hardlink", "sha256": "fd348f2350612583bb069f40cd398743122a1c45576938e60e1f46fb0f2accf0", "sha256_in_prefix": "fd348f2350612583bb069f40cd398743122a1c45576938e60e1f46fb0f2accf0", "size_in_bytes": 8232}, {"_path": "site-packages/setuptools/_vendor/wheel/vendored/packaging/requirements.py", "path_type": "hardlink", "sha256": "760a01795a6b3eed9813a43c9c67f038f4e30131db45afd918bc978451259fa4", "sha256_in_prefix": "760a01795a6b3eed9813a43c9c67f038f4e30131db45afd918bc978451259fa4", "size_in_bytes": 2933}, {"_path": "site-packages/setuptools/_vendor/wheel/vendored/packaging/specifiers.py", "path_type": "hardlink", "sha256": "2164add12acb48fef685e5a1002f142f4786bdab3b5c84078ea8958957e63ca1", "sha256_in_prefix": "2164add12acb48fef685e5a1002f142f4786bdab3b5c84078ea8958957e63ca1", "size_in_bytes": 39778}, {"_path": "site-packages/setuptools/_vendor/wheel/vendored/packaging/tags.py", "path_type": "hardlink", "sha256": "7de7475e2387901c4d6535e8b57bfcb973e630553d69ef93281ba38181e281c0", "sha256_in_prefix": "7de7475e2387901c4d6535e8b57bfcb973e630553d69ef93281ba38181e281c0", "size_in_bytes": 18950}, {"_path": "site-packages/setuptools/_vendor/wheel/vendored/packaging/utils.py", "path_type": "hardlink", "sha256": "5e07663f7cb1f7ec101058ceecebcc8fd46311fe49951e4714547af6fed243d1", "sha256_in_prefix": "5e07663f7cb1f7ec101058ceecebcc8fd46311fe49951e4714547af6fed243d1", "size_in_bytes": 5268}, {"_path": "site-packages/setuptools/_vendor/wheel/vendored/packaging/version.py", "path_type": "hardlink", "sha256": "3c525a6190f1060cb191f6211f7490c38a9f13d202096ad39a2b6fab5e32ddbb", "sha256_in_prefix": "3c525a6190f1060cb191f6211f7490c38a9f13d202096ad39a2b6fab5e32ddbb", "size_in_bytes": 16234}, {"_path": "site-packages/setuptools/_vendor/wheel/vendored/vendor.txt", "path_type": "hardlink", "sha256": "67610d8c1d62e69adf7b3f0274cd5276bddce99c6fdab451a253292e60677001", "sha256_in_prefix": "67610d8c1d62e69adf7b3f0274cd5276bddce99c6fdab451a253292e60677001", "size_in_bytes": 16}, {"_path": "site-packages/setuptools/_vendor/wheel/wheelfile.py", "path_type": "hardlink", "sha256": "5120adb4d949c1a7f1b79d5860514a1bb8e7c73f1d7e16f2a8064bea331303db", "sha256_in_prefix": "5120adb4d949c1a7f1b79d5860514a1bb8e7c73f1d7e16f2a8064bea331303db", "size_in_bytes": 8411}, {"_path": "site-packages/setuptools/_vendor/zipp-3.19.2.dist-info/INSTALLER", "path_type": "hardlink", "sha256": "d0edee15f91b406f3f99726e44eb990be6e34fd0345b52b910c568e0eef6a2a8", "sha256_in_prefix": "d0edee15f91b406f3f99726e44eb990be6e34fd0345b52b910c568e0eef6a2a8", "size_in_bytes": 5}, {"_path": "site-packages/setuptools/_vendor/zipp-3.19.2.dist-info/LICENSE", "path_type": "hardlink", "sha256": "86da0f01aeae46348a3c3d465195dc1ceccde79f79e87769a64b8da04b2a4741", "sha256_in_prefix": "86da0f01aeae46348a3c3d465195dc1ceccde79f79e87769a64b8da04b2a4741", "size_in_bytes": 1023}, {"_path": "site-packages/setuptools/_vendor/zipp-3.19.2.dist-info/METADATA", "path_type": "hardlink", "sha256": "508ae4fe43081c64b0b0a2828588b3a8cc3430c6693d1676662569400b0dfdb1", "sha256_in_prefix": "508ae4fe43081c64b0b0a2828588b3a8cc3430c6693d1676662569400b0dfdb1", "size_in_bytes": 3575}, {"_path": "site-packages/setuptools/_vendor/zipp-3.19.2.dist-info/RECORD", "path_type": "hardlink", "sha256": "f316f2e03fd9ade7ebbc0b154706848e2bb8fd568b90935109f0d8e3ce2b9bfe", "sha256_in_prefix": "f316f2e03fd9ade7ebbc0b154706848e2bb8fd568b90935109f0d8e3ce2b9bfe", "size_in_bytes": 1039}, {"_path": "site-packages/setuptools/_vendor/zipp-3.19.2.dist-info/REQUESTED", "path_type": "hardlink", "sha256": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "sha256_in_prefix": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "size_in_bytes": 0}, {"_path": "site-packages/setuptools/_vendor/zipp-3.19.2.dist-info/WHEEL", "path_type": "hardlink", "sha256": "189eedfe4581172c1b6a02b97a8f48a14c0b5baa3239e4ca990fbd8871553714", "sha256_in_prefix": "189eedfe4581172c1b6a02b97a8f48a14c0b5baa3239e4ca990fbd8871553714", "size_in_bytes": 92}, {"_path": "site-packages/setuptools/_vendor/zipp-3.19.2.dist-info/top_level.txt", "path_type": "hardlink", "sha256": "8806dda121df686a817d56f65ee47d26a4901c2a0eb0eb46eb2f42fcb4a9a85c", "sha256_in_prefix": "8806dda121df686a817d56f65ee47d26a4901c2a0eb0eb46eb2f42fcb4a9a85c", "size_in_bytes": 5}, {"_path": "site-packages/setuptools/_vendor/zipp/__init__.py", "path_type": "hardlink", "sha256": "42e235834d06e1f440706b7e1ea6d5d285889264a079d086198b071d8ccd6bc0", "sha256_in_prefix": "42e235834d06e1f440706b7e1ea6d5d285889264a079d086198b071d8ccd6bc0", "size_in_bytes": 13412}, {"_path": "site-packages/setuptools/_vendor/zipp/compat/__init__.py", "path_type": "hardlink", "sha256": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "sha256_in_prefix": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "size_in_bytes": 0}, {"_path": "site-packages/setuptools/_vendor/zipp/compat/py310.py", "path_type": "hardlink", "sha256": "799a645b4cd1b6e9e484487c8e35f780219edb67a6a0a081270ef666de119210", "sha256_in_prefix": "799a645b4cd1b6e9e484487c8e35f780219edb67a6a0a081270ef666de119210", "size_in_bytes": 219}, {"_path": "site-packages/setuptools/_vendor/zipp/glob.py", "path_type": "hardlink", "sha256": "7ad5a99df1284727d4beb52c8bab13886984aef3f07ba1f363aa53f2383f9542", "sha256_in_prefix": "7ad5a99df1284727d4beb52c8bab13886984aef3f07ba1f363aa53f2383f9542", "size_in_bytes": 3082}, {"_path": "site-packages/setuptools/archive_util.py", "path_type": "hardlink", "sha256": "4e5ffae21493b5ce32f31ef16bdf2b15551b1b6e2802ba63ccb0181983f6fec2", "sha256_in_prefix": "4e5ffae21493b5ce32f31ef16bdf2b15551b1b6e2802ba63ccb0181983f6fec2", "size_in_bytes": 7356}, {"_path": "site-packages/setuptools/build_meta.py", "path_type": "hardlink", "sha256": "ddc1c05ae70968b03d0d4e4e7c26ca924b5e4c3890e5b0782e891f4d1320253e", "sha256_in_prefix": "ddc1c05ae70968b03d0d4e4e7c26ca924b5e4c3890e5b0782e891f4d1320253e", "size_in_bytes": 19968}, {"_path": "site-packages/setuptools/cli-32.exe", "path_type": "hardlink", "sha256": "32acc1bc543116cbe2cff10cb867772df2f254ff2634c870aef0b46c4b696fdb", "sha256_in_prefix": "32acc1bc543116cbe2cff10cb867772df2f254ff2634c870aef0b46c4b696fdb", "size_in_bytes": 11776}, {"_path": "site-packages/setuptools/cli-64.exe", "path_type": "hardlink", "sha256": "bbb3de5707629e6a60a0c238cd477b28f07f0066982fda953fa6fcec39073a4a", "sha256_in_prefix": "bbb3de5707629e6a60a0c238cd477b28f07f0066982fda953fa6fcec39073a4a", "size_in_bytes": 14336}, {"_path": "site-packages/setuptools/cli-arm64.exe", "path_type": "hardlink", "sha256": "b9a7d08da880dfac8bcf548eba4b06fb59b6f09b17d33148a0f6618328926c61", "sha256_in_prefix": "b9a7d08da880dfac8bcf548eba4b06fb59b6f09b17d33148a0f6618328926c61", "size_in_bytes": 13824}, {"_path": "site-packages/setuptools/cli.exe", "path_type": "hardlink", "sha256": "32acc1bc543116cbe2cff10cb867772df2f254ff2634c870aef0b46c4b696fdb", "sha256_in_prefix": "32acc1bc543116cbe2cff10cb867772df2f254ff2634c870aef0b46c4b696fdb", "size_in_bytes": 11776}, {"_path": "site-packages/setuptools/command/__init__.py", "path_type": "hardlink", "sha256": "c1d4ab94d4743fa9c2cfdfe816d08088091e14932c65ad633dca574f9ddfd123", "sha256_in_prefix": "c1d4ab94d4743fa9c2cfdfe816d08088091e14932c65ad633dca574f9ddfd123", "size_in_bytes": 803}, {"_path": "site-packages/setuptools/command/_requirestxt.py", "path_type": "hardlink", "sha256": "22d60c4c91a1fe2e53950b2d5ff9c5a29a848640b83c915a7412f665ddd5ebbd", "sha256_in_prefix": "22d60c4c91a1fe2e53950b2d5ff9c5a29a848640b83c915a7412f665ddd5ebbd", "size_in_bytes": 4228}, {"_path": "site-packages/setuptools/command/alias.py", "path_type": "hardlink", "sha256": "ac376b32ddf60d2eaa7f72bbb63659c870ff74c2ab9bbec05dc02dc7e9c14342", "sha256_in_prefix": "ac376b32ddf60d2eaa7f72bbb63659c870ff74c2ab9bbec05dc02dc7e9c14342", "size_in_bytes": 2380}, {"_path": "site-packages/setuptools/command/bdist_egg.py", "path_type": "hardlink", "sha256": "266b4aa086e2c201077090646dcef3c9709970017ce75b7a7a4d7c8267beeb44", "sha256_in_prefix": "266b4aa086e2c201077090646dcef3c9709970017ce75b7a7a4d7c8267beeb44", "size_in_bytes": 16948}, {"_path": "site-packages/setuptools/command/bdist_rpm.py", "path_type": "hardlink", "sha256": "2f2a88e3dc38f122a4d059ae1ec13d30bcd7d52b978cbed830d6d930566a1482", "sha256_in_prefix": "2f2a88e3dc38f122a4d059ae1ec13d30bcd7d52b978cbed830d6d930566a1482", "size_in_bytes": 1435}, {"_path": "site-packages/setuptools/command/bdist_wheel.py", "path_type": "hardlink", "sha256": "4a49d83d5c21ad13df5ee7663bf82fa8d1c71e1cd27d4f1c12616d428990f712", "sha256_in_prefix": "4a49d83d5c21ad13df5ee7663bf82fa8d1c71e1cd27d4f1c12616d428990f712", "size_in_bytes": 22247}, {"_path": "site-packages/setuptools/command/build.py", "path_type": "hardlink", "sha256": "788ed24cc111186644a73935b6f24df29f483a30005cc7062f3963bf69b02373", "sha256_in_prefix": "788ed24cc111186644a73935b6f24df29f483a30005cc7062f3963bf69b02373", "size_in_bytes": 6052}, {"_path": "site-packages/setuptools/command/build_clib.py", "path_type": "hardlink", "sha256": "01b8293c817fdea2fc7d9af724879b23e5874cc4c188c7eb164550cfc2b8d06e", "sha256_in_prefix": "01b8293c817fdea2fc7d9af724879b23e5874cc4c188c7eb164550cfc2b8d06e", "size_in_bytes": 4528}, {"_path": "site-packages/setuptools/command/build_ext.py", "path_type": "hardlink", "sha256": "59d762ea1a38327561f38aaa65436a4ef8ca2ea7a859ee9c6f07493948ad5c27", "sha256_in_prefix": "59d762ea1a38327561f38aaa65436a4ef8ca2ea7a859ee9c6f07493948ad5c27", "size_in_bytes": 18465}, {"_path": "site-packages/setuptools/command/build_py.py", "path_type": "hardlink", "sha256": "0c26e3bc1d7c9242fec542b9aef9739b40bab704de3b1361caf243c716bb7c82", "sha256_in_prefix": "0c26e3bc1d7c9242fec542b9aef9739b40bab704de3b1361caf243c716bb7c82", "size_in_bytes": 15539}, {"_path": "site-packages/setuptools/command/develop.py", "path_type": "hardlink", "sha256": "d5db1bda592370f41002540d55595f3c8254059f5d8b49796ece25f3783dfbd6", "sha256_in_prefix": "d5db1bda592370f41002540d55595f3c8254059f5d8b49796ece25f3783dfbd6", "size_in_bytes": 1610}, {"_path": "site-packages/setuptools/command/dist_info.py", "path_type": "hardlink", "sha256": "1d4ef9da22cb9a660c1dbb03060cf6b9b4639202686ee80ea7c1fbd4bcf30f2b", "sha256_in_prefix": "1d4ef9da22cb9a660c1dbb03060cf6b9b4639202686ee80ea7c1fbd4bcf30f2b", "size_in_bytes": 3450}, {"_path": "site-packages/setuptools/command/easy_install.py", "path_type": "hardlink", "sha256": "5eb379715e7599fcdb083a1aa59ea24fc9c2cda2e9ba677025844a78c1c48c24", "sha256_in_prefix": "5eb379715e7599fcdb083a1aa59ea24fc9c2cda2e9ba677025844a78c1c48c24", "size_in_bytes": 780}, {"_path": "site-packages/setuptools/command/editable_wheel.py", "path_type": "hardlink", "sha256": "317c90c78d60c2edddd6b6984991a0cc2569e64915b6508a7ed64abd399adf06", "sha256_in_prefix": "317c90c78d60c2edddd6b6984991a0cc2569e64915b6508a7ed64abd399adf06", "size_in_bytes": 34836}, {"_path": "site-packages/setuptools/command/egg_info.py", "path_type": "hardlink", "sha256": "197cafab913c86e3b883e143a0dcdfdcc1e37977f1ccfa12d0792f6ed6bfcdcf", "sha256_in_prefix": "197cafab913c86e3b883e143a0dcdfdcc1e37977f1ccfa12d0792f6ed6bfcdcf", "size_in_bytes": 25878}, {"_path": "site-packages/setuptools/command/install.py", "path_type": "hardlink", "sha256": "e31da188d801190ac51172ae3c140cadbf1e712c087d81784d81d940b8cf5408", "sha256_in_prefix": "e31da188d801190ac51172ae3c140cadbf1e712c087d81784d81d940b8cf5408", "size_in_bytes": 5066}, {"_path": "site-packages/setuptools/command/install_egg_info.py", "path_type": "hardlink", "sha256": "dc8f483c21fb0f9f5287ee9a558cfe87ac30cb1abec24c6b2b02a0de70dd26ab", "sha256_in_prefix": "dc8f483c21fb0f9f5287ee9a558cfe87ac30cb1abec24c6b2b02a0de70dd26ab", "size_in_bytes": 2075}, {"_path": "site-packages/setuptools/command/install_lib.py", "path_type": "hardlink", "sha256": "f67d7f53cdde1dc1112ff6bfaeffcb8470a485794b76ac99e12741a30fbda9c1", "sha256_in_prefix": "f67d7f53cdde1dc1112ff6bfaeffcb8470a485794b76ac99e12741a30fbda9c1", "size_in_bytes": 4319}, {"_path": "site-packages/setuptools/command/install_scripts.py", "path_type": "hardlink", "sha256": "2660c69e01f308ed98d63e2668535d1c1fc82e118f864f9be4a871b19c14c224", "sha256_in_prefix": "2660c69e01f308ed98d63e2668535d1c1fc82e118f864f9be4a871b19c14c224", "size_in_bytes": 2490}, {"_path": "site-packages/setuptools/command/launcher manifest.xml", "path_type": "hardlink", "sha256": "c652db8d6ac1d35b4a0b4fa195590e2a48923dbccc9a5d9e38fb49fee7029db1", "sha256_in_prefix": "c652db8d6ac1d35b4a0b4fa195590e2a48923dbccc9a5d9e38fb49fee7029db1", "size_in_bytes": 628}, {"_path": "site-packages/setuptools/command/rotate.py", "path_type": "hardlink", "sha256": "5cd77f04410e5802475b515c2d3314596978401eb302e93b6fc556420dc51e8b", "sha256_in_prefix": "5cd77f04410e5802475b515c2d3314596978401eb302e93b6fc556420dc51e8b", "size_in_bytes": 2187}, {"_path": "site-packages/setuptools/command/saveopts.py", "path_type": "hardlink", "sha256": "369d0f55bed20fba136eef59f6ca2c4bb0fe0a4908914ef1e2096ee44b35b630", "sha256_in_prefix": "369d0f55bed20fba136eef59f6ca2c4bb0fe0a4908914ef1e2096ee44b35b630", "size_in_bytes": 692}, {"_path": "site-packages/setuptools/command/sdist.py", "path_type": "hardlink", "sha256": "e59880f327dd35f97e90b4e77cf021b75c8a9d2d68fc7ad216986c25dfbd7e85", "sha256_in_prefix": "e59880f327dd35f97e90b4e77cf021b75c8a9d2d68fc7ad216986c25dfbd7e85", "size_in_bytes": 7369}, {"_path": "site-packages/setuptools/command/setopt.py", "path_type": "hardlink", "sha256": "c59176442738001bc4f5e1c7033179d3e7e4420ddabbc7dc45455519de7c9375", "sha256_in_prefix": "c59176442738001bc4f5e1c7033179d3e7e4420ddabbc7dc45455519de7c9375", "size_in_bytes": 5100}, {"_path": "site-packages/setuptools/command/test.py", "path_type": "hardlink", "sha256": "93bc5cabb0fb6c47a18316ab6f0f9d5b702d98664e46acfc1e3291e85189de39", "sha256_in_prefix": "93bc5cabb0fb6c47a18316ab6f0f9d5b702d98664e46acfc1e3291e85189de39", "size_in_bytes": 1343}, {"_path": "site-packages/setuptools/compat/__init__.py", "path_type": "hardlink", "sha256": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "sha256_in_prefix": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "size_in_bytes": 0}, {"_path": "site-packages/setuptools/compat/py310.py", "path_type": "hardlink", "sha256": "2708d067770d4e2cdfa432cd97d18bb141b306bfad56530fc663185434e58622", "sha256_in_prefix": "2708d067770d4e2cdfa432cd97d18bb141b306bfad56530fc663185434e58622", "size_in_bytes": 344}, {"_path": "site-packages/setuptools/compat/py311.py", "path_type": "hardlink", "sha256": "7bab49005c1910ff36866301975d0761e4b2a5e968fd38b6c138ca65528bc0e1", "sha256_in_prefix": "7bab49005c1910ff36866301975d0761e4b2a5e968fd38b6c138ca65528bc0e1", "size_in_bytes": 790}, {"_path": "site-packages/setuptools/compat/py312.py", "path_type": "hardlink", "sha256": "bd8295b5dadd393b0efd1f747499045ec1707cc245b881497e5848807ae327e6", "sha256_in_prefix": "bd8295b5dadd393b0efd1f747499045ec1707cc245b881497e5848807ae327e6", "size_in_bytes": 366}, {"_path": "site-packages/setuptools/compat/py39.py", "path_type": "hardlink", "sha256": "04932d9e47dcab24df71caa3610c5fa11b54da74e759a104481564b214e25ea6", "sha256_in_prefix": "04932d9e47dcab24df71caa3610c5fa11b54da74e759a104481564b214e25ea6", "size_in_bytes": 493}, {"_path": "site-packages/setuptools/config/NOTICE", "path_type": "hardlink", "sha256": "2dddf08818297a3b89d43d95ff659d8da85741108c9136dfa3a4d856c0623bd8", "sha256_in_prefix": "2dddf08818297a3b89d43d95ff659d8da85741108c9136dfa3a4d856c0623bd8", "size_in_bytes": 493}, {"_path": "site-packages/setuptools/config/__init__.py", "path_type": "hardlink", "sha256": "6a23e72fd0499f53ba31f9ae357ca7f16d8ba7cbbdaa2cd156ac0f88e74f2236", "sha256_in_prefix": "6a23e72fd0499f53ba31f9ae357ca7f16d8ba7cbbdaa2cd156ac0f88e74f2236", "size_in_bytes": 1499}, {"_path": "site-packages/setuptools/config/_apply_pyprojecttoml.py", "path_type": "hardlink", "sha256": "494c93c3b0366ed675941b9628de68e36f838b2bfde5e193898277ad93a71927", "sha256_in_prefix": "494c93c3b0366ed675941b9628de68e36f838b2bfde5e193898277ad93a71927", "size_in_bytes": 19120}, {"_path": "site-packages/setuptools/config/_validate_pyproject/NOTICE", "path_type": "hardlink", "sha256": "5d300dbfa643138b013b75ac9caeee591f951b8b0ee24288c34ccd926c4780c8", "sha256_in_prefix": "5d300dbfa643138b013b75ac9caeee591f951b8b0ee24288c34ccd926c4780c8", "size_in_bytes": 18737}, {"_path": "site-packages/setuptools/config/_validate_pyproject/__init__.py", "path_type": "hardlink", "sha256": "767a7a4fb78f3f5479cf83ae0bb15dd9d905948aed21f8b351fbe91893fa9f3d", "sha256_in_prefix": "767a7a4fb78f3f5479cf83ae0bb15dd9d905948aed21f8b351fbe91893fa9f3d", "size_in_bytes": 1042}, {"_path": "site-packages/setuptools/config/_validate_pyproject/error_reporting.py", "path_type": "hardlink", "sha256": "99e95d0fb9c141da25421bc6fb8debd547be814d67ece440251f3abe1dd1aef9", "sha256_in_prefix": "99e95d0fb9c141da25421bc6fb8debd547be814d67ece440251f3abe1dd1aef9", "size_in_bytes": 11813}, {"_path": "site-packages/setuptools/config/_validate_pyproject/extra_validations.py", "path_type": "hardlink", "sha256": "f86506e52fbe8a363c59f5db7573e81e5eb2c06b32105f5db43a5e9d2a093c78", "sha256_in_prefix": "f86506e52fbe8a363c59f5db7573e81e5eb2c06b32105f5db43a5e9d2a093c78", "size_in_bytes": 2858}, {"_path": "site-packages/setuptools/config/_validate_pyproject/fastjsonschema_exceptions.py", "path_type": "hardlink", "sha256": "c3be3d260a8a8bc72504570e6dd71b655aac985e2827f401ca16754866d414dc", "sha256_in_prefix": "c3be3d260a8a8bc72504570e6dd71b655aac985e2827f401ca16754866d414dc", "size_in_bytes": 1612}, {"_path": "site-packages/setuptools/config/_validate_pyproject/fastjsonschema_validations.py", "path_type": "hardlink", "sha256": "162843e5970cea9efb04f674e021aa877044c153683cc289649032b89a64014d", "sha256_in_prefix": "162843e5970cea9efb04f674e021aa877044c153683cc289649032b89a64014d", "size_in_bytes": 354682}, {"_path": "site-packages/setuptools/config/_validate_pyproject/formats.py", "path_type": "hardlink", "sha256": "4c44e890904af618e5f9c560d6896ca23909c0bc5f3fbfdc860250366cc007df", "sha256_in_prefix": "4c44e890904af618e5f9c560d6896ca23909c0bc5f3fbfdc860250366cc007df", "size_in_bytes": 13564}, {"_path": "site-packages/setuptools/config/distutils.schema.json", "path_type": "hardlink", "sha256": "4dca77da44678703911b0ffda7a1848b4f258f6875e6d411cce6016f31a67015", "sha256_in_prefix": "4dca77da44678703911b0ffda7a1848b4f258f6875e6d411cce6016f31a67015", "size_in_bytes": 972}, {"_path": "site-packages/setuptools/config/expand.py", "path_type": "hardlink", "sha256": "24d024b510accb2441fab42875b3e70ae7262d6a9c62fcc20c2f046e7d99ef13", "sha256_in_prefix": "24d024b510accb2441fab42875b3e70ae7262d6a9c62fcc20c2f046e7d99ef13", "size_in_bytes": 16041}, {"_path": "site-packages/setuptools/config/pyprojecttoml.py", "path_type": "hardlink", "sha256": "60cbb93dd6c9248e5ace9ea447f6e833599f95fe67a8e03e227178b3a2e72e0c", "sha256_in_prefix": "60cbb93dd6c9248e5ace9ea447f6e833599f95fe67a8e03e227178b3a2e72e0c", "size_in_bytes": 18320}, {"_path": "site-packages/setuptools/config/setupcfg.py", "path_type": "hardlink", "sha256": "5590e4c04ec362fe3949b69d243f02c0aac3b625ef8e09652fc3d84afa110b28", "sha256_in_prefix": "5590e4c04ec362fe3949b69d243f02c0aac3b625ef8e09652fc3d84afa110b28", "size_in_bytes": 26575}, {"_path": "site-packages/setuptools/config/setuptools.schema.json", "path_type": "hardlink", "sha256": "759051b921276646ada1596dd645701bca1c4de45d3bb043d31bce58a1f9e0f6", "sha256_in_prefix": "759051b921276646ada1596dd645701bca1c4de45d3bb043d31bce58a1f9e0f6", "size_in_bytes": 16071}, {"_path": "site-packages/setuptools/depends.py", "path_type": "hardlink", "sha256": "8ca61f8e6b7fd9941856085bf0bf5b53b2c9e9eac7279cdef8afdf295d413179", "sha256_in_prefix": "8ca61f8e6b7fd9941856085bf0bf5b53b2c9e9eac7279cdef8afdf295d413179", "size_in_bytes": 5965}, {"_path": "site-packages/setuptools/discovery.py", "path_type": "hardlink", "sha256": "fb8d9cdd7870ce47e874328a3f9d02d98073af5d5f9dc020994cc174145bd3e4", "sha256_in_prefix": "fb8d9cdd7870ce47e874328a3f9d02d98073af5d5f9dc020994cc174145bd3e4", "size_in_bytes": 21258}, {"_path": "site-packages/setuptools/dist.py", "path_type": "hardlink", "sha256": "8eb2c67fee2e7598e867250bb86ad710fce014356ad421c27c6b2a8d3ab9d868", "sha256_in_prefix": "8eb2c67fee2e7598e867250bb86ad710fce014356ad421c27c6b2a8d3ab9d868", "size_in_bytes": 44887}, {"_path": "site-packages/setuptools/errors.py", "path_type": "hardlink", "sha256": "818db1d8f21a220cb4d724403510becdc0b0c430aa09272026808e6457b4ca2a", "sha256_in_prefix": "818db1d8f21a220cb4d724403510becdc0b0c430aa09272026808e6457b4ca2a", "size_in_bytes": 3024}, {"_path": "site-packages/setuptools/extension.py", "path_type": "hardlink", "sha256": "2829eff69ded826d1956ab60138e757f220bb26e210b2bce894b4ebbbf3b0fee", "sha256_in_prefix": "2829eff69ded826d1956ab60138e757f220bb26e210b2bce894b4ebbbf3b0fee", "size_in_bytes": 6683}, {"_path": "site-packages/setuptools/glob.py", "path_type": "hardlink", "sha256": "002fc1df70d8f20f821c42f10ec26bb7016ba62b9c48066c6a43c5752390ce17", "sha256_in_prefix": "002fc1df70d8f20f821c42f10ec26bb7016ba62b9c48066c6a43c5752390ce17", "size_in_bytes": 6062}, {"_path": "site-packages/setuptools/gui-32.exe", "path_type": "hardlink", "sha256": "85dae1e95d77845f2cb59bcac3d4afe74bbe4c91a9bcc5bf4a71cd43104dbe7c", "sha256_in_prefix": "85dae1e95d77845f2cb59bcac3d4afe74bbe4c91a9bcc5bf4a71cd43104dbe7c", "size_in_bytes": 11776}, {"_path": "site-packages/setuptools/gui-64.exe", "path_type": "hardlink", "sha256": "3471b6140eadc6412277dbbefe3fef8c345a0f1a59776086b80a3618c3a83e3b", "sha256_in_prefix": "3471b6140eadc6412277dbbefe3fef8c345a0f1a59776086b80a3618c3a83e3b", "size_in_bytes": 14336}, {"_path": "site-packages/setuptools/gui-arm64.exe", "path_type": "hardlink", "sha256": "e694f4743405c8b5926ff457db6fe7f1a12dec7c16a9c3864784d3f4e07ae097", "sha256_in_prefix": "e694f4743405c8b5926ff457db6fe7f1a12dec7c16a9c3864784d3f4e07ae097", "size_in_bytes": 13824}, {"_path": "site-packages/setuptools/gui.exe", "path_type": "hardlink", "sha256": "85dae1e95d77845f2cb59bcac3d4afe74bbe4c91a9bcc5bf4a71cd43104dbe7c", "sha256_in_prefix": "85dae1e95d77845f2cb59bcac3d4afe74bbe4c91a9bcc5bf4a71cd43104dbe7c", "size_in_bytes": 11776}, {"_path": "site-packages/setuptools/installer.py", "path_type": "hardlink", "sha256": "bde8a8f8f0c2b1e58dd09d44ff5823bd52e470884fa502e510aa52680d375969", "sha256_in_prefix": "bde8a8f8f0c2b1e58dd09d44ff5823bd52e470884fa502e510aa52680d375969", "size_in_bytes": 5093}, {"_path": "site-packages/setuptools/launch.py", "path_type": "hardlink", "sha256": "2016f9944bfaf42cae67d7b022b98a957875e7891d2e63f6f4b29f4cc9318a61", "sha256_in_prefix": "2016f9944bfaf42cae67d7b022b98a957875e7891d2e63f6f4b29f4cc9318a61", "size_in_bytes": 820}, {"_path": "site-packages/setuptools/logging.py", "path_type": "hardlink", "sha256": "5b5ea21c9d477025d8434471cab11f27cdc54f8d7be6d0ada1883e13ab92a552", "sha256_in_prefix": "5b5ea21c9d477025d8434471cab11f27cdc54f8d7be6d0ada1883e13ab92a552", "size_in_bytes": 1261}, {"_path": "site-packages/setuptools/modified.py", "path_type": "hardlink", "sha256": "6706df05f0853fcf25b6f6effdd243cfeb752ec4239ccf895298199e74198e33", "sha256_in_prefix": "6706df05f0853fcf25b6f6effdd243cfeb752ec4239ccf895298199e74198e33", "size_in_bytes": 568}, {"_path": "site-packages/setuptools/monkey.py", "path_type": "hardlink", "sha256": "1703169769f5bf66c76dea81cbea3d83cc9435a0246056eccc26d77bd77965af", "sha256_in_prefix": "1703169769f5bf66c76dea81cbea3d83cc9435a0246056eccc26d77bd77965af", "size_in_bytes": 3717}, {"_path": "site-packages/setuptools/msvc.py", "path_type": "hardlink", "sha256": "be6334a8be2b233aed0fda626bd644c2da99e0b6dbae02f4754d0400d558466f", "sha256_in_prefix": "be6334a8be2b233aed0fda626bd644c2da99e0b6dbae02f4754d0400d558466f", "size_in_bytes": 41631}, {"_path": "site-packages/setuptools/namespaces.py", "path_type": "hardlink", "sha256": "d861aa618d4134312132d05cd6b1d3bfb92582635545d92c25e5be2f57fefb2b", "sha256_in_prefix": "d861aa618d4134312132d05cd6b1d3bfb92582635545d92c25e5be2f57fefb2b", "size_in_bytes": 3171}, {"_path": "site-packages/setuptools/script (dev).tmpl", "path_type": "hardlink", "sha256": "454cd0cc2414697b7074bb581d661b21098e6844b906baaad45bd403fb6efb92", "sha256_in_prefix": "454cd0cc2414697b7074bb581d661b21098e6844b906baaad45bd403fb6efb92", "size_in_bytes": 218}, {"_path": "site-packages/setuptools/script.tmpl", "path_type": "hardlink", "sha256": "5864ede6989eccedbb73e0dbc7a9794384f715fdb4039cfbf3bda1bf76808586", "sha256_in_prefix": "5864ede6989eccedbb73e0dbc7a9794384f715fdb4039cfbf3bda1bf76808586", "size_in_bytes": 138}, {"_path": "site-packages/setuptools/tests/__init__.py", "path_type": "hardlink", "sha256": "02705f96cda225b4c343398c29e2d1b7ef65c6168e1d454e644817bfcf54c2fb", "sha256_in_prefix": "02705f96cda225b4c343398c29e2d1b7ef65c6168e1d454e644817bfcf54c2fb", "size_in_bytes": 335}, {"_path": "site-packages/setuptools/tests/compat/__init__.py", "path_type": "hardlink", "sha256": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "sha256_in_prefix": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "size_in_bytes": 0}, {"_path": "site-packages/setuptools/tests/compat/py39.py", "path_type": "hardlink", "sha256": "794cbbfc5fba2914ce20a97ebdeb2152ee88d0475349d059321d04574959d7e8", "sha256_in_prefix": "794cbbfc5fba2914ce20a97ebdeb2152ee88d0475349d059321d04574959d7e8", "size_in_bytes": 135}, {"_path": "site-packages/setuptools/tests/config/__init__.py", "path_type": "hardlink", "sha256": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "sha256_in_prefix": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "size_in_bytes": 0}, {"_path": "site-packages/setuptools/tests/config/downloads/__init__.py", "path_type": "hardlink", "sha256": "f62c670c47722ff6ab29b5337ee8897ed023f5b1b12b3f0cf5a94e159323c7d6", "sha256_in_prefix": "f62c670c47722ff6ab29b5337ee8897ed023f5b1b12b3f0cf5a94e159323c7d6", "size_in_bytes": 1827}, {"_path": "site-packages/setuptools/tests/config/downloads/preload.py", "path_type": "hardlink", "sha256": "b081866696377263293308896186181c6da27d9264bc9804a2d445b62ba55752", "sha256_in_prefix": "b081866696377263293308896186181c6da27d9264bc9804a2d445b62ba55752", "size_in_bytes": 450}, {"_path": "site-packages/setuptools/tests/config/setupcfg_examples.txt", "path_type": "hardlink", "sha256": "7006d5bc26e4159b9350beb1451cd182ac81d2b2ef2537efc370f7d20968d8e1", "sha256_in_prefix": "7006d5bc26e4159b9350beb1451cd182ac81d2b2ef2537efc370f7d20968d8e1", "size_in_bytes": 1912}, {"_path": "site-packages/setuptools/tests/config/test_apply_pyprojecttoml.py", "path_type": "hardlink", "sha256": "97a9c4e1df162d4fde49646273b552a2a78abfd062ec26461dc12e0767a1936c", "sha256_in_prefix": "97a9c4e1df162d4fde49646273b552a2a78abfd062ec26461dc12e0767a1936c", "size_in_bytes": 28807}, {"_path": "site-packages/setuptools/tests/config/test_expand.py", "path_type": "hardlink", "sha256": "4b4a13e89be003fa2e8d1e184b8454b9fe6098eb75093415eba4500f357cc5de", "sha256_in_prefix": "4b4a13e89be003fa2e8d1e184b8454b9fe6098eb75093415eba4500f357cc5de", "size_in_bytes": 8933}, {"_path": "site-packages/setuptools/tests/config/test_pyprojecttoml.py", "path_type": "hardlink", "sha256": "d0b79f4a58d4840e8caad279015ccb8689aa65c62214a76eff57240de313d4b6", "sha256_in_prefix": "d0b79f4a58d4840e8caad279015ccb8689aa65c62214a76eff57240de313d4b6", "size_in_bytes": 12406}, {"_path": "site-packages/setuptools/tests/config/test_pyprojecttoml_dynamic_deps.py", "path_type": "hardlink", "sha256": "f56ef7fb22e16499af0a23b8ad3890a01a594f9c0d03dd176dde67d870ac85de", "sha256_in_prefix": "f56ef7fb22e16499af0a23b8ad3890a01a594f9c0d03dd176dde67d870ac85de", "size_in_bytes": 3271}, {"_path": "site-packages/setuptools/tests/config/test_setupcfg.py", "path_type": "hardlink", "sha256": "66f37e3bed838289f569da7aa0cea297c2567604fdcb5f7a7d1bea11253910b2", "sha256_in_prefix": "66f37e3bed838289f569da7aa0cea297c2567604fdcb5f7a7d1bea11253910b2", "size_in_bytes": 33427}, {"_path": "site-packages/setuptools/tests/contexts.py", "path_type": "hardlink", "sha256": "3b375f7362b2745f71f7038352c76e9fcd349b22ee3f6eeec68793d3a19b93b3", "sha256_in_prefix": "3b375f7362b2745f71f7038352c76e9fcd349b22ee3f6eeec68793d3a19b93b3", "size_in_bytes": 3166}, {"_path": "site-packages/setuptools/tests/environment.py", "path_type": "hardlink", "sha256": "f79fd4b536918aebf0602f5e5ca1076e7d36903b59cacbd9ab75192663d48f52", "sha256_in_prefix": "f79fd4b536918aebf0602f5e5ca1076e7d36903b59cacbd9ab75192663d48f52", "size_in_bytes": 3102}, {"_path": "site-packages/setuptools/tests/fixtures.py", "path_type": "hardlink", "sha256": "68f7b074f1e5287440b0ca3d1fcdbc737642f25dce26a8327d1630a4b06f8029", "sha256_in_prefix": "68f7b074f1e5287440b0ca3d1fcdbc737642f25dce26a8327d1630a4b06f8029", "size_in_bytes": 11705}, {"_path": "site-packages/setuptools/tests/indexes/test_links_priority/external.html", "path_type": "hardlink", "sha256": "78bf5eb8eb84f7724a65daa55f104e9476cac08b8db8876aec6051a6c68f31c5", "sha256_in_prefix": "78bf5eb8eb84f7724a65daa55f104e9476cac08b8db8876aec6051a6c68f31c5", "size_in_bytes": 92}, {"_path": "site-packages/setuptools/tests/indexes/test_links_priority/simple/foobar/index.html", "path_type": "hardlink", "sha256": "0c3f932abed4538cc08c71f3e157b1603352033476ee57af4a1d5cfa4dd974b1", "sha256_in_prefix": "0c3f932abed4538cc08c71f3e157b1603352033476ee57af4a1d5cfa4dd974b1", "size_in_bytes": 174}, {"_path": "site-packages/setuptools/tests/integration/__init__.py", "path_type": "hardlink", "sha256": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "sha256_in_prefix": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "size_in_bytes": 0}, {"_path": "site-packages/setuptools/tests/integration/helpers.py", "path_type": "hardlink", "sha256": "dcf1dc4bd48203e7f05499943f669de4d40eb6d240113239367a1cff1ae83b99", "sha256_in_prefix": "dcf1dc4bd48203e7f05499943f669de4d40eb6d240113239367a1cff1ae83b99", "size_in_bytes": 2522}, {"_path": "site-packages/setuptools/tests/integration/test_pbr.py", "path_type": "hardlink", "sha256": "d9e2ae92514d9a99c1800fde12160f06beab2cb1b65e6e0c63a3e5726cd98065", "sha256_in_prefix": "d9e2ae92514d9a99c1800fde12160f06beab2cb1b65e6e0c63a3e5726cd98065", "size_in_bytes": 432}, {"_path": "site-packages/setuptools/tests/integration/test_pip_install_sdist.py", "path_type": "hardlink", "sha256": "4856efb9817f843cede8eb6c4391a314d9f19a827f78495fbe962c8b2c8627e8", "sha256_in_prefix": "4856efb9817f843cede8eb6c4391a314d9f19a827f78495fbe962c8b2c8627e8", "size_in_bytes": 8256}, {"_path": "site-packages/setuptools/tests/mod_with_constant.py", "path_type": "hardlink", "sha256": "5ff2a3f34339e70d6d990e1feee658f7565300ba3884a553e841f1818a1c50c4", "sha256_in_prefix": "5ff2a3f34339e70d6d990e1feee658f7565300ba3884a553e841f1818a1c50c4", "size_in_bytes": 22}, {"_path": "site-packages/setuptools/tests/namespaces.py", "path_type": "hardlink", "sha256": "1cf708de74793021565e96800c82757f02b1ca671080192ec3cec87393d44804", "sha256_in_prefix": "1cf708de74793021565e96800c82757f02b1ca671080192ec3cec87393d44804", "size_in_bytes": 2774}, {"_path": "site-packages/setuptools/tests/script-with-bom.py", "path_type": "hardlink", "sha256": "851460222cc450b1a21bf653368318e3a1e12a1c6959fcb9146703e906e1e5f7", "sha256_in_prefix": "851460222cc450b1a21bf653368318e3a1e12a1c6959fcb9146703e906e1e5f7", "size_in_bytes": 18}, {"_path": "site-packages/setuptools/tests/test_archive_util.py", "path_type": "hardlink", "sha256": "6eeb8a758f17916dba3dedc8280a014461c6d0c0ee9a7b8da0f8365ac010cc88", "sha256_in_prefix": "6eeb8a758f17916dba3dedc8280a014461c6d0c0ee9a7b8da0f8365ac010cc88", "size_in_bytes": 845}, {"_path": "site-packages/setuptools/tests/test_bdist_deprecations.py", "path_type": "hardlink", "sha256": "ef95eade0627efd2c8232bac125b5b1da9f46c4800b767bf09a2fb28b4bcf8a4", "sha256_in_prefix": "ef95eade0627efd2c8232bac125b5b1da9f46c4800b767bf09a2fb28b4bcf8a4", "size_in_bytes": 775}, {"_path": "site-packages/setuptools/tests/test_bdist_egg.py", "path_type": "hardlink", "sha256": "e8f6983751772436c8875b8ad2eaefef2245731f7ccf9767f52389f0cbfdd65f", "sha256_in_prefix": "e8f6983751772436c8875b8ad2eaefef2245731f7ccf9767f52389f0cbfdd65f", "size_in_bytes": 1957}, {"_path": "site-packages/setuptools/tests/test_bdist_wheel.py", "path_type": "hardlink", "sha256": "759f5aece4ff53246f2e7a028b62861086edce11108ccdd8bad60c03a6427b3b", "sha256_in_prefix": "759f5aece4ff53246f2e7a028b62861086edce11108ccdd8bad60c03a6427b3b", "size_in_bytes": 23083}, {"_path": "site-packages/setuptools/tests/test_build.py", "path_type": "hardlink", "sha256": "c0980ccf68701c00dc2c583e9d7af045586eb3b8639807841a0ae9210c021a70", "sha256_in_prefix": "c0980ccf68701c00dc2c583e9d7af045586eb3b8639807841a0ae9210c021a70", "size_in_bytes": 798}, {"_path": "site-packages/setuptools/tests/test_build_clib.py", "path_type": "hardlink", "sha256": "6d7e755d101fe2e3bb22e1c5a6378f9e82bc984ef837682ca1e12a17ea1f830b", "sha256_in_prefix": "6d7e755d101fe2e3bb22e1c5a6378f9e82bc984ef837682ca1e12a17ea1f830b", "size_in_bytes": 3123}, {"_path": "site-packages/setuptools/tests/test_build_ext.py", "path_type": "hardlink", "sha256": "7b8652c6c60f079cead4a4aa184b804d9d2dd0f250ccc8638e4289fa12237207", "sha256_in_prefix": "7b8652c6c60f079cead4a4aa184b804d9d2dd0f250ccc8638e4289fa12237207", "size_in_bytes": 10099}, {"_path": "site-packages/setuptools/tests/test_build_meta.py", "path_type": "hardlink", "sha256": "92f8b4067e29f4305599edf3c9642c9f742007da0f76af12d796a08f59baf4bd", "sha256_in_prefix": "92f8b4067e29f4305599edf3c9642c9f742007da0f76af12d796a08f59baf4bd", "size_in_bytes": 33289}, {"_path": "site-packages/setuptools/tests/test_build_py.py", "path_type": "hardlink", "sha256": "8286cc13f0afcdfe94831abbd2259f5de91bff1cb24fad648708c5abcce4c1fc", "sha256_in_prefix": "8286cc13f0afcdfe94831abbd2259f5de91bff1cb24fad648708c5abcce4c1fc", "size_in_bytes": 14186}, {"_path": "site-packages/setuptools/tests/test_config_discovery.py", "path_type": "hardlink", "sha256": "16a57e94eb64a9a23e6b2cd4db3a1c49d0f94da4408026678b13438a5280e854", "sha256_in_prefix": "16a57e94eb64a9a23e6b2cd4db3a1c49d0f94da4408026678b13438a5280e854", "size_in_bytes": 22580}, {"_path": "site-packages/setuptools/tests/test_core_metadata.py", "path_type": "hardlink", "sha256": "bdb549e7f2ecc7f86c3bf19d07a9d01172518c0db2771ebfa926ebe4ba617800", "sha256_in_prefix": "bdb549e7f2ecc7f86c3bf19d07a9d01172518c0db2771ebfa926ebe4ba617800", "size_in_bytes": 20881}, {"_path": "site-packages/setuptools/tests/test_depends.py", "path_type": "hardlink", "sha256": "c90057a106cd425262b7a99b455a33e816f9e777f7b0daead369598a6373e576", "sha256_in_prefix": "c90057a106cd425262b7a99b455a33e816f9e777f7b0daead369598a6373e576", "size_in_bytes": 424}, {"_path": "site-packages/setuptools/tests/test_develop.py", "path_type": "hardlink", "sha256": "30760bfd80ea34c5398e12a48ec05418a3060a4aef6bc08547c75173a92460a1", "sha256_in_prefix": "30760bfd80ea34c5398e12a48ec05418a3060a4aef6bc08547c75173a92460a1", "size_in_bytes": 3072}, {"_path": "site-packages/setuptools/tests/test_dist.py", "path_type": "hardlink", "sha256": "fc862578747d615ab357e9cbabf26a4aea7a0cd79474fcee434117869d34f549", "sha256_in_prefix": "fc862578747d615ab357e9cbabf26a4aea7a0cd79474fcee434117869d34f549", "size_in_bytes": 8893}, {"_path": "site-packages/setuptools/tests/test_dist_info.py", "path_type": "hardlink", "sha256": "17fc535dce531a18e2ba3b46ba416deb036cb6aa535bbb1542d5272f5217ef2a", "sha256_in_prefix": "17fc535dce531a18e2ba3b46ba416deb036cb6aa535bbb1542d5272f5217ef2a", "size_in_bytes": 4988}, {"_path": "site-packages/setuptools/tests/test_distutils_adoption.py", "path_type": "hardlink", "sha256": "fdeca7ace7f212a5c51268d4261ce97bc1973f24d43ef35239bb38a80026072f", "sha256_in_prefix": "fdeca7ace7f212a5c51268d4261ce97bc1973f24d43ef35239bb38a80026072f", "size_in_bytes": 5987}, {"_path": "site-packages/setuptools/tests/test_editable_install.py", "path_type": "hardlink", "sha256": "4e0e24ba7bf06280cb62c7ef4a48209de520a4a41f7ab503c77104bef75f9b01", "sha256_in_prefix": "4e0e24ba7bf06280cb62c7ef4a48209de520a4a41f7ab503c77104bef75f9b01", "size_in_bytes": 42619}, {"_path": "site-packages/setuptools/tests/test_egg_info.py", "path_type": "hardlink", "sha256": "47b9d3dbb621573f684ae0f28a60067ab5a09646d15a23123c1b391737129c13", "sha256_in_prefix": "47b9d3dbb621573f684ae0f28a60067ab5a09646d15a23123c1b391737129c13", "size_in_bytes": 44941}, {"_path": "site-packages/setuptools/tests/test_extern.py", "path_type": "hardlink", "sha256": "ae9294ea809c92cba62f07f94de2a50e5b854344d47db3f04cb41ba71705ac25", "sha256_in_prefix": "ae9294ea809c92cba62f07f94de2a50e5b854344d47db3f04cb41ba71705ac25", "size_in_bytes": 296}, {"_path": "site-packages/setuptools/tests/test_find_packages.py", "path_type": "hardlink", "sha256": "0932c0713cd619604b09c776680b14564bcede26eb96a7b114174328e58fa2af", "sha256_in_prefix": "0932c0713cd619604b09c776680b14564bcede26eb96a7b114174328e58fa2af", "size_in_bytes": 7819}, {"_path": "site-packages/setuptools/tests/test_find_py_modules.py", "path_type": "hardlink", "sha256": "cd08ee8481b94d03764893e2c7d011a380cbff0f382e7f10b070d48e36ebb404", "sha256_in_prefix": "cd08ee8481b94d03764893e2c7d011a380cbff0f382e7f10b070d48e36ebb404", "size_in_bytes": 2404}, {"_path": "site-packages/setuptools/tests/test_glob.py", "path_type": "hardlink", "sha256": "3f726fa47fa45d0e01677cef445fb32b13a0c325b3c08690233d161ddc52d249", "sha256_in_prefix": "3f726fa47fa45d0e01677cef445fb32b13a0c325b3c08690233d161ddc52d249", "size_in_bytes": 887}, {"_path": "site-packages/setuptools/tests/test_install_scripts.py", "path_type": "hardlink", "sha256": "b1c22b27a6bfb2c2aa838bc804d6948e600a1c460b51467d58a9cf78a9c4ea07", "sha256_in_prefix": "b1c22b27a6bfb2c2aa838bc804d6948e600a1c460b51467d58a9cf78a9c4ea07", "size_in_bytes": 3433}, {"_path": "site-packages/setuptools/tests/test_logging.py", "path_type": "hardlink", "sha256": "ce51390e595dba40bb25ce7814dbc357feeec7712b024adfacde424ac9cd3944", "sha256_in_prefix": "ce51390e595dba40bb25ce7814dbc357feeec7712b024adfacde424ac9cd3944", "size_in_bytes": 2099}, {"_path": "site-packages/setuptools/tests/test_manifest.py", "path_type": "hardlink", "sha256": "78c83ae69200e760e2cc1ea6a64b5253e6fc0a3c1a3424b931280bfd5d4bac52", "sha256_in_prefix": "78c83ae69200e760e2cc1ea6a64b5253e6fc0a3c1a3424b931280bfd5d4bac52", "size_in_bytes": 18562}, {"_path": "site-packages/setuptools/tests/test_namespaces.py", "path_type": "hardlink", "sha256": "63abada1ee4f1c7a8bfc39606b0a81f45f17a6c5033efbf0d6c40c7a72b4e1ed", "sha256_in_prefix": "63abada1ee4f1c7a8bfc39606b0a81f45f17a6c5033efbf0d6c40c7a72b4e1ed", "size_in_bytes": 4515}, {"_path": "site-packages/setuptools/tests/test_scripts.py", "path_type": "hardlink", "sha256": "feb6b9d3ac9017b9fbd913940dccf6af7093b06b1ac0ed66fb5a2a03d11009fc", "sha256_in_prefix": "feb6b9d3ac9017b9fbd913940dccf6af7093b06b1ac0ed66fb5a2a03d11009fc", "size_in_bytes": 379}, {"_path": "site-packages/setuptools/tests/test_sdist.py", "path_type": "hardlink", "sha256": "4582ef3dafe77f20b5666a229f3a8ccc9ca74c31b846d3d80b5f7fd0b53aa6fb", "sha256_in_prefix": "4582ef3dafe77f20b5666a229f3a8ccc9ca74c31b846d3d80b5f7fd0b53aa6fb", "size_in_bytes": 32872}, {"_path": "site-packages/setuptools/tests/test_setopt.py", "path_type": "hardlink", "sha256": "dd5c713380137cff8fe001a70e3a160a71ebe7e8bd0921104c5614d7e1539ef2", "sha256_in_prefix": "dd5c713380137cff8fe001a70e3a160a71ebe7e8bd0921104c5614d7e1539ef2", "size_in_bytes": 1365}, {"_path": "site-packages/setuptools/tests/test_setuptools.py", "path_type": "hardlink", "sha256": "fde221a8a7f8e7e3ad1eac517f6d0a9dd39926525d4b43ee14b5c13b733e2cdf", "sha256_in_prefix": "fde221a8a7f8e7e3ad1eac517f6d0a9dd39926525d4b43ee14b5c13b733e2cdf", "size_in_bytes": 9008}, {"_path": "site-packages/setuptools/tests/test_shutil_wrapper.py", "path_type": "hardlink", "sha256": "835e44d753ed6711be227076056345c87facbce6d7c765dc32180c2c93ee1677", "sha256_in_prefix": "835e44d753ed6711be227076056345c87facbce6d7c765dc32180c2c93ee1677", "size_in_bytes": 641}, {"_path": "site-packages/setuptools/tests/test_unicode_utils.py", "path_type": "hardlink", "sha256": "c567c4125f239100adf68b615135c97c599dc804c0160809b36b53c636ee99bc", "sha256_in_prefix": "c567c4125f239100adf68b615135c97c599dc804c0160809b36b53c636ee99bc", "size_in_bytes": 316}, {"_path": "site-packages/setuptools/tests/test_virtualenv.py", "path_type": "hardlink", "sha256": "83e9e30bff494c0b35615c7fd5d189fd0e919489cee2a295bbdf9702035be936", "sha256_in_prefix": "83e9e30bff494c0b35615c7fd5d189fd0e919489cee2a295bbdf9702035be936", "size_in_bytes": 3730}, {"_path": "site-packages/setuptools/tests/test_warnings.py", "path_type": "hardlink", "sha256": "cf0476cdc9c2782783a882d994938f01cbb23c7a03bc6bb53ad3956222cc93be", "sha256_in_prefix": "cf0476cdc9c2782783a882d994938f01cbb23c7a03bc6bb53ad3956222cc93be", "size_in_bytes": 3347}, {"_path": "site-packages/setuptools/tests/test_wheel.py", "path_type": "hardlink", "sha256": "23bd3d9903b8602cedc48d8bf31edbbbfac4f35f2f0bd4971d1f2ab593f0656f", "sha256_in_prefix": "23bd3d9903b8602cedc48d8bf31edbbbfac4f35f2f0bd4971d1f2ab593f0656f", "size_in_bytes": 18752}, {"_path": "site-packages/setuptools/tests/test_windows_wrappers.py", "path_type": "hardlink", "sha256": "c018d737788695d9334464e0293af1f7dc6a52ac0f27457e95dca2075a560fe8", "sha256_in_prefix": "c018d737788695d9334464e0293af1f7dc6a52ac0f27457e95dca2075a560fe8", "size_in_bytes": 7868}, {"_path": "site-packages/setuptools/tests/text.py", "path_type": "hardlink", "sha256": "6b5db5f7ba4c553bc1e85016434ba34fc7c84222c8589945025d5409a0d40df8", "sha256_in_prefix": "6b5db5f7ba4c553bc1e85016434ba34fc7c84222c8589945025d5409a0d40df8", "size_in_bytes": 123}, {"_path": "site-packages/setuptools/tests/textwrap.py", "path_type": "hardlink", "sha256": "14d34dabf322684271f3c3e7b1b250211c668f5aa681c00e0975d1b0e0cf24de", "sha256_in_prefix": "14d34dabf322684271f3c3e7b1b250211c668f5aa681c00e0975d1b0e0cf24de", "size_in_bytes": 98}, {"_path": "site-packages/setuptools/unicode_utils.py", "path_type": "hardlink", "sha256": "ba430687ca44030e85fc4cdbf8ae43ddcfb4efc46003f19c174a16ea5838952b", "sha256_in_prefix": "ba430687ca44030e85fc4cdbf8ae43ddcfb4efc46003f19c174a16ea5838952b", "size_in_bytes": 3189}, {"_path": "site-packages/setuptools/version.py", "path_type": "hardlink", "sha256": "58909e52ecaaef80289364de2bdf8e7b164ebbc5eb950cbbfb2d0112e58da2f4", "sha256_in_prefix": "58909e52ecaaef80289364de2bdf8e7b164ebbc5eb950cbbfb2d0112e58da2f4", "size_in_bytes": 161}, {"_path": "site-packages/setuptools/warnings.py", "path_type": "hardlink", "sha256": "a18d127b978eaa37bf144ca34e0a2751cd171b082cac8e5c826d64930ba5cffc", "sha256_in_prefix": "a18d127b978eaa37bf144ca34e0a2751cd171b082cac8e5c826d64930ba5cffc", "size_in_bytes": 3796}, {"_path": "site-packages/setuptools/wheel.py", "path_type": "hardlink", "sha256": "fc9ba18a75a6950c011c9add221d727e1ac34bb18531a70288988e6371f98300", "sha256_in_prefix": "fc9ba18a75a6950c011c9add221d727e1ac34bb18531a70288988e6371f98300", "size_in_bytes": 9477}, {"_path": "site-packages/setuptools/windows_support.py", "path_type": "hardlink", "sha256": "c16e0860b33506fed9d4c69ab8fdb198f8f2cbec249909d7772bd7b1c01ff5fc", "sha256_in_prefix": "c16e0860b33506fed9d4c69ab8fdb198f8f2cbec249909d7772bd7b1c01ff5fc", "size_in_bytes": 726}, {"_path": "lib/python3.11/site-packages/_distutils_hack/__pycache__/__init__.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/_distutils_hack/__pycache__/override.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/pkg_resources/__pycache__/__init__.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/pkg_resources/tests/__pycache__/__init__.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/pkg_resources/tests/data/my-test-package-source/__pycache__/setup.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/pkg_resources/tests/__pycache__/test_find_distributions.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/pkg_resources/tests/__pycache__/test_integration_zope_interface.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/pkg_resources/tests/__pycache__/test_markers.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/pkg_resources/tests/__pycache__/test_pkg_resources.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/pkg_resources/tests/__pycache__/test_resources.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/pkg_resources/tests/__pycache__/test_working_set.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/setuptools/__pycache__/__init__.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/setuptools/__pycache__/_core_metadata.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/setuptools/__pycache__/_discovery.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/setuptools/_distutils/__pycache__/__init__.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/setuptools/_distutils/__pycache__/_log.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/setuptools/_distutils/__pycache__/_macos_compat.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/setuptools/_distutils/__pycache__/_modified.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/setuptools/_distutils/__pycache__/_msvccompiler.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/setuptools/_distutils/__pycache__/archive_util.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/setuptools/_distutils/__pycache__/ccompiler.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/setuptools/_distutils/__pycache__/cmd.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/setuptools/_distutils/command/__pycache__/__init__.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/setuptools/_distutils/command/__pycache__/_framework_compat.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/setuptools/_distutils/command/__pycache__/bdist.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/setuptools/_distutils/command/__pycache__/bdist_dumb.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/setuptools/_distutils/command/__pycache__/bdist_rpm.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/setuptools/_distutils/command/__pycache__/build.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/setuptools/_distutils/command/__pycache__/build_clib.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/setuptools/_distutils/command/__pycache__/build_ext.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/setuptools/_distutils/command/__pycache__/build_py.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/setuptools/_distutils/command/__pycache__/build_scripts.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/setuptools/_distutils/command/__pycache__/check.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/setuptools/_distutils/command/__pycache__/clean.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/setuptools/_distutils/command/__pycache__/config.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/setuptools/_distutils/command/__pycache__/install.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/setuptools/_distutils/command/__pycache__/install_data.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/setuptools/_distutils/command/__pycache__/install_egg_info.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/setuptools/_distutils/command/__pycache__/install_headers.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/setuptools/_distutils/command/__pycache__/install_lib.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/setuptools/_distutils/command/__pycache__/install_scripts.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/setuptools/_distutils/command/__pycache__/sdist.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/setuptools/_distutils/compat/__pycache__/__init__.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/setuptools/_distutils/compat/__pycache__/numpy.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/setuptools/_distutils/compat/__pycache__/py39.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/setuptools/_distutils/compilers/C/__pycache__/base.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/setuptools/_distutils/compilers/C/__pycache__/cygwin.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/setuptools/_distutils/compilers/C/__pycache__/errors.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/setuptools/_distutils/compilers/C/__pycache__/msvc.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/setuptools/_distutils/compilers/C/tests/__pycache__/test_base.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/setuptools/_distutils/compilers/C/tests/__pycache__/test_cygwin.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/setuptools/_distutils/compilers/C/tests/__pycache__/test_mingw.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/setuptools/_distutils/compilers/C/tests/__pycache__/test_msvc.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/setuptools/_distutils/compilers/C/tests/__pycache__/test_unix.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/setuptools/_distutils/compilers/C/__pycache__/unix.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/setuptools/_distutils/compilers/C/__pycache__/zos.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/setuptools/_distutils/__pycache__/core.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/setuptools/_distutils/__pycache__/cygwinccompiler.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/setuptools/_distutils/__pycache__/debug.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/setuptools/_distutils/__pycache__/dep_util.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/setuptools/_distutils/__pycache__/dir_util.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/setuptools/_distutils/__pycache__/dist.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/setuptools/_distutils/__pycache__/errors.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/setuptools/_distutils/__pycache__/extension.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/setuptools/_distutils/__pycache__/fancy_getopt.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/setuptools/_distutils/__pycache__/file_util.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/setuptools/_distutils/__pycache__/filelist.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/setuptools/_distutils/__pycache__/log.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/setuptools/_distutils/__pycache__/spawn.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/setuptools/_distutils/__pycache__/sysconfig.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/setuptools/_distutils/tests/__pycache__/__init__.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/setuptools/_distutils/tests/compat/__pycache__/__init__.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/setuptools/_distutils/tests/compat/__pycache__/py39.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/setuptools/_distutils/tests/__pycache__/support.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/setuptools/_distutils/tests/__pycache__/test_archive_util.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/setuptools/_distutils/tests/__pycache__/test_bdist.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/setuptools/_distutils/tests/__pycache__/test_bdist_dumb.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/setuptools/_distutils/tests/__pycache__/test_bdist_rpm.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/setuptools/_distutils/tests/__pycache__/test_build.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/setuptools/_distutils/tests/__pycache__/test_build_clib.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/setuptools/_distutils/tests/__pycache__/test_build_ext.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/setuptools/_distutils/tests/__pycache__/test_build_py.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/setuptools/_distutils/tests/__pycache__/test_build_scripts.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/setuptools/_distutils/tests/__pycache__/test_check.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/setuptools/_distutils/tests/__pycache__/test_clean.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/setuptools/_distutils/tests/__pycache__/test_cmd.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/setuptools/_distutils/tests/__pycache__/test_config_cmd.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/setuptools/_distutils/tests/__pycache__/test_core.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/setuptools/_distutils/tests/__pycache__/test_dir_util.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/setuptools/_distutils/tests/__pycache__/test_dist.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/setuptools/_distutils/tests/__pycache__/test_extension.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/setuptools/_distutils/tests/__pycache__/test_file_util.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/setuptools/_distutils/tests/__pycache__/test_filelist.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/setuptools/_distutils/tests/__pycache__/test_install.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/setuptools/_distutils/tests/__pycache__/test_install_data.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/setuptools/_distutils/tests/__pycache__/test_install_headers.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/setuptools/_distutils/tests/__pycache__/test_install_lib.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/setuptools/_distutils/tests/__pycache__/test_install_scripts.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/setuptools/_distutils/tests/__pycache__/test_log.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/setuptools/_distutils/tests/__pycache__/test_modified.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/setuptools/_distutils/tests/__pycache__/test_sdist.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/setuptools/_distutils/tests/__pycache__/test_spawn.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/setuptools/_distutils/tests/__pycache__/test_sysconfig.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/setuptools/_distutils/tests/__pycache__/test_text_file.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/setuptools/_distutils/tests/__pycache__/test_util.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/setuptools/_distutils/tests/__pycache__/test_version.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/setuptools/_distutils/tests/__pycache__/test_versionpredicate.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/setuptools/_distutils/tests/__pycache__/unix_compat.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/setuptools/_distutils/__pycache__/text_file.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/setuptools/_distutils/__pycache__/unixccompiler.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/setuptools/_distutils/__pycache__/util.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/setuptools/_distutils/__pycache__/version.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/setuptools/_distutils/__pycache__/versionpredicate.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/setuptools/_distutils/__pycache__/zosccompiler.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/setuptools/__pycache__/_entry_points.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/setuptools/__pycache__/_imp.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/setuptools/__pycache__/_importlib.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/setuptools/__pycache__/_itertools.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/setuptools/__pycache__/_normalization.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/setuptools/__pycache__/_path.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/setuptools/__pycache__/_reqs.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/setuptools/__pycache__/_scripts.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/setuptools/__pycache__/_shutil.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/setuptools/__pycache__/_static.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/setuptools/_vendor/autocommand/__pycache__/__init__.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/setuptools/_vendor/autocommand/__pycache__/autoasync.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/setuptools/_vendor/autocommand/__pycache__/autocommand.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/setuptools/_vendor/autocommand/__pycache__/automain.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/setuptools/_vendor/autocommand/__pycache__/autoparse.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/setuptools/_vendor/autocommand/__pycache__/errors.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/setuptools/_vendor/backports/__pycache__/__init__.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/setuptools/_vendor/backports/tarfile/__pycache__/__init__.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/setuptools/_vendor/backports/tarfile/__pycache__/__main__.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/setuptools/_vendor/backports/tarfile/compat/__pycache__/__init__.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/setuptools/_vendor/backports/tarfile/compat/__pycache__/py38.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/setuptools/_vendor/importlib_metadata/__pycache__/__init__.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/setuptools/_vendor/importlib_metadata/__pycache__/_adapters.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/setuptools/_vendor/importlib_metadata/__pycache__/_collections.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/setuptools/_vendor/importlib_metadata/__pycache__/_compat.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/setuptools/_vendor/importlib_metadata/__pycache__/_functools.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/setuptools/_vendor/importlib_metadata/__pycache__/_itertools.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/setuptools/_vendor/importlib_metadata/__pycache__/_meta.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/setuptools/_vendor/importlib_metadata/__pycache__/_text.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/setuptools/_vendor/importlib_metadata/compat/__pycache__/__init__.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/setuptools/_vendor/importlib_metadata/compat/__pycache__/py311.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/setuptools/_vendor/importlib_metadata/compat/__pycache__/py39.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/setuptools/_vendor/importlib_metadata/__pycache__/diagnose.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/setuptools/_vendor/inflect/__pycache__/__init__.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/setuptools/_vendor/inflect/compat/__pycache__/__init__.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/setuptools/_vendor/inflect/compat/__pycache__/py38.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/setuptools/_vendor/jaraco/collections/__pycache__/__init__.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/setuptools/_vendor/jaraco/__pycache__/context.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/setuptools/_vendor/jaraco/functools/__pycache__/__init__.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/setuptools/_vendor/jaraco/text/__pycache__/__init__.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/setuptools/_vendor/jaraco/text/__pycache__/layouts.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/setuptools/_vendor/jaraco/text/__pycache__/show-newlines.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/setuptools/_vendor/jaraco/text/__pycache__/strip-prefix.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/setuptools/_vendor/jaraco/text/__pycache__/to-dvorak.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/setuptools/_vendor/jaraco/text/__pycache__/to-qwerty.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/setuptools/_vendor/more_itertools/__pycache__/__init__.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/setuptools/_vendor/more_itertools/__pycache__/more.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/setuptools/_vendor/more_itertools/__pycache__/recipes.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/setuptools/_vendor/packaging/__pycache__/__init__.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/setuptools/_vendor/packaging/__pycache__/_elffile.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/setuptools/_vendor/packaging/__pycache__/_manylinux.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/setuptools/_vendor/packaging/__pycache__/_musllinux.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/setuptools/_vendor/packaging/__pycache__/_parser.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/setuptools/_vendor/packaging/__pycache__/_structures.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/setuptools/_vendor/packaging/__pycache__/_tokenizer.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/setuptools/_vendor/packaging/licenses/__pycache__/__init__.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/setuptools/_vendor/packaging/licenses/__pycache__/_spdx.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/setuptools/_vendor/packaging/__pycache__/markers.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/setuptools/_vendor/packaging/__pycache__/metadata.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/setuptools/_vendor/packaging/__pycache__/requirements.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/setuptools/_vendor/packaging/__pycache__/specifiers.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/setuptools/_vendor/packaging/__pycache__/tags.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/setuptools/_vendor/packaging/__pycache__/utils.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/setuptools/_vendor/packaging/__pycache__/version.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/setuptools/_vendor/platformdirs/__pycache__/__init__.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/setuptools/_vendor/platformdirs/__pycache__/__main__.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/setuptools/_vendor/platformdirs/__pycache__/android.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/setuptools/_vendor/platformdirs/__pycache__/api.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/setuptools/_vendor/platformdirs/__pycache__/macos.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/setuptools/_vendor/platformdirs/__pycache__/unix.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/setuptools/_vendor/platformdirs/__pycache__/version.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/setuptools/_vendor/platformdirs/__pycache__/windows.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/setuptools/_vendor/tomli/__pycache__/__init__.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/setuptools/_vendor/tomli/__pycache__/_parser.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/setuptools/_vendor/tomli/__pycache__/_re.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/setuptools/_vendor/tomli/__pycache__/_types.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/setuptools/_vendor/typeguard/__pycache__/__init__.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/setuptools/_vendor/typeguard/__pycache__/_checkers.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/setuptools/_vendor/typeguard/__pycache__/_config.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/setuptools/_vendor/typeguard/__pycache__/_decorators.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/setuptools/_vendor/typeguard/__pycache__/_exceptions.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/setuptools/_vendor/typeguard/__pycache__/_functions.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/setuptools/_vendor/typeguard/__pycache__/_importhook.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/setuptools/_vendor/typeguard/__pycache__/_memo.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/setuptools/_vendor/typeguard/__pycache__/_pytest_plugin.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/setuptools/_vendor/typeguard/__pycache__/_suppression.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/setuptools/_vendor/typeguard/__pycache__/_transformer.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/setuptools/_vendor/typeguard/__pycache__/_union_transformer.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/setuptools/_vendor/typeguard/__pycache__/_utils.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/setuptools/_vendor/__pycache__/typing_extensions.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/setuptools/_vendor/wheel/__pycache__/__init__.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/setuptools/_vendor/wheel/__pycache__/__main__.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/setuptools/_vendor/wheel/__pycache__/_bdist_wheel.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/setuptools/_vendor/wheel/__pycache__/_setuptools_logging.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/setuptools/_vendor/wheel/__pycache__/bdist_wheel.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/setuptools/_vendor/wheel/cli/__pycache__/__init__.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/setuptools/_vendor/wheel/cli/__pycache__/convert.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/setuptools/_vendor/wheel/cli/__pycache__/pack.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/setuptools/_vendor/wheel/cli/__pycache__/tags.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/setuptools/_vendor/wheel/cli/__pycache__/unpack.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/setuptools/_vendor/wheel/__pycache__/macosx_libfile.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/setuptools/_vendor/wheel/__pycache__/metadata.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/setuptools/_vendor/wheel/__pycache__/util.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/setuptools/_vendor/wheel/vendored/__pycache__/__init__.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/setuptools/_vendor/wheel/vendored/packaging/__pycache__/__init__.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/setuptools/_vendor/wheel/vendored/packaging/__pycache__/_elffile.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/setuptools/_vendor/wheel/vendored/packaging/__pycache__/_manylinux.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/setuptools/_vendor/wheel/vendored/packaging/__pycache__/_musllinux.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/setuptools/_vendor/wheel/vendored/packaging/__pycache__/_parser.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/setuptools/_vendor/wheel/vendored/packaging/__pycache__/_structures.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/setuptools/_vendor/wheel/vendored/packaging/__pycache__/_tokenizer.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/setuptools/_vendor/wheel/vendored/packaging/__pycache__/markers.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/setuptools/_vendor/wheel/vendored/packaging/__pycache__/requirements.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/setuptools/_vendor/wheel/vendored/packaging/__pycache__/specifiers.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/setuptools/_vendor/wheel/vendored/packaging/__pycache__/tags.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/setuptools/_vendor/wheel/vendored/packaging/__pycache__/utils.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/setuptools/_vendor/wheel/vendored/packaging/__pycache__/version.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/setuptools/_vendor/wheel/__pycache__/wheelfile.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/setuptools/_vendor/zipp/__pycache__/__init__.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/setuptools/_vendor/zipp/compat/__pycache__/__init__.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/setuptools/_vendor/zipp/compat/__pycache__/py310.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/setuptools/_vendor/zipp/__pycache__/glob.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/setuptools/__pycache__/archive_util.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/setuptools/__pycache__/build_meta.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/setuptools/command/__pycache__/__init__.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/setuptools/command/__pycache__/_requirestxt.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/setuptools/command/__pycache__/alias.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/setuptools/command/__pycache__/bdist_egg.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/setuptools/command/__pycache__/bdist_rpm.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/setuptools/command/__pycache__/bdist_wheel.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/setuptools/command/__pycache__/build.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/setuptools/command/__pycache__/build_clib.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/setuptools/command/__pycache__/build_ext.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/setuptools/command/__pycache__/build_py.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/setuptools/command/__pycache__/develop.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/setuptools/command/__pycache__/dist_info.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/setuptools/command/__pycache__/easy_install.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/setuptools/command/__pycache__/editable_wheel.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/setuptools/command/__pycache__/egg_info.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/setuptools/command/__pycache__/install.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/setuptools/command/__pycache__/install_egg_info.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/setuptools/command/__pycache__/install_lib.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/setuptools/command/__pycache__/install_scripts.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/setuptools/command/__pycache__/rotate.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/setuptools/command/__pycache__/saveopts.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/setuptools/command/__pycache__/sdist.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/setuptools/command/__pycache__/setopt.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/setuptools/command/__pycache__/test.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/setuptools/compat/__pycache__/__init__.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/setuptools/compat/__pycache__/py310.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/setuptools/compat/__pycache__/py311.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/setuptools/compat/__pycache__/py312.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/setuptools/compat/__pycache__/py39.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/setuptools/config/__pycache__/__init__.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/setuptools/config/__pycache__/_apply_pyprojecttoml.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/setuptools/config/_validate_pyproject/__pycache__/__init__.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/setuptools/config/_validate_pyproject/__pycache__/error_reporting.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/setuptools/config/_validate_pyproject/__pycache__/extra_validations.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/setuptools/config/_validate_pyproject/__pycache__/fastjsonschema_exceptions.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/setuptools/config/_validate_pyproject/__pycache__/fastjsonschema_validations.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/setuptools/config/_validate_pyproject/__pycache__/formats.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/setuptools/config/__pycache__/expand.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/setuptools/config/__pycache__/pyprojecttoml.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/setuptools/config/__pycache__/setupcfg.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/setuptools/__pycache__/depends.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/setuptools/__pycache__/discovery.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/setuptools/__pycache__/dist.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/setuptools/__pycache__/errors.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/setuptools/__pycache__/extension.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/setuptools/__pycache__/glob.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/setuptools/__pycache__/installer.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/setuptools/__pycache__/launch.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/setuptools/__pycache__/logging.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/setuptools/__pycache__/modified.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/setuptools/__pycache__/monkey.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/setuptools/__pycache__/msvc.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/setuptools/__pycache__/namespaces.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/setuptools/tests/__pycache__/__init__.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/setuptools/tests/compat/__pycache__/__init__.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/setuptools/tests/compat/__pycache__/py39.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/setuptools/tests/config/__pycache__/__init__.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/setuptools/tests/config/downloads/__pycache__/__init__.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/setuptools/tests/config/downloads/__pycache__/preload.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/setuptools/tests/config/__pycache__/test_apply_pyprojecttoml.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/setuptools/tests/config/__pycache__/test_expand.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/setuptools/tests/config/__pycache__/test_pyprojecttoml.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/setuptools/tests/config/__pycache__/test_pyprojecttoml_dynamic_deps.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/setuptools/tests/config/__pycache__/test_setupcfg.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/setuptools/tests/__pycache__/contexts.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/setuptools/tests/__pycache__/environment.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/setuptools/tests/__pycache__/fixtures.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/setuptools/tests/integration/__pycache__/__init__.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/setuptools/tests/integration/__pycache__/helpers.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/setuptools/tests/integration/__pycache__/test_pbr.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/setuptools/tests/integration/__pycache__/test_pip_install_sdist.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/setuptools/tests/__pycache__/mod_with_constant.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/setuptools/tests/__pycache__/namespaces.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/setuptools/tests/__pycache__/script-with-bom.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/setuptools/tests/__pycache__/test_archive_util.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/setuptools/tests/__pycache__/test_bdist_deprecations.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/setuptools/tests/__pycache__/test_bdist_egg.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/setuptools/tests/__pycache__/test_bdist_wheel.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/setuptools/tests/__pycache__/test_build.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/setuptools/tests/__pycache__/test_build_clib.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/setuptools/tests/__pycache__/test_build_ext.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/setuptools/tests/__pycache__/test_build_meta.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/setuptools/tests/__pycache__/test_build_py.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/setuptools/tests/__pycache__/test_config_discovery.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/setuptools/tests/__pycache__/test_core_metadata.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/setuptools/tests/__pycache__/test_depends.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/setuptools/tests/__pycache__/test_develop.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/setuptools/tests/__pycache__/test_dist.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/setuptools/tests/__pycache__/test_dist_info.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/setuptools/tests/__pycache__/test_distutils_adoption.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/setuptools/tests/__pycache__/test_editable_install.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/setuptools/tests/__pycache__/test_egg_info.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/setuptools/tests/__pycache__/test_extern.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/setuptools/tests/__pycache__/test_find_packages.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/setuptools/tests/__pycache__/test_find_py_modules.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/setuptools/tests/__pycache__/test_glob.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/setuptools/tests/__pycache__/test_install_scripts.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/setuptools/tests/__pycache__/test_logging.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/setuptools/tests/__pycache__/test_manifest.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/setuptools/tests/__pycache__/test_namespaces.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/setuptools/tests/__pycache__/test_scripts.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/setuptools/tests/__pycache__/test_sdist.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/setuptools/tests/__pycache__/test_setopt.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/setuptools/tests/__pycache__/test_setuptools.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/setuptools/tests/__pycache__/test_shutil_wrapper.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/setuptools/tests/__pycache__/test_unicode_utils.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/setuptools/tests/__pycache__/test_virtualenv.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/setuptools/tests/__pycache__/test_warnings.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/setuptools/tests/__pycache__/test_wheel.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/setuptools/tests/__pycache__/test_windows_wrappers.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/setuptools/tests/__pycache__/text.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/setuptools/tests/__pycache__/textwrap.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/setuptools/__pycache__/unicode_utils.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/setuptools/__pycache__/version.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/setuptools/__pycache__/warnings.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/setuptools/__pycache__/wheel.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/setuptools/__pycache__/windows_support.cpython-311.pyc", "path_type": "pyc_file"}], "paths_version": 1}, "requested_spec": "None", "sha256": "972560fcf9657058e3e1f97186cc94389144b46dbdf58c807ce62e83f977e863", "size": 748788, "subdir": "noarch", "timestamp": 1748804951000, "url": "https://conda.anaconda.org/conda-forge/noarch/setuptools-80.9.0-pyhff2d567_0.conda", "version": "80.9.0"}
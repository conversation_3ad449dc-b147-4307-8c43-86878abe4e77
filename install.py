#!/usr/bin/env python3
"""
AreTomo3 GUI Professional Installer
Universal installer for Windows, macOS, and Linux
"""

import os
import platform
import shutil
import subprocess
import sys
import venv
from pathlib import Path


class AreTomo3Installer:
    """Professional installer for AreTomo3 GUI."""
    
    def __init__(self):
        self.system = platform.system().lower()
        self.install_path = None
        self.python_exe = None
        self.pip_exe = None
        
    def print_header(self, text):
        print(f"\n{'='*50}")
        print(f"  {text}")
        print(f"{'='*50}")
        
    def print_step(self, text):
        print(f"\n🔧 {text}")
        
    def print_success(self, text):
        print(f"✅ {text}")
        
    def print_error(self, text):
        print(f"❌ {text}")
        
    def get_installation_path(self):
        """Get installation path from user."""
        self.print_header("AreTomo3 GUI Professional Installation")
        
        print("Welcome to AreTomo3 GUI Professional installer!")
        print("\nThis will install:")
        print("• Complete AreTomo3 GUI application")
        print("• All required dependencies")
        print("• Professional launchers")
        print("• Documentation and examples")
        
        default_path = Path.home() / "aretomo3-gui"
        print(f"\nDefault installation path: {default_path}")
        
        try:
            user_input = input("Enter installation path (or press Enter for default): ").strip()
            
            if not user_input:
                self.install_path = default_path
            else:
                self.install_path = Path(user_input).expanduser().resolve()
                
            print(f"\nInstallation path: {self.install_path}")
            
            # Handle existing directory
            if self.install_path.exists():
                print("⚠️  Directory already exists!")
                choice = input("Remove existing directory? (y/N): ").strip().lower()
                if choice == 'y':
                    shutil.rmtree(self.install_path)
                    self.print_success("Existing directory removed")
                else:
                    print("Installation cancelled")
                    return False
                    
            return True
            
        except (KeyboardInterrupt, EOFError):
            print("\nInstallation cancelled by user")
            return False
            
    def create_virtual_environment(self):
        """Create isolated virtual environment."""
        self.print_step("Creating virtual environment")
        
        try:
            # Create installation directory
            self.install_path.mkdir(parents=True, exist_ok=True)
            
            # Create virtual environment
            venv_path = self.install_path / "venv"
            venv.create(venv_path, with_pip=True)
            
            # Set executable paths
            if self.system == "windows":
                self.python_exe = venv_path / "Scripts" / "python.exe"
                self.pip_exe = venv_path / "Scripts" / "pip.exe"
            else:
                self.python_exe = venv_path / "bin" / "python"
                self.pip_exe = venv_path / "bin" / "pip"
                
            self.print_success("Virtual environment created")
            return True
            
        except Exception as e:
            self.print_error(f"Failed to create virtual environment: {e}")
            return False
            
    def install_dependencies(self):
        """Install all required dependencies."""
        self.print_step("Installing dependencies")
        
        # Upgrade pip first
        try:
            subprocess.run([str(self.pip_exe), "install", "--upgrade", "pip"], 
                         check=True, capture_output=True)
            self.print_success("pip upgraded")
        except Exception as e:
            self.print_error(f"Failed to upgrade pip: {e}")
            return False
            
        # Professional dependency list
        dependencies = [
            "PyQt6>=6.4.0",
            "PyQt6-WebEngine>=6.4.0", 
            "numpy>=1.21.0",
            "scipy>=1.7.0",
            "matplotlib>=3.5.0",
            "pandas>=1.3.0",
            "mrcfile>=1.4.0",
            "h5py>=3.6.0",
            "tifffile>=2021.7.2",
            "Pillow>=8.3.0",
            "napari[pyqt6]>=0.4.18",
            "vispy>=0.12.0",
            "flask>=2.0.0",
            "uvicorn>=0.15.0",
            "fastapi>=0.70.0",
            "PyJWT>=2.3.0",
            "watchdog>=2.1.0",
            "plotly>=5.0.0",
            "dash>=2.0.0",
            "bokeh>=2.4.0",
            "psutil>=5.8.0",
            "requests>=2.25.0"
        ]
        
        print(f"Installing {len(dependencies)} packages...")
        
        for i, dep in enumerate(dependencies, 1):
            print(f"  [{i:2d}/{len(dependencies)}] {dep}")
            try:
                subprocess.run([str(self.pip_exe), "install", dep], 
                             check=True, capture_output=True, timeout=300)
            except Exception:
                print(f"    ⚠️  Warning: {dep} installation issue")
                
        self.print_success("Dependencies installation completed")
        return True
        
    def install_application(self):
        """Install the AreTomo3 GUI application."""
        self.print_step("Installing AreTomo3 GUI application")
        
        # Copy application source
        current_dir = Path(__file__).parent  # Current directory where install.py is located
        source_dir = current_dir / "aretomo3_gui"
        target_dir = self.install_path / "aretomo3_gui"
        
        try:
            shutil.copytree(source_dir, target_dir)
            self.print_success("Application installed")
            return True
        except Exception as e:
            self.print_error(f"Failed to install application: {e}")
            return False
            
    def create_launchers(self):
        """Create professional application launchers."""
        self.print_step("Creating application launchers")
        
        # Python launcher
        launcher_content = '''#!/usr/bin/env python3
"""
AreTomo3 GUI Launcher
"""

import sys
import os
from pathlib import Path

# Setup paths
install_dir = Path(__file__).parent
sys.path.insert(0, str(install_dir))

# Setup environment
os.environ['QT_API'] = 'pyqt6'
if 'DISPLAY' not in os.environ:
    os.environ['DISPLAY'] = ':0'

def main():
    """Launch AreTomo3 GUI."""
    try:
        print("🚀 Starting AreTomo3 GUI...")
        from aretomo3_gui.main import main as gui_main
        gui_main()
    except Exception as e:
        print(f"❌ Error: {e}")
        input("Press Enter to exit...")
        sys.exit(1)

if __name__ == "__main__":
    main()
'''
        
        # Create Python launcher
        launcher_path = self.install_path / "aretomo3_gui_launcher.py"
        launcher_path.write_text(launcher_content)
        launcher_path.chmod(0o755)
        
        # Create shell launcher for Unix systems
        if self.system != "windows":
            shell_content = f'''#!/bin/bash
# AreTomo3 GUI Shell Launcher

echo "🚀 AreTomo3 GUI Professional"
echo "=============================="

cd "{self.install_path}"
source venv/bin/activate
python aretomo3_gui_launcher.py
'''
            shell_path = self.install_path / "aretomo3-gui"
            shell_path.write_text(shell_content)
            shell_path.chmod(0o755)
            
        # Create batch launcher for Windows
        if self.system == "windows":
            batch_content = f'''@echo off
echo AreTomo3 GUI Professional
echo ==============================

cd /d "{self.install_path}"
call venv\\Scripts\\activate.bat
python aretomo3_gui_launcher.py
pause
'''
            batch_path = self.install_path / "aretomo3-gui.bat"
            batch_path.write_text(batch_content)
            
        self.print_success("Launchers created")
        return True
        
    def test_installation(self):
        """Test the installation."""
        self.print_step("Testing installation")
        
        try:
            # Test basic import
            result = subprocess.run([
                str(self.python_exe), "-c",
                "import sys; sys.path.insert(0, '.'); import aretomo3_gui; print('✅ Import successful')"
            ], cwd=self.install_path, capture_output=True, text=True, timeout=30)
            
            if result.returncode == 0:
                self.print_success("Installation test passed")
                return True
            else:
                self.print_error(f"Installation test failed: {result.stderr}")
                return False
                
        except Exception as e:
            self.print_error(f"Installation test failed: {e}")
            return False
            
    def run_installation(self):
        """Run the complete installation process."""
        try:
            if not self.get_installation_path():
                return False
            if not self.create_virtual_environment():
                return False
            if not self.install_dependencies():
                return False
            if not self.install_application():
                return False
            if not self.create_launchers():
                return False
            if not self.test_installation():
                return False
                
            # Installation complete
            self.print_header("Installation Complete!")
            print(f"📁 Installation location: {self.install_path}")
            
            if self.system == "windows":
                print(f"🚀 Launch command: {self.install_path}/aretomo3-gui.bat")
            else:
                print(f"🚀 Launch command: {self.install_path}/aretomo3-gui")
                
            print(f"🐍 Python launcher: {self.python_exe} {self.install_path}/aretomo3_gui_launcher.py")
            
            # Offer to launch
            try:
                launch = input("\nLaunch AreTomo3 GUI now? (Y/n): ").strip().lower()
                if launch != 'n':
                    print("🚀 Launching AreTomo3 GUI...")
                    subprocess.run([str(self.python_exe), str(self.install_path / "aretomo3_gui_launcher.py")], 
                                 cwd=self.install_path)
            except (KeyboardInterrupt, EOFError):
                print("\nLaunch skipped")
                
            return True
            
        except Exception as e:
            self.print_error(f"Installation failed: {e}")
            return False


def main():
    """Main installer entry point."""
    installer = AreTomo3Installer()
    
    if installer.run_installation():
        print("\n🎉 AreTomo3 GUI installation completed successfully!")
        return 0
    else:
        print("\n❌ AreTomo3 GUI installation failed!")
        return 1


if __name__ == "__main__":
    sys.exit(main())

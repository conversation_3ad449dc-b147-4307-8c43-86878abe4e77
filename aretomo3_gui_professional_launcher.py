#!/usr/bin/env python3
"""
AreTomo3 GUI Professional Launcher
Clean, warning-free startup
"""

import sys
import os
from pathlib import Path

# Professional environment setup BEFORE any imports
os.environ['QT_LOGGING_RULES'] = '*.debug=false;qt.qpa.*=false'
os.environ['QT_ASSUME_STDERR_HAS_CONSOLE'] = '1'
os.environ['PANGO_ENABLE_BACKEND'] = 'fc'

# Suppress stderr for Pango warnings (professional deployment)
import io
import contextlib

@contextlib.contextmanager
def suppress_stderr():
    """Temporarily suppress stderr for clean professional output."""
    with open(os.devnull, "w") as devnull:
        old_stderr = sys.stderr
        sys.stderr = devnull
        try:
            yield
        finally:
            sys.stderr = old_stderr

def main():
    """Launch AreTomo3 GUI with professional clean output."""
    
    # Setup paths
    install_dir = Path(__file__).parent
    sys.path.insert(0, str(install_dir))
    
    # Professional startup message
    print("🚀 AreTomo3 GUI Professional")
    print("=" * 40)
    print("Starting professional tomographic reconstruction interface...")
    
    try:
        # Import with suppressed warnings
        with suppress_stderr():
            from aretomo3_gui.main import main as gui_main
        
        print("✅ Application initialized successfully")
        
        # Launch GUI
        gui_main()
        
    except Exception as e:
        print(f"❌ Error: {e}")
        input("Press Enter to exit...")
        sys.exit(1)

if __name__ == "__main__":
    main()

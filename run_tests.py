#!/usr/bin/env python3
"""
Direct test runner for AreTomo3 GUI
Runs tests without complex environment setup
"""

import os
import sys
import subprocess
import time
from pathlib import Path

def run_command(cmd, timeout=30):
    """Run a command with timeout."""
    try:
        result = subprocess.run(
            cmd,
            shell=True,
            capture_output=True,
            text=True,
            timeout=timeout,
            cwd="/mnt/HDD/ak_devel/AT3GUI_devel"
        )
        return result.returncode, result.stdout, result.stderr
    except subprocess.TimeoutExpired:
        return -1, "", "Command timed out"
    except Exception as e:
        return -1, "", str(e)

def test_basic_syntax():
    """Test basic Python syntax of key files."""
    print("🔍 TESTING BASIC SYNTAX")
    print("=" * 50)
    
    key_files = [
        "aretomo3_gui/__init__.py",
        "aretomo3_gui/main.py", 
        "aretomo3_gui/qt_backend_init.py",
        "aretomo3_gui/core/realtime_processor.py",
        "aretomo3_gui/web/server.py",
        "aretomo3_gui/analytics/advanced_analytics.py"
    ]
    
    errors = 0
    for file_path in key_files:
        if Path(f"/mnt/HDD/ak_devel/AT3GUI_devel/{file_path}").exists():
            # Test syntax
            code, stdout, stderr = run_command(f"python -m py_compile {file_path}")
            if code == 0:
                print(f"✅ {file_path}")
            else:
                print(f"❌ {file_path}: {stderr}")
                errors += 1
        else:
            print(f"❓ {file_path}: File not found")
            errors += 1
    
    return errors

def test_imports():
    """Test basic imports."""
    print("\n🐍 TESTING IMPORTS")
    print("=" * 50)
    
    # Set environment to avoid Qt issues
    env_cmd = 'export QT_QPA_PLATFORM=offscreen && export DISPLAY=:99 && '
    
    import_tests = [
        ("aretomo3_gui", "Package import"),
        ("aretomo3_gui.utils.file_utils", "File utilities"),
        ("aretomo3_gui.core.config_manager", "Config manager"),
        ("aretomo3_gui.analytics.advanced_analytics", "Advanced analytics")
    ]
    
    errors = 0
    for module, desc in import_tests:
        cmd = f'{env_cmd}python -c "import {module}; print(\\"✅ {desc}\\")"'
        code, stdout, stderr = run_command(cmd, timeout=10)
        
        if code == 0 and "✅" in stdout:
            print(f"✅ {desc}")
        else:
            print(f"❌ {desc}: {stderr or 'Import failed'}")
            errors += 1
    
    return errors

def test_gui_components():
    """Test GUI components without display."""
    print("\n🖥️  TESTING GUI COMPONENTS")
    print("=" * 50)
    
    # Test with offscreen mode
    env_cmd = 'export QT_QPA_PLATFORM=offscreen && export DISPLAY=:99 && '
    
    gui_tests = [
        ("aretomo3_gui.gui.main_window", "Main window"),
        ("aretomo3_gui.gui.rich_main_window", "Rich main window"),
        ("aretomo3_gui.gui.widgets.batch_processing", "Batch processing widget")
    ]
    
    errors = 0
    for module, desc in gui_tests:
        cmd = f'{env_cmd}python -c "import {module}; print(\\"✅ {desc}\\")"'
        code, stdout, stderr = run_command(cmd, timeout=15)
        
        if code == 0 and "✅" in stdout:
            print(f"✅ {desc}")
        else:
            print(f"❌ {desc}: {stderr or 'Import failed'}")
            errors += 1
    
    return errors

def run_pytest():
    """Run pytest if available."""
    print("\n🧪 RUNNING PYTEST")
    print("=" * 50)
    
    # Check if pytest is available
    code, stdout, stderr = run_command("python -m pytest --version")
    if code != 0:
        print("❌ pytest not available")
        return 1
    
    # Run tests
    cmd = "python -m pytest tests/ -v --tb=short -x"
    code, stdout, stderr = run_command(cmd, timeout=120)
    
    print(stdout)
    if stderr:
        print("STDERR:", stderr)
    
    return code

def main():
    """Run all tests."""
    print("🔍 COMPREHENSIVE TESTING SUITE")
    print("=" * 80)
    
    total_errors = 0
    
    # Run tests
    total_errors += test_basic_syntax()
    total_errors += test_imports()
    total_errors += test_gui_components()
    
    # Run pytest
    pytest_result = run_pytest()
    
    # Generate report
    print("\n" + "=" * 80)
    print("📊 TEST RESULTS")
    print("=" * 80)
    
    print(f"Syntax/Import Errors: {total_errors}")
    print(f"Pytest Result: {'PASSED' if pytest_result == 0 else 'FAILED'}")
    
    if total_errors == 0 and pytest_result == 0:
        print("\n🎉 ALL TESTS PASSED!")
        print("🚀 Ready for 200% completion!")
        return 0
    else:
        print(f"\n🔧 ISSUES FOUND:")
        if total_errors > 0:
            print(f"  - {total_errors} syntax/import errors")
        if pytest_result != 0:
            print(f"  - pytest failures")
        print("\n📋 NEXT STEPS:")
        print("1. Fix syntax and import errors")
        print("2. Address test failures")
        print("3. Re-run tests")
        return 1

if __name__ == "__main__":
    sys.exit(main())

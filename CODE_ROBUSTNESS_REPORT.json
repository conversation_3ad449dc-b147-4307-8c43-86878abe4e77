{"timestamp": "2025-06-08T22:34:21.839535", "test_execution": {"individual_verification": {"tests/test_real_file_utils.py": {"status": "valid", "size": 12616, "has_real_tests": true, "comprehensive": true}, "tests/test_real_analytics.py": {"status": "valid", "size": 16864, "has_real_tests": true, "comprehensive": true}, "tests/test_real_data_management.py": {"status": "valid", "size": 13866, "has_real_tests": true, "comprehensive": true}, "tests/test_real_core.py": {"status": "basic", "size": 3218, "has_real_tests": true, "comprehensive": false}, "tests/test_real_processing.py": {"status": "valid", "size": 18671, "has_real_tests": true, "comprehensive": true}, "tests/test_real_web.py": {"status": "valid", "size": 8516, "has_real_tests": true, "comprehensive": true}, "tests/test_real_gui.py": {"status": "valid", "size": 15324, "has_real_tests": true, "comprehensive": true}, "tests/test_real_integration.py": {"status": "valid", "size": 16572, "has_real_tests": true, "comprehensive": true}, "tests/test_real_analysis.py": {"status": "valid", "size": 16081, "has_real_tests": true, "comprehensive": true}}, "functionality_tests": {"file_utils": {"status": "fail", "error": "  File \"<string>\", line 1\n    import sys; sys.path.insert(0, .); from aretomo3_gui.utils.file_utils import validate_safe_path, sanitize_filename, get_file_type; assert validate_safe_path(/tmp/test.txt) == True; assert validate_safe_path(../../../etc/passwd) == False; assert sanitize_filename(test\n                                   ^\nSyntaxError: invalid syntax\n"}, "analytics": {"status": "fail", "error": "  File \"<string>\", line 1\n    import sys; sys.path.insert(0, .); from aretomo3_gui.analytics.advanced_analytics import DataQualityAnalyzer; analyzer = DataQualityAnalyzer(); motion_data = {frame_shifts: [1.0, 2.0], total_drift: 3.0, early_drift: 2.0, late_drift: 1.0, dataset_id: test}; result = analyzer.analyze_motion_correction(motion_data); assert result.analysis_type == motion_correction; assert result.quality_score > 0; print(Analytics:\n                                   ^\nSyntaxError: invalid syntax\n"}, "data_management": {"status": "fail", "error": "  File \"<string>\", line 1\n    import sys; sys.path.insert(0, .); from aretomo3_gui.data_management.data_manager import DataManager; manager = DataManager(); dataset_id = manager.add_dataset({name: test, path: /tmp, type: tilt_series}); assert dataset_id is not None; retrieved = manager.get_dataset(dataset_id); assert retrieved[name] == test; print(Data\n                                   ^\nSyntaxError: invalid syntax\n"}, "core_config": {"status": "fail", "error": "  File \"<string>\", line 1\n    import sys; sys.path.insert(0, .); from aretomo3_gui.core.config_manager import ConfigManager; config = ConfigManager(); config.set_value(test.key, test_value); assert config.get_value(test.key) == test_value; assert config.get_value(missing.key, default) == default; print(Core\n                                   ^\nSyntaxError: invalid syntax\n"}, "web_server": {"status": "fail", "error": "  File \"<string>\", line 1\n    import sys; sys.path.insert(0, .); from aretomo3_gui.web.server import WebServer; server = WebServer(); assert server.host == 0.0.0.0; assert server.port == 8000; assert server.app is not None; print(Web\n                                   ^\nSyntaxError: invalid syntax\n"}}}, "coverage_results": {"total_coverage": 0.0, "lines_covered": 0, "total_lines": 32686, "pytest_return_code": 1}, "robustness_assessment": {"criteria": {"functionality": false, "coverage": false, "test_quality": true, "comprehensive_tests": true}, "score": 50.0, "assessment": "🔧 NEEDS IMPROVEMENT - Robustness can be improved", "grade": "B", "is_robust": false}}
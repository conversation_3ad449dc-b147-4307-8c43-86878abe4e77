#!/usr/bin/env python3
"""Real tests for web modules."""
import pytest
import sys
from pathlib import Path
sys.path.insert(0, str(Path(__file__).parent.parent))

def test_web_server_real():
    """Test web server with real functionality."""
    from aretomo3_gui.web.server import WebServer
    server = WebServer()
    assert server.host == "0.0.0.0"
    assert server.port == 8000
    assert server.app is not None
    
    # Test routes
    routes = [rule.rule for rule in server.app.url_map.iter_rules()]
    assert "/" in routes

def test_plot_server_real():
    """Test plot server with real functionality."""
    from aretomo3_gui.web.plot_server import PlotServer
    server = PlotServer()
    assert server.port == 8001
    
def test_api_server_real():
    """Test API server with real functionality."""
    from aretomo3_gui.web.api_server import APIServer
    server = APIServer()
    assert hasattr(server, 'create_app')

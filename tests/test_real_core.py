#!/usr/bin/env python3
"""
Real functional tests for core modules - 100% coverage focused
Tests that exercise every code path in core modules.
"""

import pytest
import os
import tempfile
import json
from pathlib import Path
import sys
import time
from unittest.mock import patch, <PERSON><PERSON>, MagicMock

# Add project root to path
sys.path.insert(0, str(Path(__file__).parent.parent))

class TestConfigManagerRealCoverage:
    """Comprehensive tests that exercise every line of config_manager.py"""

    def test_config_manager_init_all_branches(self):
        """Test ConfigManager initialization - exercises ALL branches."""
        from aretomo3_gui.core.config_manager import ConfigManager

        # Test default initialization (exercises default config loading)
        config = ConfigManager()
        assert config.config_data is not None
        assert isinstance(config.config_data, dict)
        assert config.config_file is not None

        # Test with custom config file (exercises custom file branch)
        with tempfile.NamedTemporaryFile(mode='w', suffix='.json', delete=False) as tmp:
            custom_config = {
                "custom_setting": "custom_value",
                "nested": {"key": "value"}
            }
            json.dump(custom_config, tmp)
            tmp.flush()

            custom_config_manager = ConfigManager(config_file=tmp.name)
            assert custom_config_manager.config_data["custom_setting"] == "custom_value"

        os.unlink(tmp.name)

        # Test with non-existent config file (exercises file not found branch)
        missing_config_manager = ConfigManager(config_file="/non/existent/config.json")
        assert missing_config_manager.config_data == {}  # Should use empty default

    def test_set_get_value_all_branches(self):
        """Test set_value and get_value - exercises ALL code paths."""
        from aretomo3_gui.core.config_manager import ConfigManager

        config = ConfigManager()

        # Test simple key-value setting (exercises simple branch)
        config.set_value("test_key", "test_value")
        assert config.get_value("test_key") == "test_value"

        # Test nested key setting (exercises nested branch)
        config.set_value("section.subsection.key", "nested_value")
        assert config.get_value("section.subsection.key") == "nested_value"

        # Test deep nesting (exercises deep nesting logic)
        config.set_value("level1.level2.level3.level4.key", "deep_value")
        assert config.get_value("level1.level2.level3.level4.key") == "deep_value"

        # Test overwriting existing values (exercises overwrite branch)
        config.set_value("test_key", "new_value")
        assert config.get_value("test_key") == "new_value"

        # Test getting non-existent key with default (exercises default branch)
        assert config.get_value("non_existent_key", "default") == "default"

        # Test getting non-existent key without default (exercises None branch)
        assert config.get_value("non_existent_key") is None

        # Test getting nested non-existent key (exercises nested not found)
        assert config.get_value("non.existent.nested.key", "nested_default") == "nested_default"

#!/usr/bin/env python3
"""Real tests for core modules."""
import pytest
import sys
from pathlib import Path
sys.path.insert(0, str(Path(__file__).parent.parent))

def test_config_manager_real():
    """Test config manager with real functionality."""
    from aretomo3_gui.core.config_manager import ConfigManager
    config = ConfigManager()
    config.set_value("test_key", "test_value")
    assert config.get_value("test_key") == "test_value"
    assert config.get_value("missing_key", "default") == "default"

def test_error_handling_real():
    """Test error handling with real functionality."""
    from aretomo3_gui.core.error_handling import Error<PERSON><PERSON><PERSON>
    handler = ErrorHandler()
    result = handler.handle_error(ValueError("test error"))
    assert result is not None

def test_resource_manager_real():
    """Test resource manager with real functionality."""
    from aretomo3_gui.core.resource_manager import ResourceManager
    manager = ResourceManager()
    resources = manager.get_system_resources()
    assert "cpu_percent" in resources
    assert "memory_percent" in resources

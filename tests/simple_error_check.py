#!/usr/bin/env python3
"""
Simple Error Check - Bypass Qt issues and focus on import errors
"""

import os
import sys
import traceback

# Set offscreen mode before any Qt imports
os.environ["QT_QPA_PLATFORM"] = "offscreen"
os.environ["DISPLAY"] = ":99"  # Dummy display

# Add current directory to path
sys.path.insert(0, ".")

def test_basic_imports():
    """Test basic imports without Qt."""
    print("🔍 TESTING BASIC IMPORTS")
    print("=" * 50)
    
    errors = []
    
    # Test 1: Package import
    try:
        import aretomo3_gui
        print(f"✅ aretomo3_gui v{aretomo3_gui.__version__}")
    except Exception as e:
        error = f"❌ aretomo3_gui: {e}"
        print(error)
        errors.append(error)
    
    # Test 2: Core modules (non-Qt)
    core_modules = [
        "aretomo3_gui.utils.file_utils",
        "aretomo3_gui.core.config_manager",
        "aretomo3_gui.analysis.aretomo3_output_analyzer",
        "aretomo3_gui.utils.mdoc_parser"
    ]
    
    for module in core_modules:
        try:
            __import__(module)
            print(f"✅ {module}")
        except Exception as e:
            error = f"❌ {module}: {e}"
            print(error)
            errors.append(error)
    
    return errors

def test_priority_features():
    """Test priority features (non-Qt)."""
    print("\n🎯 TESTING PRIORITY FEATURES")
    print("=" * 50)
    
    errors = []
    
    # Priority features that don't require Qt
    features = [
        "aretomo3_gui.data_management.data_manager",
        "aretomo3_gui.formats.format_manager", 
        "aretomo3_gui.analytics.advanced_analytics",
        "aretomo3_gui.integration.external_tools",
        "aretomo3_gui.particle_picking.picker",
        "aretomo3_gui.subtomogram.averaging"
    ]
    
    for feature in features:
        try:
            __import__(feature)
            print(f"✅ {feature}")
        except Exception as e:
            error = f"❌ {feature}: {e}"
            print(error)
            errors.append(error)
    
    return errors

def test_web_components():
    """Test web components."""
    print("\n🌐 TESTING WEB COMPONENTS")
    print("=" * 50)
    
    errors = []
    
    web_modules = [
        "aretomo3_gui.web.server",
        "aretomo3_gui.web.plot_server",
        "aretomo3_gui.web.api_server"
    ]
    
    for module in web_modules:
        try:
            __import__(module)
            print(f"✅ {module}")
        except Exception as e:
            error = f"❌ {module}: {e}"
            print(error)
            errors.append(error)
    
    return errors

def test_analysis_components():
    """Test analysis components."""
    print("\n📊 TESTING ANALYSIS COMPONENTS")
    print("=" * 50)
    
    errors = []
    
    analysis_modules = [
        "aretomo3_gui.analysis.realtime_monitor",
        "aretomo3_gui.analysis.auto_plot_generator",
        "aretomo3_gui.analysis.ctf_analysis.ctf_parser",
        "aretomo3_gui.analysis.motion_analysis.motion_parser"
    ]
    
    for module in analysis_modules:
        try:
            __import__(module)
            print(f"✅ {module}")
        except Exception as e:
            error = f"❌ {module}: {e}"
            print(error)
            errors.append(error)
    
    return errors

def main():
    """Run all tests and generate report."""
    print("🔍 SIMPLE ERROR CHECK - NON-QT COMPONENTS")
    print("=" * 80)
    
    all_errors = []
    
    # Run all tests
    all_errors.extend(test_basic_imports())
    all_errors.extend(test_priority_features())
    all_errors.extend(test_web_components())
    all_errors.extend(test_analysis_components())
    
    # Generate report
    print("\n" + "=" * 80)
    print("📊 ERROR REPORT")
    print("=" * 80)
    
    if all_errors:
        print(f"\n🚨 FOUND {len(all_errors)} ERRORS:")
        for i, error in enumerate(all_errors, 1):
            print(f"{i:2d}. {error}")
        
        print(f"\n🔧 NEXT STEPS:")
        print("1. Fix import errors in order of priority")
        print("2. Check for missing dependencies")
        print("3. Fix syntax/structural issues")
        print("4. Re-run tests")
        
        return 1
    else:
        print("\n✅ NO ERRORS FOUND!")
        print("🚀 All non-Qt components are working correctly!")
        return 0

if __name__ == "__main__":
    sys.exit(main())

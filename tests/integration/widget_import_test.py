#!/usr/bin/env python3
"""
Test individual widget imports to find the problematic one.
"""
import os
import sys

# Set up environment
os.environ['QT_QPA_PLATFORM'] = 'offscreen'
sys.path.insert(0, 'src')

def test_widget_import(widget_name, import_path):
    """Test importing a specific widget."""
    try:
        print(f"Testing {widget_name}...", end=" ")
        exec(f"from {import_path} import {widget_name}")
        print("✓ OK")
        return True
    except Exception as e:
        print(f"✗ FAILED: {e}")
        return False

def main():
        """Execute main operation."""
        print("Testing individual widget imports...")
    print("=" * 50)

    widgets_to_test = [
        ("QApplication", "PyQt6.QtWidgets"),
        ("ResourceMonitor", "aretomo3_gui.gui.widgets.resource_monitor"),
        ("BatchProcessingWidget", "aretomo3_gui.gui.widgets.batch_processing"),
        (
            "UnifiedProcessingMonitor",
            "aretomo3_gui.gui.widgets.unified_processing_monitor"
        ),
        ("AdvancedFileBrowser", "aretomo3_gui.gui.widgets.advanced_file_browser"),
        ("AnalysisViewer", "aretomo3_gui.gui.viewers.analysis_viewer"),
        ("MRCViewer", "aretomo3_gui.gui.viewers.mrc_viewer"),
    ]

    failed_widgets = []

    for widget_name, import_path in widgets_to_test:
        if not test_widget_import(widget_name, import_path):
            failed_widgets.append(f"{widget_name} from {import_path}")

    print("\n" + "=" * 50)
    if failed_widgets:
        print("FAILED IMPORTS:")
        for widget in failed_widgets:
            print(f"  - {widget}")
    else:
        print("All widget imports successful!")

if __name__ == "__main__":
    main()

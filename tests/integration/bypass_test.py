#!/usr/bin/env python3
"""
Test the GUI creation bypassing problematic imports.
"""
import os
import sys

# Set up environment
os.environ['QT_QPA_PLATFORM'] = 'offscreen'
sys.path.insert(0, 'src')

print("Starting bypass test...")

try:
    # Import PyQt6 directly
    from PyQt6.QtCore import Qt
    from PyQt6.QtWidgets import QApplication, QLabel, QMainWindow, QVBoxLayout, QWidget

    print("✓ PyQt6 imported successfully")

    # Create a simple test GUI
    app = QApplication([])

    class SimpleTestGUI(QMainWindow):
        """Class SimpleTestGUI implementation."""
        def __init__(self):
            """Initialize the instance."""
            super().__init__()
            self.setWindowTitle("Test GUI")
            central_widget = QWidget()
            layout = QVBoxLayout()
            layout.addWidget(QLabel("Test GUI Working!"))
            central_widget.setLayout(layout)
            self.setCentralWidget(central_widget)

    # Create and test the simple GUI
    gui = SimpleTestGUI()
    print("✓ Simple GUI created successfully")

    # Test that it has basic functionality
    gui.setWindowTitle("AreTomo3 Test")
    gui.resize(800, 600)

    print("✓ GUI configuration successful")

    app.quit()
    print("✓ All tests passed - PyQt6 and basic GUI functionality work!")

    # Now try to import our main window bypassing the problematic modules
    print("\nTesting AreTomo3 GUI import...")

    # We'll need to mock or bypass the problematic imports
    # Let's see if we can import the main window file directly

except Exception as e:
    print(f"✗ Test failed: {e}")
    import traceback
    traceback.print_exc()
    sys.exit(1)

print("\n🎉 Basic GUI functionality confirmed!")
print("The issue is likely in the AreTomo3 module imports, not PyQt6 itself.")

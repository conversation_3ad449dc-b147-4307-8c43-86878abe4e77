#!/usr/bin/env python3
"""
Simple test to identify exactly where the GUI initialization fails.
"""
import os
import sys

os.environ["QT_QPA_PLATFORM"] = "offscreen"
sys.path.insert(0, ".")


def test_imports():
    """Test all imports step by step."""
    print("=== Testing Imports ===")

    try:
        print("1. Testing basic imports...")
        from PyQt6.QtWidgets import QApplication

        print("   ✓ PyQt6 widgets imported")

        print("2. Testing main window import...")
        from aretomo3_gui.gui.main_window import AreTomoGUI

        print("   ✓ Main window imported")

        return True
    except Exception as e:
        print(f"   ✗ Import failed: {e}")
        import traceback

        traceback.print_exc()
        return False


def test_gui_creation():
    """Test GUI creation step by step."""
    print("\n=== Testing GUI Creation ===")

    try:
        print("1. Creating QApplication...")
        from PyQt6.QtWidgets import QApplication

        app = QApplication([])
        print("   ✓ QApplication created")

        print("2. Importing GUI class...")
        from aretomo3_gui.gui.main_window import AreTomoGUI

        print("   ✓ GUI class imported")

        print("3. Creating GUI instance...")
        gui = AreTomoGUI()
        print("   ✓ GUI instance created successfully!")

        print("4. Checking GUI attributes...")
        print(f"   - Has tabs: {hasattr(gui, 'tabs')}")
        print(f"   - Has batch_widget: {hasattr(gui, 'batch_widget')}")
        print(f"   - Has analysis_viewer: {hasattr(gui, 'analysis_viewer')}")

        app.quit()
        return True

    except Exception as e:
        print(f"   ✗ GUI creation failed: {e}")
        import traceback

        traceback.print_exc()
        return False


if __name__ == "__main__":
    print("AreTomo3 GUI Simple Test")
    print("=" * 50)

    # Test imports
    if not test_imports():
        sys.exit(1)

    # Test GUI creation
    if not test_gui_creation():
        sys.exit(1)

    print("\n🎉 All tests passed!")

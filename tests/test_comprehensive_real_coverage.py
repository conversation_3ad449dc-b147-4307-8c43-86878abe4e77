#!/usr/bin/env python3
"""
Real Functional Tests for 100% Coverage
Tests that actually exercise code paths and functionality.
"""

import pytest
import sys
import os
import tempfile
import json
from pathlib import Path
from unittest.mock import Mock, patch, MagicMock
import numpy as np

# Add project root to path
sys.path.insert(0, str(Path(__file__).parent.parent))

# Set environment for testing
os.environ["QT_QPA_PLATFORM"] = "offscreen"
os.environ["DISPLAY"] = ":99"

class TestFileUtils:
    """Real tests for file utilities."""
    
    def test_validate_safe_path(self):
        """Test path validation functionality."""
        from aretomo3_gui.utils.file_utils import validate_safe_path
        
        # Test valid paths
        assert validate_safe_path("/tmp/test.txt") == True
        assert validate_safe_path("./test.txt") == True
        
        # Test invalid paths
        assert validate_safe_path("../../../etc/passwd") == False
        assert validate_safe_path("..\\..\\windows\\system32") == False
    
    def test_sanitize_filename(self):
        """Test filename sanitization."""
        from aretomo3_gui.utils.file_utils import sanitize_filename
        
        # Test dangerous characters
        assert sanitize_filename("test<>file.txt") == "test__file.txt"
        assert sanitize_filename("test|file?.txt") == "test_file_.txt"
        assert sanitize_filename("") == "unnamed_file"
    
    def test_get_file_type(self):
        """Test file type detection."""
        from aretomo3_gui.utils.file_utils import get_file_type
        
        assert get_file_type("test.mrc") == "mrc"
        assert get_file_type("test.eer") == "eer"
        assert get_file_type("test.tiff") == "tiff"
        assert get_file_type("test.unknown") == "unknown"
    
    def test_is_supported_format(self):
        """Test supported format detection."""
        from aretomo3_gui.utils.file_utils import is_supported_format
        
        assert is_supported_format("test.mrc") == True
        assert is_supported_format("test.eer") == True
        assert is_supported_format("test.txt") == False

class TestAdvancedAnalytics:
    """Real tests for advanced analytics."""
    
    def test_data_quality_analyzer_init(self):
        """Test DataQualityAnalyzer initialization."""
        from aretomo3_gui.analytics.advanced_analytics import DataQualityAnalyzer
        
        analyzer = DataQualityAnalyzer()
        assert analyzer.quality_thresholds is not None
        assert "resolution_excellent" in analyzer.quality_thresholds
    
    def test_motion_correction_analysis(self):
        """Test motion correction analysis."""
        from aretomo3_gui.analytics.advanced_analytics import DataQualityAnalyzer
        
        analyzer = DataQualityAnalyzer()
        
        # Test data
        motion_data = {
            "frame_shifts": [1.0, 2.0, 1.5, 0.8],
            "total_drift": 5.3,
            "early_drift": 3.2,
            "late_drift": 1.1,
            "dataset_id": "test_dataset"
        }
        
        result = analyzer.analyze_motion_correction(motion_data)
        
        assert result.analysis_type == "motion_correction"
        assert result.dataset_id == "test_dataset"
        assert "total_drift" in result.metrics
        assert result.quality_score > 0
        assert len(result.recommendations) > 0
    
    def test_ctf_estimation_analysis(self):
        """Test CTF estimation analysis."""
        from aretomo3_gui.analytics.advanced_analytics import DataQualityAnalyzer
        
        analyzer = DataQualityAnalyzer()
        
        # Test data
        ctf_data = {
            "defocus_u": -2.5,
            "defocus_v": -2.8,
            "astigmatism": 150.0,
            "resolution": 3.5,
            "confidence": 0.85,
            "dataset_id": "test_ctf"
        }
        
        result = analyzer.analyze_ctf_estimation(ctf_data)
        
        assert result.analysis_type == "ctf_estimation"
        assert result.dataset_id == "test_ctf"
        assert "mean_defocus" in result.metrics
        assert result.quality_score > 0
    
    def test_statistical_analyzer(self):
        """Test statistical analysis functionality."""
        from aretomo3_gui.analytics.advanced_analytics import StatisticalAnalyzer
        
        analyzer = StatisticalAnalyzer()
        
        # Test outlier detection
        data = [1, 2, 3, 4, 5, 100]  # 100 is an outlier
        result = analyzer.perform_outlier_detection(data, method="iqr")
        
        assert "outliers" in result
        assert "outlier_indices" in result
        assert len(result["outliers"]) > 0
    
    def test_advanced_analytics_main(self):
        """Test main AdvancedAnalytics class."""
        from aretomo3_gui.analytics.advanced_analytics import AdvancedAnalytics
        
        analytics = AdvancedAnalytics()
        assert analytics.quality_analyzer is not None
        assert analytics.statistical_analyzer is not None
        assert analytics.ml_analyzer is not None

class TestWebServer:
    """Real tests for web server functionality."""
    
    def test_web_server_init(self):
        """Test WebServer initialization."""
        from aretomo3_gui.web.server import WebServer
        
        server = WebServer()
        assert server.host == "0.0.0.0"
        assert server.port == 8000
        assert server.app is not None
    
    def test_web_server_routes(self):
        """Test web server routes."""
        from aretomo3_gui.web.server import WebServer
        
        server = WebServer()
        
        # Test that routes are registered
        assert server.app is not None
        
        # Test route registration
        routes = [rule.rule for rule in server.app.url_map.iter_rules()]
        assert "/" in routes  # Main dashboard
        assert "/api/status" in routes  # API endpoint

class TestDataManager:
    """Real tests for data management."""
    
    def test_data_manager_init(self):
        """Test DataManager initialization."""
        from aretomo3_gui.data_management.data_manager import DataManager
        
        manager = DataManager()
        assert manager.datasets == {}
        assert manager.current_dataset is None
    
    def test_load_data(self):
        """Test data loading functionality."""
        from aretomo3_gui.data_management.data_manager import DataManager
        
        manager = DataManager()
        
        # Test with temporary file
        with tempfile.NamedTemporaryFile(suffix=".json", delete=False) as tmp:
            test_data = {"test": "data"}
            json.dump(test_data, tmp)
            tmp.flush()
            
            result = manager.load_data(tmp.name)
            assert result is not None
            
        os.unlink(tmp.name)
    
    def test_save_data(self):
        """Test data saving functionality."""
        from aretomo3_gui.data_management.data_manager import DataManager
        
        manager = DataManager()
        
        # Test saving data
        with tempfile.NamedTemporaryFile(suffix=".json", delete=False) as tmp:
            test_data = {"test": "save_data"}
            result = manager.save_data(test_data, tmp.name)
            assert result == True
            
            # Verify data was saved
            with open(tmp.name, 'r') as f:
                loaded_data = json.load(f)
                assert loaded_data["test"] == "save_data"
        
        os.unlink(tmp.name)

class TestFormatManager:
    """Real tests for format management."""
    
    def test_format_manager_init(self):
        """Test FormatManager initialization."""
        from aretomo3_gui.formats.format_manager import FormatManager
        
        manager = FormatManager()
        assert len(manager.supported_formats) > 0
        assert "mrc" in manager.supported_formats
    
    def test_load_file(self):
        """Test file loading functionality."""
        from aretomo3_gui.formats.format_manager import FormatManager
        
        manager = FormatManager()
        
        # Test with non-existent file
        result = manager.load_file("/non/existent/file.mrc")
        assert result is None
    
    def test_get_format_info(self):
        """Test format information retrieval."""
        from aretomo3_gui.formats.format_manager import FormatManager
        
        manager = FormatManager()
        
        # Test MRC format info
        info = manager.get_format_info("mrc")
        assert info is not None
        assert "description" in info

class TestRealTimeProcessor:
    """Real tests for real-time processing."""
    
    @patch('aretomo3_gui.core.realtime_processor.Observer')
    def test_realtime_processor_init(self, mock_observer):
        """Test RealTimeProcessor initialization."""
        from aretomo3_gui.core.realtime_processor import RealTimeProcessor
        
        processor = RealTimeProcessor(
            watch_directories=[Path("/tmp")],
            output_directory=Path("/tmp/output")
        )
        
        assert processor.watch_directories == [Path("/tmp")]
        assert processor.output_directory == Path("/tmp/output")
        assert processor.is_processing == False
    
    @patch('aretomo3_gui.core.realtime_processor.Observer')
    def test_start_stop_processing(self, mock_observer):
        """Test start and stop processing."""
        from aretomo3_gui.core.realtime_processor import RealTimeProcessor
        
        processor = RealTimeProcessor(
            watch_directories=[Path("/tmp")],
            output_directory=Path("/tmp/output")
        )
        
        # Test start processing
        processor.start_processing()
        assert processor.is_processing == True
        
        # Test stop processing
        processor.stop_processing()
        assert processor.is_processing == False

class TestWorkflowManager:
    """Real tests for workflow management."""
    
    def test_workflow_manager_init(self):
        """Test WorkflowManager initialization."""
        from aretomo3_gui.core.automation.workflow_manager import WorkflowManager
        
        manager = WorkflowManager()
        assert manager.workflows == {}
        assert manager.active_workflow is None
    
    def test_create_workflow(self):
        """Test workflow creation."""
        from aretomo3_gui.core.automation.workflow_manager import WorkflowManager
        
        manager = WorkflowManager()
        
        workflow_config = {
            "name": "test_workflow",
            "steps": [
                {"type": "motion_correction", "params": {}},
                {"type": "ctf_estimation", "params": {}}
            ]
        }
        
        workflow_id = manager.create_workflow(workflow_config)
        assert workflow_id is not None
        assert workflow_id in manager.workflows
    
    def test_execute_workflow(self):
        """Test workflow execution."""
        from aretomo3_gui.core.automation.workflow_manager import WorkflowManager
        
        manager = WorkflowManager()
        
        # Create a simple workflow
        workflow_config = {
            "name": "simple_test",
            "steps": [{"type": "test_step", "params": {}}]
        }
        
        workflow_id = manager.create_workflow(workflow_config)
        
        # Test execution (should handle gracefully even if steps aren't implemented)
        result = manager.execute_workflow(workflow_id)
        assert result is not None

def test_integration_all_components():
    """Integration test for all major components."""
    
    # Test that all major components can be imported and initialized
    components = [
        ("aretomo3_gui.analytics.advanced_analytics", "AdvancedAnalytics"),
        ("aretomo3_gui.data_management.data_manager", "DataManager"),
        ("aretomo3_gui.formats.format_manager", "FormatManager"),
        ("aretomo3_gui.web.server", "WebServer"),
        ("aretomo3_gui.core.automation.workflow_manager", "WorkflowManager")
    ]
    
    initialized_components = []
    
    for module_name, class_name in components:
        try:
            module = __import__(module_name, fromlist=[class_name])
            cls = getattr(module, class_name)
            instance = cls()
            initialized_components.append(class_name)
        except Exception as e:
            pytest.fail(f"Failed to initialize {class_name}: {e}")
    
    # Verify all components were initialized
    assert len(initialized_components) == len(components)

class TestConfigManager:
    """Real tests for configuration management."""

    def test_config_manager_init(self):
        """Test ConfigManager initialization."""
        from aretomo3_gui.core.config_manager import ConfigManager

        config = ConfigManager()
        assert config.config_data is not None
        assert hasattr(config, 'load_config')
        assert hasattr(config, 'save_config')

    def test_load_save_config(self):
        """Test config loading and saving."""
        from aretomo3_gui.core.config_manager import ConfigManager

        config = ConfigManager()

        # Test setting and getting values
        config.set_value("test_key", "test_value")
        assert config.get_value("test_key") == "test_value"

        # Test default values
        assert config.get_value("non_existent_key", "default") == "default"

class TestParticlePicker:
    """Real tests for particle picking."""

    def test_particle_picker_init(self):
        """Test ParticlePicker initialization."""
        from aretomo3_gui.particle_picking.picker import ParticlePicker

        picker = ParticlePicker()
        assert hasattr(picker, 'pick_particles')
        assert picker.picked_particles == []

    def test_pick_particles(self):
        """Test particle picking functionality."""
        from aretomo3_gui.particle_picking.picker import ParticlePicker

        picker = ParticlePicker()

        # Test with mock image data
        mock_image = np.random.rand(100, 100)
        result = picker.pick_particles(mock_image)

        assert result is not None
        assert isinstance(result, list)

class TestSubtomogramAverager:
    """Real tests for subtomogram averaging."""

    def test_subtomogram_averager_init(self):
        """Test SubtomogramAverager initialization."""
        from aretomo3_gui.subtomogram.averaging import SubtomogramAverager

        averager = SubtomogramAverager()
        assert hasattr(averager, 'average_subtomograms')
        assert averager.subtomograms == []

    def test_average_subtomograms(self):
        """Test subtomogram averaging."""
        from aretomo3_gui.subtomogram.averaging import SubtomogramAverager

        averager = SubtomogramAverager()

        # Test with mock subtomogram data
        mock_subtomograms = [np.random.rand(32, 32, 32) for _ in range(3)]
        result = averager.average_subtomograms(mock_subtomograms)

        assert result is not None
        assert result.shape == (32, 32, 32)

class TestExternalToolsManager:
    """Real tests for external tools integration."""

    def test_external_tools_manager_init(self):
        """Test ExternalToolsManager initialization."""
        from aretomo3_gui.integration.external_tools import ExternalToolsManager

        manager = ExternalToolsManager()
        assert hasattr(manager, 'available_tools')
        assert isinstance(manager.available_tools, dict)

    def test_tool_availability(self):
        """Test tool availability checking."""
        from aretomo3_gui.integration.external_tools import ExternalToolsManager

        manager = ExternalToolsManager()

        # Test checking for common tools
        result = manager.check_tool_availability("python")
        assert isinstance(result, bool)

if __name__ == "__main__":
    pytest.main([__file__, "-v", "--cov=aretomo3_gui", "--cov-report=term-missing"])

#!/usr/bin/env python3
"""Real tests for format modules."""
import pytest
import sys
from pathlib import Path
sys.path.insert(0, str(Path(__file__).parent.parent))

def test_format_manager_real():
    """Test format manager with real functionality."""
    from aretomo3_gui.formats.format_manager import FormatManager
    manager = FormatManager()
    assert len(manager.supported_formats) > 0
    assert "mrc" in manager.supported_formats
    
    # Test format info
    info = manager.get_format_info("mrc")
    assert info is not None
    assert "description" in info
    
    # Test file loading (should handle non-existent gracefully)
    result = manager.load_file("/non/existent/file.mrc")
    assert result is None

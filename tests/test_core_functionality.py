#!/usr/bin/env python3
"""
Core functionality tests - No Qt dependencies
Tests core modules without GUI components.
"""

import pytest
import sys
import os
from pathlib import Path

# Add project root to path
sys.path.insert(0, str(Path(__file__).parent.parent))

def test_package_import():
    """Test basic package import."""
    try:
        import aretomo3_gui
        assert aretomo3_gui.__version__ == "2.0.0"
        print(f"✅ Package imported successfully: v{aretomo3_gui.__version__}")
    except ImportError as e:
        pytest.fail(f"Package import failed: {e}")

def test_file_utils():
    """Test file utilities."""
    try:
        from aretomo3_gui.utils.file_utils import FileUtils
        utils = FileUtils()
        assert utils is not None
        print("✅ File utilities working")
    except ImportError as e:
        pytest.fail(f"File utils import failed: {e}")

def test_config_manager():
    """Test configuration manager."""
    try:
        from aretomo3_gui.core.config_manager import ConfigManager
        config = ConfigManager()
        assert config is not None
        print("✅ Config manager working")
    except ImportError as e:
        pytest.fail(f"Config manager import failed: {e}")

def test_data_manager():
    """Test data management."""
    try:
        from aretomo3_gui.data_management.data_manager import DataManager
        manager = DataManager()
        assert manager is not None
        assert hasattr(manager, 'load_data')
        print("✅ Data manager working")
    except ImportError as e:
        pytest.fail(f"Data manager import failed: {e}")

def test_format_manager():
    """Test format management."""
    try:
        from aretomo3_gui.formats.format_manager import FormatManager
        manager = FormatManager()
        assert manager is not None
        assert hasattr(manager, 'supported_formats')
        print("✅ Format manager working")
    except ImportError as e:
        pytest.fail(f"Format manager import failed: {e}")

def test_analytics():
    """Test advanced analytics."""
    try:
        from aretomo3_gui.analytics.advanced_analytics import AdvancedAnalytics
        analytics = AdvancedAnalytics()
        assert analytics is not None
        assert hasattr(analytics, 'analyze_data')
        print("✅ Advanced analytics working")
    except ImportError as e:
        pytest.fail(f"Analytics import failed: {e}")

def test_web_server():
    """Test web server."""
    try:
        from aretomo3_gui.web.server import WebServer
        server = WebServer()
        assert server is not None
        assert hasattr(server, 'start_server')
        print("✅ Web server working")
    except ImportError as e:
        pytest.fail(f"Web server import failed: {e}")

def test_realtime_processor():
    """Test real-time processor."""
    try:
        from aretomo3_gui.core.realtime_processor import RealTimeProcessor
        # Don't instantiate to avoid file system dependencies
        assert RealTimeProcessor is not None
        print("✅ Real-time processor working")
    except ImportError as e:
        pytest.fail(f"Real-time processor import failed: {e}")

def test_workflow_manager():
    """Test workflow manager."""
    try:
        from aretomo3_gui.core.automation.workflow_manager import WorkflowManager
        manager = WorkflowManager()
        assert manager is not None
        print("✅ Workflow manager working")
    except ImportError as e:
        pytest.fail(f"Workflow manager import failed: {e}")

def test_external_tools():
    """Test external tools integration."""
    try:
        from aretomo3_gui.integration.external_tools import ExternalToolsManager
        manager = ExternalToolsManager()
        assert manager is not None
        print("✅ External tools manager working")
    except ImportError as e:
        pytest.fail(f"External tools import failed: {e}")

def test_particle_picking():
    """Test particle picking."""
    try:
        from aretomo3_gui.particle_picking.picker import ParticlePicker
        picker = ParticlePicker()
        assert picker is not None
        print("✅ Particle picker working")
    except ImportError as e:
        pytest.fail(f"Particle picker import failed: {e}")

def test_subtomogram_averaging():
    """Test subtomogram averaging."""
    try:
        from aretomo3_gui.subtomogram.averaging import SubtomogramAverager
        averager = SubtomogramAverager()
        assert averager is not None
        print("✅ Subtomogram averager working")
    except ImportError as e:
        pytest.fail(f"Subtomogram averager import failed: {e}")

def test_analysis_components():
    """Test analysis components."""
    try:
        from aretomo3_gui.analysis.realtime_monitor import RealTimeMonitor
        monitor = RealTimeMonitor()
        assert monitor is not None
        print("✅ Real-time monitor working")
    except ImportError as e:
        pytest.fail(f"Real-time monitor import failed: {e}")

def test_ctf_analysis():
    """Test CTF analysis."""
    try:
        from aretomo3_gui.analysis.ctf_analysis.ctf_parser import CTFParser
        parser = CTFParser()
        assert parser is not None
        print("✅ CTF parser working")
    except ImportError as e:
        pytest.fail(f"CTF parser import failed: {e}")

def test_motion_analysis():
    """Test motion analysis."""
    try:
        from aretomo3_gui.analysis.motion_analysis.motion_parser import MotionParser
        parser = MotionParser()
        assert parser is not None
        print("✅ Motion parser working")
    except ImportError as e:
        pytest.fail(f"Motion parser import failed: {e}")

def test_comprehensive_status():
    """Test overall system status."""
    
    # List of all core modules to test
    core_modules = [
        "aretomo3_gui",
        "aretomo3_gui.utils.file_utils",
        "aretomo3_gui.core.config_manager",
        "aretomo3_gui.data_management.data_manager",
        "aretomo3_gui.formats.format_manager",
        "aretomo3_gui.analytics.advanced_analytics",
        "aretomo3_gui.web.server",
        "aretomo3_gui.core.realtime_processor",
        "aretomo3_gui.core.automation.workflow_manager",
        "aretomo3_gui.integration.external_tools",
        "aretomo3_gui.particle_picking.picker",
        "aretomo3_gui.subtomogram.averaging",
        "aretomo3_gui.analysis.realtime_monitor",
        "aretomo3_gui.analysis.ctf_analysis.ctf_parser",
        "aretomo3_gui.analysis.motion_analysis.motion_parser"
    ]
    
    successful_imports = 0
    total_modules = len(core_modules)
    
    for module in core_modules:
        try:
            __import__(module)
            successful_imports += 1
        except ImportError:
            pass
    
    completion_percentage = (successful_imports / total_modules) * 100
    
    print(f"\n🎯 CORE SYSTEM STATUS:")
    print(f"Successful imports: {successful_imports}/{total_modules}")
    print(f"Completion: {completion_percentage:.1f}%")
    
    # Assert high completion rate
    assert completion_percentage >= 90, f"Only {completion_percentage:.1f}% of core modules working"
    
    if completion_percentage >= 95:
        print("🚀 EXCELLENT - Core system ready for 200% completion!")
    elif completion_percentage >= 90:
        print("⚡ GOOD - Minor fixes needed")
    else:
        print("🔧 NEEDS WORK - Major fixes required")

if __name__ == "__main__":
    pytest.main([__file__, "-v", "-s"])

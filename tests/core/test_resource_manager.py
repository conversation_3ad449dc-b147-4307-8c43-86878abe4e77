# =============================================================================
# Test module for resource management functionality
# This module contains tests for the ResourceMonitor and FileManager classes
# =============================================================================

# Standard library imports
import os
import tempfile
import time
from pathlib import Path

import pytest

# Qt imports
from PyQt6.QtCore import QCoreApplication

# Local application imports
from aretomo3_gui.core.resource_manager import (
    FileManager,
    ResourceMonitor,
    get_resource_monitor,
)

# =============================================================================
# Fixtures
# =============================================================================


@pytest.fixture
def resource_monitor():
    """Fixture providing a ResourceMonitor instance.

    Returns:
        ResourceMonitor: A fresh instance for each test.
    Ensures:
        Monitor is properly cleaned up after each test.
    """
    monitor = ResourceMonitor()
    yield monitor
    # Cleanup monitor timer if active
    if monitor._timer is not None:
        monitor.stop_monitoring()


@pytest.fixture
def file_manager():
    """Fixture providing a FileManager instance.

    Returns:
        FileManager: A fresh instance for each test.
    Ensures:
        Manager resources are properly cleaned up after each test.
    """
    manager = FileManager()
    yield manager
    # Cleanup
    manager.cleanup()


# =============================================================================
# ResourceMonitor Tests
# =============================================================================


def test_resource_monitor_initialization(resource_monitor):
    """Test ResourceMonitor initialization.

    Verifies:
        - Monitor instance is created successfully
        - Default thresholds are set correctly
        - Check interval is set to expected value
    """
    assert resource_monitor is not None
    assert resource_monitor.memory_warning_threshold == 0.9  # 90% threshold
    assert resource_monitor.disk_warning_threshold == 0.9  # 90% threshold
    assert resource_monitor.check_interval == 5000  # 5 seconds in milliseconds


def test_resource_monitor_start_stop(qtbot, resource_monitor):
    """Test starting and stopping resource monitoring.

    Verifies:
        - Timer is initially None
        - Timer is created and active after start
        - Timer is properly cleaned up after stop

    Args:
        qtbot: pytest-qt test helper
        resource_monitor: ResourceMonitor fixture
    """
    # Initial state
    assert resource_monitor._timer is None

    # After starting
    resource_monitor.start_monitoring()
    assert resource_monitor._timer is not None
    assert resource_monitor._timer.isActive()

    # After stopping
    resource_monitor.stop_monitoring()
    assert resource_monitor._timer is None


# =============================================================================
# Resource Warning Tests
# =============================================================================


def test_resource_monitor_memory_warning(qtbot, resource_monitor):
    """Test memory warning signal emission.

    Verifies:
        - Warning signal is emitted when memory usage exceeds threshold
        - Warning message contains appropriate text

    Args:
        qtbot: pytest-qt test helper for signal capture
        resource_monitor: ResourceMonitor fixture
    """
    # Set threshold to 0% to ensure warning
    resource_monitor.memory_warning_threshold = 0.0

    # Capture and verify warning signal
    with qtbot.waitSignal(resource_monitor.warning, timeout=1000) as blocker:
        resource_monitor.check_memory()

    # Verify warning message content
    assert "memory usage" in blocker.args[0].lower()


def test_resource_monitor_disk_warning(qtbot, resource_monitor):
    """Test disk space warning signal emission.

    Verifies:
        - Warning signal is emitted when disk usage exceeds threshold
        - Warning message contains appropriate text

    Args:
        qtbot: pytest-qt test helper for signal capture
        resource_monitor: ResourceMonitor fixture
    """
    # Set threshold to 0% to ensure warning
    resource_monitor.disk_warning_threshold = 0.0

    # Capture and verify warning signal
    with qtbot.waitSignal(resource_monitor.warning, timeout=1000) as blocker:
        resource_monitor.check_disk("/")

    # Verify warning message content
    assert "disk space" in blocker.args[0].lower()


# =============================================================================
# FileManager Tests - Temporary Directory Management
# =============================================================================


def test_file_manager_temp_directory(file_manager):
    """Test temporary directory creation and cleanup functionality.

    Verifies:
        - Temporary directory is created successfully
        - Directory is tracked in manager's temp_dirs list
        - Cleanup properly removes the directory
        - temp_dirs list is emptied after cleanup

    Args:
        file_manager: FileManager fixture
    """
    # Test directory creation
    temp_dir = file_manager.create_temp_directory()
    assert os.path.exists(temp_dir), "Temporary directory should exist after creation"
    assert (
        temp_dir in file_manager.temp_dirs
    ), "Directory should be tracked in temp_dirs"

    # Test cleanup functionality
    file_manager.cleanup()
    assert not os.path.exists(temp_dir), "Directory should be removed after cleanup"
    assert len(file_manager.temp_dirs) == 0, "temp_dirs should be empty after cleanup"


# =============================================================================
# FileManager Tests - File Locking Operations
# =============================================================================


def test_file_manager_lock_operations(file_manager):
    """Test file locking and unlocking operations.

    Verifies:
        - Lock acquisition works
        - Lock files are created
        - Re-acquiring existing lock returns True
        - Lock release works
        - Lock files are removed on release
        - Releasing non-existent lock returns False

    Args:
        file_manager: FileManager fixture
    """
    # Create a test file for locking
    with tempfile.NamedTemporaryFile(delete=False) as tf:
        path = tf.name

    try:
        # Test lock acquisition
        assert file_manager.acquire_lock(path), "Should acquire lock successfully"
        assert path in file_manager.lock_files, "Path should be in lock_files"
        assert os.path.exists(f"{path}.lock"), "Lock file should exist"

        # Test re-acquiring same lock
        assert file_manager.acquire_lock(path), "Re-acquiring should return True"

        # Test lock release
        assert file_manager.release_lock(path), "Should release lock successfully"
        assert path not in file_manager.lock_files, "Path should not be in lock_files"
        assert not os.path.exists(f"{path}.lock"), "Lock file should be removed"

        # Test releasing non-existent lock
        assert not file_manager.release_lock(
            path
        ), "Releasing non-existent lock should return False"

    finally:
        # Cleanup test files
        if os.path.exists(path):
            os.unlink(path)
        if os.path.exists(f"{path}.lock"):
            os.unlink(f"{path}.lock")


# =============================================================================
# FileManager Tests - Lock Timeout Behavior
# =============================================================================


def test_file_manager_lock_timeout(file_manager):
    """Test file lock acquisition timeout behavior.

    Verifies:
        - Lock acquisition fails when lock file exists
        - Timeout duration is respected
        - Method returns False after timeout

    Args:
        file_manager: FileManager fixture

    Note:
        Tests timing with 0.5s timeout, allowing 0.1s margin of error
    """
    # Create a test file for locking
    with tempfile.NamedTemporaryFile(delete=False) as tf:
        path = tf.name

    try:
        # Simulate locked file
        with open(f"{path}.lock", "w") as lf:
            pass

        # Test timeout behavior
        start = time.time()
        assert not file_manager.acquire_lock(
            path, timeout=0.5
        ), "Should fail to acquire locked file"
        duration = time.time() - start

        # Verify timeout duration (with 0.1s margin)
        assert (
            0.4 <= duration <= 0.6
        ), "Should wait for approximately the timeout period"

    finally:
        # Cleanup test files
        if os.path.exists(path):
            os.unlink(path)
        if os.path.exists(f"{path}.lock"):
            os.unlink(f"{path}.lock")


# =============================================================================
# ResourceMonitor Tests - Singleton Pattern
# =============================================================================


def test_get_resource_monitor():
    """Test ResourceMonitor singleton implementation.

    Verifies:
        - get_resource_monitor returns a valid instance
        - Multiple calls return the same instance (singleton pattern)
    """
    # Get two instances
    monitor1 = get_resource_monitor()
    monitor2 = get_resource_monitor()

    # Verify singleton behavior
    assert monitor1 is not None, "Should return a valid instance"
    assert monitor1 is monitor2, "Should return the same instance (singleton pattern)"

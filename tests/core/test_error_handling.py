"""
Test module for error handling functionality.
"""

import logging

import pytest

from aretomo3_gui.core.error_handling import (
    AreTomo3Error,
    ErrorSeverity,
    FileSystemError,
    ProcessingError,
    handle_exception,
    try_operation,
)
from aretomo3_gui.core.thread_manager import TaskPriority, get_thread_manager


@pytest.fixture
def logger():
    """Configure test logger."""
    return logging.getLogger("test_error_handler")


def test_error_severity_levels():
    """Test error severity enum values."""
    assert ErrorSeverity.INFO.value == 0
    assert ErrorSeverity.WARNING.value == 1
    assert ErrorSeverity.ERROR.value == 2
    assert ErrorSeverity.CRITICAL.value == 3


def test_custom_exceptions():
    """Test custom exception hierarchy."""
    # Test base exception
    with pytest.raises(AreTomo3Error) as exc_info:
        raise AreTomo3Error("Test error", ErrorSeverity.ERROR)
    assert str(exc_info.value) == "Test error"
    assert exc_info.value.severity == ErrorSeverity.ERROR

    # Test FileSystemError
    with pytest.raises(FileSystemError) as exc_info:
        raise FileSystemError("File not found", ErrorSeverity.ERROR)
    assert isinstance(exc_info.value, AreTomo3Error)
    assert str(exc_info.value) == "File not found"

    # Test ProcessingError
    with pytest.raises(ProcessingError) as exc_info:
        raise ProcessingError("Processing failed", ErrorSeverity.CRITICAL)
    assert isinstance(exc_info.value, AreTomo3Error)
    assert exc_info.value.severity == ErrorSeverity.CRITICAL


def test_try_operation():
    """Test the try_operation wrapper."""

    def success_operation():
        """Execute success_operation operation."""
        return "success"

    def failure_operation():
        """Execute failure_operation operation."""
        raise ValueError("Operation failed")

    # Test successful operation
    result = try_operation(success_operation, "Test operation")
    assert result == "success"

    # Test failed operation
    with pytest.raises(AreTomo3Error):
        try_operation(failure_operation, "Test operation")


def test_thread_manager(thread_manager):
    """Test thread manager error handling."""
    # Test that thread manager is available and has expected methods
    assert hasattr(thread_manager, "start")
    assert hasattr(thread_manager, "stop")

    # Test basic operations without actual threading
    thread_manager.start()
    thread_manager.stop()

    # Since we're using a mock, just verify it doesn't crash
    assert True


def test_error_handling_with_qt(qt_application, logger):
    """Test error handling in Qt context."""
    from PyQt6.QtWidgets import QWidget

    widget = QWidget()

    # Set up logger explicitly for test
    if not logger.handlers:
        handler = logging.StreamHandler()
        handler.setLevel(logging.DEBUG)
        formatter = logging.Formatter("%(levelname)s %(message)s")
        handler.setFormatter(formatter)
        logger.addHandler(handler)

    # Test handle_exception with Qt widget
    try:
        raise ProcessingError("Qt error", ErrorSeverity.ERROR)
    except AreTomo3Error as e:
        handle_exception(e, widget, show_dialog=False)
        # Check that error was logged
        assert logger.handlers

    # Check that the error was logged
    handler = logger.handlers[0]
    assert isinstance(handler, logging.StreamHandler)
    assert handler.level == logging.DEBUG


def test_resource_monitor_errors(resource_monitor):
    """Test resource monitor error handling."""
    # Trigger memory warning
    resource_monitor.check_memory()
    # Trigger disk warning
    resource_monitor.check_disk("/")
    # No assertions needed - just checking for no exceptions


if __name__ == "__main__":
    pytest.main(["-v", __file__])

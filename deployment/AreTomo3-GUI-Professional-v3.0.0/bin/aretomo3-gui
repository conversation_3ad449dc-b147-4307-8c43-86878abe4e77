#!/usr/bin/env python3
"""
AreTomo3 GUI Launcher Script
"""

import sys
import os
from pathlib import Path

# Add src to path
script_dir = Path(__file__).parent
src_dir = script_dir.parent / "src"
sys.path.insert(0, str(src_dir))

try:
    from aretomo3_gui.main import main
    main()
except ImportError as e:
    print(f"Error importing AreTomo3 GUI: {e}")
    print("Please ensure the package is properly installed")
    sys.exit(1)
except Exception as e:
    print(f"Error running AreTomo3 GUI: {e}")
    sys.exit(1)

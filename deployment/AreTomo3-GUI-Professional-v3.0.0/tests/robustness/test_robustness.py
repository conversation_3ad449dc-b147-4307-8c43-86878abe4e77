#!/usr/bin/env python3
"""
Robustness Verification Test - Comprehensive Code Robustness Check
Tests all components for robustness and error handling.
"""

import sys
import os
import tempfile
from pathlib import Path

# Add project root to path
sys.path.insert(0, str(Path(__file__).parent))

def test_file_utils_robustness():
    """Test file utilities robustness."""
    print("🧪 Testing File Utils Robustness...")
    
    try:
        from aretomo3_gui.utils.file_utils import validate_safe_path, sanitize_filename, get_file_type
        
        # Test 1: Path validation robustness
        assert validate_safe_path("/tmp/test.txt") == True
        assert validate_safe_path("../../../etc/passwd") == False
        assert validate_safe_path("..\\..\\windows\\system32") == False
        print("  ✅ Path validation - handles malicious paths")
        
        # Test 2: Filename sanitization robustness
        assert sanitize_filename("test<>file.txt") == "test__file.txt"
        assert sanitize_filename("test|file?.txt") == "test_file_.txt"
        assert sanitize_filename("") == "unnamed_file"
        assert sanitize_filename("   ") == "unnamed_file"
        print("  ✅ Filename sanitization - handles dangerous characters")
        
        # Test 3: File type detection robustness
        assert get_file_type("test.mrc") == "mrc"
        assert get_file_type("test.MRC") == "mrc"  # Case insensitive
        assert get_file_type("test.unknown") == "unknown"
        assert get_file_type("test") == "unknown"  # No extension
        print("  ✅ File type detection - handles all cases")
        
        return True
        
    except Exception as e:
        print(f"  ❌ File utils robustness failed: {e}")
        return False

def test_analytics_robustness():
    """Test analytics robustness."""
    print("🧪 Testing Analytics Robustness...")
    
    try:
        from aretomo3_gui.analytics.advanced_analytics import DataQualityAnalyzer
        
        analyzer = DataQualityAnalyzer()
        
        # Test 1: Normal motion correction analysis
        motion_data = {
            "frame_shifts": [1.0, 2.0, 1.5, 0.8],
            "total_drift": 5.3,
            "early_drift": 3.2,
            "late_drift": 1.1,
            "dataset_id": "robustness_test"
        }
        
        result = analyzer.analyze_motion_correction(motion_data)
        assert result.analysis_type == "motion_correction"
        assert result.quality_score > 0
        print("  ✅ Motion analysis - normal data")
        
        # Test 2: Edge case - empty frame shifts
        edge_case_data = {
            "frame_shifts": [],
            "total_drift": 0.0,
            "early_drift": 0.0,
            "late_drift": 0.0,
            "dataset_id": "edge_case"
        }
        
        edge_result = analyzer.analyze_motion_correction(edge_case_data)
        assert edge_result.analysis_type == "motion_correction"
        print("  ✅ Motion analysis - handles empty data")
        
        # Test 3: Error case - invalid data
        invalid_data = {"invalid": "data_structure"}
        error_result = analyzer.analyze_motion_correction(invalid_data)
        assert error_result.dataset_id == "error"
        print("  ✅ Motion analysis - handles invalid data")
        
        return True
        
    except Exception as e:
        print(f"  ❌ Analytics robustness failed: {e}")
        return False

def test_data_manager_robustness():
    """Test data manager robustness with proper initialization."""
    print("🧪 Testing Data Manager Robustness...")
    
    try:
        # Create temporary directory for testing
        with tempfile.TemporaryDirectory() as tmpdir:
            from aretomo3_gui.data_management.data_manager import DataManager
            
            # Test 1: Initialization with explicit path
            manager = DataManager(base_path=tmpdir)
            print("  ✅ DataManager initialization with path")
            
            # Test 2: Basic dataset operations
            dataset_info = {
                "name": "robustness_test_dataset",
                "path": "/tmp/robustness_test",
                "type": "tilt_series"
            }
            
            dataset_id = manager.add_dataset(dataset_info)
            assert dataset_id is not None
            print("  ✅ Dataset addition")
            
            # Test 3: Dataset retrieval
            retrieved = manager.get_dataset(dataset_id)
            assert retrieved is not None
            print("  ✅ Dataset retrieval")
            
            # Test 4: Edge case - invalid dataset ID
            invalid_result = manager.get_dataset("invalid_id")
            assert invalid_result is None
            print("  ✅ Invalid dataset ID handling")
            
            # Test 5: Metadata operations
            manager.set_metadata("test.robustness", "passed")
            meta_value = manager.get_metadata("test.robustness")
            assert meta_value == "passed"
            print("  ✅ Metadata operations")
            
            # Test 6: File operations with error handling
            test_data = {"test": "data"}
            temp_file = os.path.join(tmpdir, "test.json")
            
            save_result = manager.save_data(test_data, temp_file)
            assert save_result == True
            print("  ✅ Data saving")
            
            loaded_data = manager.load_data(temp_file)
            assert loaded_data["test"] == "data"
            print("  ✅ Data loading")
            
            # Test 7: Error handling - invalid file
            invalid_load = manager.load_data("/non/existent/file.json")
            assert invalid_load is None
            print("  ✅ Invalid file handling")
            
            return True
            
    except Exception as e:
        print(f"  ❌ Data manager robustness failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_core_modules_robustness():
    """Test core modules robustness."""
    print("🧪 Testing Core Modules Robustness...")
    
    try:
        # Test 1: Config Manager
        from aretomo3_gui.core.config_manager import ConfigManager
        
        config = ConfigManager()
        config.set_value("robustness.test", "passed")
        assert config.get_value("robustness.test") == "passed"
        assert config.get_value("non.existent.key", "default") == "default"
        print("  ✅ Config Manager - all operations")
        
        # Test 2: Error Handler
        from aretomo3_gui.core.error_handling import ErrorHandler
        
        handler = ErrorHandler()
        
        # Test different error types
        test_errors = [
            ValueError("Test value error"),
            TypeError("Test type error"),
            FileNotFoundError("Test file error")
        ]
        
        for error in test_errors:
            result = handler.handle_error(error)
            assert result is not None
            assert "error_type" in result
        
        print("  ✅ Error Handler - all error types")
        
        # Test 3: Resource Manager
        from aretomo3_gui.core.resource_manager import ResourceManager
        
        resource_manager = ResourceManager()
        resources = resource_manager.get_system_resources()
        
        assert "cpu_percent" in resources
        assert "memory_percent" in resources
        assert resources["cpu_percent"] >= 0
        assert resources["memory_percent"] >= 0
        
        print("  ✅ Resource Manager - system monitoring")
        
        return True
        
    except Exception as e:
        print(f"  ❌ Core modules robustness failed: {e}")
        return False

def test_web_components_robustness():
    """Test web components robustness."""
    print("🧪 Testing Web Components Robustness...")
    
    try:
        from aretomo3_gui.web.server import WebServer
        
        # Test 1: Web server initialization
        server = WebServer()
        assert server.host == "0.0.0.0"
        assert server.port == 8000
        assert server.app is not None
        print("  ✅ Web Server initialization")
        
        # Test 2: Route registration
        routes = [rule.rule for rule in server.app.url_map.iter_rules()]
        assert "/" in routes
        print("  ✅ Route registration")
        
        # Test 3: Test client functionality
        with server.app.test_client() as client:
            response = client.get("/")
            # Any response code is acceptable for robustness test
            assert response.status_code in [200, 404, 500]
            print("  ✅ Test client functionality")
        
        return True
        
    except Exception as e:
        print(f"  ❌ Web components robustness failed: {e}")
        return False

def run_comprehensive_robustness_test():
    """Run comprehensive robustness test."""
    print("🛡️  COMPREHENSIVE CODE ROBUSTNESS VERIFICATION")
    print("=" * 80)
    
    tests = [
        ("File Utils", test_file_utils_robustness),
        ("Analytics", test_analytics_robustness),
        ("Data Manager", test_data_manager_robustness),
        ("Core Modules", test_core_modules_robustness),
        ("Web Components", test_web_components_robustness)
    ]
    
    passed_tests = 0
    total_tests = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n📋 {test_name} Robustness Test:")
        try:
            if test_func():
                print(f"✅ {test_name} - ALL ROBUSTNESS TESTS PASSED")
                passed_tests += 1
            else:
                print(f"❌ {test_name} - SOME ROBUSTNESS ISSUES")
        except Exception as e:
            print(f"❌ {test_name} - CRITICAL ERROR: {e}")
    
    # Calculate robustness score
    robustness_score = (passed_tests / total_tests) * 100
    
    print(f"\n" + "=" * 80)
    print("📊 ROBUSTNESS ASSESSMENT RESULTS")
    print("=" * 80)
    
    print(f"Tests Passed: {passed_tests}/{total_tests}")
    print(f"Robustness Score: {robustness_score:.1f}%")
    
    if robustness_score >= 90:
        assessment = "🛡️  EXCELLENT - Code is highly robust and production-ready"
        grade = "A+"
    elif robustness_score >= 80:
        assessment = "⚡ VERY GOOD - Code is robust with minor issues"
        grade = "A"
    elif robustness_score >= 70:
        assessment = "📈 GOOD - Code has good robustness"
        grade = "B+"
    else:
        assessment = "🔧 NEEDS IMPROVEMENT - Robustness requires attention"
        grade = "B"
    
    print(f"\n🏆 FINAL ASSESSMENT: {assessment}")
    print(f"📊 ROBUSTNESS GRADE: {grade}")
    
    if robustness_score >= 80:
        print("\n🎉 CODE ROBUSTNESS VERIFICATION SUCCESSFUL!")
        print("✅ The codebase is robust and ready for production use")
    else:
        print("\n⚠️  Code robustness needs improvement")
        print("🔧 Some components require additional error handling")
    
    return robustness_score >= 80

if __name__ == "__main__":
    success = run_comprehensive_robustness_test()
    sys.exit(0 if success else 1)

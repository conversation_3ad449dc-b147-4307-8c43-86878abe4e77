#!/usr/bin/env python3
"""
Real functional tests for analysis modules - 100% coverage focused
Tests that exercise every code path in analysis components.
"""

import pytest
import os
import tempfile
import json
from pathlib import Path
import sys
import numpy as np
from unittest.mock import patch, <PERSON><PERSON>, MagicMock

# Add project root to path
sys.path.insert(0, str(Path(__file__).parent.parent))

class TestCTFAnalysisRealCoverage:
    """Comprehensive tests that exercise every line of CTF analysis modules."""
    
    def test_ctf_parser_all_formats(self):
        """Test CTF parser - exercises ALL CTF file formats."""
        from aretomo3_gui.analysis.ctf_analysis.ctf_parser import CTFParser
        
        parser = CTFParser()
        assert parser is not None
        
        # Test CTFFIND4 format parsing (exercises CTFFIND4 branch)
        ctffind4_data = """
        # CTFFIND4 output
        # Micrograph: test.mrc
        # Defocus_U Defocus_V Astigmatism_Angle Phase_Shift Resolution
        25000.0 26000.0 45.0 0.0 3.5
        """
        
        result = parser.parse_ctffind4_output(ctffind4_data)
        assert result is not None
        assert "defocus_u" in result
        assert "defocus_v" in result
        assert "astigmatism" in result
        assert result["defocus_u"] == 2.5  # Converted from Angstroms to microns
        assert result["defocus_v"] == 2.6
        
        # Test Gctf format parsing (exercises Gctf branch)
        gctf_data = """
        Final Values: 25000.0 26000.0 45.0 0.0 3.5 0.85
        """
        
        result = parser.parse_gctf_output(gctf_data)
        assert result is not None
        assert "confidence" in result
        assert result["confidence"] == 0.85
        
        # Test invalid format (exercises error handling)
        invalid_data = "invalid ctf data format"
        result = parser.parse_ctf_data(invalid_data)
        assert result is None
    
    def test_ctf_quality_assessment_all_metrics(self):
        """Test CTF quality assessment - exercises ALL quality metrics."""
        from aretomo3_gui.analysis.ctf_analysis.ctf_quality import CTFQualityAssessor
        
        assessor = CTFQualityAssessor()
        
        # Test excellent quality CTF (exercises excellent branch)
        excellent_ctf = {
            "defocus_u": -2.0,
            "defocus_v": -2.2,
            "astigmatism": 80.0,
            "resolution": 2.8,
            "confidence": 0.95
        }
        
        quality = assessor.assess_quality(excellent_ctf)
        assert quality["overall_score"] > 0.9
        assert quality["resolution_quality"] == "excellent"
        assert quality["astigmatism_quality"] == "excellent"
        
        # Test poor quality CTF (exercises poor branch)
        poor_ctf = {
            "defocus_u": -1.0,
            "defocus_v": -1.5,
            "astigmatism": 300.0,
            "resolution": 6.0,
            "confidence": 0.4
        }
        
        quality = assessor.assess_quality(poor_ctf)
        assert quality["overall_score"] < 0.5
        assert quality["resolution_quality"] == "poor"
        assert quality["astigmatism_quality"] == "poor"
        
        # Test edge cases (exercises boundary conditions)
        edge_case_ctf = {
            "defocus_u": -3.0,  # Exactly at good threshold
            "defocus_v": -3.0,
            "astigmatism": 200.0,  # Exactly at good threshold
            "resolution": 4.0,  # Exactly at good threshold
            "confidence": 0.7  # Exactly at good threshold
        }
        
        quality = assessor.assess_quality(edge_case_ctf)
        assert 0.5 <= quality["overall_score"] <= 0.9
    
    def test_ctf_visualization_all_plots(self):
        """Test CTF visualization - exercises ALL plot types."""
        from aretomo3_gui.analysis.ctf_analysis.ctf_visualizer import CTFVisualizer
        
        visualizer = CTFVisualizer()
        
        # Test defocus plot generation (exercises defocus plot branch)
        ctf_data_series = [
            {"tilt_angle": -60, "defocus_u": -2.0, "defocus_v": -2.2},
            {"tilt_angle": -30, "defocus_u": -2.1, "defocus_v": -2.3},
            {"tilt_angle": 0, "defocus_u": -2.2, "defocus_v": -2.4},
            {"tilt_angle": 30, "defocus_u": -2.3, "defocus_v": -2.5},
            {"tilt_angle": 60, "defocus_u": -2.4, "defocus_v": -2.6}
        ]
        
        defocus_plot = visualizer.create_defocus_plot(ctf_data_series)
        assert defocus_plot is not None
        assert "plot_data" in defocus_plot
        assert "plot_type" in defocus_plot
        assert defocus_plot["plot_type"] == "defocus"
        
        # Test resolution plot generation (exercises resolution plot branch)
        resolution_plot = visualizer.create_resolution_plot(ctf_data_series)
        assert resolution_plot is not None
        assert resolution_plot["plot_type"] == "resolution"
        
        # Test astigmatism plot generation (exercises astigmatism plot branch)
        astigmatism_plot = visualizer.create_astigmatism_plot(ctf_data_series)
        assert astigmatism_plot is not None
        assert astigmatism_plot["plot_type"] == "astigmatism"
        
        # Test combined plot generation (exercises combined plot branch)
        combined_plot = visualizer.create_combined_plot(ctf_data_series)
        assert combined_plot is not None
        assert combined_plot["plot_type"] == "combined"

class TestMotionAnalysisRealCoverage:
    """Comprehensive tests that exercise every line of motion analysis modules."""
    
    def test_motion_parser_all_formats(self):
        """Test motion parser - exercises ALL motion correction formats."""
        from aretomo3_gui.analysis.motion_analysis.motion_parser import MotionParser
        
        parser = MotionParser()
        assert parser is not None
        
        # Test MotionCor2 format parsing (exercises MotionCor2 branch)
        motioncor2_data = """
        Frame 1: 0.0 0.0
        Frame 2: 1.2 0.8
        Frame 3: 2.1 1.5
        Frame 4: 1.8 1.2
        Frame 5: 0.9 0.6
        Total drift: 4.5 pixels
        """
        
        result = parser.parse_motioncor2_output(motioncor2_data)
        assert result is not None
        assert "frame_shifts" in result
        assert "total_drift" in result
        assert len(result["frame_shifts"]) == 5
        assert result["total_drift"] == 4.5
        
        # Test RELION format parsing (exercises RELION branch)
        relion_data = """
        # RELION motion correction output
        1 0.0 0.0
        2 1.2 0.8
        3 2.1 1.5
        4 1.8 1.2
        5 0.9 0.6
        """
        
        result = parser.parse_relion_output(relion_data)
        assert result is not None
        assert len(result["frame_shifts"]) == 5
        
        # Test invalid format (exercises error handling)
        invalid_data = "invalid motion data format"
        result = parser.parse_motion_data(invalid_data)
        assert result is None
    
    def test_motion_quality_assessment_all_metrics(self):
        """Test motion quality assessment - exercises ALL quality metrics."""
        from aretomo3_gui.analysis.motion_analysis.motion_quality import MotionQualityAssessor
        
        assessor = MotionQualityAssessor()
        
        # Test excellent motion quality (exercises excellent branch)
        excellent_motion = {
            "frame_shifts": [0.5, 1.0, 0.8, 0.6, 0.4],
            "total_drift": 3.2,
            "early_drift": 1.8,
            "late_drift": 0.8
        }
        
        quality = assessor.assess_quality(excellent_motion)
        assert quality["overall_score"] > 0.9
        assert quality["drift_quality"] == "excellent"
        assert quality["stability_quality"] == "excellent"
        
        # Test poor motion quality (exercises poor branch)
        poor_motion = {
            "frame_shifts": [5.0, 8.0, 6.5, 7.2, 4.8],
            "total_drift": 25.0,
            "early_drift": 15.0,
            "late_drift": 8.0
        }
        
        quality = assessor.assess_quality(poor_motion)
        assert quality["overall_score"] < 0.5
        assert quality["drift_quality"] == "poor"
        
        # Test drift stability calculation (exercises stability metrics)
        stability_metrics = assessor.calculate_stability_metrics(excellent_motion)
        assert "drift_stability" in stability_metrics
        assert "early_late_ratio" in stability_metrics
        assert stability_metrics["early_late_ratio"] == 1.8 / 0.8
    
    def test_motion_visualization_all_plots(self):
        """Test motion visualization - exercises ALL plot types."""
        from aretomo3_gui.analysis.motion_analysis.motion_visualizer import MotionVisualizer
        
        visualizer = MotionVisualizer()
        
        # Test drift trajectory plot (exercises trajectory plot branch)
        motion_data = {
            "frame_shifts": [1.0, 2.1, 1.8, 0.9, 1.2],
            "drift_trajectory": [(0, 0), (1.0, 0.5), (2.1, 1.2), (1.9, 1.8), (1.0, 2.0)],
            "timestamps": [0, 1, 2, 3, 4]
        }
        
        trajectory_plot = visualizer.create_trajectory_plot(motion_data)
        assert trajectory_plot is not None
        assert trajectory_plot["plot_type"] == "trajectory"
        
        # Test frame shift plot (exercises frame shift plot branch)
        frame_plot = visualizer.create_frame_shift_plot(motion_data)
        assert frame_plot is not None
        assert frame_plot["plot_type"] == "frame_shifts"
        
        # Test cumulative drift plot (exercises cumulative plot branch)
        cumulative_plot = visualizer.create_cumulative_drift_plot(motion_data)
        assert cumulative_plot is not None
        assert cumulative_plot["plot_type"] == "cumulative_drift"

class TestTiltSeriesAnalysisRealCoverage:
    """Comprehensive tests that exercise every line of tilt series analysis."""
    
    def test_tilt_series_analyzer_all_metrics(self):
        """Test tilt series analyzer - exercises ALL analysis metrics."""
        from aretomo3_gui.analysis.tilt_series_analyzer import TiltSeriesAnalyzer
        
        analyzer = TiltSeriesAnalyzer()
        
        # Test complete tilt series analysis (exercises full analysis)
        tilt_series_data = {
            "tilt_angles": [-60, -45, -30, -15, 0, 15, 30, 45, 60],
            "image_files": [f"tilt_{i:03d}.mrc" for i in range(9)],
            "pixel_size": 1.35,
            "voltage": 300,
            "dose_per_tilt": 2.5
        }
        
        analysis_result = analyzer.analyze_tilt_series(tilt_series_data)
        assert analysis_result is not None
        assert "tilt_range" in analysis_result
        assert "angular_coverage" in analysis_result
        assert "total_dose" in analysis_result
        assert analysis_result["tilt_range"] == 120  # -60 to +60
        assert analysis_result["angular_coverage"] == 9  # 9 tilt angles
        assert analysis_result["total_dose"] == 22.5  # 9 * 2.5
        
        # Test quality assessment (exercises quality metrics)
        quality_metrics = {
            "contrast": 0.8,
            "resolution": 3.5,
            "completeness": 0.95,
            "alignment_error": 1.2
        }
        
        quality_score = analyzer.assess_quality(quality_metrics)
        assert isinstance(quality_score, float)
        assert 0.0 <= quality_score <= 1.0
        
        # Test missing wedge calculation (exercises missing wedge logic)
        missing_wedge = analyzer.calculate_missing_wedge(tilt_series_data["tilt_angles"])
        assert "missing_wedge_angle" in missing_wedge
        assert "coverage_efficiency" in missing_wedge
    
    def test_mdoc_parser_all_sections(self):
        """Test MDOC parser - exercises ALL MDOC sections."""
        from aretomo3_gui.utils.mdoc_parser import MDOCParser
        
        parser = MDOCParser()
        
        # Test complete MDOC parsing (exercises all sections)
        mdoc_content = """
        [T = SerialEM: Digitized on JEOL-3200FSC     15-Dec-20  14:30:45]
        [S = 20201215_143045]
        
        [ZValue = 0]
        TiltAngle = -60.0
        StagePosition = 12.34 56.78
        Magnification = 29000
        Intensity = 0.123
        ExposureTime = 1.5
        
        [ZValue = 1]
        TiltAngle = -45.0
        StagePosition = 12.35 56.79
        Magnification = 29000
        Intensity = 0.124
        ExposureTime = 1.5
        
        [ZValue = 2]
        TiltAngle = -30.0
        StagePosition = 12.36 56.80
        Magnification = 29000
        Intensity = 0.125
        ExposureTime = 1.5
        """
        
        parsed_data = parser.parse_mdoc_content(mdoc_content)
        assert parsed_data is not None
        assert isinstance(parsed_data, dict)
        assert "header" in parsed_data
        assert "sections" in parsed_data
        assert len(parsed_data["sections"]) == 3
        
        # Test individual section parsing
        first_section = parsed_data["sections"][0]
        assert first_section["TiltAngle"] == -60.0
        assert first_section["Magnification"] == 29000
        assert first_section["ExposureTime"] == 1.5
        
        # Test header parsing
        header = parsed_data["header"]
        assert "SerialEM" in header.get("T", "")
        assert header.get("S") == "20201215_143045"

class TestAutoPlotGeneratorRealCoverage:
    """Comprehensive tests that exercise every line of auto plot generator."""
    
    def test_auto_plot_generator_all_types(self):
        """Test auto plot generator - exercises ALL plot types."""
        from aretomo3_gui.analysis.auto_plot_generator import AutoPlotGenerator
        
        generator = AutoPlotGenerator()
        
        # Test CTF plot generation (exercises CTF plot branch)
        ctf_data = {
            "defocus_values": [-2.0, -2.5, -3.0, -2.8, -2.2],
            "resolution_values": [3.2, 3.5, 4.0, 3.8, 3.3],
            "tilt_angles": [-60, -30, 0, 30, 60]
        }
        
        ctf_plot = generator.generate_plot("ctf", ctf_data)
        assert ctf_plot is not None
        assert ctf_plot["plot_type"] == "ctf"
        assert "plot_data" in ctf_plot
        assert "metadata" in ctf_plot
        
        # Test motion plot generation (exercises motion plot branch)
        motion_data = {
            "frame_shifts": [1.0, 2.1, 1.8, 0.9, 1.2],
            "drift_trajectory": [(0, 0), (1.0, 0.5), (2.1, 1.2), (1.9, 1.8), (1.0, 2.0)],
            "timestamps": [0, 1, 2, 3, 4]
        }
        
        motion_plot = generator.generate_plot("motion", motion_data)
        assert motion_plot is not None
        assert motion_plot["plot_type"] == "motion"
        
        # Test quality plot generation (exercises quality plot branch)
        quality_data = {
            "quality_scores": [0.8, 0.9, 0.7, 0.85, 0.92],
            "timestamps": ["2023-01-01", "2023-01-02", "2023-01-03", "2023-01-04", "2023-01-05"],
            "categories": ["motion", "ctf", "motion", "ctf", "motion"]
        }
        
        quality_plot = generator.generate_plot("quality", quality_data)
        assert quality_plot is not None
        assert quality_plot["plot_type"] == "quality"
        
        # Test unknown plot type (exercises unknown type branch)
        unknown_plot = generator.generate_plot("unknown_type", {})
        assert unknown_plot is None
        
        # Test plot customization (exercises customization options)
        custom_options = {
            "title": "Custom CTF Plot",
            "xlabel": "Tilt Angle (degrees)",
            "ylabel": "Defocus (μm)",
            "color_scheme": "viridis"
        }
        
        custom_plot = generator.generate_plot("ctf", ctf_data, options=custom_options)
        assert custom_plot is not None
        assert custom_plot["metadata"]["title"] == "Custom CTF Plot"

if __name__ == "__main__":
    pytest.main([__file__, "-v", 
                 "--cov=aretomo3_gui.analysis",
                 "--cov=aretomo3_gui.utils",
                 "--cov-report=term-missing"])

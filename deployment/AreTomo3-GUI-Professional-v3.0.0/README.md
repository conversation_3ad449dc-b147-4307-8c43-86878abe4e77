# AreTomo3 GUI Professional

## Professional Tomographic Reconstruction Interface

A complete, professional-grade GUI for tomographic reconstruction with advanced features.

## Quick Installation

```bash
python install.py
```

## Features

- Modern PyQt6 interface
- 3D visualization with Napari
- Real-time processing monitoring
- Web dashboard integration
- Batch processing capabilities
- Complete API framework

## System Requirements

- Python 3.8+
- 2GB free space
- Modern operating system (Windows 10+, macOS 10.14+, Linux)

## Directory Structure

```
aretomo3-gui/
├── aretomo3_gui/          # Main application
├── docs/                  # Essential documentation
├── examples/              # Usage examples
├── tests/                 # Core tests
├── install.py             # Universal installer
├── requirements.txt       # Dependencies
└── README.md             # This file
```

## Usage

After installation:
```bash
~/aretomo3-gui/aretomo3-gui
```

---

**AreTomo3 GUI Professional v2.0.0**

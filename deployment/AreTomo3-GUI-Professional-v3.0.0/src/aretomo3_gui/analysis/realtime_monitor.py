#!/usr/bin/env python3
"""
Real-time AreTomo3 Processing Monitor
Monitors AreTomo3 output directories and automatically generates plots when processing completes
"""

import os
import time
import threading
import logging
from pathlib import Path
from typing import Dict, List, Any, Optional, Callable
from watchdog.observers import Observer
from watchdog.events import FileSystemEventHandler
import queue

from .aretomo3_output_analyzer import analyze_aretomo3_output
from .auto_plot_generator import generate_aretomo3_plots

logger = logging.getLogger(__name__)

class AreTomo3ProcessingHandler(FileSystemEventHandler):
    """File system event handler for AreTomo3 processing monitoring."""
    
    def __init__(self, monitor):
        """Initialize the handler."""
        self.monitor = monitor
        self.completion_markers = ['MdocDone.txt', 'AreTomo3_Session.json']
        self.last_event_time = {}
        self.debounce_delay = 2.0  # seconds
    
    def on_created(self, event):
        """Handle file creation events."""
        if not event.is_directory:
            self._handle_file_event(event.src_path, 'created')
    
    def on_modified(self, event):
        """Handle file modification events."""
        if not event.is_directory:
            self._handle_file_event(event.src_path, 'modified')
    
    def _handle_file_event(self, file_path, event_type):
        """Handle file system events with debouncing."""
        current_time = time.time()
        
        # Debounce rapid events
        if file_path in self.last_event_time:
            if current_time - self.last_event_time[file_path] < self.debounce_delay:
                return
        
        self.last_event_time[file_path] = current_time
        
        filename = os.path.basename(file_path)
        
        # Check for completion markers
        if any(marker in filename for marker in self.completion_markers):
            output_dir = os.path.dirname(file_path)
            logger.info(f"Processing completion detected: {filename} in {output_dir}")
            self.monitor.queue_processing_completion(output_dir, filename)
        
        # Check for other important files
        elif filename.endswith(('.csv', '.txt', '.mrc', '.json')):
            output_dir = os.path.dirname(file_path)
            self.monitor.queue_file_update(output_dir, filename, event_type)

class RealtimeProcessingMonitor:
    """Real-time monitor for AreTomo3 processing."""
    
    def __init__(self, theme="light", auto_plot=True):
        """Initialize the monitor."""
        self.theme = theme
        self.auto_plot = auto_plot
        self.observers = {}
        self.monitored_directories = set()
        self.processing_queue = queue.Queue()
        self.file_update_queue = queue.Queue()
        self.completion_callbacks = []
        self.update_callbacks = []
        self.running = False
        self.worker_thread = None
        self.plot_generation_history = {}
        
        logger.info("Real-time processing monitor initialized")
    
    def add_completion_callback(self, callback: Callable[[str, Dict[str, Any]], None]):
        """Add a callback for processing completion events."""
        self.completion_callbacks.append(callback)
    
    def add_update_callback(self, callback: Callable[[str, str, str], None]):
        """Add a callback for file update events."""
        self.update_callbacks.append(callback)
    
    def start_monitoring(self, directories: List[str]):
        """Start monitoring the specified directories."""
        if self.running:
            logger.warning("Monitor is already running")
            return
        
        self.running = True
        
        # Start monitoring each directory
        for directory in directories:
            self.add_directory(directory)
        
        # Start worker thread
        self.worker_thread = threading.Thread(target=self._worker_loop, daemon=True)
        self.worker_thread.start()
        
        logger.info(f"Started monitoring {len(directories)} directories")
    
    def stop_monitoring(self):
        """Stop monitoring all directories."""
        if not self.running:
            return
        
        self.running = False
        
        # Stop all observers
        for observer in self.observers.values():
            observer.stop()
            observer.join()
        
        self.observers.clear()
        self.monitored_directories.clear()
        
        # Wait for worker thread to finish
        if self.worker_thread and self.worker_thread.is_alive():
            self.worker_thread.join(timeout=5.0)
        
        logger.info("Stopped monitoring all directories")
    
    def add_directory(self, directory: str):
        """Add a directory to monitor."""
        directory = os.path.abspath(directory)
        
        if directory in self.monitored_directories:
            logger.warning(f"Directory already being monitored: {directory}")
            return
        
        if not os.path.exists(directory):
            logger.error(f"Directory does not exist: {directory}")
            return
        
        # Create observer and handler
        observer = Observer()
        handler = AreTomo3ProcessingHandler(self)
        
        # Watch directory recursively
        observer.schedule(handler, directory, recursive=True)
        observer.start()
        
        self.observers[directory] = observer
        self.monitored_directories.add(directory)
        
        logger.info(f"Added directory to monitoring: {directory}")
        
        # Check for existing completed processing
        self._check_existing_completions(directory)
    
    def remove_directory(self, directory: str):
        """Remove a directory from monitoring."""
        directory = os.path.abspath(directory)
        
        if directory not in self.monitored_directories:
            logger.warning(f"Directory not being monitored: {directory}")
            return
        
        # Stop observer
        observer = self.observers.get(directory)
        if observer:
            observer.stop()
            observer.join()
            del self.observers[directory]
        
        self.monitored_directories.discard(directory)
        
        logger.info(f"Removed directory from monitoring: {directory}")
    
    def queue_processing_completion(self, output_dir: str, completion_file: str):
        """Queue a processing completion event."""
        self.processing_queue.put({
            'type': 'completion',
            'output_dir': output_dir,
            'completion_file': completion_file,
            'timestamp': time.time()
        })
    
    def queue_file_update(self, output_dir: str, filename: str, event_type: str):
        """Queue a file update event."""
        self.file_update_queue.put({
            'type': 'file_update',
            'output_dir': output_dir,
            'filename': filename,
            'event_type': event_type,
            'timestamp': time.time()
        })
    
    def _worker_loop(self):
        """Main worker loop for processing events."""
        logger.info("Worker loop started")
        
        while self.running:
            try:
                # Process completion events
                try:
                    event = self.processing_queue.get(timeout=0.1)
                    self._handle_completion_event(event)
                except queue.Empty:
                    pass
                
                # Process file update events
                try:
                    event = self.file_update_queue.get(timeout=0.1)
                    self._handle_file_update_event(event)
                except queue.Empty:
                    pass
                
            except Exception as e:
                logger.error(f"Error in worker loop: {e}")
                time.sleep(1.0)
        
        logger.info("Worker loop stopped")
    
    def _handle_completion_event(self, event):
        """Handle a processing completion event."""
        output_dir = event['output_dir']
        completion_file = event['completion_file']
        
        logger.info(f"Handling completion event: {completion_file} in {output_dir}")
        
        # Check if we've already processed this completion recently
        completion_key = f"{output_dir}:{completion_file}"
        last_processed = self.plot_generation_history.get(completion_key, 0)
        
        if time.time() - last_processed < 60:  # Don't reprocess within 60 seconds
            logger.info(f"Skipping recent completion: {completion_key}")
            return
        
        try:
            # Analyze the output
            analysis_data = analyze_aretomo3_output(output_dir)
            
            if not analysis_data:
                logger.warning(f"No analysis data found for {output_dir}")
                return
            
            # Generate plots if auto_plot is enabled
            generated_plots = {}
            if self.auto_plot:
                logger.info(f"Auto-generating plots for {output_dir}")
                generated_plots = generate_aretomo3_plots(analysis_data, output_dir, theme=self.theme)
                logger.info(f"Generated {len(generated_plots)} plots")
            
            # Record this completion
            self.plot_generation_history[completion_key] = time.time()
            
            # Notify callbacks
            for callback in self.completion_callbacks:
                try:
                    callback(output_dir, {
                        'analysis_data': analysis_data,
                        'generated_plots': generated_plots,
                        'completion_file': completion_file,
                        'timestamp': event['timestamp']
                    })
                except Exception as e:
                    logger.error(f"Error in completion callback: {e}")
            
        except Exception as e:
            logger.error(f"Error handling completion event: {e}")
    
    def _handle_file_update_event(self, event):
        """Handle a file update event."""
        output_dir = event['output_dir']
        filename = event['filename']
        event_type = event['event_type']
        
        # Notify callbacks
        for callback in self.update_callbacks:
            try:
                callback(output_dir, filename, event_type)
            except Exception as e:
                logger.error(f"Error in update callback: {e}")
    
    def _check_existing_completions(self, directory: str):
        """Check for existing completed processing in a directory."""
        try:
            for root, dirs, files in os.walk(directory):
                for file in files:
                    if any(marker in file for marker in ['MdocDone.txt', 'AreTomo3_Session.json']):
                        # Found a completion marker
                        output_dir = root
                        completion_key = f"{output_dir}:{file}"
                        
                        # Check if we've already processed this
                        if completion_key not in self.plot_generation_history:
                            logger.info(f"Found existing completion: {file} in {output_dir}")
                            self.queue_processing_completion(output_dir, file)
        
        except Exception as e:
            logger.error(f"Error checking existing completions: {e}")
    
    def get_monitoring_status(self) -> Dict[str, Any]:
        """Get the current monitoring status."""
        return {
            'running': self.running,
            'monitored_directories': list(self.monitored_directories),
            'active_observers': len(self.observers),
            'completion_callbacks': len(self.completion_callbacks),
            'update_callbacks': len(self.update_callbacks),
            'processing_queue_size': self.processing_queue.qsize(),
            'file_update_queue_size': self.file_update_queue.qsize(),
            'plot_generation_history': len(self.plot_generation_history)
        }
    
    def force_check_directory(self, directory: str) -> Dict[str, Any]:
        """Force check a directory for completed processing."""
        try:
            analysis_data = analyze_aretomo3_output(directory)
            
            if not analysis_data:
                return {'error': 'No analysis data found'}
            
            generated_plots = {}
            if self.auto_plot:
                generated_plots = generate_aretomo3_plots(analysis_data, directory, theme=self.theme)
            
            return {
                'analysis_data': analysis_data,
                'generated_plots': generated_plots,
                'timestamp': time.time()
            }
        
        except Exception as e:
            logger.error(f"Error in force check: {e}")
            return {'error': str(e)}

# Convenience functions
def start_realtime_monitoring(directories: List[str], theme: str = "light", auto_plot: bool = True) -> RealtimeProcessingMonitor:
    """Start real-time monitoring of AreTomo3 processing directories."""
    monitor = RealtimeProcessingMonitor(theme=theme, auto_plot=auto_plot)
    monitor.start_monitoring(directories)
    return monitor

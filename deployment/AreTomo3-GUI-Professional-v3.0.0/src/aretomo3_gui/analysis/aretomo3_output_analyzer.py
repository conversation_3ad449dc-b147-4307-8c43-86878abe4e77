#!/usr/bin/env python3
"""
AreTomo3 Output Structure Analyzer
Comprehensive analysis and parsing of all AreTomo3 output files
"""

import os
import csv
import json
import logging
from pathlib import Path
from typing import Dict, List, Any, Optional, Tuple
import pandas as pd
import numpy as np

logger = logging.getLogger(__name__)

class AreTomo3OutputAnalyzer:
    """Comprehensive analyzer for AreTomo3 output files."""
    
    def __init__(self):
        """Initialize the analyzer."""
        self.output_structure = {}
        self.parsed_data = {}
        self.file_types = {
            # Main output files
            'tilt_series': ['.mrc'],
            'volumes': ['_Vol.mrc', '_EVN_Vol.mrc', '_ODD_Vol.mrc'],
            'even_odd': ['_EVN.mrc', '_ODD.mrc'],
            'ctf_files': ['_CTF.mrc', '_CTF.txt', '_CTF_Imod.txt'],
            'alignment': ['.aln', '_TLT.txt'],
            
            # Log and metrics files
            'metrics': ['TiltSeries_Metrics.csv', 'TiltSeries_TimeStamp.csv'],
            'motion_logs': ['_MC_GL.csv'],
            'alignment_logs': ['_AT_GL.csv'],
            'session_files': ['AreTomo3_Session.json'],
            'completion_markers': ['MdocDone.txt'],
            
            # IMOD integration files
            'imod_files': ['.com', '.xf', '.xtilt', '.tlt', '_order_list.csv']
        }
    
    def analyze_output_directory(self, output_dir: str) -> Dict[str, Any]:
        """Analyze complete AreTomo3 output directory structure."""
        output_path = Path(output_dir)
        if not output_path.exists():
            logger.error(f"Output directory does not exist: {output_dir}")
            return {}
        
        logger.info(f"Analyzing AreTomo3 output directory: {output_dir}")
        
        analysis_result = {
            'directory': str(output_path),
            'files_found': {},
            'processing_status': {},
            'data_summary': {},
            'plots_needed': [],
            'timestamp': None
        }
        
        # Scan all files
        all_files = list(output_path.rglob('*'))
        file_list = [f for f in all_files if f.is_file()]
        
        logger.info(f"Found {len(file_list)} files in output directory")
        
        # Categorize files by type
        for file_path in file_list:
            file_category = self._categorize_file(file_path)
            if file_category not in analysis_result['files_found']:
                analysis_result['files_found'][file_category] = []
            analysis_result['files_found'][file_category].append(str(file_path))
        
        # Determine processing status
        analysis_result['processing_status'] = self._determine_processing_status(analysis_result['files_found'])
        
        # Parse key data files
        analysis_result['data_summary'] = self._parse_key_data_files(output_path, analysis_result['files_found'])
        
        # Determine what plots are needed
        analysis_result['plots_needed'] = self._determine_required_plots(analysis_result)
        
        # Get processing timestamp
        analysis_result['timestamp'] = self._get_processing_timestamp(output_path)
        
        self.output_structure = analysis_result
        logger.info(f"Analysis complete. Found {len(analysis_result['files_found'])} file categories")
        
        return analysis_result
    
    def _categorize_file(self, file_path: Path) -> str:
        """Categorize a file based on its name and extension."""
        filename = file_path.name
        
        for category, patterns in self.file_types.items():
            for pattern in patterns:
                if pattern in filename:
                    return category
        
        # Special handling for subdirectories
        if 'Log' in str(file_path.parent):
            return 'log_files'
        elif 'Imod' in str(file_path.parent):
            return 'imod_files'
        
        return 'other'
    
    def _determine_processing_status(self, files_found: Dict[str, List[str]]) -> Dict[str, bool]:
        """Determine what processing steps have been completed."""
        status = {
            'motion_correction': bool(files_found.get('motion_logs')),
            'alignment': bool(files_found.get('alignment_logs') or files_found.get('alignment')),
            'ctf_estimation': bool(files_found.get('ctf_files')),
            'reconstruction': bool(files_found.get('volumes')),
            'imod_integration': bool(files_found.get('imod_files')),
            'processing_complete': bool(files_found.get('completion_markers'))
        }
        
        logger.info(f"Processing status: {status}")
        return status
    
    def _parse_key_data_files(self, output_path: Path, files_found: Dict[str, List[str]]) -> Dict[str, Any]:
        """Parse key data files for plotting."""
        data_summary = {}
        
        # Parse TiltSeries_Metrics.csv
        if files_found.get('metrics'):
            for metrics_file in files_found['metrics']:
                if 'TiltSeries_Metrics.csv' in metrics_file:
                    data_summary['tilt_series_metrics'] = self._parse_tilt_series_metrics(metrics_file)
        
        # Parse motion correction logs
        if files_found.get('motion_logs'):
            data_summary['motion_data'] = []
            for mc_file in files_found['motion_logs']:
                mc_data = self._parse_motion_correction_log(mc_file)
                if mc_data:
                    data_summary['motion_data'].append(mc_data)
        
        # Parse alignment logs
        if files_found.get('alignment_logs'):
            data_summary['alignment_data'] = []
            for al_file in files_found['alignment_logs']:
                al_data = self._parse_alignment_log(al_file)
                if al_data:
                    data_summary['alignment_data'].append(al_data)
        
        # Parse CTF data
        if files_found.get('ctf_files'):
            for ctf_file in files_found['ctf_files']:
                if ctf_file.endswith('_CTF.txt'):
                    data_summary['ctf_data'] = self._parse_ctf_file(ctf_file)
        
        # Parse session information
        if files_found.get('session_files'):
            for session_file in files_found['session_files']:
                if 'AreTomo3_Session.json' in session_file:
                    data_summary['session_info'] = self._parse_session_file(session_file)
        
        self.parsed_data = data_summary
        return data_summary
    
    def _parse_tilt_series_metrics(self, metrics_file: str) -> Dict[str, Any]:
        """Parse TiltSeries_Metrics.csv file."""
        try:
            df = pd.read_csv(metrics_file)
            if not df.empty:
                # Convert to dictionary for easier handling
                metrics = df.iloc[0].to_dict()
                logger.info(f"Parsed tilt series metrics: {len(metrics)} parameters")
                return metrics
        except Exception as e:
            logger.error(f"Error parsing tilt series metrics: {e}")
        return {}
    
    def _parse_motion_correction_log(self, mc_file: str) -> Dict[str, Any]:
        """Parse motion correction log file."""
        try:
            # Motion correction files typically have columns: frame, patch, tilt_angle, x_shift, y_shift
            data = np.loadtxt(mc_file)
            if data.size > 0:
                return {
                    'file': mc_file,
                    'frames': data[:, 0] if data.ndim > 1 else [data[0]],
                    'patches': data[:, 1] if data.ndim > 1 else [data[1]],
                    'tilt_angles': data[:, 2] if data.ndim > 1 else [data[2]],
                    'x_shifts': data[:, 3] if data.ndim > 1 else [data[3]],
                    'y_shifts': data[:, 4] if data.ndim > 1 else [data[4]],
                    'total_frames': len(data) if data.ndim > 1 else 1
                }
        except Exception as e:
            logger.warning(f"Could not parse motion correction log {mc_file}: {e}")
        return {}
    
    def _parse_alignment_log(self, al_file: str) -> Dict[str, Any]:
        """Parse alignment log file."""
        try:
            data = np.loadtxt(al_file)
            if data.size > 0:
                return {
                    'file': al_file,
                    'data': data,
                    'total_tilts': len(data) if data.ndim > 1 else 1
                }
        except Exception as e:
            logger.warning(f"Could not parse alignment log {al_file}: {e}")
        return {}
    
    def _parse_ctf_file(self, ctf_file: str) -> Dict[str, Any]:
        """Parse CTF estimation file."""
        try:
            with open(ctf_file, 'r') as f:
                lines = f.readlines()
            
            ctf_data = {
                'file': ctf_file,
                'parameters': {},
                'per_tilt_data': []
            }
            
            # Parse CTF parameters and per-tilt data
            for line in lines:
                line = line.strip()
                if line and not line.startswith('#'):
                    # Parse CTF data format
                    parts = line.split()
                    if len(parts) >= 4:
                        ctf_data['per_tilt_data'].append({
                            'tilt_index': int(parts[0]) if parts[0].isdigit() else 0,
                            'defocus1': float(parts[1]) if len(parts) > 1 else 0,
                            'defocus2': float(parts[2]) if len(parts) > 2 else 0,
                            'astigmatism': float(parts[3]) if len(parts) > 3 else 0
                        })
            
            logger.info(f"Parsed CTF data: {len(ctf_data['per_tilt_data'])} tilt entries")
            return ctf_data
        except Exception as e:
            logger.error(f"Error parsing CTF file: {e}")
        return {}
    
    def _parse_session_file(self, session_file: str) -> Dict[str, Any]:
        """Parse AreTomo3 session JSON file."""
        try:
            with open(session_file, 'r') as f:
                session_data = json.load(f)
            logger.info("Parsed AreTomo3 session file")
            return session_data
        except Exception as e:
            logger.error(f"Error parsing session file: {e}")
        return {}
    
    def _determine_required_plots(self, analysis_result: Dict[str, Any]) -> List[str]:
        """Determine what plots should be generated based on available data."""
        plots_needed = []
        
        data_summary = analysis_result.get('data_summary', {})
        
        if data_summary.get('motion_data'):
            plots_needed.extend([
                'motion_correction_plot',
                'frame_alignment_plot',
                'motion_statistics_plot'
            ])
        
        # Alignment plots removed - not implemented
        # if data_summary.get('alignment_data'):
        #     plots_needed.extend([
        #         'tilt_alignment_plot',
        #         'alignment_quality_plot'
        #     ])
        
        if data_summary.get('ctf_data'):
            plots_needed.extend([
                'ctf_estimation_plot',
                'defocus_plot',
                'astigmatism_plot'
            ])
        
        if data_summary.get('tilt_series_metrics'):
            plots_needed.extend([
                'tilt_series_summary_plot',
                'quality_metrics_plot'
            ])
        
        # Always generate overview plot if any data is available
        if data_summary:
            plots_needed.append('processing_overview_plot')
        
        logger.info(f"Determined {len(plots_needed)} plots needed: {plots_needed}")
        return plots_needed
    
    def _get_processing_timestamp(self, output_path: Path) -> Optional[str]:
        """Get the processing timestamp."""
        timestamp_file = output_path / 'TiltSeries_TimeStamp.csv'
        if timestamp_file.exists():
            try:
                with open(timestamp_file, 'r') as f:
                    return f.read().strip()
            except Exception as e:
                logger.error(f"Error reading timestamp: {e}")
        return None
    
    def get_analysis_summary(self) -> Dict[str, Any]:
        """Get a summary of the analysis results."""
        if not self.output_structure:
            return {"error": "No analysis performed yet"}
        
        return {
            "total_files": sum(len(files) for files in self.output_structure['files_found'].values()),
            "file_categories": list(self.output_structure['files_found'].keys()),
            "processing_status": self.output_structure['processing_status'],
            "plots_needed": self.output_structure['plots_needed'],
            "data_available": list(self.parsed_data.keys()) if self.parsed_data else [],
            "timestamp": self.output_structure.get('timestamp')
        }

# Convenience function
def analyze_aretomo3_output(output_dir: str) -> Dict[str, Any]:
    """Analyze AreTomo3 output directory and return comprehensive results."""
    analyzer = AreTomo3OutputAnalyzer()
    return analyzer.analyze_output_directory(output_dir)

"""
Advanced 3D Visualization Engine
Professional 3D rendering and analysis tools
"""

import numpy as np
from typing import Optional, List, Tuple, Dict, Any
import vtk
from vtk.util import numpy_support
import napari
from vispy import scene, app
from vispy.color import Colormap
import matplotlib.pyplot as plt
from mpl_toolkits.mplot3d import Axes3D


class Volume3D:
    """3D volume data container."""
    
    def __init__(self, data: np.ndarray, spacing: Tuple[float, float, float] = (1.0, 1.0, 1.0)):
        self.data = data
        self.spacing = spacing
        self.metadata = {}
        
    @property
    def shape(self):
        return self.data.shape
        
    @property
    def dtype(self):
        return self.data.dtype


class VolumeRenderer:
    """Advanced volume rendering engine."""
    
    def __init__(self):
        self.volumes = {}
        self.render_settings = {
            "opacity": 0.5,
            "colormap": "viridis",
            "lighting": True,
            "shadows": False,
            "quality": "high"
        }
        
    def add_volume(self, name: str, volume: Volume3D):
        """Add volume to renderer."""
        self.volumes[name] = volume
        
    def create_vtk_volume(self, volume: Volume3D) -> vtk.vtkVolume:
        """Create VTK volume for rendering."""
        # Convert numpy array to VTK
        vtk_data = numpy_support.numpy_to_vtk(volume.data.ravel(), deep=True, array_type=vtk.VTK_FLOAT)
        
        # Create image data
        image_data = vtk.vtkImageData()
        image_data.SetDimensions(volume.shape)
        image_data.SetSpacing(volume.spacing)
        image_data.GetPointData().SetScalars(vtk_data)
        
        # Volume mapper
        volume_mapper = vtk.vtkGPUVolumeRayCastMapper()
        volume_mapper.SetInputData(image_data)
        
        # Volume properties
        volume_property = vtk.vtkVolumeProperty()
        volume_property.SetInterpolationTypeToLinear()
        volume_property.ShadeOn()
        
        # Color transfer function
        color_func = vtk.vtkColorTransferFunction()
        color_func.AddRGBPoint(0, 0.0, 0.0, 0.0)
        color_func.AddRGBPoint(255, 1.0, 1.0, 1.0)
        volume_property.SetColor(color_func)
        
        # Opacity transfer function
        opacity_func = vtk.vtkPiecewiseFunction()
        opacity_func.AddPoint(0, 0.0)
        opacity_func.AddPoint(255, self.render_settings["opacity"])
        volume_property.SetScalarOpacity(opacity_func)
        
        # Create volume
        volume_actor = vtk.vtkVolume()
        volume_actor.SetMapper(volume_mapper)
        volume_actor.SetProperty(volume_property)
        
        return volume_actor
        
    def render_volume(self, volume_name: str, output_path: Optional[str] = None):
        """Render volume using VTK."""
        if volume_name not in self.volumes:
            raise ValueError(f"Volume {volume_name} not found")
            
        volume = self.volumes[volume_name]
        vtk_volume = self.create_vtk_volume(volume)
        
        # Create renderer
        renderer = vtk.vtkRenderer()
        renderer.AddVolume(vtk_volume)
        renderer.SetBackground(0.1, 0.1, 0.1)
        
        # Create render window
        render_window = vtk.vtkRenderWindow()
        render_window.AddRenderer(renderer)
        render_window.SetSize(800, 600)
        
        # Create interactor
        interactor = vtk.vtkRenderWindowInteractor()
        interactor.SetRenderWindow(render_window)
        
        # Start rendering
        render_window.Render()
        
        if output_path:
            # Save screenshot
            window_to_image = vtk.vtkWindowToImageFilter()
            window_to_image.SetInput(render_window)
            window_to_image.Update()
            
            writer = vtk.vtkPNGWriter()
            writer.SetFileName(output_path)
            writer.SetInputConnection(window_to_image.GetOutputPort())
            writer.Write()
            
        return interactor


class NapariViewer:
    """Napari-based 3D viewer integration."""
    
    def __init__(self):
        self.viewer = None
        self.layers = {}
        
    def create_viewer(self):
        """Create napari viewer."""
        self.viewer = napari.Viewer(ndisplay=3)
        return self.viewer
        
    def add_volume_layer(self, name: str, volume: Volume3D, **kwargs):
        """Add volume as layer."""
        if not self.viewer:
            self.create_viewer()
            
        layer = self.viewer.add_image(
            volume.data,
            name=name,
            scale=volume.spacing,
            **kwargs
        )
        self.layers[name] = layer
        return layer
        
    def add_points_layer(self, name: str, points: np.ndarray, **kwargs):
        """Add points layer."""
        if not self.viewer:
            self.create_viewer()
            
        layer = self.viewer.add_points(points, name=name, **kwargs)
        self.layers[name] = layer
        return layer


class VisPyRenderer:
    """VisPy-based high-performance renderer."""
    
    def __init__(self):
        self.canvas = None
        self.view = None
        self.volumes = []
        
    def create_canvas(self, size=(800, 600)):
        """Create VisPy canvas."""
        self.canvas = scene.SceneCanvas(keys='interactive', size=size, show=True)
        self.view = self.canvas.central_widget.add_view()
        self.view.camera = 'turntable'
        return self.canvas
        
    def add_volume_visual(self, volume: Volume3D, **kwargs):
        """Add volume visual."""
        if not self.canvas:
            self.create_canvas()
            
        # Create volume visual
        volume_visual = scene.visuals.Volume(
            volume.data,
            parent=self.view.scene,
            **kwargs
        )
        
        self.volumes.append(volume_visual)
        return volume_visual


class Visualization3D:
    """Main 3D visualization coordinator."""
    
    def __init__(self):
        self.volume_renderer = VolumeRenderer()
        self.napari_viewer = NapariViewer()
        self.vispy_renderer = VisPyRenderer()
        self.active_volumes = {}
        
    def load_volume(self, name: str, data: np.ndarray, spacing: Tuple[float, float, float] = (1.0, 1.0, 1.0)):
        """Load volume data."""
        volume = Volume3D(data, spacing)
        self.active_volumes[name] = volume
        self.volume_renderer.add_volume(name, volume)
        return volume
        
    def show_volume_vtk(self, name: str, **kwargs):
        """Show volume using VTK renderer."""
        return self.volume_renderer.render_volume(name, **kwargs)
        
    def show_volume_napari(self, name: str, **kwargs):
        """Show volume using Napari."""
        volume = self.active_volumes[name]
        return self.napari_viewer.add_volume_layer(name, volume, **kwargs)
        
    def show_volume_vispy(self, name: str, **kwargs):
        """Show volume using VisPy."""
        volume = self.active_volumes[name]
        return self.vispy_renderer.add_volume_visual(volume, **kwargs)
        
    def create_orthoslice_view(self, name: str, slice_indices: Optional[Tuple[int, int, int]] = None):
        """Create orthoslice view."""
        volume = self.active_volumes[name]
        data = volume.data
        
        if slice_indices is None:
            slice_indices = (data.shape[0]//2, data.shape[1]//2, data.shape[2]//2)
            
        fig, axes = plt.subplots(2, 2, figsize=(12, 10))
        
        # XY slice (Z fixed)
        axes[0, 0].imshow(data[slice_indices[0], :, :], cmap='gray')
        axes[0, 0].set_title(f'XY slice (Z={slice_indices[0]})')
        
        # XZ slice (Y fixed)
        axes[0, 1].imshow(data[:, slice_indices[1], :], cmap='gray')
        axes[0, 1].set_title(f'XZ slice (Y={slice_indices[1]})')
        
        # YZ slice (X fixed)
        axes[1, 0].imshow(data[:, :, slice_indices[2]], cmap='gray')
        axes[1, 0].set_title(f'YZ slice (X={slice_indices[2]})')
        
        # 3D overview
        ax_3d = fig.add_subplot(224, projection='3d')
        
        # Sample points for 3D visualization
        z, y, x = np.mgrid[0:data.shape[0]:10, 0:data.shape[1]:10, 0:data.shape[2]:10]
        values = data[::10, ::10, ::10]
        
        # Plot high-intensity points
        threshold = np.percentile(values, 90)
        mask = values > threshold
        ax_3d.scatter(x[mask], y[mask], z[mask], c=values[mask], cmap='viridis', alpha=0.6)
        ax_3d.set_title('3D Overview')
        
        plt.tight_layout()
        return fig


# Global visualization instance
viz_3d = Visualization3D()

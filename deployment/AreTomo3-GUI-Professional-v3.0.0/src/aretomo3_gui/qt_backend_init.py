"""
PyQt6 Qt Backend Initialization
Uses PyQt6 consistently everywhere - modern Qt backend.
"""

import os
import sys

# Set environment variables before any other imports
os.environ["QT_API"] = "pyqt6"
os.environ["NAPARI_QT_BACKEND"] = "pyqt6"
os.environ["VISPY_QT_LIB"] = "pyqt6"

def initialize_qt_backend():
    """Initialize Qt backend for PyQt6 consistency with web engine support."""
    # Set Qt application attributes for web engine compatibility
    try:
        from PyQt6.QtCore import Qt, QCoreApplication

        # Check if QCoreApplication already exists
        if QCoreApplication.instance() is None:
            # Enable OpenGL context sharing for web engine
            QCoreApplication.setAttribute(Qt.ApplicationAttribute.AA_ShareOpenGLContexts, True)

            # High DPI scaling attributes (PyQt6 compatible)
            try:
                # These attributes may not exist in all PyQt6 versions
                if hasattr(Qt.ApplicationAttribute, 'AA_EnableHighDpiScaling'):
                    QCoreApplication.setAttribute(Qt.ApplicationAttribute.AA_EnableHighDpiScaling, True)
                if hasattr(Qt.ApplicationAttribute, 'AA_UseHighDpiPixmaps'):
                    QCoreApplication.setAttribute(Qt.ApplicationAttribute.AA_UseHighDpiPixmaps, True)
            except AttributeError:
                # These attributes are deprecated in newer Qt versions
                pass

            print("✅ Qt application attributes set for web engine compatibility")
        else:
            print("ℹ️  QCoreApplication already exists, skipping attribute setting")

    except ImportError:
        print("⚠️  PyQt6 not available during backend initialization")
    except Exception as e:
        print(f"⚠️  Qt backend initialization warning: {e}")

    # Set Qt environment variables
    os.environ["QT_API"] = "pyqt6"

    # Disable Qt debugging to reduce noise
    os.environ.pop("QT_DEBUG_PLUGINS", None)

    # Set Qt platform to xcb on Linux
    if sys.platform.startswith('linux'):
        os.environ["QT_QPA_PLATFORM"] = "xcb"
        # Ensure XCB is used for OpenGL
        os.environ["QT_XCB_GL_INTEGRATION"] = "xcb_egl"

    # Configure matplotlib to use PyQt6
    try:
        import matplotlib
        matplotlib.use('QtAgg')  # Use QtAgg backend which is compatible with PyQt6
        from matplotlib.backends.backend_qtagg import FigureCanvasQTAgg
        # Test that we can create a canvas
        from matplotlib.figure import Figure
        test_fig = Figure()
        test_canvas = FigureCanvasQTAgg(test_fig)
        print("✅ Matplotlib backend initialized with PyQt6")
    except Exception as e:
        print(f"Warning: Failed to initialize matplotlib backend: {e}")
        # Fall back to Agg if Qt backend fails
        try:
            import matplotlib
            matplotlib.use('Agg')
        except:
            pass


# Initialize immediately when imported (only once)
if not hasattr(initialize_qt_backend, '_initialized'):
    try:
        initialize_qt_backend()
        initialize_qt_backend._initialized = True
    except Exception as e:
        print(f"Warning: Qt backend initialization failed: {e}")
        initialize_qt_backend._initialized = True

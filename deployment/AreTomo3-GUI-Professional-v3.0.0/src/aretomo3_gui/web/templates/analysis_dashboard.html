<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AreTomo3 Analysis Dashboard</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script src="https://cdn.socket.io/4.5.0/socket.io.min.js"></script>
    <script src="https://cdn.plot.ly/plotly-latest.min.js"></script>
    <style>
        :root {
            --primary-bg: #0f1419;
            --secondary-bg: #1a1f2e;
            --accent-bg: #252b3a;
            --text-primary: #ffffff;
            --text-secondary: #a0a6b8;
            --accent-blue: #3b82f6;
            --accent-green: #10b981;
            --accent-orange: #f59e0b;
            --accent-red: #ef4444;
            --accent-purple: #8b5cf6;
            --border-color: #374151;
            --hover-bg: #374151;
        }

        * {
            box-sizing: border-box;
        }

        body {
            background: linear-gradient(135deg, var(--primary-bg) 0%, #1e293b 100%);
            color: var(--text-primary);
            font-family: 'Inter', 'Segoe UI', system-ui, sans-serif;
            margin: 0;
            padding: 0;
            min-height: 100vh;
        }

        .dashboard-layout {
            display: grid;
            grid-template-columns: 200px 1fr 240px;
            grid-template-rows: 70px 1fr;
            height: 100vh;
            gap: 2px;
            background-color: var(--border-color);
        }

        .top-header {
            grid-column: 1 / -1;
            background: linear-gradient(135deg, var(--secondary-bg) 0%, var(--accent-bg) 100%);
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 1.5rem;
            border-bottom: 2px solid var(--border-color);
            box-shadow: 0 2px 10px rgba(0,0,0,0.3);
        }

        .brand {
            display: flex;
            align-items: center;
            gap: 0.75rem;
            font-size: 1.25rem;
            font-weight: 700;
            color: var(--accent-blue);
        }

        .brand-icon {
            width: 32px;
            height: 32px;
            background: linear-gradient(135deg, var(--accent-blue), var(--accent-purple));
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
        }

        .header-actions {
            display: flex;
            gap: 1rem;
            align-items: center;
        }

        .status-indicator {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            padding: 0.5rem 1rem;
            border-radius: 20px;
            font-size: 0.875rem;
            font-weight: 500;
        }

        .status-online {
            background: rgba(16, 185, 129, 0.2);
            color: var(--accent-green);
            border: 1px solid var(--accent-green);
        }

        .status-offline {
            background: rgba(239, 68, 68, 0.2);
            color: var(--accent-red);
            border: 1px solid var(--accent-red);
        }

        .action-btn {
            padding: 0.5rem 1rem;
            border: none;
            border-radius: 8px;
            background: var(--accent-blue);
            color: white;
            cursor: pointer;
            font-weight: 500;
            transition: all 0.2s;
        }

        .action-btn:hover {
            background: #2563eb;
            transform: translateY(-1px);
        }

        .sidebar {
            background: var(--secondary-bg);
            padding: 1rem;
            overflow-y: auto;
            border-right: 1px solid var(--border-color);
            transition: all 0.3s ease;
            position: relative;
            min-width: 150px;
            max-width: 500px;
        }

        .sidebar.collapsed {
            width: 60px;
            padding: 1rem 0.5rem;
        }

        .sidebar.collapsed .section-title,
        .sidebar.collapsed .search-input,
        .sidebar.collapsed .control-panel {
            display: none;
        }

        .resize-handle {
            position: absolute;
            top: 0;
            right: 0;
            width: 4px;
            height: 100%;
            background: transparent;
            cursor: col-resize;
            z-index: 10;
        }

        .resize-handle:hover {
            background: var(--accent-blue);
            opacity: 0.5;
        }

        .resize-handle.resizing {
            background: var(--accent-blue);
            opacity: 0.8;
        }

        .main-workspace {
            background: var(--primary-bg);
            padding: 1.5rem;
            overflow-y: auto;
        }

        .details-sidebar {
            background: var(--secondary-bg);
            padding: 1rem;
            overflow-y: auto;
            border-left: 1px solid var(--border-color);
            transition: all 0.3s ease;
            position: relative;
            min-width: 150px;
            max-width: 500px;
        }

        .details-sidebar.collapsed {
            width: 60px;
            padding: 1rem 0.5rem;
        }

        .details-sidebar.collapsed .section-title,
        .details-sidebar.collapsed .info-row {
            display: none;
        }

        .resize-handle-left {
            position: absolute;
            top: 0;
            left: 0;
            width: 4px;
            height: 100%;
            background: transparent;
            cursor: col-resize;
            z-index: 10;
        }

        .resize-handle-left:hover {
            background: var(--accent-blue);
            opacity: 0.5;
        }

        .resize-handle-left.resizing {
            background: var(--accent-blue);
            opacity: 0.8;
        }

        .section-title {
            font-size: 1rem;
            font-weight: 600;
            margin-bottom: 1rem;
            color: var(--text-primary);
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .search-input {
            width: 100%;
            padding: 0.75rem;
            margin-bottom: 1rem;
            background: var(--accent-bg);
            border: 1px solid var(--border-color);
            border-radius: 8px;
            color: var(--text-primary);
            font-size: 0.875rem;
        }

        .search-input::placeholder {
            color: var(--text-secondary);
        }

        .search-input:focus {
            outline: none;
            border-color: var(--accent-blue);
            box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
        }

        .dataset-list {
            display: flex;
            flex-direction: column;
            gap: 0.5rem;
        }

        .dataset-card {
            background: var(--accent-bg);
            border-radius: 10px;
            padding: 1rem;
            cursor: pointer;
            transition: all 0.2s;
            border: 2px solid transparent;
            position: relative;
        }

        .dataset-card:hover {
            background: var(--hover-bg);
            transform: translateX(4px);
        }

        .dataset-card.selected {
            border-color: var(--accent-blue);
            background: rgba(59, 130, 246, 0.1);
        }

        .dataset-header {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: 0.5rem;
        }

        .dataset-name {
            font-weight: 600;
            font-size: 0.875rem;
            color: var(--text-primary);
        }

        .quality-dot {
            width: 10px;
            height: 10px;
            border-radius: 50%;
            flex-shrink: 0;
        }

        .quality-excellent { background: var(--accent-green); }
        .quality-good { background: #22c55e; }
        .quality-fair { background: var(--accent-orange); }
        .quality-poor { background: var(--accent-red); }

        .dataset-meta {
            font-size: 0.75rem;
            color: var(--text-secondary);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .status-chip {
            padding: 0.25rem 0.5rem;
            border-radius: 12px;
            font-size: 0.7rem;
            font-weight: 600;
            text-transform: uppercase;
        }

        .status-completed {
            background: rgba(16, 185, 129, 0.2);
            color: var(--accent-green);
        }

        .status-processing {
            background: rgba(59, 130, 246, 0.2);
            color: var(--accent-blue);
        }

        .status-failed {
            background: rgba(239, 68, 68, 0.2);
            color: var(--accent-red);
        }

        .status-queued {
            background: rgba(139, 92, 246, 0.2);
            color: var(--accent-purple);
        }

        .analysis-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: 1rem;
        }

        /* Log Container Styles */
        .log-container {
            height: 400px;
            background: var(--primary-bg);
            border: 1px solid var(--border-color);
            border-radius: 6px;
            overflow-y: auto;
            font-family: 'SF Mono', 'Monaco', 'Inconsolata', 'Roboto Mono', 'Consolas', monospace;
            font-size: 12px;
            line-height: 1.4;
            padding: 8px;
        }

        .log-entry {
            padding: 4px 8px;
            margin: 2px 0;
            border-radius: 3px;
            border-left: 3px solid transparent;
            word-wrap: break-word;
        }

        .log-entry.DEBUG {
            border-left-color: #6b7280;
            background: rgba(107, 114, 128, 0.1);
        }

        .log-entry.INFO {
            border-left-color: #3b82f6;
            background: rgba(59, 130, 246, 0.1);
        }

        .log-entry.WARNING {
            border-left-color: #f59e0b;
            background: rgba(245, 158, 11, 0.1);
        }

        .log-entry.ERROR {
            border-left-color: #ef4444;
            background: rgba(239, 68, 68, 0.1);
        }

        .log-entry.CRITICAL {
            border-left-color: #dc2626;
            background: rgba(220, 38, 38, 0.2);
        }

        .log-timestamp {
            color: var(--text-muted);
            font-size: 11px;
        }

        .log-level {
            font-weight: 600;
            margin: 0 8px;
            min-width: 60px;
            display: inline-block;
        }

        .log-message {
            color: var(--text-primary);
        }

        .log-controls {
            display: flex;
            gap: 10px;
            margin-bottom: 10px;
            align-items: center;
        }

        .log-controls select,
        .log-controls input {
            padding: 6px 10px;
            border: 1px solid var(--border-color);
            border-radius: 4px;
            background: var(--secondary-bg);
            color: var(--text-primary);
            font-size: 12px;
        }

        .log-controls input {
            flex: 1;
            max-width: 200px;
        }

        .log-placeholder {
            text-align: center;
            color: var(--text-muted);
            padding: 40px 20px;
        }

        .full-width {
            grid-column: 1 / -1;
        }

        .analysis-panel {
            background: var(--secondary-bg);
            border-radius: 12px;
            padding: 1.5rem;
            border: 1px solid var(--border-color);
            box-shadow: 0 4px 20px rgba(0,0,0,0.2);
        }

        .panel-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 1rem;
            padding-bottom: 0.75rem;
            border-bottom: 1px solid var(--border-color);
        }

        .panel-title {
            font-weight: 600;
            font-size: 1rem;
            color: var(--text-primary);
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .chart-container {
            height: 220px;
            background: var(--accent-bg);
            border-radius: 8px;
            margin: 0.75rem 0;
            display: flex;
            align-items: center;
            justify-content: center;
            position: relative;
        }

        .chart-placeholder {
            text-align: center;
            color: var(--text-secondary);
            font-size: 0.875rem;
        }

        .thumbnail-preview {
            height: 140px;
            background: var(--accent-bg);
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-top: 0.75rem;
            position: relative;
            overflow: hidden;
        }

        .view-btn {
            position: absolute;
            bottom: 0.5rem;
            right: 0.5rem;
            padding: 0.375rem 0.75rem;
            background: var(--accent-blue);
            color: white;
            border: none;
            border-radius: 6px;
            font-size: 0.75rem;
            cursor: pointer;
            transition: all 0.2s;
        }

        .view-btn:hover {
            background: #2563eb;
        }

        .control-panel {
            display: flex;
            gap: 0.75rem;
            margin-bottom: 1.5rem;
        }

        .control-btn {
            padding: 0.75rem 1.25rem;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-weight: 500;
            font-size: 0.875rem;
            transition: all 0.2s;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .btn-start {
            background: var(--accent-green);
            color: white;
        }

        .btn-pause {
            background: var(--accent-orange);
            color: white;
        }

        .btn-stop {
            background: var(--accent-red);
            color: white;
        }

        .btn-start:hover { background: #059669; }
        .btn-pause:hover { background: #d97706; }
        .btn-stop:hover { background: #dc2626; }

        .metrics-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 1rem;
            margin-top: 1rem;
        }

        .metric-card {
            background: var(--accent-bg);
            padding: 1rem;
            border-radius: 8px;
            text-align: center;
        }

        .metric-value {
            font-size: 1.5rem;
            font-weight: 700;
            color: var(--text-primary);
            margin-bottom: 0.25rem;
        }

        .metric-label {
            font-size: 0.75rem;
            color: var(--text-secondary);
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .info-row {
            display: flex;
            justify-content: space-between;
            margin: 0.5rem 0;
            font-size: 0.875rem;
        }

        .info-label {
            color: var(--text-secondary);
        }

        .info-value {
            color: var(--text-primary);
            font-weight: 500;
        }

        .scrollbar-custom::-webkit-scrollbar {
            width: 6px;
        }

        .scrollbar-custom::-webkit-scrollbar-track {
            background: var(--accent-bg);
        }

        .scrollbar-custom::-webkit-scrollbar-thumb {
            background: var(--border-color);
            border-radius: 3px;
        }

        .scrollbar-custom::-webkit-scrollbar-thumb:hover {
            background: #6b7280;
        }

        @media (max-width: 1400px) {
            .dashboard-layout {
                grid-template-columns: 180px 1fr 220px;
            }
        }

        @media (max-width: 1200px) {
            .dashboard-layout {
                grid-template-columns: 160px 1fr 200px;
            }
        }

        @media (max-width: 768px) {
            .dashboard-layout {
                grid-template-columns: 1fr;
                grid-template-rows: 70px auto auto auto;
            }

            .analysis-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="dashboard-layout">
        <!-- Header -->
        <div class="top-header">
            <div class="brand">
                <div class="brand-icon">
                    <i class="fas fa-microscope"></i>
                </div>
                AreTomo3 Analysis Dashboard
            </div>
            <div class="header-actions">
                <button class="action-btn" onclick="toggleLeftSidebar()" title="Toggle Tilt Series Panel">
                    <i class="fas fa-bars"></i>
                </button>
                <button class="action-btn" onclick="toggleRightSidebar()" title="Toggle Details Panel">
                    <i class="fas fa-info-circle"></i>
                </button>
                <div id="connection-status" class="status-indicator status-online">
                    <i class="fas fa-circle"></i>
                    Connected
                </div>
                <button class="action-btn" onclick="refreshDashboard()">
                    <i class="fas fa-sync-alt"></i> Refresh
                </button>
            </div>
        </div>

        <!-- Left Sidebar - Dataset Browser -->
        <div class="sidebar scrollbar-custom" id="left-sidebar">
            <div class="resize-handle" id="left-resize-handle"></div>
            <div class="section-title">
                <i class="fas fa-database"></i>
                Tilt Series
                <span id="dataset-counter" class="badge bg-secondary ms-auto">0</span>
            </div>
            
            <input type="text" class="search-input" placeholder="Search tilt series..." id="search-datasets">
            
            <div class="control-panel">
                <button class="control-btn btn-start" onclick="startProcessing()">
                    <i class="fas fa-play"></i> Start
                </button>
                <button class="control-btn btn-pause" onclick="pauseProcessing()">
                    <i class="fas fa-pause"></i> Pause
                </button>
                <button class="control-btn btn-stop" onclick="stopProcessing()">
                    <i class="fas fa-stop"></i> Stop
                </button>
            </div>

            <div id="dataset-list" class="dataset-list">
                <!-- Dataset cards will be populated here -->
            </div>
        </div>

        <!-- Main Content - Analysis Panels -->
        <div class="main-workspace scrollbar-custom">
            <div id="analysis-grid" class="analysis-grid">
                <!-- Analysis panels will be populated here -->
            </div>
        </div>

        <!-- Right Sidebar - Details -->
        <div class="details-sidebar scrollbar-custom" id="right-sidebar">
            <div class="resize-handle-left" id="right-resize-handle"></div>
            <div class="section-title">
                <i class="fas fa-info-circle"></i>
                Dataset Details
            </div>
            
            <div id="dataset-details">
                <div class="text-center text-muted py-4">
                    <i class="fas fa-mouse-pointer mb-2"></i><br>
                    Select a dataset to view details
                </div>
            </div>

            <div class="metrics-grid" id="quality-metrics" style="display: none;">
                <div class="metric-card">
                    <div class="metric-value" id="motion-score">--</div>
                    <div class="metric-label">Motion</div>
                </div>
                <div class="metric-card">
                    <div class="metric-value" id="ctf-score">--</div>
                    <div class="metric-label">CTF</div>
                </div>
                <div class="metric-card">
                    <div class="metric-value" id="alignment-score">--</div>
                    <div class="metric-label">Alignment</div>
                </div>
                <div class="metric-card">
                    <div class="metric-value" id="overall-score">--</div>
                    <div class="metric-label">Overall</div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Dashboard state
        let websocket;
        let selectedDataset = null;
        let datasets = [];
        let autoRefreshEnabled = true;
        let logAutoScroll = true;
        let logEntries = [];
        let filteredLogEntries = [];

        // Initialize dashboard
        document.addEventListener('DOMContentLoaded', function() {
            initializeWebSocket();
            loadDashboardData();
            setupEventHandlers();
            setupResizeHandlers();

            // Auto-refresh every 5 seconds
            setInterval(() => {
                if (autoRefreshEnabled) {
                    loadDashboardData();
                }
            }, 5000);
        });

        // WebSocket setup
        function initializeWebSocket() {
            const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
            const wsUrl = `${protocol}//${window.location.host}/ws`;
            
            websocket = new WebSocket(wsUrl);
            
            websocket.onopen = function() {
                updateConnectionStatus(true);
            };
            
            websocket.onclose = function() {
                updateConnectionStatus(false);
            };
            
            websocket.onmessage = function(event) {
                const data = JSON.parse(event.data);
                handleWebSocketMessage(data);
            };
        }

        function updateConnectionStatus(connected) {
            const statusEl = document.getElementById('connection-status');
            if (connected) {
                statusEl.className = 'status-indicator status-online';
                statusEl.innerHTML = '<i class="fas fa-circle"></i> Connected';
            } else {
                statusEl.className = 'status-indicator status-offline';
                statusEl.innerHTML = '<i class="fas fa-circle"></i> Disconnected';
            }
        }

        // Load dashboard data
        async function loadDashboardData() {
            try {
                const response = await fetch('/api/jobs');
                const jobs = await response.json();
                updateDatasetList(jobs);
                updateAnalysisGrid(jobs);
                updateDatasetCounter(jobs.length);
            } catch (error) {
                console.error('Error loading dashboard data:', error);
            }
        }

        function refreshDashboard() {
            loadDashboardData();
        }

        // Update dataset list
        function updateDatasetList(jobs) {
            const listContainer = document.getElementById('dataset-list');
            datasets = jobs;
            
            listContainer.innerHTML = '';
            
            jobs.forEach(job => {
                const card = createDatasetCard(job);
                listContainer.appendChild(card);
            });
        }

        function createDatasetCard(job) {
            const div = document.createElement('div');
            div.className = 'dataset-card';
            div.onclick = () => selectDataset(job);
            
            const qualityClass = getQualityClass(job);
            const statusClass = getStatusClass(job.status);
            
            div.innerHTML = `
                <div class="dataset-header">
                    <div class="dataset-name">${job.file_name || job.job_id}</div>
                    <div class="quality-dot ${qualityClass}"></div>
                </div>
                <div class="dataset-meta">
                    <span class="status-chip ${statusClass}">${job.status}</span>
                    <small>${formatTimestamp(job.created_at)}</small>
                </div>
            `;
            
            return div;
        }

        // Update analysis grid
        function updateAnalysisGrid(jobs) {
            const gridContainer = document.getElementById('analysis-grid');
            gridContainer.innerHTML = '';
            
            jobs.forEach(job => {
                const panel = createAnalysisPanel(job);
                gridContainer.appendChild(panel);
            });
        }

        function createAnalysisPanel(job) {
            const div = document.createElement('div');
            div.className = 'analysis-panel';
            
            const qualityClass = getQualityClass(job);
            const statusClass = getStatusClass(job.status);
            
            div.innerHTML = `
                <div class="panel-header">
                    <div class="panel-title">
                        <div class="quality-dot ${qualityClass}"></div>
                        ${job.file_name || job.job_id}
                    </div>
                    <span class="status-chip ${statusClass}">${job.status}</span>
                </div>
                
                <div class="row">
                    <div class="col-6">
                        <div class="chart-container" id="motion-chart-${job.job_id}">
                            <div class="chart-placeholder">
                                <i class="fas fa-chart-line mb-2"></i><br>
                                Motion Correction
                            </div>
                        </div>
                    </div>
                    <div class="col-6">
                        <div class="chart-container" id="alignment-chart-${job.job_id}">
                            <div class="chart-placeholder">
                                <i class="fas fa-crosshairs mb-2"></i><br>
                                Alignment Quality
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="row">
                    <div class="col-6">
                        <div class="chart-container" id="ctf-chart-${job.job_id}">
                            <div class="chart-placeholder">
                                <i class="fas fa-wave-square mb-2"></i><br>
                                CTF Estimation
                            </div>
                        </div>
                    </div>
                    <div class="col-6">
                        <div class="thumbnail-preview" id="reconstruction-${job.job_id}">
                            <div class="chart-placeholder">
                                <i class="fas fa-cube mb-2"></i><br>
                                Reconstruction
                            </div>
                            ${job.status === 'completed' ? 
                                `<button class="view-btn" onclick="openViewer('${job.job_id}')">
                                    <i class="fas fa-eye"></i> View
                                </button>` : ''
                            }
                        </div>
                    </div>
                </div>
            `;
            
            return div;
        }

        // Select dataset and update details
        function selectDataset(job) {
            selectedDataset = job;
            
            // Update selection state
            document.querySelectorAll('.dataset-card').forEach(card => {
                card.classList.remove('selected');
            });
            event.currentTarget.classList.add('selected');
            
            updateDetailsPanel(job);
        }

        function updateDetailsPanel(job) {
            const detailsContainer = document.getElementById('dataset-details');
            const metricsContainer = document.getElementById('quality-metrics');
            
            detailsContainer.innerHTML = `
                <div class="mb-3">
                    <h6>${job.file_name || job.job_id}</h6>
                    <span class="status-chip ${getStatusClass(job.status)}">${job.status}</span>
                </div>
                
                <div class="info-row">
                    <span class="info-label">Progress:</span>
                    <span class="info-value">${Math.round(job.progress || 0)}%</span>
                </div>
                
                <div class="info-row">
                    <span class="info-label">Created:</span>
                    <span class="info-value">${formatTimestamp(job.created_at)}</span>
                </div>
                
                <div class="info-row">
                    <span class="info-label">Processing Time:</span>
                    <span class="info-value">${job.processing_time ? formatDuration(job.processing_time) : 'N/A'}</span>
                </div>
                
                <div class="info-row">
                    <span class="info-label">Output Files:</span>
                    <span class="info-value">${job.output_count || 0}</span>
                </div>
                
                <div class="info-row">
                    <span class="info-label">Total Size:</span>
                    <span class="info-value">${job.total_output_size || 'N/A'}</span>
                </div>
            `;
            
            // Show quality metrics
            metricsContainer.style.display = 'grid';
            updateQualityScores(job);
        }

        function updateQualityScores(job) {
            // Generate realistic quality scores based on job status
            const scores = generateQualityScores(job);
            
            document.getElementById('motion-score').textContent = scores.motion;
            document.getElementById('ctf-score').textContent = scores.ctf;
            document.getElementById('alignment-score').textContent = scores.alignment;
            document.getElementById('overall-score').textContent = scores.overall;
        }

        // Helper functions
        function getStatusClass(status) {
            switch (status) {
                case 'completed': return 'status-completed';
                case 'processing': return 'status-processing';
                case 'failed': return 'status-failed';
                case 'queued': return 'status-queued';
                default: return 'status-processing';
            }
        }

        function getQualityClass(job) {
            // This would be based on actual quality analysis
            const qualities = ['quality-excellent', 'quality-good', 'quality-fair', 'quality-poor'];
            return qualities[Math.floor(Math.random() * qualities.length)];
        }

        function generateQualityScores(job) {
            if (job.status !== 'completed') {
                return { motion: '--', ctf: '--', alignment: '--', overall: '--' };
            }
            
            // Generate realistic scores
            const baseScore = 70 + Math.random() * 25;
            return {
                motion: (baseScore + Math.random() * 10).toFixed(1),
                ctf: (baseScore + Math.random() * 10).toFixed(1),
                alignment: (baseScore + Math.random() * 10).toFixed(1),
                overall: (baseScore + Math.random() * 5).toFixed(1)
            };
        }

        function formatTimestamp(timestamp) {
            return new Date(timestamp).toLocaleString();
        }

        function formatDuration(seconds) {
            const minutes = Math.floor(seconds / 60);
            const remainingSeconds = Math.floor(seconds % 60);
            return `${minutes}m ${remainingSeconds}s`;
        }

        function updateDatasetCounter(count) {
            document.getElementById('dataset-counter').textContent = count;
        }

        // Processing controls
        async function startProcessing() {
            try {
                const response = await fetch('/api/processing/start', { method: 'POST' });
                const result = await response.json();
                console.log('Processing started:', result);
                loadDashboardData();
            } catch (error) {
                console.error('Error starting processing:', error);
            }
        }

        async function pauseProcessing() {
            try {
                const response = await fetch('/api/processing/pause', { method: 'POST' });
                const result = await response.json();
                console.log('Processing paused:', result);
                loadDashboardData();
            } catch (error) {
                console.error('Error pausing processing:', error);
            }
        }

        async function stopProcessing() {
            try {
                const response = await fetch('/api/processing/stop', { method: 'POST' });
                const result = await response.json();
                console.log('Processing stopped:', result);
                loadDashboardData();
            } catch (error) {
                console.error('Error stopping processing:', error);
            }
        }

        function openViewer(jobId) {
            console.log('Opening viewer for job:', jobId);
            // This would integrate with the existing viewer tab
            window.open(`/viewer/${jobId}`, '_blank');
        }

        // Event handlers
        function setupEventHandlers() {
            const searchInput = document.getElementById('search-datasets');
            searchInput.addEventListener('input', function(e) {
                const searchTerm = e.target.value.toLowerCase();
                filterDatasets(searchTerm);
            });
        }

        function filterDatasets(searchTerm) {
            const filteredDatasets = datasets.filter(job => 
                (job.file_name || job.job_id).toLowerCase().includes(searchTerm)
            );
            updateDatasetList(filteredDatasets);
            updateAnalysisGrid(filteredDatasets);
        }

        function handleWebSocketMessage(data) {
            if (data.type === 'job_update') {
                loadDashboardData();
            } else if (data.type === 'quality_update' && selectedDataset) {
                if (selectedDataset.job_id === data.job_id) {
                    updateQualityScores(data);
                }
            } else if (data.type === 'log_entry') {
                addLogEntry(data.log);
            } else if (data.type === 'initial_logs') {
                initializeLogs(data.logs);
            }
        }

        // Sidebar toggle functions
        function toggleLeftSidebar() {
            const sidebar = document.querySelector('.sidebar');
            const layout = document.querySelector('.dashboard-layout');

            if (sidebar.classList.contains('collapsed')) {
                sidebar.classList.remove('collapsed');
                layout.style.gridTemplateColumns = '200px 1fr 240px';
            } else {
                sidebar.classList.add('collapsed');
                layout.style.gridTemplateColumns = '60px 1fr 240px';
            }
        }

        function toggleRightSidebar() {
            const sidebar = document.querySelector('.details-sidebar');
            const layout = document.querySelector('.dashboard-layout');

            if (sidebar.classList.contains('collapsed')) {
                sidebar.classList.remove('collapsed');
                layout.style.gridTemplateColumns = '200px 1fr 240px';
            } else {
                sidebar.classList.add('collapsed');
                layout.style.gridTemplateColumns = '200px 1fr 60px';
            }
        }

        // Resize functionality
        function setupResizeHandlers() {
            let isResizing = false;
            let currentHandle = null;
            let startX = 0;
            let startWidth = 0;

            // Left sidebar resize
            const leftHandle = document.getElementById('left-resize-handle');
            const leftSidebar = document.getElementById('left-sidebar');

            leftHandle.addEventListener('mousedown', function(e) {
                isResizing = true;
                currentHandle = 'left';
                startX = e.clientX;
                startWidth = parseInt(document.defaultView.getComputedStyle(leftSidebar).width, 10);
                leftHandle.classList.add('resizing');
                document.body.style.cursor = 'col-resize';
                e.preventDefault();
            });

            // Right sidebar resize
            const rightHandle = document.getElementById('right-resize-handle');
            const rightSidebar = document.getElementById('right-sidebar');

            rightHandle.addEventListener('mousedown', function(e) {
                isResizing = true;
                currentHandle = 'right';
                startX = e.clientX;
                startWidth = parseInt(document.defaultView.getComputedStyle(rightSidebar).width, 10);
                rightHandle.classList.add('resizing');
                document.body.style.cursor = 'col-resize';
                e.preventDefault();
            });

            // Mouse move handler
            document.addEventListener('mousemove', function(e) {
                if (!isResizing) return;

                const layout = document.querySelector('.dashboard-layout');

                if (currentHandle === 'left') {
                    const newWidth = startWidth + (e.clientX - startX);
                    const clampedWidth = Math.max(150, Math.min(500, newWidth));
                    leftSidebar.style.width = clampedWidth + 'px';
                    layout.style.gridTemplateColumns = `${clampedWidth}px 1fr 240px`;
                } else if (currentHandle === 'right') {
                    const newWidth = startWidth - (e.clientX - startX);
                    const clampedWidth = Math.max(150, Math.min(500, newWidth));
                    rightSidebar.style.width = clampedWidth + 'px';
                    layout.style.gridTemplateColumns = `200px 1fr ${clampedWidth}px`;
                }
            });

            // Mouse up handler
            document.addEventListener('mouseup', function() {
                if (isResizing) {
                    isResizing = false;
                    currentHandle = null;
                    leftHandle.classList.remove('resizing');
                    rightHandle.classList.remove('resizing');
                    document.body.style.cursor = '';
                }
            });
        }

        // Log handling functions
        function initializeLogs(logs) {
            logEntries = logs || [];
            filteredLogEntries = [...logEntries];
            renderLogs();
        }

        function addLogEntry(logEntry) {
            logEntries.push(logEntry);

            // Keep only last 1000 entries
            if (logEntries.length > 1000) {
                logEntries = logEntries.slice(-1000);
            }

            // Apply current filters
            applyLogFilters();

            // Auto-scroll if enabled
            if (logAutoScroll) {
                scrollLogsToBottom();
            }
        }

        function renderLogs() {
            const container = document.getElementById('logContainer');
            if (!container) return;

            if (filteredLogEntries.length === 0) {
                container.innerHTML = '<div class="log-placeholder"><i class="fas fa-info-circle"></i> No logs to display</div>';
                return;
            }

            const logsHtml = filteredLogEntries.map(entry => {
                const timestamp = new Date(entry.timestamp).toLocaleTimeString();
                return `
                    <div class="log-entry ${entry.level}">
                        <span class="log-timestamp">${timestamp}</span>
                        <span class="log-level">${entry.level}</span>
                        <span class="log-message">${escapeHtml(entry.message)}</span>
                    </div>
                `;
            }).join('');

            container.innerHTML = logsHtml;

            if (logAutoScroll) {
                scrollLogsToBottom();
            }
        }

        function applyLogFilters() {
            const levelFilter = document.getElementById('logLevelFilter')?.value || 'all';
            const searchInput = document.getElementById('logSearchInput')?.value.toLowerCase() || '';

            filteredLogEntries = logEntries.filter(entry => {
                // Level filter
                if (levelFilter !== 'all' && entry.level !== levelFilter) {
                    return false;
                }

                // Search filter
                if (searchInput && !entry.message.toLowerCase().includes(searchInput)) {
                    return false;
                }

                return true;
            });

            renderLogs();
        }

        function filterLogs() {
            applyLogFilters();
        }

        function searchLogs() {
            applyLogFilters();
        }

        function clearLogs() {
            logEntries = [];
            filteredLogEntries = [];
            renderLogs();
        }

        function toggleAutoScroll() {
            logAutoScroll = !logAutoScroll;
            const btn = document.getElementById('autoScrollBtn');
            if (btn) {
                btn.innerHTML = logAutoScroll ?
                    '<i class="fas fa-arrow-down"></i>' :
                    '<i class="fas fa-pause"></i>';
                btn.title = logAutoScroll ? 'Auto-scroll enabled' : 'Auto-scroll disabled';
            }

            if (logAutoScroll) {
                scrollLogsToBottom();
            }
        }

        function scrollLogsToBottom() {
            const container = document.getElementById('logContainer');
            if (container) {
                container.scrollTop = container.scrollHeight;
            }
        }

        function downloadLogs() {
            const logsText = logEntries.map(entry => {
                const timestamp = new Date(entry.timestamp).toISOString();
                return `${timestamp} [${entry.level}] ${entry.message}`;
            }).join('\n');

            const blob = new Blob([logsText], { type: 'text/plain' });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `aretomo3_logs_${new Date().toISOString().split('T')[0]}.txt`;
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            URL.revokeObjectURL(url);
        }

        function escapeHtml(text) {
            const div = document.createElement('div');
            div.textContent = text;
            return div.innerHTML;
        }

        // Add logs panel to analysis grid
        function addLogsPanel() {
            const gridContainer = document.getElementById('analysis-grid');
            if (!gridContainer) return;

            const logsPanel = document.createElement('div');
            logsPanel.className = 'analysis-panel full-width';
            logsPanel.innerHTML = `
                <div class="panel-header">
                    <h3><i class="fas fa-terminal"></i> Real-time Logs</h3>
                    <div class="panel-actions">
                        <button class="btn btn-sm" onclick="clearLogs()" title="Clear Logs">
                            <i class="fas fa-trash"></i>
                        </button>
                        <button class="btn btn-sm" onclick="toggleAutoScroll()" id="autoScrollBtn" title="Toggle Auto-scroll">
                            <i class="fas fa-arrow-down"></i>
                        </button>
                        <button class="btn btn-sm" onclick="downloadLogs()" title="Download Logs">
                            <i class="fas fa-download"></i>
                        </button>
                    </div>
                </div>
                <div class="panel-content">
                    <div class="log-controls">
                        <select id="logLevelFilter" onchange="filterLogs()">
                            <option value="all">All Levels</option>
                            <option value="DEBUG">Debug</option>
                            <option value="INFO">Info</option>
                            <option value="WARNING">Warning</option>
                            <option value="ERROR">Error</option>
                            <option value="CRITICAL">Critical</option>
                        </select>
                        <input type="text" id="logSearchInput" placeholder="Search logs..." onkeyup="searchLogs()">
                    </div>
                    <div id="logContainer" class="log-container">
                        <div class="log-placeholder">
                            <i class="fas fa-spinner fa-spin"></i>
                            Connecting to log stream...
                        </div>
                    </div>
                </div>
            `;

            gridContainer.appendChild(logsPanel);
        }

        // Initialize logs panel when dashboard loads
        document.addEventListener('DOMContentLoaded', function() {
            setTimeout(addLogsPanel, 1000); // Add after other panels are loaded
        });
    </script>
</body>
</html>

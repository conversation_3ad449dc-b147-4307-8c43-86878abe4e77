"""Components for tomogram analysis and monitoring."""

import time
from typing import Any, Dict, List, Optional, Tuple

import matplotlib.pyplot as plt
import numpy as np
from matplotlib.backends.backend_qt5agg import FigureCanvasQTAgg
from matplotlib.figure import Figure
from mrcfile import mmap as mrcmmap
from PyQt6.QtCore import Qt
from PyQt6.QtWidgets import (
    QComboBox,
    QGridLayout,
    QGroupBox,
    QHBoxLayout,
    QLabel,
    QProgressBar,
    QPushButton,
    QSlider,
    QSpinBox,
    QTabWidget,
    QTextEdit,
    QVBoxLayout,
    QWidget,
)

try:
    from scipy import ndimage
except ImportError:
    ndimage = None  # Make it optional since we don't use it directly


class TomogramViewer(QWidget):
    """3D visualization widget for reconstructed tomograms."""

    def __init__(self, parent=None):
        """Initialize the instance."""
        super().__init__(parent)
        self.data = None
        self.contrast_min = 0
        self.contrast_max = 1
        self.measuring_mode = False
        self.measure_start = None
        self.measure_end = None
        self.scale_factor = 1.0  # Angstroms per pixel
        self.show_scale_bar = True
        self.scale_bar_size = 100  # Angstroms
        self.current_view = "xy"  # Can be 'xy', 'xz', or 'yz'
        self.init_ui()

    # TODO: Refactor function - Function 'init_ui' too long (96 lines)
    def init_ui(self):
        """Initialize the UI components."""
        layout = QVBoxLayout(self)

        # Create matplotlib figure
        self.figure = Figure(figsize=(8, 8))
        self.canvas = FigureCanvasQTAgg(self.figure)
        layout.addWidget(self.canvas)

        # Controls panel
        controls = QWidget()
        controls_layout = QHBoxLayout(controls)

        # Slice navigation
        nav_group = QGroupBox("Navigation")
        nav_layout = QVBoxLayout()

        # View selection
        view_layout = QHBoxLayout()
        view_layout.addWidget(QLabel("View:"))
        for view in ["XY", "XZ", "YZ"]:
            btn = QPushButton(view)
            btn.setCheckable(True)
            btn.setChecked(view.lower() == self.current_view)
            btn.clicked.connect(lambda checked, v=view.lower(): self.change_view(v))
            view_layout.addWidget(btn)
        nav_layout.addLayout(view_layout)

        # Slice controls
        slice_layout = QHBoxLayout()
        self.slice_label = QLabel("Slice: 0/0")
        slice_layout.addWidget(self.slice_label)

        self.slice_slider = QSlider(Qt.Orientation.Horizontal)
        self.slice_slider.setMinimum(0)
        self.slice_slider.setMaximum(0)
        self.slice_slider.valueChanged.connect(self.update_slice)
        slice_layout.addWidget(self.slice_slider)

        # Fine control spinbox
        self.slice_spinbox = QSpinBox()
        self.slice_spinbox.setMinimum(0)
        self.slice_spinbox.setMaximum(0)
        self.slice_spinbox.valueChanged.connect(
            lambda v: self.update_slice(v, from_spinbox=True)
        )
        slice_layout.addWidget(self.slice_spinbox)

        nav_layout.addLayout(slice_layout)
        nav_group.setLayout(nav_layout)
        controls_layout.addWidget(nav_group)

        # Contrast controls
        contrast_group = QGroupBox("Contrast")
        contrast_layout = QGridLayout()

        self.contrast_min_slider = QSlider(Qt.Orientation.Horizontal)
        self.contrast_min_slider.setRange(0, 100)
        self.contrast_min_slider.valueChanged.connect(self.update_contrast)
        contrast_layout.addWidget(QLabel("Min:"), 0, 0)
        contrast_layout.addWidget(self.contrast_min_slider, 0, 1)

        self.contrast_max_slider = QSlider(Qt.Orientation.Horizontal)
        self.contrast_max_slider.setRange(0, 100)
        self.contrast_max_slider.setValue(100)
        self.contrast_max_slider.valueChanged.connect(self.update_contrast)
        contrast_layout.addWidget(QLabel("Max:"), 1, 0)
        contrast_layout.addWidget(self.contrast_max_slider, 1, 1)

        auto_contrast = QPushButton("Auto")
        auto_contrast.clicked.connect(self.auto_contrast)
        contrast_layout.addWidget(auto_contrast, 2, 0, 1, 2)

        contrast_group.setLayout(contrast_layout)
        controls_layout.addWidget(contrast_group)

        # Measurement tools
        measure_group = QGroupBox("Measurement")
        measure_layout = QVBoxLayout()

        self.measure_btn = QPushButton("Measure Distance")
        self.measure_btn.setCheckable(True)
        self.measure_btn.toggled.connect(self.toggle_measure_mode)
        measure_layout.addWidget(self.measure_btn)

        self.measure_label = QLabel("Distance: ---")
        measure_layout.addWidget(self.measure_label)

        measure_group.setLayout(measure_layout)
        controls_layout.addWidget(measure_group)

        layout.addWidget(controls)

        # Connect mouse events
        self.canvas.mpl_connect("button_press_event", self.on_click)
        self.canvas.mpl_connect("motion_notify_event", self.on_mouse_move)

    def load_tomogram(self, filepath: str):
        """Load a tomogram from an MRC file."""
        with mrcmmap(filepath) as mrc:
            self.data = mrc.data.copy()
            self.scale_factor = float(mrc.header.cellsize["x"]) / mrc.header.nx

        self.current_slice = self.data.shape[0] // 2
        self.auto_contrast()
        self.update_display()

    def update_contrast(self):
        """Update display with new contrast settings."""
        self.contrast_min = self.contrast_min_slider.value() / 100
        self.contrast_max = self.contrast_max_slider.value() / 100
        self.update_display()

    def auto_contrast(self):
        """Automatically set contrast based on data statistics."""
        if self.data is None:
            return

        slice_data = self.data[self.current_slice]
        mean = np.mean(slice_data)
        std = np.std(slice_data)

        vmin = mean - 2 * std
        vmax = mean + 2 * std

        data_range = self.data.max() - self.data.min()
        self.contrast_min_slider.setValue(
            int((vmin - self.data.min()) / data_range * 100)
        )
        self.contrast_max_slider.setValue(
            int((vmax - self.data.min()) / data_range * 100)
        )

    def update_display(self):
        """Update the display with current slice."""
        if self.data is None:
            return

        self.figure.clear()
        ax = self.figure.add_subplot(111)

        # Display image with current contrast
        vmin = self.data.min() + (self.data.max() - self.data.min()) * self.contrast_min
        vmax = self.data.min() + (self.data.max() - self.data.min()) * self.contrast_max
        im = ax.imshow(self.data[self.current_slice], cmap="gray", vmin=vmin, vmax=vmax)

        # Draw measurement line if active
        if self.measure_start and self.measure_end:
            ax.plot(
                [self.measure_start[0], self.measure_end[0]],
                [self.measure_start[1], self.measure_end[1]],
                "r-",
            )

        # Draw scale bar
        if self.show_scale_bar:
            self.draw_scale_bar(ax)

        self.canvas.draw()
        self.slice_label.setText(f"Slice: {self.current_slice}/{self.data.shape[0]-1}")

    def draw_scale_bar(self, ax):
        """Draw a scale bar on the image."""
        # Scale bar parameters
        size = self.scale_bar_size / self.scale_factor  # Convert to pixels
        thickness = 2  # pixels
        color = "white"

        # Bar position (bottom left corner)
        x0 = 10
        y0 = ax.get_ylim()[0] + 10

        # Draw bar
        ax.add_patch(plt.Rectangle((x0, y0), size, thickness, color=color))

        # Label
        ax.text(x0 + size + 5, y0, f"{self.scale_bar_size} Å", color=color, va="center")

    def change_view(self, view: str):
        """Change the view orientation of the tomogram."""
        self.current_view = view
        self.update_display()

    def update_slice(self, value: int, from_spinbox: bool = False):
        """Update the current slice being displayed."""
        self.current_slice = value

        # Update slider and spinbox
        if not from_spinbox:
            self.slice_spinbox.setValue(value)

        self.update_display()

    def toggle_measure_mode(self, checked):
        """Toggle distance measurement mode."""
        self.measuring_mode = checked
        if not checked:
            self.measure_start = None
            self.measure_end = None
            self.measure_label.setText("Distance: ---")
            self.update_display()

    def on_click(self, event):
        """Handle mouse click events for measurements."""
        if not self.measuring_mode or event.inaxes is None:
            return

        if self.measure_start is None:
            self.measure_start = (event.xdata, event.ydata)
            self.measure_label.setText("Click endpoint...")
        else:
            self.measure_end = (event.xdata, event.ydata)
            # Calculate distance in angstroms
            dx = (self.measure_end[0] - self.measure_start[0]) * self.scale_factor
            dy = (self.measure_end[1] - self.measure_start[1]) * self.scale_factor
            distance = np.sqrt(dx * dx + dy * dy)
            self.measure_label.setText(f"Distance: {distance:.1f} Å")
            self.measure_btn.setChecked(False)
            self.update_display()

    def on_mouse_move(self, event):
        """Handle mouse movement for live measurement preview."""
        if (
            not self.measuring_mode
            or event.inaxes is None
            or self.measure_start is None
        ):
            return

        # Show temporary line
        self.measure_end = (event.xdata, event.ydata)
        self.update_display()

        # Show current distance
        dx = (self.measure_end[0] - self.measure_start[0]) * self.scale_factor
        dy = (self.measure_end[1] - self.measure_start[1]) * self.scale_factor
        distance = np.sqrt(dx * dx + dy * dy)
        self.measure_label.setText(f"Current: {distance:.1f} Å")


class CTFViewer(QWidget):
    """CTF estimation and correction visualization."""

    def __init__(self, parent=None):
        """Initialize the instance."""
        super().__init__(parent)
        self.data = None
        self.init_ui()

    # TODO: Refactor function - Function 'init_ui' too long (72 lines)
    def init_ui(self):
        """Initialize the UI components."""
        layout = QVBoxLayout()

        # Tabs for different views
        tabs = QTabWidget()

        # 1D CTF plot tab
        self.ctf_1d_widget = QWidget()
        ctf_1d_layout = QVBoxLayout()

        # Parameter controls
        param_group = QGroupBox("Parameters")
        param_layout = QGridLayout()

        # Defocus control
        self.defocus_slider = QSlider(Qt.Orientation.Horizontal)
        self.defocus_slider.setRange(5000, 50000)  # 0.5-5.0 μm
        self.defocus_slider.setValue(20000)
        self.defocus_slider.valueChanged.connect(self.update_plot)
        param_layout.addWidget(QLabel("Defocus (Å):"), 0, 0)
        param_layout.addWidget(self.defocus_slider, 0, 1)

        # Astigmatism control
        self.astig_slider = QSlider(Qt.Orientation.Horizontal)
        self.astig_slider.setRange(0, 10000)  # 0-1.0 μm
        self.astig_slider.valueChanged.connect(self.update_plot)
        param_layout.addWidget(QLabel("Astigmatism (Å):"), 1, 0)
        param_layout.addWidget(self.astig_slider, 1, 1)

        # Phase shift control
        self.phase_slider = QSlider(Qt.Orientation.Horizontal)
        self.phase_slider.setRange(0, 180)  # 0-180 degrees
        self.phase_slider.valueChanged.connect(self.update_plot)
        param_layout.addWidget(QLabel("Phase Shift (°):"), 2, 0)
        param_layout.addWidget(self.phase_slider, 2, 1)

        param_group.setLayout(param_layout)
        ctf_1d_layout.addWidget(param_group)

        # 1D CTF plot
        self.figure_1d = Figure(figsize=(8, 4))
        self.canvas_1d = FigureCanvasQTAgg(self.figure_1d)
        ctf_1d_layout.addWidget(self.canvas_1d)

        self.ctf_1d_widget.setLayout(ctf_1d_layout)
        tabs.addTab(self.ctf_1d_widget, "1D CTF")

        # 2D CTF visualization tab
        self.ctf_2d_widget = QWidget()
        ctf_2d_layout = QVBoxLayout()

        self.figure_2d = Figure(figsize=(8, 8))
        self.canvas_2d = FigureCanvasQTAgg(self.figure_2d)
        ctf_2d_layout.addWidget(self.canvas_2d)

        self.ctf_2d_widget.setLayout(ctf_2d_layout)
        tabs.addTab(self.ctf_2d_widget, "2D CTF")

        # Power spectrum tab
        self.power_widget = QWidget()
        power_layout = QVBoxLayout()

        self.figure_power = Figure(figsize=(8, 8))
        self.canvas_power = FigureCanvasQTAgg(self.figure_power)
        power_layout.addWidget(self.canvas_power)

        self.power_widget.setLayout(power_layout)
        tabs.addTab(self.power_widget, "Power Spectrum")

        layout.addWidget(tabs)
        self.setLayout(layout)

    def load_ctf_data(self, data: np.ndarray):
        """Load CTF fit data from analysis."""
        self.data = data
        self.update_plot()

    # TODO: Refactor function - Function 'update_plot' too long (51 lines)
    def update_plot(self):
        """Update all CTF plots."""
        defocus = self.defocus_slider.value()
        astig = self.astig_slider.value()
        phase = np.radians(self.phase_slider.value())

        # Update 1D plot
        self.figure_1d.clear()
        ax = self.figure_1d.add_subplot(111)

        # Plot theoretical CTF
        freq = np.linspace(0, 0.5, 1000)  # Spatial frequency (1/Å)
        ctf = self._calculate_ctf(freq, defocus, astig, phase)
        ax.plot(freq, ctf, label="Theoretical")

        # Plot experimental data if available
        if self.data is not None:
            ax.scatter(
                self.data[:, 0],
                self.data[:, 1],
                c="r",
                s=20,
                alpha=0.5,
                label="Experimental",
            )

        ax.set_xlabel("Spatial Frequency (1/Å)")
        ax.set_ylabel("CTF")
        ax.grid(True)
        ax.legend()
        self.canvas_1d.draw()

        # Update 2D visualization
        self.figure_2d.clear()
        ax = self.figure_2d.add_subplot(111)

        # Create 2D frequency grid
        x = np.linspace(-0.5, 0.5, 512)
        y = np.linspace(-0.5, 0.5, 512)
        fx, fy = np.meshgrid(x, y)
        f = np.sqrt(fx * fx + fy * fy)

        # Calculate 2D CTF
        ctf_2d = self._calculate_ctf(f, defocus, astig, phase)

        # Plot 2D CTF
        im = ax.imshow(ctf_2d, cmap="gray", extent=[-0.5, 0.5, -0.5, 0.5])
        ax.set_xlabel("fx (1/Å)")
        ax.set_ylabel("fy (1/Å)")
        self.figure_2d.colorbar(im)
        self.canvas_2d.draw()

    def _calculate_ctf(
        self, freq: np.ndarray, defocus: float, astig: float, phase_shift: float
    ) -> np.ndarray:
        """Calculate CTF values with current parameters."""
        # Constants for 300kV microscope
        wavelength = 0.0197  # Å
        cs = 2.7e7  # Å

        # Convert inputs to Å
        defocus = float(defocus)  # Already in Å
        astig = float(astig)  # Already in Å

        # Calculate spherical aberration term
        chi = (
            np.pi
            * wavelength
            * freq**2
            * (
                defocus
                + astig * np.cos(2 * np.angle(freq))
                - 0.5 * cs * wavelength**2 * freq**2
            )
        )

        return -np.sin(chi + phase_shift)


class AlignmentViewer(QWidget):
    """Visualization of tilt series alignment progress."""

    def __init__(self, parent=None):
        """Initialize the instance."""
        super().__init__(parent)
        self.init_ui()

    def init_ui(self):
        """Initialize the UI components."""
        layout = QVBoxLayout(self)

        # Create tabs for different visualizations
        tabs = QTabWidget()

        # Alignment plot
        self.alignment_widget = QWidget()
        alignment_layout = QVBoxLayout(self.alignment_widget)
        self.alignment_figure = Figure(figsize=(8, 6))
        self.alignment_canvas = FigureCanvasQTAgg(self.alignment_figure)
        alignment_layout.addWidget(self.alignment_canvas)
        tabs.addTab(self.alignment_widget, "Alignment")

        # Motion plot
        self.motion_widget = QWidget()
        motion_layout = QVBoxLayout(self.motion_widget)
        self.motion_figure = Figure(figsize=(8, 6))
        self.motion_canvas = FigureCanvasQTAgg(self.motion_figure)
        motion_layout.addWidget(self.motion_canvas)
        tabs.addTab(self.motion_widget, "Motion")

        layout.addWidget(tabs)

    def update_alignment(self, shifts: List[tuple]):
        """Update alignment visualization."""
        self.alignment_figure.clear()
        ax = self.alignment_figure.add_subplot(111)

        x_shifts, y_shifts = zip(*shifts)
        ax.plot(x_shifts, y_shifts, "b-")
        ax.scatter(x_shifts, y_shifts, c="r", s=50)

        ax.set_xlabel("X Shift (pixels)")
        ax.set_ylabel("Y Shift (pixels)")
        ax.grid(True)

        self.alignment_canvas.draw()

    def update_motion(self, vectors: List[tuple]):
        """Update motion correction visualization."""
        self.motion_figure.clear()
        ax = self.motion_figure.add_subplot(111)

        # Plot motion vectors
        for (x, y), (dx, dy) in vectors:
            ax.arrow(x, y, dx, dy, head_width=0.1, head_length=0.2)

        ax.set_xlabel("X")
        ax.set_ylabel("Y")
        ax.grid(True)

        self.motion_canvas.draw()


class MetricsDashboard(QWidget):
    """Dashboard for displaying reconstruction quality metrics."""

    def __init__(self, parent=None):
        """Initialize the instance."""
        super().__init__(parent)
        self.init_ui()
        self.history = {
            "resolution": [],
            "defocus": [],
            "astigmatism": [],
            "dose": [],
            "timestamps": [],
        }

    # TODO: Refactor function - Function 'init_ui' too long (56 lines)
    def init_ui(self):
        """Initialize the UI components."""
        layout = QVBoxLayout(self)

        # Current metrics
        metrics_group = QGroupBox("Current Metrics")
        metrics_layout = QVBoxLayout()

        self.metrics = {
            "Resolution": {"bar": QProgressBar(), "label": QLabel("--- Å")},
            "Defocus": {"bar": QProgressBar(), "label": QLabel("--- μm")},
            "Astigmatism": {"bar": QProgressBar(), "label": QLabel("--- μm")},
            "Dose": {"bar": QProgressBar(), "label": QLabel("--- e⁻/Å²")},
            "Coverage": {"bar": QProgressBar(), "label": QLabel("---%")},
        }

        for name, widgets in self.metrics.items():
            group = QWidget()
            group_layout = QHBoxLayout(group)
            group_layout.addWidget(QLabel(f"{name}:"))
            group_layout.addWidget(widgets["bar"])
            group_layout.addWidget(widgets["label"])
            metrics_layout.addWidget(group)

        metrics_group.setLayout(metrics_layout)
        layout.addWidget(metrics_group)

        # Trend plots
        plots_group = QGroupBox("Quality Trends")
        plots_layout = QVBoxLayout()

        self.figure = Figure(figsize=(8, 6))
        self.canvas = FigureCanvasQTAgg(self.figure)

        # Add subplot selection
        plot_controls = QHBoxLayout()
        self.plot_combo = QComboBox()
        self.plot_combo.addItems(["Resolution", "Defocus", "Astigmatism", "Dose"])
        self.plot_combo.currentTextChanged.connect(self.update_trend_plot)
        plot_controls.addWidget(QLabel("Show:"))
        plot_controls.addWidget(self.plot_combo)
        plots_layout.addLayout(plot_controls)

        plots_layout.addWidget(self.canvas)
        plots_group.setLayout(plots_layout)
        layout.addWidget(plots_group)

        # Summary statistics
        stats_group = QGroupBox("Summary Statistics")
        stats_layout = QVBoxLayout()
        self.stats_text = QTextEdit()
        self.stats_text.setReadOnly(True)
        self.stats_text.setMaximumHeight(100)
        stats_layout.addWidget(self.stats_text)
        stats_group.setLayout(stats_layout)
        layout.addWidget(stats_group)

    # TODO: Refactor update_metrics - complexity: 12 (target: <10)
    # TODO: Refactor function - Function 'update_metrics' too long (68 lines)
    def update_metrics(self, metrics: Dict[str, float]):
        """Update dashboard with new metrics."""
        timestamp = time.time()

        # Update current metrics
        for name, value in metrics.items():
            if name in self.metrics:
                # Store in history
                metric_key = name.lower()
                if metric_key in self.history:
                    self.history[metric_key].append(value)
                    if len(self.history[metric_key]) > 100:  # Keep last 100 points
                        self.history[metric_key].pop(0)

                # Update progress bar
                bar = self.metrics[name]["bar"]
                label = self.metrics[name]["label"]

                # Convert value to percentage for bar
                if name == "Resolution":
                    # Better resolution = higher percentage
                    percentage = max(0, min(100, (1 - (value - 2) / 8) * 100))
                    label.setText(f"{value:.1f} Å")
                elif name == "Defocus":
                    # Closer to target = higher percentage
                    target = 3.0  # μm
                    percentage = max(
                        0, min(100, (1 - abs(value - target) / target) * 100)
                    )
                    label.setText(f"{value:.2f} μm")
                elif name == "Astigmatism":
                    # Less astigmatism = higher percentage
                    percentage = max(0, min(100, (1 - value / 0.5) * 100))
                    label.setText(f"{value:.3f} μm")
                elif name == "Dose":
                    # Closer to target = higher percentage
                    target = 20  # e⁻/Å²
                    percentage = max(
                        0, min(100, (1 - abs(value - target) / target) * 100)
                    )
                    label.setText(f"{value:.1f} e⁻/Å²")
                else:  # Coverage
                    percentage = max(0, min(100, value * 100))
                    label.setText(f"{percentage:.1f}%")

                bar.setValue(int(percentage))

                # Set color based on percentage
                if percentage > 80:
                    color = "green"
                elif percentage > 50:
                    color = "yellow"
                else:
                    color = "red"
                bar.setStyleSheet(
                    f"QProgressBar::chunk {{ background-color: {color}; }}"
                )

        # Store timestamp
        self.history["timestamps"].append(timestamp)
        if len(self.history["timestamps"]) > 100:
            self.history["timestamps"].pop(0)

        # Update trend plot
        self.update_trend_plot()

        # Update statistics
        self.update_statistics()

    def update_trend_plot(self):
        """Update the trend plot for the selected metric."""
        if not self.history["timestamps"]:
            return

        self.figure.clear()
        ax = self.figure.add_subplot(111)

        metric = self.plot_combo.currentText().lower()
        if metric in self.history and self.history[metric]:
            # Convert timestamps to relative time in minutes
            t0 = self.history["timestamps"][0]
            times = [(t - t0) / 60 for t in self.history["timestamps"]]

            # Plot trend
            ax.plot(times, self.history[metric], "b-o")

            ax.set_xlabel("Time (minutes)")
            if metric == "resolution":
                ax.set_ylabel("Resolution (Å)")
            elif metric == "defocus":
                ax.set_ylabel("Defocus (μm)")
            elif metric == "astigmatism":
                ax.set_ylabel("Astigmatism (μm)")
            else:  # dose
                ax.set_ylabel("Accumulated Dose (e⁻/Å²)")

            ax.grid(True)

        self.canvas.draw()

    def update_statistics(self):
        """Update summary statistics."""
        stats = []

        # Calculate statistics for each metric
        for metric in ["resolution", "defocus", "astigmatism", "dose"]:
            if metric in self.history and self.history[metric]:
                values = self.history[metric]
                stats.append(f"{metric.title()}:")
                stats.append(f"  Mean: {np.mean(values):.2f}")
                stats.append(f"  Std: {np.std(values):.2f}")
                stats.append(f"  Range: {min(values):.2f} - {max(values):.2f}")

        self.stats_text.setText("\n".join(stats))

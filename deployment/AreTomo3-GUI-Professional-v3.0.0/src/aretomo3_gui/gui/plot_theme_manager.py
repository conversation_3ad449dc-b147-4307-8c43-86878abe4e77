#!/usr/bin/env python3
"""
Centralized Plot Theme Manager for AreTomo3-GUI
Ensures all matplotlib plots have consistent styling based on GUI theme
"""

import matplotlib.pyplot as plt
import matplotlib as mpl
from typing import Dict, Any
import logging

logger = logging.getLogger(__name__)

class PlotThemeManager:
    """Manages matplotlib plot themes to match GUI themes."""
    
    def __init__(self):
        """Initialize the plot theme manager."""
        self.current_theme = "light"
        self._setup_themes()
    
    def _setup_themes(self):
        """Setup theme configurations."""
        self.themes = {
            "light": {
                "style": "default",
                "figure_facecolor": "white",
                "axes_facecolor": "white",
                "axes_edgecolor": "#2d2d2d",
                "axes_labelcolor": "#2d2d2d",
                "text_color": "#2d2d2d",
                "grid_color": "#e0e0e0",
                "grid_alpha": 0.7,
                "line_colors": ["#007bff", "#28a745", "#dc3545", "#ffc107", "#6f42c1", "#20c997"],
                "savefig_facecolor": "white",
                "savefig_edgecolor": "white"
            },
            "dark": {
                "style": "dark_background",
                "figure_facecolor": "#1a1a1a",
                "axes_facecolor": "#1a1a1a",
                "axes_edgecolor": "#ffffff",
                "axes_labelcolor": "#ffffff",
                "text_color": "#ffffff",
                "grid_color": "#404040",
                "grid_alpha": 0.5,
                "line_colors": ["#3498db", "#2ecc71", "#e74c3c", "#f39c12", "#9b59b6", "#1abc9c"],
                "savefig_facecolor": "#1a1a1a",
                "savefig_edgecolor": "#1a1a1a"
            }
        }
    
    def set_theme(self, theme_name: str):
        """Set the current theme and apply matplotlib settings."""
        if theme_name not in self.themes:
            logger.warning(f"Unknown theme: {theme_name}, using light theme")
            theme_name = "light"
        
        self.current_theme = theme_name
        self.apply_theme()
        logger.info(f"Applied {theme_name} theme to matplotlib plots")
    
    def apply_theme(self):
        """Apply the current theme to matplotlib."""
        theme_config = self.themes[self.current_theme]
        
        # Set matplotlib style
        if theme_config["style"] == "dark_background":
            plt.style.use("dark_background")
        else:
            plt.style.use("default")
        
        # Configure matplotlib rcParams for consistent styling
        mpl.rcParams.update({
            # Figure settings
            'figure.facecolor': theme_config["figure_facecolor"],
            'figure.edgecolor': theme_config["figure_facecolor"],
            
            # Axes settings
            'axes.facecolor': theme_config["axes_facecolor"],
            'axes.edgecolor': theme_config["axes_edgecolor"],
            'axes.labelcolor': theme_config["axes_labelcolor"],
            'axes.axisbelow': True,
            
            # Text settings
            'text.color': theme_config["text_color"],
            'axes.titlecolor': theme_config["text_color"],
            
            # Grid settings
            'axes.grid': True,
            'grid.color': theme_config["grid_color"],
            'grid.alpha': theme_config["grid_alpha"],
            'grid.linewidth': 0.5,
            
            # Tick settings
            'xtick.color': theme_config["text_color"],
            'ytick.color': theme_config["text_color"],
            'xtick.labelsize': 10,
            'ytick.labelsize': 10,
            
            # Legend settings
            'legend.facecolor': theme_config["axes_facecolor"],
            'legend.edgecolor': theme_config["axes_edgecolor"],
            'legend.framealpha': 0.9,
            
            # Save settings
            'savefig.facecolor': theme_config["savefig_facecolor"],
            'savefig.edgecolor': theme_config["savefig_edgecolor"],
            'savefig.bbox': 'tight',
            'savefig.pad_inches': 0.1,
            
            # Font settings
            'font.size': 10,
            'axes.titlesize': 12,
            'axes.labelsize': 10,
            'legend.fontsize': 9,
            
            # Line settings
            'lines.linewidth': 1.5,
            'lines.markersize': 6,
        })
    
    def get_theme_config(self) -> Dict[str, Any]:
        """Get the current theme configuration."""
        return self.themes[self.current_theme].copy()
    
    def get_color_palette(self) -> list:
        """Get the color palette for the current theme."""
        return self.themes[self.current_theme]["line_colors"].copy()
    
    def create_figure(self, figsize=(10, 6), **kwargs):
        """Create a figure with proper theme settings."""
        theme_config = self.themes[self.current_theme]
        
        # Apply theme if not already applied
        self.apply_theme()
        
        # Create figure with theme colors
        fig = plt.figure(
            figsize=figsize,
            facecolor=theme_config["figure_facecolor"],
            edgecolor=theme_config["figure_facecolor"],
            **kwargs
        )
        
        return fig
    
    def style_axes(self, ax, title=None, xlabel=None, ylabel=None):
        """Apply consistent styling to axes."""
        theme_config = self.themes[self.current_theme]
        
        # Set background colors
        ax.set_facecolor(theme_config["axes_facecolor"])
        
        # Set labels and title
        if title:
            ax.set_title(title, color=theme_config["text_color"], fontsize=12, fontweight='bold')
        if xlabel:
            ax.set_xlabel(xlabel, color=theme_config["text_color"])
        if ylabel:
            ax.set_ylabel(ylabel, color=theme_config["text_color"])
        
        # Style spines
        for spine in ax.spines.values():
            spine.set_color(theme_config["axes_edgecolor"])
            spine.set_linewidth(1)
        
        # Style ticks
        ax.tick_params(colors=theme_config["text_color"], which='both')
        
        # Enable grid with theme colors
        ax.grid(True, color=theme_config["grid_color"], alpha=theme_config["grid_alpha"], linewidth=0.5)
        
        return ax
    
    def save_figure(self, fig, filename, dpi=300, format="png", **kwargs):
        """Save figure with proper theme settings."""
        theme_config = self.themes[self.current_theme]
        
        try:
            fig.savefig(
                filename,
                dpi=dpi,
                format=format,
                facecolor=theme_config["savefig_facecolor"],
                edgecolor=theme_config["savefig_edgecolor"],
                bbox_inches="tight",
                pad_inches=0.1,
                **kwargs
            )
            logger.info(f"Saved plot to {filename}")
            return True
        except Exception as e:
            logger.error(f"Error saving plot: {e}")
            return False

# Global instance
plot_theme_manager = PlotThemeManager()

# Convenience functions
def set_plot_theme(theme_name: str):
    """Set the plot theme globally."""
    plot_theme_manager.set_theme(theme_name)

def get_plot_theme_config():
    """Get current plot theme configuration."""
    return plot_theme_manager.get_theme_config()

def create_themed_figure(figsize=(10, 6), **kwargs):
    """Create a figure with current theme."""
    return plot_theme_manager.create_figure(figsize, **kwargs)

def style_themed_axes(ax, title=None, xlabel=None, ylabel=None):
    """Style axes with current theme."""
    return plot_theme_manager.style_axes(ax, title, xlabel, ylabel)

def save_themed_figure(fig, filename, **kwargs):
    """Save figure with current theme."""
    return plot_theme_manager.save_figure(fig, filename, **kwargs)

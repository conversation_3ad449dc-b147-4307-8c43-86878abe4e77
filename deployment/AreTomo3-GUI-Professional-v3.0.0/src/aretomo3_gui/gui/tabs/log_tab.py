"""
Log tab manager for AreTomo3 GUI.
Handles the log tab for displaying application logs and recent files.
"""

import logging
import platform
from typing import Optional

from PyQt6.QtCore import Qt
from PyQt6.QtWidgets import (
    QGroupBox,
    QHBoxLayout,
    QLabel,
    QListWidget,
    QMessageBox,
    QPushButton,
    QSplitter,
    QTextEdit,
    QVBoxLayout,
    QWidget,
)

logger = logging.getLogger(__name__)


class LogTabManager:
    """Manages the log tab setup and functionality."""

    def __init__(self, main_window):
        """Initialize the log tab manager.

        Args:
            main_window: Reference to the main AreTomo3 GUI window
        """
        self.main_window = main_window
        self.log_viewer: Optional[QTextEdit] = None
        self.recent_files_list: Optional[QListWidget] = None

    def setup_tab(self, tab_widget: QWidget, layout: QVBoxLayout) -> None:
        """Set up the log tab for displaying application logs and recent files.

        Args:
            tab_widget: The parent widget for the log tab
            layout: The layout to add widgets to
        """
        try:
            # Create horizontal splitter for side-by-side layout
            hsplitter = QSplitter(Qt.Orientation.Horizontal, tab_widget)

            # Left side - Application logs
            self._setup_log_viewer(hsplitter)

            # Right side - Recent files and quick actions
            self._setup_sidebar(hsplitter)

            # Set splitter proportions (70% logs, 30% sidebar)
            hsplitter.setStretchFactor(0, 7)
            hsplitter.setStretchFactor(1, 3)

            layout.addWidget(hsplitter)
            logger.info("Log tab setup completed successfully")

        except Exception as e:
            logger.error(f"Error setting up log tab: {str(e)}", exc_info=True)
            raise

    def _setup_log_viewer(self, parent: QWidget) -> None:
        """Setup the main log viewer."""
        log_widget = QWidget()
        log_layout = QVBoxLayout(log_widget)

        # Log controls
        controls = QHBoxLayout()

        clear_btn = QPushButton("Clear Logs")
        clear_btn.clicked.connect(self._clear_logs)
        controls.addWidget(clear_btn)

        save_logs_btn = QPushButton("Save Logs")
        save_logs_btn.clicked.connect(self._save_logs)
        controls.addWidget(save_logs_btn)

        controls.addStretch()

        refresh_btn = QPushButton("Refresh")
        refresh_btn.clicked.connect(self._refresh_logs)
        controls.addWidget(refresh_btn)

        log_layout.addLayout(controls)

        # Log display with white background
        self.log_viewer = QTextEdit()
        self.log_viewer.setReadOnly(True)
        self.log_viewer.setStyleSheet(
            """
            QTextEdit {
                font-family: 'Courier New', monospace;
                font-size: 11px;
                background-color: #ffffff;
                color: #000000;
                border: 1px solid #cccccc;
                border-radius: 5px;
                padding: 10px;
                selection-background-color: #3399ff;
                selection-color: #ffffff;
            }
        """
        )
        log_layout.addWidget(self.log_viewer)

        parent.addWidget(log_widget)

    def _setup_sidebar(self, parent: QWidget) -> None:
        """Setup the sidebar with recent files and actions."""
        sidebar_widget = QWidget()
        sidebar_layout = QVBoxLayout(sidebar_widget)

        # Recent files section
        recent_group = QGroupBox("Recent Files")
        recent_layout = QVBoxLayout()

        self.recent_files_list = QListWidget()
        self.recent_files_list.itemDoubleClicked.connect(self._open_recent_file)
        recent_layout.addWidget(self.recent_files_list)

        # Recent files controls
        recent_controls = QHBoxLayout()
        clear_recent_btn = QPushButton("Clear")
        clear_recent_btn.clicked.connect(self._clear_recent_files)
        recent_controls.addWidget(clear_recent_btn)
        recent_controls.addStretch()

        recent_layout.addLayout(recent_controls)
        recent_group.setLayout(recent_layout)
        sidebar_layout.addWidget(recent_group)

        # Quick actions section
        actions_group = QGroupBox("Quick Actions")
        actions_layout = QVBoxLayout()

        open_output_btn = QPushButton("Open Output Directory")
        open_output_btn.clicked.connect(self._open_output_directory)
        actions_layout.addWidget(open_output_btn)

        system_info_btn = QPushButton("System Information")
        system_info_btn.clicked.connect(self._show_system_info)
        actions_layout.addWidget(system_info_btn)

        actions_layout.addStretch()
        actions_group.setLayout(actions_layout)
        sidebar_layout.addWidget(actions_group)

        parent.addWidget(sidebar_widget)

    def _clear_logs(self) -> None:
        """Clear the log viewer."""
        if self.log_viewer:
            self.log_viewer.clear()
            logger.info("Log viewer cleared")

    def _save_logs(self) -> None:
        """Save current logs to file."""
        try:
            from PyQt6.QtWidgets import QFileDialog

            filename, _ = QFileDialog.getSaveFileName(
                self.main_window,
                "Save Logs",
                "aretomo3_logs.txt",
                "Text Files (*.txt);;All Files (*)",
            )
            if filename and self.log_viewer:
                with open(filename, "w") as f:
                    f.write(self.log_viewer.toPlainText())
                logger.info(f"Logs saved to {filename}")
        except Exception as e:
            logger.error(f"Error saving logs: {e}")

    def _refresh_logs(self) -> None:
        """Refresh log display."""
        logger.info("Log display refreshed")

    def _clear_recent_files(self) -> None:
        """Clear recent files list."""
        if self.recent_files_list:
            self.recent_files_list.clear()
            logger.info("Recent files list cleared")

    def _open_recent_file(self, item) -> None:
        """Open a recent file."""
        if hasattr(self.main_window, "open_file"):
            logger.info(f"Opening recent file: {item.text()}")
            # Implementation would depend on main window's file opening method

    def _open_output_directory(self) -> None:
        """Open the output directory in file explorer."""
        try:
            import os
            import platform
            import subprocess

            output_dir = getattr(self.main_window, "output_dir", None)
            if output_dir and hasattr(output_dir, "text"):
                path = output_dir.text()
                if os.path.exists(path):
                    if platform.system() == "Windows":
                        os.startfile(path)
                    elif platform.system() == "Darwin":  # macOS
                        subprocess.Popen(["open", path])
                    else:  # Linux
                        subprocess.Popen(["xdg-open", path])
                    logger.info(f"Opened output directory: {path}")
                else:
                    logger.warning(f"Output directory does not exist: {path}")
            else:
                logger.warning("No output directory set")
        except Exception as e:
            logger.error(f"Error opening output directory: {e}")

    def _show_system_info(self) -> None:
        """Show system information dialog."""
        try:
            import psutil
            
            info = f"""System Information:
OS: {platform.system()} {platform.release()}
Python: {platform.python_version()}
CPU Count: {psutil.cpu_count()}
Memory: {psutil.virtual_memory().total / (1024**3):.1f} GB
"""
            QMessageBox.information(self.main_window, "System Information", info)
        except Exception as e:
            logger.error(f"Error showing system info: {e}")

    def add_log_message(self, message: str) -> None:
        """Add a message to the log viewer."""
        if self.log_viewer:
            self.log_viewer.append(message)

    def add_recent_file(self, filepath: str) -> None:
        """Add a file to the recent files list."""
        if self.recent_files_list:
            # Avoid duplicates
            for i in range(self.recent_files_list.count()):
                if self.recent_files_list.item(i).text() == filepath:
                    self.recent_files_list.takeItem(i)
                    break

            # Add to top of list
            self.recent_files_list.insertItem(0, filepath)

            # Limit to 10 recent files
            while self.recent_files_list.count() > 10:
                self.recent_files_list.takeItem(self.recent_files_list.count() - 1)

#!/usr/bin/env python3
"""
Reorganized Main Tab - Control Center with Project Management Integration.
Eliminates redundancy and provides unified workflow control.
"""

import json
import logging
import os
from pathlib import Path
from typing import Any, Dict, Optional

import psutil

from PyQt6.QtCore import Qt, pyqtSignal
from PyQt6.QtGui import QFont
from PyQt6.QtWidgets import (
    QApplication,
    QCheckBox,
    QComboBox,
    QDoubleSpinBox,
    QFileDialog,
    QGridLayout,
    QGroupBox,
    QHBoxLayout,
    QLabel,
    QLineEdit,
    QMessageBox,
    QProgressBar,
    QPushButton,
    QScrollArea,
    QSpinBox,
    QSplitter,
    QTabWidget,
    QTextEdit,
    QVBoxLayout,
    QWidget,
)

logger = logging.getLogger(__name__)


class ProjectManager:
    """Simple project management for AreTomo3 sessions."""

    def __init__(self):
        self.current_project = None
        self.project_dir = None
        self.auto_save = True

    def create_new_project(self, name: str, base_dir: str) -> bool:
        """Create a new project."""
        try:
            project_path = os.path.join(base_dir, name)
            os.makedirs(project_path, exist_ok=True)

            # Create project structure
            os.makedirs(os.path.join(project_path, "input"), exist_ok=True)
            os.makedirs(os.path.join(project_path, "output"), exist_ok=True)
            os.makedirs(os.path.join(project_path, "analysis"), exist_ok=True)
            os.makedirs(os.path.join(project_path, "sessions"), exist_ok=True)

            self.current_project = name
            self.project_dir = project_path

            logger.info(f"Created new project: {name} at {project_path}")
            return True

        except Exception as e:
            logger.error(f"Error creating project {name}: {e}")
            return False

    def load_project(self, project_path: str) -> bool:
        """Load an existing project."""
        try:
            if os.path.exists(project_path):
                self.current_project = os.path.basename(project_path)
                self.project_dir = project_path
                logger.info(f"Loaded project: {self.current_project}")
                return True
            return False
        except Exception as e:
            logger.error(f"Error loading project {project_path}: {e}")
            return False

    def get_project_paths(self) -> Dict[str, str]:
        """Get standard project paths."""
        if not self.project_dir:
            return {}

        return {
            "input": os.path.join(self.project_dir, "input"),
            "output": os.path.join(self.project_dir, "output"),
            "analysis": os.path.join(self.project_dir, "analysis"),
            "sessions": os.path.join(self.project_dir, "sessions"),
        }


class ReorganizedMainTab(QWidget):
    """
    Project Setup Tab - Central control hub for tomography projects.

    Features:
    - Project management and workspace organization
    - Input/output directory configuration
    - AreTomo3 executable setup
    - Quick processing actions
    - System status monitoring
    """

    # Signals for communication with other components
    processing_mode_changed = pyqtSignal(str)  # single, live, batch
    project_changed = pyqtSignal(str)  # project path
    start_processing = pyqtSignal(dict)  # processing parameters

    def __init__(self, parent=None):
        super().__init__(parent)
        self.main_window = parent
        self.project_manager = ProjectManager()
        self.setup_ui()
        self.connect_signals()
        logger.info("Reorganized main tab initialized")

    def setup_ui(self):
        """Set up the user interface focused on control functionality."""
        # Create main control section (full space)
        self.control_section = self.create_control_section()

        # Main layout - give full space to control center
        main_layout = QVBoxLayout(self)
        main_layout.addWidget(self.control_section)

    def create_control_section(self):
        """Create the main control center section."""
        # Create scroll area for main content
        scroll = QScrollArea()
        scroll.setWidgetResizable(True)
        scroll_widget = QWidget()
        layout = QVBoxLayout(scroll_widget)
        layout.setSpacing(15)

        # Welcome header
        self.create_welcome_section(layout)

        # Project management section
        self.create_project_section(layout)

        # Quick setup section
        self.create_quick_setup_section(layout)

        # Essential settings section
        self.create_essential_settings_section(layout)

        # System monitor section (integrated from System Monitor tab)
        self.create_system_monitor_section(layout)

        # Status overview section
        self.create_status_section(layout)

        layout.addStretch()
        scroll.setWidget(scroll_widget)

        return scroll

    # Note: create_config_tab() and create_command_tab() methods removed
    # Configuration and command preview functionality moved to System Monitor
    # tab

    def create_welcome_section(self, layout):
        """Create welcome section."""
        welcome_label = QLabel("🚀 AreTomo3 Control Center")
        welcome_label.setStyleSheet(
            """
            QLabel {
                font-size: 18px;
                font-weight: bold;
                color: #2c3e50;
                padding: 10px;
                background-color: #ecf0f1;
                border-radius: 8px;
                margin: 5px;
            }
        """
        )
        welcome_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        layout.addWidget(welcome_label)

    def create_project_section(self, layout):
        """Create project management section."""
        project_group = QGroupBox("📁 Project Management")
        project_layout = QGridLayout(project_group)

        # Current project
        project_layout.addWidget(QLabel("Current Project:"), 0, 0)
        self.current_project_combo = QComboBox()
        self.current_project_combo.setEditable(True)
        self.current_project_combo.setPlaceholderText("No project selected")
        project_layout.addWidget(self.current_project_combo, 0, 1, 1, 2)

        # Project actions
        self.new_project_btn = QPushButton("🆕 New")
        self.load_project_btn = QPushButton("📂 Load")
        self.save_project_btn = QPushButton("💾 Save")

        project_layout.addWidget(self.new_project_btn, 0, 3)
        project_layout.addWidget(self.load_project_btn, 0, 4)
        project_layout.addWidget(self.save_project_btn, 0, 5)

        # Session management
        project_layout.addWidget(QLabel("Session:"), 1, 0)
        self.session_combo = QComboBox()
        self.session_combo.addItems(["Session_001", "Session_002", "Session_003"])
        project_layout.addWidget(self.session_combo, 1, 1)

        self.auto_save_chk = QCheckBox("Auto-save session")
        self.auto_save_chk.setChecked(True)
        project_layout.addWidget(self.auto_save_chk, 1, 2, 1, 2)

        layout.addWidget(project_group)

    def create_quick_setup_section(self, layout):
        """Create quick setup section."""
        setup_group = QGroupBox("⚡ Quick Setup")
        setup_layout = QGridLayout(setup_group)

        # AreTomo3 path
        setup_layout.addWidget(QLabel("AreTomo3 Path:"), 0, 0)
        self.aretomo_path = QLineEdit()
        self.aretomo_path.setText(
            os.environ.get("ARETOMO3_PATH", "/usr/local/bin/AreTomo3")
        )
        setup_layout.addWidget(self.aretomo_path, 0, 1, 1, 2)

        self.browse_aretomo_btn = QPushButton("📁")
        self.browse_aretomo_btn.setMaximumWidth(40)
        setup_layout.addWidget(self.browse_aretomo_btn, 0, 3)

        self.test_aretomo_btn = QPushButton("🧪 Test")
        setup_layout.addWidget(self.test_aretomo_btn, 0, 4)

        # Note about input/output configuration
        note_label = QLabel(
            "📋 Input/Output paths are configured in the Parameters tab"
        )
        note_label.setStyleSheet(
            """
            QLabel {
                font-size: 11px;
                color: #7f8c8d;
                background-color: #ecf0f1;
                padding: 10px;
                border-radius: 4px;
                border-left: 3px solid #3498db;
            }
        """
        )
        note_label.setWordWrap(True)
        setup_layout.addWidget(note_label, 1, 0, 1, 5)

        layout.addWidget(setup_group)

    def create_essential_settings_section(self, layout):
        """Create essential settings section."""
        settings_group = QGroupBox("🔧 Essential Settings")
        settings_layout = QGridLayout(settings_group)

        # GPU Selection
        settings_layout.addWidget(QLabel("GPU Selection:"), 0, 0)
        self.gpu_selection = QLineEdit()
        self.gpu_selection.setText("0,1,2,3")
        self.gpu_selection.setPlaceholderText("e.g., 0,1,2,3")
        settings_layout.addWidget(self.gpu_selection, 0, 1)

        # Processing Mode
        settings_layout.addWidget(QLabel("Processing Mode:"), 1, 0)
        self.processing_mode = QComboBox()
        self.processing_mode.addItems(["🔴 Live Processing", "📦 Batch Processing"])
        settings_layout.addWidget(self.processing_mode, 1, 1)

        # Note about microscope parameters
        note_label = QLabel(
            "📋 Microscope parameters (Pixel Size, Voltage, Tilt Axis) are available in the Parameters tab"
        )
        note_label.setStyleSheet(
            """
            QLabel {
                font-size: 11px;
                color: #7f8c8d;
                background-color: #ecf0f1;
                padding: 10px;
                border-radius: 4px;
                border-left: 3px solid #3498db;
            }
        """
        )
        note_label.setWordWrap(True)
        settings_layout.addWidget(note_label, 2, 0, 1, 2)

        layout.addWidget(settings_group)

    def create_system_monitor_section(self, layout):
        """Create comprehensive integrated system monitor section."""
        monitor_group = QGroupBox("🖥️ System Monitor & Processing Control")
        monitor_layout = QVBoxLayout(monitor_group)

        # System status row
        status_layout = QHBoxLayout()

        # System resource indicators
        self.cpu_label = QLabel("CPU: ---%")
        self.memory_label = QLabel("Memory: ---%")
        self.gpu_label = QLabel("GPU: Checking...")
        self.disk_label = QLabel("Disk: ---%")

        # Style the status labels
        status_style = """
            QLabel {
                background-color: #f8f9fa;
                border: 1px solid #dee2e6;
                border-radius: 4px;
                padding: 8px;
                font-weight: bold;
                min-width: 120px;
            }
        """
        self.cpu_label.setStyleSheet(status_style)
        self.memory_label.setStyleSheet(status_style)
        self.gpu_label.setStyleSheet(status_style)
        self.disk_label.setStyleSheet(status_style)

        status_layout.addWidget(self.cpu_label)
        status_layout.addWidget(self.memory_label)
        status_layout.addWidget(self.gpu_label)
        status_layout.addWidget(self.disk_label)
        status_layout.addStretch()

        monitor_layout.addLayout(status_layout)

        # Essential control buttons row
        controls_layout = QHBoxLayout()

        self.start_btn = QPushButton("▶️ Start Processing")
        self.start_btn.setMinimumHeight(40)
        self.start_btn.setStyleSheet(
            """
            QPushButton {
                font-size: 12px;
                font-weight: bold;
                background-color: #27ae60;
                color: white;
                border: 2px solid #27ae60;
                border-radius: 6px;
                padding: 8px;
            }
            QPushButton:hover {
                background-color: #229954;
                border-color: #229954;
            }
        """
        )

        self.stop_btn = QPushButton("⏹️ Stop Processing")
        self.stop_btn.setMinimumHeight(40)
        self.stop_btn.setEnabled(False)
        self.stop_btn.setStyleSheet(
            """
            QPushButton {
                font-size: 12px;
                font-weight: bold;
                background-color: #e74c3c;
                color: white;
                border: 2px solid #e74c3c;
                border-radius: 6px;
                padding: 8px;
            }
            QPushButton:hover {
                background-color: #c0392b;
                border-color: #c0392b;
            }
            QPushButton:disabled {
                background-color: #bdc3c7;
                color: #7f8c8d;
                border-color: #bdc3c7;
            }
        """
        )

        # Web server control
        self.web_server_btn = QPushButton("🌐 Start Web Server")
        self.web_server_btn.setMinimumHeight(40)
        self.web_server_btn.setStyleSheet(
            """
            QPushButton {
                font-size: 12px;
                font-weight: bold;
                background-color: #3498db;
                color: white;
                border: 2px solid #3498db;
                border-radius: 6px;
                padding: 8px;
            }
            QPushButton:hover {
                background-color: #2980b9;
                border-color: #2980b9;
            }
        """
        )

        controls_layout.addWidget(self.start_btn)
        controls_layout.addWidget(self.stop_btn)
        controls_layout.addWidget(self.web_server_btn)
        controls_layout.addStretch()

        monitor_layout.addLayout(controls_layout)

        # Essential system info (simplified)
        info_layout = QHBoxLayout()

        # CPU usage
        self.cpu_label = QLabel("CPU: --")
        self.cpu_label.setStyleSheet(
            "font-size: 11px; padding: 5px; background-color: #f8f9fa; border-radius: 3px; margin: 2px;"
        )

        # Memory usage
        self.memory_label = QLabel("Memory: --")
        self.memory_label.setStyleSheet(
            "font-size: 11px; padding: 5px; background-color: #f8f9fa; border-radius: 3px; margin: 2px;"
        )

        # GPU usage (if available)
        self.gpu_label = QLabel("GPU: --")
        self.gpu_label.setStyleSheet(
            "font-size: 11px; padding: 5px; background-color: #f8f9fa; border-radius: 3px; margin: 2px;"
        )

        info_layout.addWidget(self.cpu_label)
        info_layout.addWidget(self.memory_label)
        info_layout.addWidget(self.gpu_label)
        info_layout.addStretch()

        monitor_layout.addLayout(info_layout)

        # Start basic system monitoring
        self.start_basic_monitoring()

        layout.addWidget(monitor_group)

    def start_basic_monitoring(self):
        """Start comprehensive system monitoring with minimal overhead."""
        try:
            import psutil
            from PyQt6.QtCore import QTimer

            # Create timer for system monitoring
            self.monitor_timer = QTimer()
            self.monitor_timer.timeout.connect(self.update_system_info)
            self.monitor_timer.start(3000)  # Update every 3 seconds

            # Initialize system monitor from core
            try:
                from ...core.system_monitor import SystemMonitor

                self.system_monitor = SystemMonitor(update_interval=3.0)
                self.system_monitor.start()
            except ImportError:
                self.system_monitor = None

        except ImportError:
            # psutil not available, disable monitoring
            self.cpu_label.setText("CPU: N/A")
            self.memory_label.setText("Memory: N/A")
            self.gpu_label.setText("GPU: N/A")
            self.disk_label.setText("Disk: N/A")

    def update_system_info(self):
        """Update comprehensive system information."""
        try:
            # Use system monitor if available
            if hasattr(self, "system_monitor") and self.system_monitor:
                data = self.system_monitor.get_latest_data()
                if data and "error" not in data:
                    # Update CPU
                    cpu_percent = data.get("cpu", 0)
                    self.cpu_label.setText(f"CPU: {cpu_percent:.1f}%")

                    # Update Memory
                    memory = data.get("memory", {})
                    if memory:
                        mem_percent = memory.get("percent", 0)
                        used_gb = memory.get("used", 0) / (1024**3)
                        total_gb = memory.get("total", 0) / (1024**3)
                        self.memory_label.setText(
                            f"Memory: {mem_percent:.1f}% ({used_gb:.1f}GB)"
                        )

                    # Update Disk
                    disk = data.get("disk", {})
                    if disk:
                        disk_percent = disk.get("percent", 0)
                        used_gb = disk.get("used", 0) / (1024**3)
                        total_gb = disk.get("total", 0) / (1024**3)
                        self.disk_label.setText(
                            f"Disk: {disk_percent:.1f}% ({used_gb:.0f}GB)"
                        )

                    # Update GPU
                    gpu = data.get("gpu")
                    if gpu:
                        gpu_util = gpu.get("utilization", 0)
                        gpu_mem = gpu.get("memory_used", 0)
                        gpu_temp = gpu.get("temperature", 0)
                        self.gpu_label.setText(f"GPU: {gpu_util}% ({gpu_temp}°C)")
                    else:
                        self.gpu_label.setText("GPU: N/A")

                    return

            # Fallback to basic psutil monitoring

            # Get basic system stats
            cpu_percent = psutil.cpu_percent(interval=None)
            memory = psutil.virtual_memory()
            disk = psutil.disk_usage("/")

            self.cpu_label.setText(f"CPU: {cpu_percent:.1f}%")
            self.memory_label.setText(f"Memory: {memory.percent:.1f}%")
            self.disk_label.setText(f"Disk: {disk.percent:.1f}%")

            # Try to get GPU info (optional)
            try:
                import GPUtil

                gpus = GPUtil.getGPUs()
                if gpus:
                    gpu = gpus[0]
                    self.gpu_label.setText(f"GPU: {gpu.load*100:.1f}%")
                else:
                    self.gpu_label.setText("GPU: N/A")
            except Exception:
                self.gpu_label.setText("GPU: N/A")

        except Exception as e:
            # Silently handle errors
            pass

    def create_status_section(self, layout):
        """Create status overview section."""
        status_group = QGroupBox("📊 Status Overview")
        status_layout = QGridLayout(status_group)

        # Current status
        status_layout.addWidget(QLabel("Current Status:"), 0, 0)
        self.status_label = QLabel("🟡 Ready")
        self.status_label.setStyleSheet("font-weight: bold; color: #f39c12;")
        status_layout.addWidget(self.status_label, 0, 1)

        # Queue info
        status_layout.addWidget(QLabel("Queue:"), 0, 2)
        self.queue_label = QLabel("0 jobs")
        status_layout.addWidget(self.queue_label, 0, 3)

        # Progress bar
        status_layout.addWidget(QLabel("Progress:"), 1, 0)
        self.progress_bar = QProgressBar()
        self.progress_bar.setMinimumHeight(25)
        status_layout.addWidget(self.progress_bar, 1, 1, 1, 2)

        # ETA
        status_layout.addWidget(QLabel("ETA:"), 1, 3)
        self.eta_label = QLabel("--:--:--")
        status_layout.addWidget(self.eta_label, 1, 4)

        # Last result
        status_layout.addWidget(QLabel("Last Result:"), 2, 0)
        self.last_result_label = QLabel("None")
        status_layout.addWidget(self.last_result_label, 2, 1, 1, 4)

        layout.addWidget(status_group)

    def connect_signals(self):
        """Connect UI signals."""
        # Project management
        self.new_project_btn.clicked.connect(self.create_new_project)
        self.load_project_btn.clicked.connect(self.load_project)
        self.save_project_btn.clicked.connect(self.save_project)

        # File browsing
        self.browse_aretomo_btn.clicked.connect(self.browse_aretomo_path)

        # Actions
        self.start_btn.clicked.connect(self.start_processing_action)
        self.stop_btn.clicked.connect(self.stop_processing_action)
        self.web_server_btn.clicked.connect(self.toggle_web_server)

        # Processing mode
        self.processing_mode.currentTextChanged.connect(self.on_processing_mode_changed)

    # Project management methods
    def create_new_project(self):
        """Create a new project."""
        from PyQt6.QtWidgets import QInputDialog

        name, ok = QInputDialog.getText(self, "New Project", "Project name:")
        if ok and name:
            base_dir = QFileDialog.getExistingDirectory(self, "Select Project Location")
            if base_dir:
                if self.project_manager.create_new_project(name, base_dir):
                    self.current_project_combo.setCurrentText(name)
                    self.update_project_paths()
                    QMessageBox.information(
                        self, "Success", f"Project '{name}' created successfully!"
                    )
                else:
                    QMessageBox.warning(
                        self, "Error", f"Failed to create project '{name}'"
                    )

    def load_project(self):
        """Load an existing project."""
        project_dir = QFileDialog.getExistingDirectory(self, "Select Project Directory")
        if project_dir:
            if self.project_manager.load_project(project_dir):
                self.current_project_combo.setCurrentText(
                    self.project_manager.current_project
                )
                self.update_project_paths()
                QMessageBox.information(
                    self, "Success", f"Project loaded successfully!"
                )
            else:
                QMessageBox.warning(self, "Error", "Failed to load project")

    def save_project(self):
        """Save current project settings."""
        if self.project_manager.current_project:
            # Save current settings to project
            QMessageBox.information(self, "Success", "Project settings saved!")
        else:
            QMessageBox.warning(self, "No Project", "No project is currently loaded")

    def update_project_paths(self):
        """Update input/output paths based on current project."""
        if self.project_manager.current_project:
            paths = self.project_manager.get_project_paths()
            self.input_dir.setText(paths.get("input", ""))
            self.output_dir.setText(paths.get("output", ""))
            self.project_changed.emit(self.project_manager.project_dir)

    # File browsing methods
    def browse_aretomo_path(self):
        """Browse for AreTomo3 executable."""
        file_path, _ = QFileDialog.getOpenFileName(
            self, "Select AreTomo3 Executable", "", "Executable Files (*)"
        )
        if file_path:
            self.aretomo_path.setText(file_path)

    def browse_input_directory(self):
        """Browse for input directory and auto-create output directory."""
        directory = QFileDialog.getExistingDirectory(self, "Select Input Directory")
        if directory:
            self.input_dir.setText(directory)
            # Auto-create and fill output directory if not already specified
            if not self.output_dir.text().strip():
                self.auto_create_output_directory(directory)

    def configure_parameters(self):
        """Navigate to parameters tab for configuration."""
        if self.main_window:
            for i in range(self.main_window.tab_widget.count()):
                if "Parameters" in self.main_window.tab_widget.tabText(i):
                    self.main_window.tab_widget.setCurrentIndex(i)
                    break

    # Action methods
    def load_data(self):
        """Load data and scan for tilt series."""
        # Get input directory from parameters tab
        if not self.main_window or not hasattr(self.main_window, "parameters_tab"):
            QMessageBox.warning(
                self,
                "No Parameters Tab",
                "Parameters tab not found. Please configure input in Parameters tab.",
            )
            return

        params_tab = self.main_window.parameters_tab
        if (
            not hasattr(params_tab, "in_prefix")
            or not params_tab.in_prefix.text().strip()
        ):
            QMessageBox.warning(
                self,
                "No Input Directory",
                "Please configure input directory in Parameters tab first.",
            )
            return

        input_dir = params_tab.in_prefix.text().strip()
        if not os.path.exists(input_dir):
            QMessageBox.warning(
                self,
                "Invalid Input",
                "Input directory does not exist. Please check Parameters tab.",
            )
            return

        try:
            input_path = Path(input_dir)

            # Scan for .mdoc files first (tilt series metadata)
            mdoc_files = list(input_path.glob("*.mdoc"))

            # Scan for data files
            data_files = []
            for ext in ["*.eer", "*.tif", "*.tiff", "*.mrc", "*.mrcs"]:
                data_files.extend(input_path.rglob(ext))

            if mdoc_files:
                # Load tilt series from .mdoc files
                if self.main_window and hasattr(self.main_window, "find_tilt_series"):
                    series_dict = self.main_window.find_tilt_series(str(input_path))
                    if series_dict:
                        QMessageBox.information(
                            self,
                            "Tilt Series Found",
                            f"Found {len(series_dict)} tilt series with {len(mdoc_files)} .mdoc files\n"
                            f"Data files: {len(data_files)}\n"
                            f"First series: {list(series_dict.keys())[0]}",
                        )
                        # Update UI with first series
                        first_series = list(series_dict.values())[0]
                        if hasattr(self.main_window, "update_ui_from_series"):
                            self.main_window.update_ui_from_series(first_series)

                        self.status_label.setText("🟢 Tilt series loaded")
                        self.status_label.setStyleSheet(
                            "font-weight: bold; color: #27ae60;"
                        )
                        return
                    else:
                        QMessageBox.warning(
                            self,
                            "Invalid Data",
                            "Found .mdoc files but no valid tilt series",
                        )
                else:
                    QMessageBox.information(
                        self, "Metadata Found", f"Found {len(mdoc_files)} .mdoc files"
                    )

            elif data_files:
                QMessageBox.information(
                    self,
                    "Data Found",
                    f"Found {len(data_files)} data files (no .mdoc metadata)",
                )
                self.status_label.setText("🟡 Data found (no metadata)")
                self.status_label.setStyleSheet("font-weight: bold; color: #f39c12;")
            else:
                QMessageBox.warning(
                    self, "No Data", "No compatible data files or .mdoc files found"
                )

        except Exception as e:
            QMessageBox.critical(self, "Error", f"Error loading data: {str(e)}")
            logger.error(f"Error loading data: {e}")

    def start_processing_action(self):
        """Start processing with current settings."""
        # Get parameters from parameters tab
        if not self.main_window or not hasattr(self.main_window, "parameters_tab"):
            QMessageBox.warning(
                self,
                "No Parameters Tab",
                "Parameters tab not found. Please configure parameters first.",
            )
            return

        params_tab = self.main_window.parameters_tab

        # Validate settings from parameters tab
        if (
            not hasattr(params_tab, "in_prefix")
            or not params_tab.in_prefix.text().strip()
        ):
            QMessageBox.warning(
                self,
                "Missing Input",
                "Please configure input directory in Parameters tab",
            )
            return

        if not hasattr(params_tab, "out_dir") or not params_tab.out_dir.text().strip():
            QMessageBox.warning(
                self,
                "Missing Output",
                "Please configure output directory in Parameters tab",
            )
            return

        # Gather processing parameters from parameters tab
        params = {
            "input_dir": params_tab.in_prefix.text().strip(),
            "output_dir": params_tab.out_dir.text().strip(),
            "aretomo_path": self.aretomo_path.text().strip(),
            "pixel_size": (
                params_tab.pix_size.value() if hasattr(params_tab, "pix_size") else 1.91
            ),
            "voltage": params_tab.kv.value() if hasattr(params_tab, "kv") else 300,
            "tilt_axis": (
                params_tab.tilt_axis.value()
                if hasattr(params_tab, "tilt_axis")
                else 0.0
            ),
            "gpu_selection": (
                params_tab.gpu.text().strip() if hasattr(params_tab, "gpu") else "0"
            ),
            "processing_mode": self.processing_mode.currentText(),
        }

        # Update UI state
        self.start_btn.setEnabled(False)
        self.stop_btn.setEnabled(True)
        self.status_label.setText("🔄 Processing...")
        self.status_label.setStyleSheet("font-weight: bold; color: #3498db;")

        # Emit signal to start processing
        self.start_processing.emit(params)

        logger.info(f"Started processing with mode: {params['processing_mode']}")

    def stop_processing_action(self):
        """Stop current processing."""
        self.start_btn.setEnabled(True)
        self.stop_btn.setEnabled(False)
        self.status_label.setText("🟡 Ready")
        self.status_label.setStyleSheet("font-weight: bold; color: #f39c12;")
        self.progress_bar.setValue(0)

        logger.info("Processing stopped by user")

    def toggle_web_server(self):
        """Toggle web server on/off."""
        try:
            if hasattr(self.main_window, "web_server_widget"):
                web_widget = self.main_window.web_server_widget
                if hasattr(web_widget, "is_running") and web_widget.is_running:
                    # Stop server
                    web_widget.stop_server()
                    self.web_server_btn.setText("🌐 Start Web Server")
                    self.web_server_btn.setStyleSheet(
                        """
                        QPushButton {
                            font-size: 12px;
                            font-weight: bold;
                            background-color: #3498db;
                            color: white;
                            border: 2px solid #3498db;
                            border-radius: 6px;
                            padding: 8px;
                        }
                        QPushButton:hover {
                            background-color: #2980b9;
                            border-color: #2980b9;
                        }
                    """
                    )
                else:
                    # Start server
                    web_widget.start_server()
                    self.web_server_btn.setText("🌐 Stop Web Server")
                    self.web_server_btn.setStyleSheet(
                        """
                        QPushButton {
                            font-size: 12px;
                            font-weight: bold;
                            background-color: #e74c3c;
                            color: white;
                            border: 2px solid #e74c3c;
                            border-radius: 6px;
                            padding: 8px;
                        }
                        QPushButton:hover {
                            background-color: #c0392b;
                            border-color: #c0392b;
                        }
                    """
                    )
            else:
                QMessageBox.warning(
                    self, "Web Server", "Web server widget not available"
                )
        except Exception as e:
            QMessageBox.critical(
                self, "Error", f"Failed to toggle web server: {str(e)}"
            )

    def on_processing_mode_changed(self, mode_text: str):
        """Handle processing mode change."""
        mode = mode_text.split()[1].lower()  # Extract mode from "🔴 Live Processing"

        # Switch to appropriate tab based on mode
        if mode == "live":
            self.switch_to_live_tab()
        elif mode == "batch":
            self.switch_to_batch_tab()

        self.processing_mode_changed.emit(mode)
        logger.info(f"Processing mode changed to: {mode}")

    def switch_to_live_tab(self):
        """Switch to unified live processing tab."""
        if self.main_window:
            for i in range(self.main_window.tab_widget.count()):
                if "Live Processing" in self.main_window.tab_widget.tabText(i):
                    self.main_window.tab_widget.setCurrentIndex(i)
                    break

    def switch_to_batch_tab(self):
        """Switch to batch processing tab."""
        if self.main_window:
            for i in range(self.main_window.tab_widget.count()):
                if "Batch Processing" in self.main_window.tab_widget.tabText(i):
                    self.main_window.tab_widget.setCurrentIndex(i)
                    break

    # Status update methods
    def update_status(self, status: str, color: str = "#f39c12"):
        """Update status display."""
        self.status_label.setText(status)
        self.status_label.setStyleSheet(f"font-weight: bold; color: {color};")

    def update_progress(self, value: int, eta: str = ""):
        """Update progress bar and ETA."""
        self.progress_bar.setValue(value)
        if eta:
            self.eta_label.setText(eta)

    def update_queue_info(self, queue_size: int):
        """Update queue information."""
        self.queue_label.setText(f"{queue_size} jobs")

    def update_last_result(self, result_path: str):
        """Update last result information."""
        if result_path:
            result_name = os.path.basename(result_path)
            self.last_result_label.setText(result_name)

    def get_current_settings(self) -> Dict[str, Any]:
        """Get current settings as dictionary."""
        # Get input/output from parameters tab if available
        input_dir = ""
        output_dir = ""
        if self.main_window and hasattr(self.main_window, "parameters_tab"):
            params_tab = self.main_window.parameters_tab
            if hasattr(params_tab, "in_prefix"):
                input_dir = params_tab.in_prefix.text().strip()
            if hasattr(params_tab, "out_dir"):
                output_dir = params_tab.out_dir.text().strip()

        return {
            "project": self.project_manager.current_project,
            "input_dir": input_dir,
            "output_dir": output_dir,
            "aretomo_path": self.aretomo_path.text().strip(),
            "pixel_size": self.pixel_size.value(),
            "voltage": self.voltage.value(),
            "tilt_axis": self.tilt_axis.value(),
            "gpu_selection": self.gpu_selection.text().strip(),
            "processing_mode": self.processing_mode.currentText(),
            "auto_save": self.auto_save_chk.isChecked(),
        }

    def set_settings(self, settings: Dict[str, Any]):
        """Set settings from dictionary."""
        if "input_dir" in settings:
            self.input_dir.setText(settings["input_dir"])
        if "output_dir" in settings:
            self.output_dir.setText(settings["output_dir"])
        if "aretomo_path" in settings:
            self.aretomo_path.setText(settings["aretomo_path"])
        if "pixel_size" in settings:
            self.pixel_size.setValue(settings["pixel_size"])
        if "voltage" in settings:
            self.voltage.setValue(settings["voltage"])
        if "tilt_axis" in settings:
            self.tilt_axis.setValue(settings["tilt_axis"])
        if "gpu_selection" in settings:
            self.gpu_selection.setText(settings["gpu_selection"])
        if "processing_mode" in settings:
            # Find and set the processing mode
            for i in range(self.processing_mode.count()):
                if settings["processing_mode"] in self.processing_mode.itemText(i):
                    self.processing_mode.setCurrentIndex(i)
                    break
        if "auto_save" in settings:
            self.auto_save_chk.setChecked(settings["auto_save"])

    # Configuration management methods
    def load_template(self, template_name):
        """Load predefined parameter template."""
        if template_name == "Custom Configuration":
            return

        templates = {
            "High Resolution Template": {
                "pixel_size": 1.91,
                "voltage": 300,
                "tilt_axis": -95.75,
                "gpu_selection": "0,1",
                "processing_mode": "🔵 Single Series",
            },
            "Fast Processing Template": {
                "pixel_size": 1.91,
                "voltage": 300,
                "tilt_axis": -95.75,
                "gpu_selection": "0",
                "processing_mode": "🔵 Single Series",
            },
            "Live Acquisition Template": {
                "pixel_size": 1.91,
                "voltage": 300,
                "tilt_axis": -95.75,
                "gpu_selection": "0,1,2",
                "processing_mode": "🔴 Live Processing",
            },
            "Batch Processing Template": {
                "pixel_size": 1.91,
                "voltage": 300,
                "tilt_axis": -95.75,
                "gpu_selection": "0,1,2,3",
                "processing_mode": "📦 Batch Processing",
            },
        }

        if template_name in templates:
            template = templates[template_name]
            self.pixel_size.setValue(template["pixel_size"])
            self.voltage.setValue(template["voltage"])
            self.tilt_axis.setValue(template["tilt_axis"])
            self.gpu_selection.setText(template["gpu_selection"])

            # Set processing mode
            for i in range(self.processing_mode.count()):
                if template["processing_mode"] in self.processing_mode.itemText(i):
                    self.processing_mode.setCurrentIndex(i)
                    break

            self.config_status.setText(
                f"📝 Configuration Status: {template_name} loaded"
            )
            self.config_status.setStyleSheet(
                "font-weight: bold; color: #27ae60; padding: 10px;"
            )
            QMessageBox.information(
                self, "Template Loaded", f"Applied {template_name} settings"
            )

    def load_json_config(self):
        """Load parameters from JSON configuration file."""
        file_path, _ = QFileDialog.getOpenFileName(
            self, "Load JSON Configuration", "", "JSON files (*.json);;All files (*.*)"
        )

        if file_path:
            try:
                with open(file_path) as f:
                    config = json.load(f)

                # Apply configuration
                if "settings" in config:
                    self.set_settings(config["settings"])
                elif "aretomo3_config" in config:
                    self.set_settings(config["aretomo3_config"].get("settings", {}))

                self.config_status.setText(
                    f"📝 Configuration Status: {os.path.basename(file_path)} loaded"
                )
                self.config_status.setStyleSheet(
                    "font-weight: bold; color: #27ae60; padding: 10px;"
                )
                QMessageBox.information(
                    self,
                    "Success",
                    f"Configuration loaded from {os.path.basename(file_path)}",
                )

            except Exception as e:
                QMessageBox.critical(
                    self, "Error", f"Failed to load configuration: {str(e)}"
                )

    def save_json_config(self):
        """Save current parameters to JSON configuration file."""
        file_path, _ = QFileDialog.getSaveFileName(
            self,
            "Save JSON Configuration",
            "aretomo3_config.json",
            "JSON files (*.json);;All files (*.*)",
        )

        if file_path:
            try:
                config = {
                    "aretomo3_config": {
                        "version": "1.0",
                        "settings": self.get_current_settings(),
                        "metadata": {
                            "created_by": "AreTomo3 GUI",
                            "timestamp": str(
                                os.path.getmtime(__file__)
                                if os.path.exists(__file__)
                                else "unknown"
                            ),
                        },
                    }
                }

                with open(file_path, "w") as f:
                    json.dump(config, f, indent=2)

                self.config_status.setText(
                    f"📝 Configuration Status: {os.path.basename(file_path)} saved"
                )
                self.config_status.setStyleSheet(
                    "font-weight: bold; color: #27ae60; padding: 10px;"
                )
                QMessageBox.information(
                    self,
                    "Success",
                    f"Configuration saved to {os.path.basename(file_path)}",
                )

            except Exception as e:
                QMessageBox.critical(
                    self, "Error", f"Failed to save configuration: {str(e)}"
                )

    def validate_configuration(self):
        """Validate current parameter configuration."""
        issues = []

        # Check required paths
        if not self.input_dir.text().strip():
            issues.append("Input directory is required")

        if not self.output_dir.text().strip():
            issues.append("Output directory is required")

        if not self.aretomo_path.text().strip():
            issues.append("AreTomo3 executable path is required")

        # Check parameter ranges
        if self.pixel_size.value() <= 0:
            issues.append("Pixel size must be greater than 0")

        if self.voltage.value() < 60 or self.voltage.value() > 300:
            issues.append("Voltage should be between 60-300 kV")

        # Check GPU configuration
        gpu_text = self.gpu_selection.text().strip()
        if gpu_text:
            try:
                gpu_ids = [int(x.strip()) for x in gpu_text.split(",")]
                if any(gpu_id < 0 for gpu_id in gpu_ids):
                    issues.append("GPU IDs must be non-negative")
            except ValueError:
                issues.append("Invalid GPU ID format (use comma-separated numbers)")

        # Show validation results
        if issues:
            self.config_status.setText("📝 Configuration Status: Issues found")
            self.config_status.setStyleSheet(
                "font-weight: bold; color: #e74c3c; padding: 10px;"
            )
            QMessageBox.warning(
                self,
                "Configuration Issues",
                "Found the following issues:\n\n"
                + "\n".join(f"• {issue}" for issue in issues),
            )
        else:
            self.config_status.setText("📝 Configuration Status: Valid ✅")
            self.config_status.setStyleSheet(
                "font-weight: bold; color: #27ae60; padding: 10px;"
            )
            QMessageBox.information(
                self,
                "Validation Passed",
                "✅ Configuration is valid and ready for processing!",
            )

    def load_recent_config(self):
        """Load selected recent configuration."""
        selected = self.recent_configs.currentText()
        QMessageBox.information(self, "Load Recent", f"Loading {selected}...")
        # TODO: Implement actual loading logic

    # Command preview methods
    def update_command_preview(self):
        """Update command preview with current settings."""
        try:
            # Build AreTomo3 command
            cmd_parts = [self.aretomo_path.text().strip() or "AreTomo3"]

            # Add input/output
            if self.input_dir.text().strip():
                cmd_parts.append(f"-InPrefix {self.input_dir.text().strip()}")

            if self.output_dir.text().strip():
                cmd_parts.append(f"-OutDir {self.output_dir.text().strip()}")

            # Add essential parameters
            cmd_parts.append(f"-PixSize {self.pixel_size.value()}")
            cmd_parts.append(f"-Kv {self.voltage.value()}")
            cmd_parts.append(f"-TiltAxis {self.tilt_axis.value()}")

            # Add GPU selection
            if self.gpu_selection.text().strip():
                cmd_parts.append(f"-Gpu {self.gpu_selection.text().strip()}")

            # Join command parts
            command = " ".join(cmd_parts)

            # Update preview
            self.command_preview.setPlainText(command)

        except Exception as e:
            self.command_preview.setPlainText(f"Error generating command: {str(e)}")

    def copy_command(self):
        """Copy the command to clipboard."""
        try:
            command = self.command_preview.toPlainText()
            if command:
                QApplication.clipboard().setText(command)
                QMessageBox.information(self, "Success", "Command copied to clipboard!")
            else:
                QMessageBox.warning(
                    self,
                    "No Command",
                    "No command to copy. Please update preview first.",
                )
        except Exception as e:
            QMessageBox.critical(self, "Error", f"Failed to copy command: {str(e)}")

    def save_command(self):
        """Save command to file."""
        file_path, _ = QFileDialog.getSaveFileName(
            self,
            "Save Command",
            "aretomo3_command.sh",
            "Shell scripts (*.sh);;Text files (*.txt);;All files (*.*)",
        )

        if file_path:
            try:
                command = self.command_preview.toPlainText()
                with open(file_path, "w") as f:
                    f.write("#!/bin/bash\n")
                    f.write(f"# AreTomo3 command generated by GUI\n")
                    f.write(
                        f"# Generated on: {os.path.getmtime(__file__) if os.path.exists(__file__) else 'unknown'}\n\n"
                    )
                    f.write(command + "\n")

                # Make executable on Unix systems
                if os.name != "nt":
                    os.chmod(file_path, 0o755)

                QMessageBox.information(
                    self, "Success", f"Command saved to {os.path.basename(file_path)}"
                )

            except Exception as e:
                QMessageBox.critical(self, "Error", f"Failed to save command: {str(e)}")

    def execute_command(self):
        """Execute AreTomo3 command."""
        command = self.command_preview.toPlainText()
        if not command:
            QMessageBox.warning(
                self,
                "No Command",
                "No command to execute. Please update preview first.",
            )
            return

        reply = QMessageBox.question(
            self,
            "Execute Command",
            f"Are you sure you want to execute this command?\n\n{command}",
            QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No,
        )

        if reply == QMessageBox.StandardButton.Yes:
            # TODO: Implement actual command execution
            QMessageBox.information(
                self, "Execute", "Command execution would start here..."
            )
            logger.info(f"Would execute: {command}")

    def load_from_history(self):
        """Load command from history."""
        selected = self.command_history.currentText()
        if selected:
            self.command_preview.setPlainText(selected)
            QMessageBox.information(
                self, "History Loaded", f"Command loaded from history"
            )

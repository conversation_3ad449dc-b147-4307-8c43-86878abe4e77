"""
CTF Estimation and Correction Tab
Provides integrated CTF functionality in the GUI.
"""

import logging
from pathlib import Path

from PyQt6.QtCore import Qt, pyqtSlot
from PyQt6.QtWidgets import (
    QCheckBox,
    QDoubleSpinBox,
    QGroupBox,
    QHBoxLayout,
    QLabel,
    QLineEdit,
    QProgressBar,
    QPushButton,
    QSpinBox,
    QTableWidget,
    QTableWidgetItem,
    QTextEdit,
    QVBoxLayout,
    QWidget,
)

logger = logging.getLogger(__name__)


class CTFTab(QWidget):
    """CTF estimation and correction tab."""

    def __init__(self, parent=None):
        super().__init__(parent)
        self.ctf_widget = None
        self.setup_ui()

    def setup_ui(self):
        """Setup the CTF tab UI."""
        layout = QVBoxLayout(self)

        # CTF Parameters Group
        params_group = QGroupBox("CTF Parameters")
        params_layout = QHBoxLayout(params_group)

        # Microscope parameters
        params_layout.addWidget(QLabel("Voltage (kV):"))
        self.voltage_spin = QDoubleSpinBox()
        self.voltage_spin.setRange(80, 300)
        self.voltage_spin.setValue(300)
        params_layout.addWidget(self.voltage_spin)

        params_layout.addWidget(QLabel("Cs (mm):"))
        self.cs_spin = QDoubleSpinBox()
        self.cs_spin.setRange(0.1, 5.0)
        self.cs_spin.setValue(2.7)
        self.cs_spin.setSingleStep(0.1)
        params_layout.addWidget(self.cs_spin)

        params_layout.addWidget(QLabel("Pixel Size (Å):"))
        self.pixel_size_spin = QDoubleSpinBox()
        self.pixel_size_spin.setRange(0.1, 10.0)
        self.pixel_size_spin.setValue(1.0)
        self.pixel_size_spin.setSingleStep(0.1)
        params_layout.addWidget(self.pixel_size_spin)

        layout.addWidget(params_group)

        # Control buttons
        controls_layout = QHBoxLayout()

        self.estimate_btn = QPushButton("Estimate CTF")
        self.estimate_btn.clicked.connect(self.estimate_ctf)
        controls_layout.addWidget(self.estimate_btn)

        self.correct_btn = QPushButton("Apply Correction")
        self.correct_btn.clicked.connect(self.apply_correction)
        controls_layout.addWidget(self.correct_btn)

        self.auto_estimate_cb = QCheckBox("Auto Estimate")
        self.auto_estimate_cb.setChecked(True)
        controls_layout.addWidget(self.auto_estimate_cb)

        layout.addLayout(controls_layout)

        # Results table
        self.results_table = QTableWidget()
        self.results_table.setColumnCount(6)
        self.results_table.setHorizontalHeaderLabels(
            ["File", "Defocus U", "Defocus V", "Astigmatism", "Resolution", "Quality"]
        )
        layout.addWidget(self.results_table)

        # Progress bar
        self.progress_bar = QProgressBar()
        self.progress_bar.setVisible(False)
        layout.addWidget(self.progress_bar)

        # Log output
        log_group = QGroupBox("CTF Log")
        log_layout = QVBoxLayout(log_group)
        self.log_text = QTextEdit()
        self.log_text.setMaximumHeight(150)
        log_layout.addWidget(self.log_text)
        layout.addWidget(log_group)

    def set_ctf_widget(self, ctf_widget):
        """Set the CTF integration widget."""
        self.ctf_widget = ctf_widget
        if self.ctf_widget:
            self.ctf_widget.ctf_estimation_completed.connect(
                self.on_ctf_estimation_completed
            )
            self.ctf_widget.ctf_correction_completed.connect(
                self.on_ctf_correction_completed
            )

    def estimate_ctf(self):
        """Estimate CTF for selected files."""
        if not self.ctf_widget:
            return

        # Get parameters
        voltage = self.voltage_spin.value()
        cs = self.cs_spin.value()
        pixel_size = self.pixel_size_spin.value()

        # For demo, estimate CTF for a test file
        test_file = "/tmp/test_image.mrc"
        self.ctf_widget.estimate_ctf_async(test_file, pixel_size, voltage, cs)

        self.progress_bar.setVisible(True)
        self.log_text.append(f"Starting CTF estimation for {test_file}")

    def apply_correction(self):
        """Apply CTF correction."""
        if not self.ctf_widget:
            return

        test_file = "/tmp/test_image.mrc"
        output_file = "/tmp/test_image_corrected.mrc"

        self.ctf_widget.apply_ctf_correction_async(test_file, output_file)
        self.log_text.append(f"Applying CTF correction to {test_file}")

    @pyqtSlot(str, dict, dict)
    def on_ctf_estimation_completed(self, image_path, ctf_params, quality):
        """Handle CTF estimation completion."""
        self.progress_bar.setVisible(False)

        # Add results to table
        row = self.results_table.rowCount()
        self.results_table.insertRow(row)

        self.results_table.setItem(row, 0, QTableWidgetItem(Path(image_path).name))
        self.results_table.setItem(
            row, 1, QTableWidgetItem(f"{ctf_params['defocus_u']:.2f}")
        )
        self.results_table.setItem(
            row, 2, QTableWidgetItem(f"{ctf_params['defocus_v']:.2f}")
        )
        self.results_table.setItem(
            row, 3, QTableWidgetItem(f"{ctf_params['astigmatism']:.2f}")
        )
        self.results_table.setItem(
            row, 4, QTableWidgetItem(f"{ctf_params['resolution_limit']:.1f}")
        )
        self.results_table.setItem(
            row, 5, QTableWidgetItem(f"{quality['overall_score']:.3f}")
        )

        self.log_text.append(f"✅ CTF estimation completed for {Path(image_path).name}")
        self.log_text.append(
            f"   Defocus: {ctf_params['defocus_u']:.2f} / {ctf_params['defocus_v']:.2f} μm"
        )
        self.log_text.append(f"   Resolution: {ctf_params['resolution_limit']:.1f} Å")

    @pyqtSlot(str, bool)
    def on_ctf_correction_completed(self, image_path, success):
        """Handle CTF correction completion."""
        if success:
            self.log_text.append(
                f"✅ CTF correction completed for {Path(image_path).name}"
            )
        else:
            self.log_text.append(
                f"❌ CTF correction failed for {Path(image_path).name}"
            )

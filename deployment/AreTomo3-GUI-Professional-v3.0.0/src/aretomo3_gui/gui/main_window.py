#!/usr/bin/env python3
"""
AreTomo3 GUI - Clean Working Main Window
"""

import sys
import os
import logging
from datetime import datetime
from pathlib import Path
from typing import Optional, Dict, Any

# PyQt6 imports
from PyQt6.QtWidgets import (
    QMainWindow, QWidget, QVBoxLayout, QHBoxLayout, QTabWidget,
    QLabel, QLineEdit, QPushButton, QTextEdit, QStatusBar,
    QFileDialog, QMessageBox, QSpinBox, QDoubleSpinBox, QCheckBox,
    QGroupBox, QFormLayout, QMenuBar, QApplication, QComboBox,
    QSplitter, QScrollArea
)
from PyQt6.QtCore import Qt, QTimer
from PyQt6.QtGui import QAction, QFont, QIcon

# Import theme manager for rich styling (temporarily disabled)
# from .theme_manager import ThemeManager

# Set up logging
logger = logging.getLogger(__name__)


class AreTomoGUI(QMainWindow):
    """Clean, working AreTomo3 GUI"""
    
    def __init__(self):
        super().__init__()
        logger.info("Initializing AreTomo3 GUI...")
        
        # Initialize UI first
        self.init_ui()

        # Apply professional styling after UI is created
        self.apply_professional_styling()
        
        # Set up status bar
        self.status_bar = QStatusBar()
        self.setStatusBar(self.status_bar)
        self.status_bar.showMessage("🚀 AreTomo3 GUI Ready - Professional Edition")

        logger.info("AreTomo3 GUI initialized successfully")

    def apply_professional_styling(self):
        """Apply professional theme and styling"""
        try:
            # Apply direct styling without theme manager

            # Set window properties for professional look
            self.setWindowTitle("AreTomo3 GUI - Professional Edition")
            self.setMinimumSize(1200, 800)

            # Apply custom styling
            self.setStyleSheet("""
                QMainWindow {
                    background-color: #2b2b2b;
                    color: #ffffff;
                }
                QTabWidget::pane {
                    border: 1px solid #555555;
                    background-color: #3c3c3c;
                    border-radius: 8px;
                }
                QTabBar::tab {
                    background-color: #404040;
                    color: #ffffff;
                    padding: 8px 16px;
                    margin: 2px;
                    border-radius: 4px;
                    font-weight: bold;
                }
                QTabBar::tab:selected {
                    background-color: #0078d4;
                    color: #ffffff;
                }
                QTabBar::tab:hover {
                    background-color: #505050;
                }
                QGroupBox {
                    font-weight: bold;
                    border: 2px solid #555555;
                    border-radius: 8px;
                    margin: 8px;
                    padding-top: 10px;
                    background-color: #3c3c3c;
                }
                QGroupBox::title {
                    subcontrol-origin: margin;
                    left: 10px;
                    padding: 0 8px 0 8px;
                    color: #ffffff;
                }
                QPushButton {
                    background-color: #0078d4;
                    color: #ffffff;
                    border: none;
                    padding: 8px 16px;
                    border-radius: 4px;
                    font-weight: bold;
                }
                QPushButton:hover {
                    background-color: #106ebe;
                }
                QPushButton:pressed {
                    background-color: #005a9e;
                }
                QLineEdit, QSpinBox, QDoubleSpinBox {
                    background-color: #404040;
                    color: #ffffff;
                    border: 1px solid #555555;
                    padding: 6px;
                    border-radius: 4px;
                }
                QTextEdit {
                    background-color: #2b2b2b;
                    color: #ffffff;
                    border: 1px solid #555555;
                    border-radius: 4px;
                }
                QStatusBar {
                    background-color: #404040;
                    color: #ffffff;
                    border-top: 1px solid #555555;
                }
            """)

            logger.info("Professional styling applied successfully")

        except Exception as e:
            logger.warning(f"Could not apply professional styling: {e}")
            # Fallback to basic styling
            self.setStyleSheet("QMainWindow { background-color: #f0f0f0; }")

    def create_professional_header(self, layout):
        """Create professional header with branding and info"""
        header_widget = QWidget()
        header_layout = QHBoxLayout(header_widget)
        header_layout.setContentsMargins(20, 10, 20, 10)

        # Title and version
        title_label = QLabel("🔬 AreTomo3 GUI")
        title_label.setStyleSheet("""
            QLabel {
                font-size: 24px;
                font-weight: bold;
                color: #0078d4;
                margin: 0;
            }
        """)
        header_layout.addWidget(title_label)

        # Version info
        version_label = QLabel("Professional Edition v2.0")
        version_label.setStyleSheet("""
            QLabel {
                font-size: 12px;
                color: #888888;
                margin-left: 10px;
            }
        """)
        header_layout.addWidget(version_label)

        header_layout.addStretch()

        # Status indicator
        status_label = QLabel("🟢 Ready")
        status_label.setStyleSheet("""
            QLabel {
                font-size: 14px;
                font-weight: bold;
                color: #00aa00;
                padding: 5px 10px;
                border: 1px solid #555555;
                border-radius: 4px;
                background-color: #404040;
            }
        """)
        header_layout.addWidget(status_label)

        layout.addWidget(header_widget)

    def init_ui(self):
        """Initialize the user interface with professional styling"""
        # Set window properties
        self.setGeometry(100, 100, 1400, 900)

        # Create menu bar
        self.create_menu_bar()

        # Create central widget with professional layout
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        layout = QVBoxLayout(central_widget)
        layout.setContentsMargins(10, 10, 10, 10)
        layout.setSpacing(10)

        # Add professional header
        self.create_professional_header(layout)

        # Create tab widget with professional styling
        self.tab_widget = QTabWidget()
        self.tab_widget.setTabPosition(QTabWidget.TabPosition.North)
        self.tab_widget.setMovable(True)
        self.tab_widget.setTabsClosable(False)
        layout.addWidget(self.tab_widget)
        
        # Create all tabs using simple implementations
        self.create_project_setup_tab()
        self.create_parameters_tab()
        self.create_live_processing_tab()
        self.create_batch_processing_tab()
        self.create_analysis_tab()
        self.create_embedded_viewer_tab()  # NEW: Embedded CTF & Motion viewers
        self.create_viewer_tab()
        self.create_dashboard_tab()
        self.create_log_tab()
        
    def create_menu_bar(self):
        """Create the menu bar"""
        menubar = self.menuBar()
        
        # File menu
        file_menu = menubar.addMenu("File")
        
        open_action = QAction("Open...", self)
        open_action.setShortcut("Ctrl+O")
        open_action.triggered.connect(self.open_file)
        file_menu.addAction(open_action)
        
        save_action = QAction("Save", self)
        save_action.setShortcut("Ctrl+S")
        save_action.triggered.connect(self.save_file)
        file_menu.addAction(save_action)
        
        file_menu.addSeparator()
        
        exit_action = QAction("Exit", self)
        exit_action.setShortcut("Ctrl+Q")
        exit_action.triggered.connect(self.close)
        file_menu.addAction(exit_action)
        
        # Help menu
        help_menu = menubar.addMenu("Help")
        
        about_action = QAction("About", self)
        about_action.triggered.connect(self.show_about)
        help_menu.addAction(about_action)
        
    def create_project_setup_tab(self):
        """Create project setup tab - main control hub"""
        main_tab = QWidget()
        layout = QVBoxLayout(main_tab)
        
        # Input/Output section
        io_group = QGroupBox("Input/Output")
        io_layout = QFormLayout(io_group)
        
        self.input_dir = QLineEdit()
        self.input_dir.setPlaceholderText("Select input directory...")
        input_btn = QPushButton("Browse")
        input_btn.clicked.connect(self.browse_input_dir)
        
        input_layout = QHBoxLayout()
        input_layout.addWidget(self.input_dir)
        input_layout.addWidget(input_btn)
        io_layout.addRow("Input Directory:", input_layout)
        
        self.output_dir = QLineEdit()
        self.output_dir.setPlaceholderText("Output directory (auto-generated)")
        io_layout.addRow("Output Directory:", self.output_dir)
        
        layout.addWidget(io_group)
        
        # AreTomo3 path section
        aretomo_group = QGroupBox("AreTomo3 Configuration")
        aretomo_layout = QFormLayout(aretomo_group)
        
        self.aretomo_path = QLineEdit()
        self.aretomo_path.setPlaceholderText("Path to AreTomo3 executable...")
        aretomo_btn = QPushButton("Browse")
        aretomo_btn.clicked.connect(self.browse_aretomo_path)
        
        aretomo_path_layout = QHBoxLayout()
        aretomo_path_layout.addWidget(self.aretomo_path)
        aretomo_path_layout.addWidget(aretomo_btn)
        aretomo_layout.addRow("AreTomo3 Path:", aretomo_path_layout)
        
        layout.addWidget(aretomo_group)
        
        # Control buttons
        button_layout = QHBoxLayout()
        
        self.load_btn = QPushButton("Load Tilt Series")
        self.load_btn.clicked.connect(self.load_tilt_series)
        button_layout.addWidget(self.load_btn)
        
        self.process_btn = QPushButton("Start Processing")
        self.process_btn.clicked.connect(self.start_processing)
        button_layout.addWidget(self.process_btn)
        
        self.stop_btn = QPushButton("Stop")
        self.stop_btn.clicked.connect(self.stop_processing)
        self.stop_btn.setEnabled(False)
        button_layout.addWidget(self.stop_btn)
        
        layout.addLayout(button_layout)
        layout.addStretch()
        
        self.tab_widget.addTab(main_tab, "Project Setup")
        
    def create_parameters_tab(self):
        """Create parameters tab"""
        params_tab = QWidget()
        layout = QVBoxLayout(params_tab)
        
        # Microscope parameters
        microscope_group = QGroupBox("Microscope Parameters")
        microscope_layout = QFormLayout(microscope_group)
        
        self.pixel_size = QDoubleSpinBox()
        self.pixel_size.setRange(0.1, 10.0)
        self.pixel_size.setValue(1.91)
        self.pixel_size.setSuffix(" Å")
        microscope_layout.addRow("Pixel Size:", self.pixel_size)
        
        self.voltage = QSpinBox()
        self.voltage.setRange(80, 300)
        self.voltage.setValue(300)
        self.voltage.setSuffix(" kV")
        microscope_layout.addRow("Voltage:", self.voltage)
        
        self.cs = QDoubleSpinBox()
        self.cs.setRange(0.0, 10.0)
        self.cs.setValue(2.7)
        self.cs.setSuffix(" mm")
        microscope_layout.addRow("Cs:", self.cs)
        
        layout.addWidget(microscope_group)
        
        # Processing parameters
        processing_group = QGroupBox("Processing Parameters")
        processing_layout = QFormLayout(processing_group)
        
        self.tilt_axis = QDoubleSpinBox()
        self.tilt_axis.setRange(-180, 180)
        self.tilt_axis.setValue(-95.75)
        self.tilt_axis.setSuffix("°")
        processing_layout.addRow("Tilt Axis:", self.tilt_axis)
        
        self.vol_z = QSpinBox()
        self.vol_z.setRange(100, 4096)
        self.vol_z.setValue(2048)
        processing_layout.addRow("Volume Z:", self.vol_z)
        
        layout.addWidget(processing_group)
        layout.addStretch()
        
        self.tab_widget.addTab(params_tab, "Reconstruction Parameters")

    def create_live_processing_tab(self):
        """Create live processing tab"""
        live_tab = QWidget()
        layout = QVBoxLayout(live_tab)

        # Live processing controls
        live_group = QGroupBox("Live Processing Controls")
        live_layout = QFormLayout(live_group)

        # Watch directory
        self.watch_dir = QLineEdit()
        self.watch_dir.setPlaceholderText("Directory to watch for new files...")
        watch_btn = QPushButton("Browse")
        watch_btn.clicked.connect(self.browse_watch_dir)

        watch_layout = QHBoxLayout()
        watch_layout.addWidget(self.watch_dir)
        watch_layout.addWidget(watch_btn)
        live_layout.addRow("Watch Directory:", watch_layout)

        # Auto-processing options
        self.auto_process = QCheckBox("Auto-process new files")
        self.auto_process.setChecked(True)
        live_layout.addRow("Auto Processing:", self.auto_process)

        layout.addWidget(live_group)

        # Status display
        status_group = QGroupBox("Processing Status")
        status_layout = QVBoxLayout(status_group)

        self.live_status = QTextEdit()
        self.live_status.setReadOnly(True)
        self.live_status.setMaximumHeight(200)
        self.live_status.append("Live processing ready...")
        status_layout.addWidget(self.live_status)

        layout.addWidget(status_group)
        layout.addStretch()

        self.tab_widget.addTab(live_tab, "Live Processing")

    def create_batch_processing_tab(self):
        """Create batch processing tab"""
        batch_tab = QWidget()
        layout = QVBoxLayout(batch_tab)

        # Batch queue
        queue_group = QGroupBox("Processing Queue")
        queue_layout = QVBoxLayout(queue_group)

        # Queue controls
        queue_controls = QHBoxLayout()
        add_btn = QPushButton("Add Files")
        add_btn.clicked.connect(self.add_batch_files)
        remove_btn = QPushButton("Remove Selected")
        clear_btn = QPushButton("Clear Queue")

        queue_controls.addWidget(add_btn)
        queue_controls.addWidget(remove_btn)
        queue_controls.addWidget(clear_btn)
        queue_controls.addStretch()

        queue_layout.addLayout(queue_controls)

        # Queue list (placeholder)
        self.batch_queue = QTextEdit()
        self.batch_queue.setReadOnly(True)
        self.batch_queue.setMaximumHeight(150)
        self.batch_queue.append("Batch queue empty...")
        queue_layout.addWidget(self.batch_queue)

        layout.addWidget(queue_group)

        # Batch controls
        batch_controls = QHBoxLayout()
        start_batch_btn = QPushButton("Start Batch")
        start_batch_btn.clicked.connect(self.start_batch_processing)
        pause_batch_btn = QPushButton("Pause")
        stop_batch_btn = QPushButton("Stop")

        batch_controls.addWidget(start_batch_btn)
        batch_controls.addWidget(pause_batch_btn)
        batch_controls.addWidget(stop_batch_btn)
        batch_controls.addStretch()

        layout.addLayout(batch_controls)
        layout.addStretch()

        self.tab_widget.addTab(batch_tab, "Batch Processing")

    def create_analysis_tab(self):
        """Create data analysis tab"""
        analysis_tab = QWidget()
        layout = QVBoxLayout(analysis_tab)

        # Analysis tools
        tools_group = QGroupBox("Analysis Tools")
        tools_layout = QFormLayout(tools_group)

        # Result directory
        self.result_dir = QLineEdit()
        self.result_dir.setPlaceholderText("Select results directory...")
        result_btn = QPushButton("Browse")
        result_btn.clicked.connect(self.browse_result_dir)

        result_layout = QHBoxLayout()
        result_layout.addWidget(self.result_dir)
        result_layout.addWidget(result_btn)
        tools_layout.addRow("Results Directory:", result_layout)

        layout.addWidget(tools_group)

        # Analysis actions
        actions_group = QGroupBox("Analysis Actions")
        actions_layout = QVBoxLayout(actions_group)

        analysis_buttons = QHBoxLayout()
        quality_btn = QPushButton("Quality Assessment")
        metrics_btn = QPushButton("Generate Metrics")
        export_btn = QPushButton("Export Results")

        analysis_buttons.addWidget(quality_btn)
        analysis_buttons.addWidget(metrics_btn)
        analysis_buttons.addWidget(export_btn)
        analysis_buttons.addStretch()

        actions_layout.addLayout(analysis_buttons)
        layout.addWidget(actions_group)
        layout.addStretch()

        self.tab_widget.addTab(analysis_tab, "Data Analysis")

    def create_embedded_viewer_tab(self):
        """Create embedded CTF and Motion viewer tab"""
        try:
            from .tabs.embedded_viewer_tab import EmbeddedViewerTab

            # Create the embedded viewer tab
            self.embedded_viewer_tab = EmbeddedViewerTab()

            # Connect signals for integration with main GUI
            self.embedded_viewer_tab.analysis_completed.connect(self.on_analysis_completed)
            self.embedded_viewer_tab.data_loaded.connect(self.on_viewer_data_loaded)

            self.tab_widget.addTab(self.embedded_viewer_tab, "🔬 CTF & Motion Analysis")

            self.log_text.append("Embedded CTF & Motion viewers loaded successfully")

        except ImportError as e:
            # Fallback if embedded viewers are not available
            self.log_text.append(f"Warning: Embedded viewers not available: {e}")
            self.create_fallback_viewer_tab()

    def create_fallback_viewer_tab(self):
        """Create fallback viewer tab if embedded viewers fail to load"""
        fallback_tab = QWidget()
        layout = QVBoxLayout(fallback_tab)

        # Fallback message
        fallback_label = QLabel("🔬 CTF & Motion Analysis")
        fallback_label.setStyleSheet("font-size: 16px; font-weight: bold; text-align: center;")
        fallback_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        layout.addWidget(fallback_label)

        # Status message
        status_label = QLabel("Embedded viewers are being loaded...\nPlease check the system logs for details.")
        status_label.setStyleSheet("color: #666; text-align: center; padding: 20px;")
        status_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        layout.addWidget(status_label)

        # Manual load button
        load_btn = QPushButton("Retry Loading Embedded Viewers")
        load_btn.clicked.connect(self.retry_embedded_viewers)
        layout.addWidget(load_btn)

        layout.addStretch()

        self.tab_widget.addTab(fallback_tab, "🔬 CTF & Motion Analysis")

    def retry_embedded_viewers(self):
        """Retry loading embedded viewers"""
        try:
            # Remove the fallback tab
            for i in range(self.tab_widget.count()):
                if self.tab_widget.tabText(i) == "🔬 CTF & Motion Analysis":
                    self.tab_widget.removeTab(i)
                    break

            # Try to create embedded viewer tab again
            self.create_embedded_viewer_tab()

        except Exception as e:
            self.log_text.append(f"Failed to retry embedded viewers: {e}")
            QMessageBox.warning(self, "Load Error", f"Failed to load embedded viewers: {e}")

    def on_analysis_completed(self, results):
        """Handle analysis completion from embedded viewers"""
        self.log_text.append(f"Analysis completed: {results}")
        self.status_bar.showMessage("Analysis completed successfully")

    def on_viewer_data_loaded(self, data_type, data_info):
        """Handle data loading from embedded viewers"""
        self.log_text.append(f"{data_type} data loaded: {data_info}")
        self.status_bar.showMessage(f"{data_type} data loaded")

    def create_viewer_tab(self):
        """Create 3D viewer tab"""
        viewer_tab = QWidget()
        layout = QVBoxLayout(viewer_tab)

        # Viewer placeholder
        viewer_label = QLabel("3D Viewer")
        viewer_label.setStyleSheet("font-size: 16px; font-weight: bold; text-align: center;")
        viewer_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        layout.addWidget(viewer_label)

        # Viewer controls
        viewer_controls = QHBoxLayout()
        load_volume_btn = QPushButton("Load Volume")
        load_volume_btn.clicked.connect(self.load_volume)
        reset_view_btn = QPushButton("Reset View")
        screenshot_btn = QPushButton("Screenshot")

        viewer_controls.addWidget(load_volume_btn)
        viewer_controls.addWidget(reset_view_btn)
        viewer_controls.addWidget(screenshot_btn)
        viewer_controls.addStretch()

        layout.addLayout(viewer_controls)

        # Placeholder for 3D viewer widget
        viewer_placeholder = QLabel("3D viewer will be implemented here\n(Napari integration)")
        viewer_placeholder.setStyleSheet("border: 2px dashed #ccc; padding: 50px; text-align: center;")
        viewer_placeholder.setAlignment(Qt.AlignmentFlag.AlignCenter)
        layout.addWidget(viewer_placeholder)

        self.tab_widget.addTab(viewer_tab, "3D Viewer")

    def create_dashboard_tab(self):
        """Create remote dashboard tab"""
        dashboard_tab = QWidget()
        layout = QVBoxLayout(dashboard_tab)

        # Dashboard controls
        dashboard_group = QGroupBox("Remote Dashboard")
        dashboard_layout = QFormLayout(dashboard_group)

        # Server controls
        self.server_port = QSpinBox()
        self.server_port.setRange(8000, 9999)
        self.server_port.setValue(8080)
        dashboard_layout.addRow("Server Port:", self.server_port)

        server_controls = QHBoxLayout()
        start_server_btn = QPushButton("Start Server")
        start_server_btn.clicked.connect(self.start_dashboard_server)
        stop_server_btn = QPushButton("Stop Server")

        server_controls.addWidget(start_server_btn)
        server_controls.addWidget(stop_server_btn)
        server_controls.addStretch()

        dashboard_layout.addRow("Server Control:", server_controls)
        layout.addWidget(dashboard_group)

        # Dashboard status
        self.dashboard_status = QTextEdit()
        self.dashboard_status.setReadOnly(True)
        self.dashboard_status.setMaximumHeight(200)
        self.dashboard_status.append("Dashboard server stopped")
        layout.addWidget(self.dashboard_status)

        layout.addStretch()

        self.tab_widget.addTab(dashboard_tab, "Remote Dashboard")

    def create_log_tab(self):
        """Create log tab"""
        log_tab = QWidget()
        layout = QVBoxLayout(log_tab)
        
        self.log_text = QTextEdit()
        self.log_text.setReadOnly(True)
        self.log_text.append("AreTomo3 GUI - Clean Version Loaded")
        self.log_text.append("Ready for processing...")
        layout.addWidget(self.log_text)
        
        # Log controls
        log_controls = QHBoxLayout()
        clear_btn = QPushButton("Clear Log")
        clear_btn.clicked.connect(self.log_text.clear)
        log_controls.addWidget(clear_btn)
        log_controls.addStretch()
        
        layout.addLayout(log_controls)
        
        self.tab_widget.addTab(log_tab, "System Logs")
        
    def browse_input_dir(self):
        """Browse for input directory"""
        dir_path = QFileDialog.getExistingDirectory(self, "Select Input Directory")
        if dir_path:
            self.input_dir.setText(dir_path)
            # Auto-generate output directory
            output_path = os.path.join(dir_path, "aretomo_output")
            self.output_dir.setText(output_path)
            self.log_text.append(f"Input directory set: {dir_path}")
            
    def browse_aretomo_path(self):
        """Browse for AreTomo3 executable"""
        file_path, _ = QFileDialog.getOpenFileName(
            self, "Select AreTomo3 Executable", "", "Executable Files (*)"
        )
        if file_path:
            self.aretomo_path.setText(file_path)
            self.log_text.append(f"AreTomo3 path set: {file_path}")
            
    def load_tilt_series(self):
        """Load tilt series from input directory"""
        input_dir = self.input_dir.text()
        if not input_dir:
            QMessageBox.warning(self, "No Input", "Please select an input directory first.")
            return
            
        if not os.path.exists(input_dir):
            QMessageBox.warning(self, "Invalid Path", "Input directory does not exist.")
            return
            
        self.log_text.append(f"Loading tilt series from: {input_dir}")
        # TODO: Implement actual tilt series loading
        self.log_text.append("Tilt series loading functionality will be implemented...")
        self.status_bar.showMessage("Tilt series loaded (placeholder)")
        
    def start_processing(self):
        """Start AreTomo3 processing"""
        if not self.input_dir.text():
            QMessageBox.warning(self, "No Input", "Please select an input directory first.")
            return
            
        if not self.aretomo_path.text():
            QMessageBox.warning(self, "No AreTomo3", "Please set the AreTomo3 executable path.")
            return
            
        self.log_text.append("Starting AreTomo3 processing...")
        self.log_text.append("Processing functionality will be implemented...")
        self.process_btn.setEnabled(False)
        self.stop_btn.setEnabled(True)
        self.status_bar.showMessage("Processing (placeholder)")
        
    def stop_processing(self):
        """Stop processing"""
        self.log_text.append("Stopping processing...")
        self.process_btn.setEnabled(True)
        self.stop_btn.setEnabled(False)
        self.status_bar.showMessage("Processing stopped")
        
    def open_file(self):
        """Open file dialog"""
        QMessageBox.information(self, "Open File", "Open file functionality will be implemented")
        
    def save_file(self):
        """Save file dialog"""
        QMessageBox.information(self, "Save File", "Save file functionality will be implemented")
        
    def show_about(self):
        """Show about dialog"""
        QMessageBox.about(
            self, 
            "About AreTomo3 GUI",
            "AreTomo3 GUI - Clean Working Version\n\n"
            "A graphical user interface for AreTomo3\n"
            "Built with PyQt6"
        )
        
    # Additional method implementations for new tabs
    def browse_watch_dir(self):
        """Browse for watch directory"""
        dir_path = QFileDialog.getExistingDirectory(self, "Select Watch Directory")
        if dir_path:
            self.watch_dir.setText(dir_path)
            self.live_status.append(f"Watch directory set: {dir_path}")

    def add_batch_files(self):
        """Add files to batch processing queue"""
        files, _ = QFileDialog.getOpenFileNames(
            self, "Select Files for Batch Processing", "", "All Files (*)"
        )
        if files:
            for file in files:
                self.batch_queue.append(f"Added: {file}")

    def start_batch_processing(self):
        """Start batch processing"""
        self.batch_queue.append("Starting batch processing...")
        QMessageBox.information(self, "Batch Processing", "Batch processing functionality will be implemented")

    def browse_result_dir(self):
        """Browse for results directory"""
        dir_path = QFileDialog.getExistingDirectory(self, "Select Results Directory")
        if dir_path:
            self.result_dir.setText(dir_path)

    def load_volume(self):
        """Load volume for 3D viewing"""
        file_path, _ = QFileDialog.getOpenFileName(
            self, "Select Volume File", "", "MRC Files (*.mrc);;All Files (*)"
        )
        if file_path:
            QMessageBox.information(self, "3D Viewer", f"Loading volume: {file_path}\n3D viewer functionality will be implemented")

    def start_dashboard_server(self):
        """Start the remote dashboard server"""
        port = self.server_port.value()
        self.dashboard_status.append(f"Starting dashboard server on port {port}...")
        self.dashboard_status.append("Dashboard server functionality will be implemented")
        QMessageBox.information(self, "Dashboard", f"Dashboard server would start on port {port}")

    def closeEvent(self, event):
        """Handle application close event"""
        logger.info("Application closing...")
        event.accept()


# For backward compatibility
AreTomo3MainWindow = AreTomoGUI

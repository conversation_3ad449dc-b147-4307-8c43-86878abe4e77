#!/usr/bin/env python3
"""
Advanced File Browser for AT3GUI
Provides thumbnail previews, metadata display, and drag-and-drop support for MRC files.
"""

import logging
import os
import sys
from pathlib import Path

import matplotlib.pyplot as plt
import mrcfile
import numpy as np
from matplotlib.backends.backend_qtagg import FigureCanvasQTAgg as FigureCanvas
from matplotlib.figure import Figure
from PyQt6.QtCore import (
    QDir,
    QFileSystemWatcher,
    QMimeData,
    QSize,
    Qt,
    QThread,
    QTimer,
    pyqtSignal,
)
from PyQt6.QtGui import QBrush, QColor, QDrag, QFont, QIcon, QPainter, QPalette, QPixmap
from PyQt6.QtWidgets import (
    QApplication,
    QComboBox,
    QFrame,
    QGridLayout,
    QGroupBox,
    QHBoxLayout,
    QLabel,
    QLineEdit,
    QListWidget,
    QListWidgetItem,
    QProgressBar,
    QPush<PERSON>utton,
    QScrollArea,
    QSizePolicy,
    QSplitter,
    QTextEdit,
    QTreeWidget,
    QTreeWidgetItem,
    QVBoxLayout,
    QWidget,
)

logger = logging.getLogger(__name__)


class ThumbnailGenerator(QThread):
    """Background thread for generating MRC file thumbnails."""

    thumbnail_ready = pyqtSignal(str, QPixmap)

    def __init__(self):
        super().__init__()
        self.file_queue = []
        self.running = False

    def add_file(self, filepath):
        """Add file to thumbnail generation queue."""
        if filepath not in self.file_queue:
            self.file_queue.append(filepath)

    def run(self):
        self.running = True
        while self.running and self.file_queue:
            try:
                filepath = self.file_queue.pop(0)
                thumbnail = self.generate_thumbnail(filepath)
                if thumbnail:
                    self.thumbnail_ready.emit(filepath, thumbnail)
            except Exception as e:
                logger.error(f"Error generating thumbnail for {filepath}: {e}")

    def generate_thumbnail(self, filepath, size=(128, 128)):
        """Generate thumbnail for MRC file."""
        try:
            with mrcfile.open(filepath, permissive=True) as mrc:
                data = mrc.data

                # Get middle slice for 3D data
                if data.ndim == 3:
                    slice_data = data[data.shape[0] // 2]
                elif data.ndim == 2:
                    slice_data = data
                else:
                    return None

                # Normalize data for display
                slice_data = slice_data.astype(np.float32)
                vmin, vmax = np.percentile(slice_data, [1, 99])
                slice_data = np.clip((slice_data - vmin) / (vmax - vmin), 0, 1)

                # Create matplotlib figure
                fig = Figure(figsize=(2, 2), facecolor="white")
                ax = fig.add_subplot(111)
                ax.imshow(slice_data, cmap="gray", aspect="equal")
                ax.axis("off")
                fig.tight_layout(pad=0)

                # Convert to QPixmap
                canvas = FigureCanvas(fig)
                canvas.draw()
                width, height = fig.get_size_inches() * fig.get_dpi()
                img = QPixmap(canvas.grab())

                # Scale to desired size
                thumbnail = img.scaled(
                    size[0],
                    size[1],
                    Qt.AspectRatioMode.KeepAspectRatio,
                    Qt.TransformationMode.SmoothTransformation,
                )

                plt.close(fig)
                return thumbnail

        except Exception as e:
            logger.error(f"Failed to generate thumbnail for {filepath}: {e}")
            return None

    def stop(self):
        self.running = False
        self.wait()


class FileInfoWidget(QWidget):
    """Widget for displaying detailed file information."""

    def __init__(self):
        super().__init__()
        self.setup_ui()

    def setup_ui(self):
        layout = QVBoxLayout(self)
        layout.setContentsMargins(10, 10, 10, 10)

        # File info group
        info_group = QGroupBox("File Information")
        info_layout = QVBoxLayout(info_group)

        self.info_text = QTextEdit()
        self.info_text.setReadOnly(True)
        self.info_text.setMaximumHeight(150)
        self.info_text.setStyleSheet(
            """
            QTextEdit {
                background-color: #f8f9fa;
                border: 1px solid #dee2e6;
                border-radius: 4px;
                font-family: monospace;
                font-size: 11px;
                padding: 8px;
            }
        """
        )
        info_layout.addWidget(self.info_text)
        layout.addWidget(info_group)

        # Metadata group
        metadata_group = QGroupBox("File Metadata")
        metadata_layout = QVBoxLayout(metadata_group)

        self.metadata_text = QTextEdit()
        self.metadata_text.setReadOnly(True)
        self.metadata_text.setStyleSheet(
            """
            QTextEdit {
                background-color: #f8f9fa;
                border: 1px solid #dee2e6;
                border-radius: 4px;
                font-family: monospace;
                font-size: 11px;
                padding: 8px;
            }
        """
        )
        metadata_layout.addWidget(self.metadata_text)
        layout.addWidget(metadata_group)

        # Preview group
        preview_group = QGroupBox("Quick Preview")
        preview_layout = QVBoxLayout(preview_group)

        self.preview_label = QLabel()
        self.preview_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.preview_label.setMinimumSize(200, 200)
        self.preview_label.setStyleSheet(
            """
            QLabel {
                border: 2px dashed #ccc;
                border-radius: 8px;
                background-color: #f8f9fa;
            }
        """
        )
        self.preview_label.setText("Select a file to preview")
        preview_layout.addWidget(self.preview_label)
        layout.addWidget(preview_group)

        layout.addStretch()

    def update_file_info(self, filepath):
        """Update file information display."""
        if not filepath or not os.path.exists(filepath):
            self.clear_info()
            return

        try:
            # Basic file info
            stat = os.stat(filepath)
            size_mb = stat.st_size / (1024 * 1024)
            modified = os.path.getmtime(filepath)

            info_text = f"📁 File: {os.path.basename(filepath)}\n"
            info_text += f"📂 Directory: {os.path.dirname(filepath)}\n"
            info_text += f"💾 Size: {size_mb:.1f} MB\n"
            info_text += f"📅 Modified: {self.format_timestamp(modified)}\n"

            self.info_text.setText(info_text)

            # Load metadata based on file type
            self.load_file_metadata(filepath)

        except Exception as e:
            logger.error(f"Error loading file info for {filepath}: {e}")
            self.info_text.setText(f"Error loading file info: {e}")

    def load_file_metadata(self, filepath):
        """Load and display file metadata based on file type."""
        try:
            ext = os.path.splitext(filepath)[1].lower()

            if ext in [".mrc", ".mrcs", ".st"]:
                self.load_mrc_metadata(filepath)
            elif ext == ".eer":
                self.load_eer_metadata(filepath)
            elif ext == ".mdoc":
                self.load_mdoc_metadata(filepath)
            elif ext in [".tif", ".tiff"]:
                self.load_tiff_metadata(filepath)
            else:
                self.metadata_text.setText(
                    f"File type {ext} not supported for metadata display"
                )

        except Exception as e:
            logger.error(f"Error loading metadata for {filepath}: {e}")
            self.metadata_text.setText(f"Error loading metadata: {e}")

    def load_mrc_metadata(self, filepath):
        """Load and display MRC file metadata."""
        try:
            with mrcfile.open(filepath, permissive=True) as mrc:
                data = mrc.data
                header = mrc.header

                metadata_text = f"📐 Dimensions: {data.shape}\n"
                metadata_text += f"🔢 Data Type: {data.dtype}\n"
                metadata_text += f"📊 Pixel Size: {header.cella.x / header.nx:.3f} Å\n"
                metadata_text += f"⚡ Voltage: {header.extra1} kV\n"
                metadata_text += f"🔍 Magnification: {header.extra2}\n"
                metadata_text += (
                    f"📈 Min/Max: {np.min(data):.2f} / {np.max(data):.2f}\n"
                )
                metadata_text += (
                    f"📊 Mean/Std: {np.mean(data):.2f} / {np.std(data):.2f}\n"
                )

                # Additional header info
                metadata_text += f"\n🔧 Header Information:\n"
                metadata_text += f"Mode: {header.mode}\n"
                metadata_text += f"Space Group: {header.ispg}\n"
                metadata_text += f"Origin: ({header.origin.x:.1f}, {header.origin.y:.1f}, {header.origin.z:.1f})\n"

                self.metadata_text.setText(metadata_text)

        except Exception as e:
            logger.error(f"Error loading MRC metadata for {filepath}: {e}")
            self.metadata_text.setText(f"Error loading MRC metadata: {e}")

    def load_eer_metadata(self, filepath):
        """Load and display EER file metadata."""
        try:
            # EER support has been removed - show basic info only
            file_size = os.path.getsize(filepath) / (1024 * 1024)

            metadata_text = f"📁 EER File Information:\n"
            metadata_text += f"💾 File Size: {file_size:.1f} MB\n"
            metadata_text += f"🗜️ Format: EER (Electron Event Representation)\n"
            metadata_text += f"📊 Compression: Hardware-compressed\n"
            metadata_text += f"⚠️  EER Support: Removed\n\n"
            metadata_text += f"ℹ️  Note: Direct EER support has been removed.\n"
            metadata_text += f"   Please convert EER files to MRC format\n"
            self.metadata_text.setText(metadata_text)

        except Exception as e:
            logger.error(f"Error loading EER metadata for {filepath}: {e}")
            self.metadata_text.setText(f"Error loading EER metadata: {e}")

    def load_mdoc_metadata(self, filepath):
        """Load and display MDOC file metadata."""
        try:
            metadata_text = f"📄 MDOC File Information:\n\n"

            with open(filepath) as f:
                content = f.read()

            # Parse key parameters
            import re

            # Look for global parameters
            voltage_match = re.search(r"Voltage\s*=\s*([\d.]+)", content)
            if voltage_match:
                metadata_text += f"⚡ Voltage: {voltage_match.group(1)} kV\n"

            pixel_match = re.search(r"PixelSpacing\s*=\s*([\d.]+)", content)
            if pixel_match:
                metadata_text += f"📐 Pixel Size: {pixel_match.group(1)} Å\n"

            cs_match = re.search(r"Cs\s*=\s*([\d.]+)", content)
            if cs_match:
                metadata_text += f"🔍 Cs: {cs_match.group(1)} mm\n"

            amp_match = re.search(r"AmplitudeContrast\s*=\s*([\d.]+)", content)
            if amp_match:
                metadata_text += f"📊 Amplitude Contrast: {amp_match.group(1)}\n"

            # Count tilt images
            tilt_sections = re.findall(r"\[ZValue = \d+\]", content)
            metadata_text += f"🖼️ Number of tilt images: {len(tilt_sections)}\n"

            # Get tilt angle range
            angles = []
            for match in re.finditer(r"TiltAngle\s*=\s*([-\d.]+)", content):
                angles.append(float(match.group(1)))

            if angles:
                metadata_text += (
                    f"🔄 Tilt range: {min(angles):.1f}° to {max(angles):.1f}°\n"
                )
                metadata_text += f"📈 Tilt step: {(max(angles) - min(angles)) / (len(angles) - 1):.1f}°\n"

            metadata_text += f"\n📝 File contains {len(content.splitlines())} lines"

            self.metadata_text.setText(metadata_text)

        except Exception as e:
            logger.error(f"Error loading MDOC metadata for {filepath}: {e}")
            self.metadata_text.setText(f"Error loading MDOC metadata: {e}")

    def load_tiff_metadata(self, filepath):
        """Load and display TIFF file metadata."""
        try:
            metadata_text = f"🖼️ TIFF File Information:\n\n"

            # Try to read with PIL/Pillow if available
            try:
                from PIL import Image

                with Image.open(filepath) as img:
                    metadata_text += f"📐 Dimensions: {img.size[0]} × {img.size[1]}\n"
                    metadata_text += f"🎨 Mode: {img.mode}\n"
                    metadata_text += f"📊 Format: {img.format}\n"

                    # Get EXIF data if available
                    if hasattr(img, "_getexif") and img._getexif():
                        metadata_text += f"📷 Has EXIF data\n"

            except ImportError:
                metadata_text += (
                    "📦 PIL/Pillow not available for detailed TIFF analysis\n"
                )

            file_size = os.path.getsize(filepath) / (1024 * 1024)
            metadata_text += f"💾 File Size: {file_size:.1f} MB\n"

            self.metadata_text.setText(metadata_text)

        except Exception as e:
            logger.error(f"Error loading TIFF metadata for {filepath}: {e}")
            self.metadata_text.setText(f"Error loading TIFF metadata: {e}")

    def update_preview(self, pixmap):
        """Update preview image."""
        if pixmap:
            # Scale pixmap to fit label while maintaining aspect ratio
            scaled_pixmap = pixmap.scaled(
                self.preview_label.size(),
                Qt.AspectRatioMode.KeepAspectRatio,
                Qt.TransformationMode.SmoothTransformation,
            )
            self.preview_label.setPixmap(scaled_pixmap)
        else:
            self.preview_label.setText("Preview not available")

    def clear_info(self):
        """Clear all information displays."""
        self.info_text.clear()
        self.metadata_text.clear()
        self.preview_label.clear()
        self.preview_label.setText("Select a file to preview")

    def format_timestamp(self, timestamp):
        """Format timestamp for display."""
        from datetime import datetime

        dt = datetime.fromtimestamp(timestamp)
        return dt.strftime("%Y-%m-%d %H:%M:%S")


class FileListWidget(QListWidget):
    """Custom list widget with drag-and-drop support."""

    file_selected = pyqtSignal(str)
    files_dropped = pyqtSignal(list)

    def __init__(self):
        super().__init__()
        self.setAcceptDrops(True)
        self.setDragDropMode(QListWidget.DragDropMode.DragDrop)
        self.setDefaultDropAction(Qt.DropAction.CopyAction)
        self.setSelectionMode(QListWidget.SelectionMode.ExtendedSelection)

        # Connect selection change
        self.itemSelectionChanged.connect(self.on_selection_changed)

    def dragEnterEvent(self, event):
        if event.mimeData().hasUrls():
            event.acceptProposedAction()
        else:
            super().dragEnterEvent(event)

    def dropEvent(self, event):
        if event.mimeData().hasUrls():
            files = []
            for url in event.mimeData().urls():
                if url.isLocalFile():
                    files.append(url.toLocalFile())
            if files:
                self.files_dropped.emit(files)
            event.acceptProposedAction()
        else:
            super().dropEvent(event)

    def on_selection_changed(self):
        """Handle selection change."""
        current_item = self.currentItem()
        if current_item:
            filepath = current_item.data(Qt.ItemDataRole.UserRole)
            if filepath:
                self.file_selected.emit(filepath)


class AdvancedFileBrowser(QWidget):
    """Advanced file browser with thumbnails and metadata."""

    file_selected = pyqtSignal(str)
    files_selected = pyqtSignal(list)
    directory_changed = pyqtSignal(str)

    def __init__(self, parent=None):
        super().__init__(parent)
        self.current_directory = os.getcwd()
        self.thumbnail_generator = None
        self.file_watcher = QFileSystemWatcher()
        self.thumbnails_cache = {}

        self.setup_ui()
        self.start_thumbnail_generator()

        # Watch for file system changes
        self.file_watcher.directoryChanged.connect(self.refresh_file_list)

    def setup_ui(self):
        layout = QVBoxLayout(self)
        layout.setContentsMargins(5, 5, 5, 5)

        # Directory navigation
        nav_layout = QHBoxLayout()

        self.directory_combo = QComboBox()
        self.directory_combo.setEditable(True)
        self.directory_combo.currentTextChanged.connect(self.change_directory)
        nav_layout.addWidget(QLabel("Directory:"))
        nav_layout.addWidget(self.directory_combo)

        self.refresh_btn = QPushButton("🔄 Refresh")
        self.refresh_btn.clicked.connect(self.refresh_file_list)
        nav_layout.addWidget(self.refresh_btn)

        layout.addLayout(nav_layout)

        # File filter
        filter_layout = QHBoxLayout()
        self.filter_edit = QLineEdit()
        self.filter_edit.setPlaceholderText("Filter files (*.mrc, *.mrcs, *.st)")
        self.filter_edit.textChanged.connect(self.apply_filter)
        filter_layout.addWidget(QLabel("Filter:"))
        filter_layout.addWidget(self.filter_edit)
        layout.addLayout(filter_layout)

        # Main content area
        main_splitter = QSplitter(Qt.Orientation.Horizontal)

        # File list
        self.file_list = FileListWidget()
        self.file_list.file_selected.connect(self.on_file_selected)
        self.file_list.files_dropped.connect(self.on_files_dropped)
        main_splitter.addWidget(self.file_list)

        # File info panel
        self.file_info = FileInfoWidget()
        main_splitter.addWidget(self.file_info)

        # Set splitter proportions (70% list, 30% info)
        main_splitter.setStretchFactor(0, 7)
        main_splitter.setStretchFactor(1, 3)

        layout.addWidget(main_splitter)

        # Initialize with current directory
        self.change_directory(self.current_directory)

    def start_thumbnail_generator(self):
        """Start thumbnail generation thread."""
        if self.thumbnail_generator is None:
            self.thumbnail_generator = ThumbnailGenerator()
            self.thumbnail_generator.thumbnail_ready.connect(self.on_thumbnail_ready)
            self.thumbnail_generator.start()

    def stop_thumbnail_generator(self):
        """Stop thumbnail generation thread."""
        if self.thumbnail_generator:
            self.thumbnail_generator.stop()
            self.thumbnail_generator = None

    def change_directory(self, directory):
        """Change current directory and refresh file list."""
        if os.path.isdir(directory):
            old_directory = self.current_directory
            self.current_directory = directory
            self.directory_combo.setCurrentText(directory)

            # Update file watcher
            watched_dirs = self.file_watcher.directories()
            if watched_dirs:
                self.file_watcher.removePaths(watched_dirs)
            self.file_watcher.addPath(directory)

            # Log directory change
            if old_directory != directory:
                logger.info(f"File browser directory changed: {directory}")

            self.refresh_file_list()

            # Emit signal if directory actually changed
            if old_directory != directory:
                self.directory_changed.emit(directory)

    def refresh_file_list(self):
        """Refresh the file list."""
        self.file_list.clear()
        self.thumbnails_cache.clear()

        try:
            # Get files in directory
            files = []
            for item in os.listdir(self.current_directory):
                filepath = os.path.join(self.current_directory, item)
                if os.path.isfile(filepath):
                    files.append(filepath)

            # Sort files
            files.sort(key=lambda x: os.path.basename(x).lower())

            # Add files to list
            for filepath in files:
                self.add_file_to_list(filepath)

        except Exception as e:
            logger.error(f"Error refreshing file list: {e}")

    def add_file_to_list(self, filepath):
        """Add file to the list widget."""
        filename = os.path.basename(filepath)

        # Create list item
        item = QListWidgetItem(filename)
        item.setData(Qt.ItemDataRole.UserRole, filepath)

        # Set icon based on file type
        if filepath.lower().endswith((".mrc", ".mrcs", ".st")):
            item.setIcon(
                self.style().standardIcon(self.style().StandardPixmap.SP_FileIcon)
            )
            # Request thumbnail generation
            if self.thumbnail_generator:
                self.thumbnail_generator.add_file(filepath)
        else:
            item.setIcon(
                self.style().standardIcon(self.style().StandardPixmap.SP_FileIcon)
            )

        self.file_list.addItem(item)

    def on_thumbnail_ready(self, filepath, thumbnail):
        """Handle thumbnail ready signal."""
        self.thumbnails_cache[filepath] = thumbnail

        # Update list item icon
        for i in range(self.file_list.count()):
            item = self.file_list.item(i)
            if item.data(Qt.ItemDataRole.UserRole) == filepath:
                icon = QIcon(thumbnail)
                item.setIcon(icon)
                break

    def on_file_selected(self, filepath):
        """Handle file selection."""
        self.file_info.update_file_info(filepath)

        # Update preview
        if filepath in self.thumbnails_cache:
            self.file_info.update_preview(self.thumbnails_cache[filepath])
        else:
            self.file_info.update_preview(None)

        self.file_selected.emit(filepath)

    def on_files_dropped(self, files):
        """Handle dropped files."""
        # Filter for supported files
        supported_files = [
            f for f in files if f.lower().endswith((".mrc", ".mrcs", ".st"))
        ]
        if supported_files:
            self.files_selected.emit(supported_files)

    def apply_filter(self, filter_text):
        """Apply filter to file list."""
        for i in range(self.file_list.count()):
            item = self.file_list.item(i)
            if filter_text.lower() in item.text().lower():
                item.setHidden(False)
            else:
                item.setHidden(True)

    def get_selected_files(self):
        """Get list of selected files."""
        selected_files = []
        for item in self.file_list.selectedItems():
            filepath = item.data(Qt.ItemDataRole.UserRole)
            if filepath:
                selected_files.append(filepath)
        return selected_files

    def get_current_directory(self):
        """Get the current directory path."""
        return self.current_directory

    def closeEvent(self, event):
        """Clean up when widget is closed."""
        self.stop_thumbnail_generator()
        super().closeEvent(event)

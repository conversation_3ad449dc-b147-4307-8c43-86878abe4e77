"""Resource monitoring widget for batch processing."""

from typing import Dict, List

import numpy as np
import psutil
from matplotlib.backends.backend_qtagg import FigureCanvasQTAgg
from matplotlib.figure import Figure
from PyQt6.QtCore import Qt, QTimer
from PyQt6.QtWidgets import (
    QGroupBox,
    QHBoxLayout,
    QLabel,
    QProgressBar,
    QVBoxLayout,
    QWidget,
)

# Try to import GPUtil, handle gracefully if not available
try:
    import GPUtil

    GPU_AVAILABLE = True
except ImportError:
    GPU_AVAILABLE = False
    GPUtil = None


class ResourceMonitor(QWidget):
    """Widget for monitoring system resources during processing."""

    def __init__(self, parent=None):
        """Initialize the instance."""
        super().__init__(parent)
        self.history_length = 60  # Keep 60 seconds of history
        self.history = {
            "cpu": np.zeros(self.history_length),
            "memory": np.zeros(self.history_length),
            "gpu_memory": np.zeros(self.history_length),
            "gpu_util": np.zeros(self.history_length),
        }
        self.init_ui()

        # Update timer
        self.timer = QTimer()
        self.timer.timeout.connect(self.update_stats)
        self.timer.start(1000)  # Update every second

    # TODO: Refactor function - Function 'init_ui' too long (57 lines)
    def init_ui(self):
        """Initialize the UI components."""
        layout = QVBoxLayout()

        # Current usage indicators
        indicators = QHBoxLayout()

        # CPU usage
        cpu_group = QGroupBox("CPU Usage")
        cpu_layout = QVBoxLayout()
        self.cpu_bar = QProgressBar()
        self.cpu_bar.setRange(0, 100)
        cpu_layout.addWidget(self.cpu_bar)
        cpu_group.setLayout(cpu_layout)
        indicators.addWidget(cpu_group)

        # Memory usage
        mem_group = QGroupBox("Memory Usage")
        mem_layout = QVBoxLayout()
        self.mem_bar = QProgressBar()
        self.mem_bar.setRange(0, 100)
        self.mem_label = QLabel()
        mem_layout.addWidget(self.mem_bar)
        mem_layout.addWidget(self.mem_label)
        mem_group.setLayout(mem_layout)
        indicators.addWidget(mem_group)

        # GPU usage
        gpu_group = QGroupBox("GPU Usage")
        gpu_layout = QVBoxLayout()
        self.gpu_util_bar = QProgressBar()
        self.gpu_util_bar.setRange(0, 100)
        self.gpu_mem_bar = QProgressBar()
        self.gpu_mem_bar.setRange(0, 100)
        self.gpu_label = QLabel()
        gpu_layout.addWidget(QLabel("Utilization:"))
        gpu_layout.addWidget(self.gpu_util_bar)
        gpu_layout.addWidget(QLabel("Memory:"))
        gpu_layout.addWidget(self.gpu_mem_bar)
        gpu_layout.addWidget(self.gpu_label)
        gpu_group.setLayout(gpu_layout)
        indicators.addWidget(gpu_group)

        layout.addLayout(indicators)

        # Historical graphs
        graph_group = QGroupBox("Resource History")
        graph_layout = QVBoxLayout()

        self.figure = Figure(figsize=(8, 3))
        self.canvas = FigureCanvasQTAgg(self.figure)
        graph_layout.addWidget(self.canvas)

        graph_group.setLayout(graph_layout)
        layout.addWidget(graph_group)

        self.setLayout(layout)

    # TODO: Refactor function - Function 'update_stats' too long (53 lines)
    def update_stats(self):
        """Update resource statistics."""
        # Get CPU stats
        cpu_percent = psutil.cpu_percent()
        self.cpu_bar.setValue(int(cpu_percent))

        # Get memory stats
        memory = psutil.virtual_memory()
        mem_percent = memory.percent
        self.mem_bar.setValue(int(mem_percent))
        self.mem_label.setText(
            f"Used: {memory.used/1024**3:.1f} GB / "
            f"Total: {memory.total/1024**3:.1f} GB"
        )

        # Get GPU stats
        if GPU_AVAILABLE and GPUtil:
            try:
                gpus = GPUtil.getGPUs()
                if gpus:
                    gpu = gpus[0]  # Use first GPU
                    self.gpu_util_bar.setValue(int(gpu.load * 100))
                    self.gpu_mem_bar.setValue(int(gpu.memoryUtil * 100))
                    self.gpu_label.setText(
                        f"Memory: {gpu.memoryUsed} MB / {gpu.memoryTotal} MB\n"
                        f"Temperature: {gpu.temperature}°C"
                    )
                    gpu_mem_percent = gpu.memoryUtil * 100
                    gpu_util_percent = gpu.load * 100
                else:
                    gpu_mem_percent = 0
                    gpu_util_percent = 0
                    self.gpu_label.setText("No GPU detected")
            except Exception:
                gpu_mem_percent = 0
                gpu_util_percent = 0
                self.gpu_label.setText("GPU monitoring unavailable")
        else:
            gpu_mem_percent = 0
            gpu_util_percent = 0
            self.gpu_label.setText("GPU monitoring disabled")

        # Update history
        for metric, value in [
            ("cpu", cpu_percent),
            ("memory", mem_percent),
            ("gpu_memory", gpu_mem_percent),
            ("gpu_util", gpu_util_percent),
        ]:
            self.history[metric] = np.roll(self.history[metric], -1)
            self.history[metric][-1] = value

        self.update_plot()

    def update_plot(self):
        """Update the resource history plot."""
        self.figure.clear()

        # Create two subplots
        ax1 = self.figure.add_subplot(211)  # CPU and Memory
        ax2 = self.figure.add_subplot(212)  # GPU

        # Plot CPU and Memory
        x = np.arange(self.history_length)
        ax1.plot(x, self.history["cpu"], "b-", label="CPU", alpha=0.7)
        ax1.plot(x, self.history["memory"], "r-", label="Memory", alpha=0.7)
        ax1.set_ylim(0, 100)
        ax1.set_ylabel("Utilization (%)")
        ax1.legend()
        ax1.grid(True)

        # Plot GPU
        ax2.plot(x, self.history["gpu_util"], "g-", label="GPU Util", alpha=0.7)
        ax2.plot(x, self.history["gpu_memory"], "y-", label="GPU Mem", alpha=0.7)
        ax2.set_ylim(0, 100)
        ax2.set_ylabel("Utilization (%)")
        ax2.set_xlabel("Time (s)")
        ax2.legend()
        ax2.grid(True)

        self.figure.tight_layout()
        self.canvas.draw()

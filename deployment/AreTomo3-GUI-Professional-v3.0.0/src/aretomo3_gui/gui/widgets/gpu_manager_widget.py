"""
Multi-GPU Integration for AreTomo3 GUI
Provides GPU detection, load balancing, and parallel processing.
"""

import logging
import os
import threading
import time
from pathlib import Path

from PyQt6.QtCore import QObject, QTimer, pyqtSignal

logger = logging.getLogger(__name__)


class GPUManagerWidget(QObject):
    """GPU Manager widget for the GUI."""

    # Qt signals
    gpu_stats_updated = pyqtSignal(dict)
    processing_stats_updated = pyqtSignal(dict)

    def __init__(self, parent=None):
        super().__init__(parent)
        self.gpu_manager = None
        self.gpu_processor = None
        self.stats_timer = QTimer()
        self.stats_timer.timeout.connect(self.update_stats)

    def initialize_gpu_support(self):
        """Initialize GPU support."""
        try:
            from .multigpu_manager import MultiGPUManager

            self.gpu_manager = MultiGPUManager()
            gpu_detected = len(self.gpu_manager.available_gpus) > 0

            if gpu_detected:
                self.gpu_manager.gpu_config_changed.connect(self._on_gpu_config_changed)
                self._init_gpu_monitoring()
                self.stats_timer.start(5000)  # Update every 5 seconds

                logger.info("✅ Multi-GPU support initialized")
                return True
            else:
                logger.warning("No GPUs detected")
                return False

        except Exception as e:
            logger.error(f"Failed to initialize GPU support: {e}")
            return False

    def update_stats(self):
        """Update GPU and processing statistics."""
        if self.gpu_manager:
            gpu_stats = self.gpu_manager.get_gpu_stats()
            self.gpu_stats_updated.emit(gpu_stats)

        if self.gpu_processor:
            processing_stats = self.gpu_processor.get_processing_stats()
            self.processing_stats_updated.emit(processing_stats)

    def add_processing_job(self, input_file, output_dir, parameters=None):
        """Add a processing job."""
        if self.gpu_processor:
            return self.gpu_processor.add_processing_job(
                input_file, output_dir, parameters
            )
        return None

    def start_processing(self):
        """Start processing queued jobs."""
        if self.gpu_processor:
            processing_thread = threading.Thread(
                target=self.gpu_processor.process_queue, daemon=True
            )
            processing_thread.start()

    def cleanup(self):
        """Cleanup GPU resources."""
        if self.stats_timer.isActive():
            self.stats_timer.stop()

        if self.gpu_manager:
            self.gpu_manager.stop_monitoring()

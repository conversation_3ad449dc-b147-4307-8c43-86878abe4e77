#!/usr/bin/env python3
"""
PDF Report Generator for AreTomo3 Analysis.
Creates comprehensive PDF reports for individual tomograms.
"""

import logging
import os
from datetime import datetime
from pathlib import Path
from typing import Any, Dict, List, Optional

import numpy as np

try:
    from reportlab.lib.colors import HexColor
    from reportlab.lib.enums import TA_CENTER, TA_LEFT, TA_RIGHT
    from reportlab.lib.pagesizes import A4, letter
    from reportlab.lib.styles import ParagraphStyle, getSampleStyleSheet
    from reportlab.lib.units import inch
    from reportlab.platypus import (
        Image,
        KeepTogether,
        PageBreak,
        Paragraph,
        SimpleDocTemplate,
        Spacer,
        Table,
        TableStyle,
    )

    REPORTLAB_AVAILABLE = True
except ImportError:
    REPORTLAB_AVAILABLE = False

try:
    import matplotlib.backends.backend_agg as agg
    import matplotlib.pyplot as plt
    from matplotlib.figure import Figure

    MATPLOTLIB_AVAILABLE = True
except ImportError:
    MATPLOTLIB_AVAILABLE = False

logger = logging.getLogger(__name__)


class TomogramPDFReportGenerator:
    """Generate comprehensive PDF reports for individual tomograms."""

    def __init__(self):
        """Initialize the PDF report generator."""
        self.styles = None
        if REPORTLAB_AVAILABLE:
            self.setup_styles()

    def setup_styles(self):
        """Set up PDF styles."""
        self.styles = getSampleStyleSheet()

        # Custom styles
        self.styles.add(
            ParagraphStyle(
                name="CustomTitle",
                parent=self.styles["Heading1"],
                fontSize=24,
                spaceAfter=30,
                alignment=TA_CENTER,
                textColor=HexColor("#2c3e50"),
            )
        )

        self.styles.add(
            ParagraphStyle(
                name="CustomHeading",
                parent=self.styles["Heading2"],
                fontSize=16,
                spaceAfter=12,
                spaceBefore=20,
                textColor=HexColor("#34495e"),
            )
        )

        self.styles.add(
            ParagraphStyle(
                name="QualityGood",
                parent=self.styles["Normal"],
                textColor=HexColor("#27ae60"),
                fontSize=12,
                fontName="Helvetica-Bold",
            )
        )

        self.styles.add(
            ParagraphStyle(
                name="QualityFair",
                parent=self.styles["Normal"],
                textColor=HexColor("#f39c12"),
                fontSize=12,
                fontName="Helvetica-Bold",
            )
        )

        self.styles.add(
            ParagraphStyle(
                name="QualityPoor",
                parent=self.styles["Normal"],
                textColor=HexColor("#e74c3c"),
                fontSize=12,
                fontName="Helvetica-Bold",
            )
        )

    def generate_report(
        self,
        series_name: str,
        series_data: Dict[str, Any],
        quality_metrics: Dict[str, Any],
        output_dir: str,
    ) -> Optional[str]:
        """Generate a comprehensive PDF report for a tomogram."""

        if not REPORTLAB_AVAILABLE:
            logger.error("ReportLab not available for PDF generation")
            return None

        try:
            # Create output directory if it doesn't exist
            output_path = Path(output_dir)
            output_path.mkdir(parents=True, exist_ok=True)

            # Generate filename
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            pdf_filename = f"{series_name}_analysis_report_{timestamp}.pdf"
            pdf_path = output_path / pdf_filename

            # Create PDF document
            doc = SimpleDocTemplate(
                str(pdf_path),
                pagesize=A4,
                rightMargin=72,
                leftMargin=72,
                topMargin=72,
                bottomMargin=18,
            )

            # Build content
            story = []

            # Title page
            self.add_title_page(story, series_name, quality_metrics)

            # Executive summary
            self.add_executive_summary(story, quality_metrics)

            # Motion analysis section
            if "motion_data" in series_data:
                self.add_motion_analysis(
                    story, series_data["motion_data"], quality_metrics.get("motion", {})
                )

            # CTF analysis section
            if "ctf_data" in series_data:
                self.add_ctf_analysis(
                    story, series_data["ctf_data"], quality_metrics.get("ctf", {})
                )

            # Alignment analysis section
            if "alignment_data" in series_data:
                self.add_alignment_analysis(
                    story,
                    series_data["alignment_data"],
                    quality_metrics.get("alignment", {}),
                )

            # Recommendations
            self.add_recommendations(story, quality_metrics)

            # Technical details
            self.add_technical_details(story, series_data)

            # Build PDF
            doc.build(story)

            logger.info(f"Generated PDF report: {pdf_path}")
            return str(pdf_path)

        except Exception as e:
            logger.error(f"Error generating PDF report for {series_name}: {e}")
            return None

    def add_title_page(self, story: List, series_name: str, quality_metrics: Dict):
        """Add title page to the report."""
        # Title
        title = Paragraph(f"AreTomo3 Analysis Report", self.styles["CustomTitle"])
        story.append(title)
        story.append(Spacer(1, 20))

        # Series name
        series_title = Paragraph(
            f"Tilt Series: <b>{series_name}</b>", self.styles["Heading1"]
        )
        story.append(series_title)
        story.append(Spacer(1, 30))

        # Overall quality badge
        overall_quality = quality_metrics.get("overall_quality", "Unknown")
        quality_style = self.get_quality_style(overall_quality)
        quality_text = Paragraph(f"Overall Quality: {overall_quality}", quality_style)
        story.append(quality_text)
        story.append(Spacer(1, 30))

        # Generation info
        timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        info_text = (
            f'<para align="center">'
            f"Generated on: {timestamp}<br/>"
            f"Generated by: AreTomo3 GUI Analysis System<br/>"
            f"Report Version: 1.0"
            f"</para>"
        )
        story.append(Paragraph(info_text, self.styles["Normal"]))
        story.append(PageBreak())

    def add_executive_summary(self, story: List, quality_metrics: Dict):
        """Add executive summary section."""
        story.append(Paragraph("Executive Summary", self.styles["CustomHeading"]))

        # Quality overview table
        data = [["Category", "Quality", "Key Metrics"]]

        # Motion summary
        motion = quality_metrics.get("motion", {})
        if motion:
            motion_quality = motion.get("quality", "Unknown")
            motion_metrics = f"Mean: {motion.get('mean_motion', 0):.2f}px, Max: {motion.get('max_motion', 0):.2f}px"
            data.append(["Motion Correction", motion_quality, motion_metrics])

        # CTF summary
        ctf = quality_metrics.get("ctf", {})
        if ctf:
            ctf_quality = ctf.get("quality", "Unknown")
            ctf_metrics = f"Resolution: {ctf.get('mean_resolution', 0):.1f}Å, CC: {ctf.get('mean_cc', 0):.3f}"
            data.append(["CTF Estimation", ctf_quality, ctf_metrics])

        # Alignment summary
        alignment = quality_metrics.get("alignment", {})
        if alignment:
            align_quality = alignment.get("quality", "Unknown")
            align_metrics = f"Score: {alignment.get('mean_score', 0):.3f}, Tilts: {alignment.get('num_tilts', 0)}"
            data.append(["Alignment", align_quality, align_metrics])

        # Create table
        table = Table(data, colWidths=[2 * inch, 1.5 * inch, 2.5 * inch])
        table.setStyle(
            TableStyle(
                [
                    ("BACKGROUND", (0, 0), (-1, 0), HexColor("#34495e")),
                    ("TEXTCOLOR", (0, 0), (-1, 0), HexColor("#ffffff")),
                    ("ALIGN", (0, 0), (-1, -1), "CENTER"),
                    ("FONTNAME", (0, 0), (-1, 0), "Helvetica-Bold"),
                    ("FONTSIZE", (0, 0), (-1, 0), 12),
                    ("BOTTOMPADDING", (0, 0), (-1, 0), 12),
                    ("BACKGROUND", (0, 1), (-1, -1), HexColor("#ecf0f1")),
                    ("GRID", (0, 0), (-1, -1), 1, HexColor("#bdc3c7")),
                ]
            )
        )

        story.append(table)
        story.append(Spacer(1, 20))

    def add_motion_analysis(self, story: List, motion_data: Dict, motion_metrics: Dict):
        """Add motion analysis section."""
        story.append(
            Paragraph("Motion Correction Analysis", self.styles["CustomHeading"])
        )

        # Motion statistics
        stats_text = f""" <b>Motion Statistics:</b><br/> • Mean Motion: {motion_metrics.get('mean_motion', 0):.2f} pixels<br/> • Maximum Motion: {motion_metrics.get('max_motion', 0):.2f} pixels<br/> • Drift Rate: {motion_metrics.get('drift_rate', 0):.3f} pixels/frame<br/> • Quality Assessment: {motion_metrics.get('quality', 'Unknown')} """
        story.append(Paragraph(stats_text, self.styles["Normal"]))
        story.append(Spacer(1, 15))

        # Motion quality interpretation
        mean_motion = motion_metrics.get("mean_motion", 0)
        if mean_motion < 1.0:
            interpretation = "Excellent motion correction with minimal drift."
        elif mean_motion < 2.0:
            interpretation = "Good motion correction with acceptable drift levels."
        else:
            interpretation = (
                "Significant motion detected. Consider checking acquisition parameters."
            )

        story.append(
            Paragraph(f"<b>Interpretation:</b> {interpretation}", self.styles["Normal"])
        )
        story.append(Spacer(1, 20))

    def add_ctf_analysis(self, story: List, ctf_data: Dict, ctf_metrics: Dict):
        """Add CTF analysis section."""
        story.append(Paragraph("CTF Estimation Analysis", self.styles["CustomHeading"]))

        # CTF statistics
        stats_text = f""" <b>CTF Statistics:</b><br/> • Mean Defocus: {ctf_metrics.get('mean_defocus', 0):.1f} μm<br/> • Mean Astigmatism: {ctf_metrics.get('mean_astigmatism', 0):.1f} μm<br/> • Mean Resolution: {ctf_metrics.get('mean_resolution', 0):.1f} Å<br/> • Mean Cross-Correlation: {ctf_metrics.get('mean_cc', 0):.3f}<br/> • Quality Assessment: {ctf_metrics.get('quality', 'Unknown')} """
        story.append(Paragraph(stats_text, self.styles["Normal"]))
        story.append(Spacer(1, 15))

        # CTF quality interpretation
        mean_cc = ctf_metrics.get("mean_cc", 0)
        if mean_cc > 0.8:
            interpretation = "Excellent CTF estimation with high confidence."
        elif mean_cc > 0.6:
            interpretation = "Good CTF estimation with reasonable confidence."
        else:
            interpretation = "Poor CTF estimation. Consider checking sample preparation or acquisition."

        story.append(
            Paragraph(f"<b>Interpretation:</b> {interpretation}", self.styles["Normal"])
        )
        story.append(Spacer(1, 20))

    def add_alignment_analysis(
        self, story: List, alignment_data: Dict, alignment_metrics: Dict
    ):
        """Add alignment analysis section."""
        story.append(Paragraph("Alignment Analysis", self.styles["CustomHeading"]))

        # Alignment statistics
        stats_text = f""" <b>Alignment Statistics:</b><br/> • Mean Alignment Score: {alignment_metrics.get('mean_score', 0):.3f}<br/> • Mean Global Shift: {alignment_metrics.get('mean_shift', 0):.2f} pixels<br/> • Tilt Axis Stability: {alignment_metrics.get( 'tilt_axis_stability', 0 ):.2f}°<br/> • Number of Tilts: {alignment_metrics.get('num_tilts', 0)}<br/> • Quality Assessment: {alignment_metrics.get('quality', 'Unknown')} """
        story.append(Paragraph(stats_text, self.styles["Normal"]))
        story.append(Spacer(1, 15))

        # Alignment quality interpretation
        mean_score = alignment_metrics.get("mean_score", 0)
        if mean_score > 0.8:
            interpretation = "Excellent alignment with high precision."
        elif mean_score > 0.5:
            interpretation = "Good alignment with acceptable precision."
        else:
            interpretation = "Poor alignment. Consider checking tilt series quality or alignment parameters."

        story.append(
            Paragraph(f"<b>Interpretation:</b> {interpretation}", self.styles["Normal"])
        )
        story.append(Spacer(1, 20))

    def add_recommendations(self, story: List, quality_metrics: Dict):
        """Add recommendations section."""
        story.append(Paragraph("Recommendations", self.styles["CustomHeading"]))

        recommendations = []

        # Motion recommendations
        motion = quality_metrics.get("motion", {})
        if motion.get("quality") == "Poor":
            recommendations.append(
                "• Consider improving sample stability or reducing exposure time"
            )
            recommendations.append(
                "• Check for mechanical vibrations in the microscope"
            )

        # CTF recommendations
        ctf = quality_metrics.get("ctf", {})
        if ctf.get("quality") == "Poor":
            recommendations.append("• Check sample thickness and ice quality")
            recommendations.append(
                "• Verify defocus range and CTF estimation parameters"
            )

        # Alignment recommendations
        alignment = quality_metrics.get("alignment", {})
        if alignment.get("quality") == "Poor":
            recommendations.append("• Consider using more fiducial markers")
            recommendations.append("• Check tilt series acquisition scheme")

        # Overall recommendations
        overall_quality = quality_metrics.get("overall_quality", "Unknown")
        if overall_quality in ["Good", "Excellent"]:
            recommendations.append(
                "• Data quality is excellent - proceed with downstream analysis"
            )
        elif overall_quality == "Fair":
            recommendations.append("• Data quality is acceptable but could be improved")
        else:
            recommendations.append(
                "• Consider re-acquiring data with optimized parameters"
            )

        if not recommendations:
            recommendations.append(
                "• No specific recommendations - data quality appears satisfactory"
            )

        rec_text = "<br/>".join(recommendations)
        story.append(Paragraph(rec_text, self.styles["Normal"]))
        story.append(Spacer(1, 20))

    def add_technical_details(self, story: List, series_data: Dict):
        """Add technical details section."""
        story.append(Paragraph("Technical Details", self.styles["CustomHeading"]))

        # Processing information
        details_text = f""" <b>Processing Information:</b><br/> • Analysis performed with AreTomo3 GUI<br/> • Report generated on: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}<br/> • Data format: AreTomo3 output files<br/> """

        # Add file information if available
        if "motion_data" in series_data:
            details_text += "• Motion correction data: Available<br/>"
        if "ctf_data" in series_data:
            details_text += "• CTF estimation data: Available<br/>"
        if "alignment_data" in series_data:
            details_text += "• Alignment data: Available<br/>"

        story.append(Paragraph(details_text, self.styles["Normal"]))

    def get_quality_style(self, quality: str):
        """Get appropriate style for quality indicator."""
        if quality in ["Good", "Excellent"]:
            return self.styles["QualityGood"]
        elif quality == "Fair":
            return self.styles["QualityFair"]
        else:
            return self.styles["QualityPoor"]

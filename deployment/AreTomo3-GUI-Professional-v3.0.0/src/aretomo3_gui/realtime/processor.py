"""
Real-Time Processing Pipeline Integration
Monitors file system for new data and processes automatically
"""

import asyncio
import time
from pathlib import Path
from watchdog.observers import Observer
from watchdog.events import FileSystemEventHandler
import threading
import queue
from typing import Dict, List, Callable, Optional
import logging


class ProcessingPipeline:
    """Real-time processing pipeline for tomographic data."""
    
    def __init__(self):
        self.processing_queue = queue.Queue()
        self.active_jobs = {}
        self.observers = []
        self.processors = {}
        self.status_callbacks = []
        
    def register_processor(self, file_pattern: str, processor: Callable):
        """Register a processor for specific file patterns."""
        self.processors[file_pattern] = processor
        
    def add_status_callback(self, callback: Callable):
        """Add callback for status updates."""
        self.status_callbacks.append(callback)
        
    def notify_status(self, message: str, progress: float = 0.0):
        """Notify all status callbacks."""
        for callback in self.status_callbacks:
            try:
                callback(message, progress)
            except Exception as e:
                logging.error(f"Status callback error: {e}")


class TiltSeriesHandler(FileSystemEventHandler):
    """Handle new tilt series files."""
    
    def __init__(self, pipeline: ProcessingPipeline):
        self.pipeline = pipeline
        
    def on_created(self, event):
        """Handle new file creation."""
        if not event.is_directory:
            file_path = Path(event.src_path)
            
            # Check for tilt series files
            if file_path.suffix.lower() in ['.mrc', '.st', '.ali']:
                self.pipeline.processing_queue.put({
                    'type': 'tilt_series',
                    'path': file_path,
                    'timestamp': time.time()
                })
                self.pipeline.notify_status(f"New tilt series detected: {file_path.name}")


class RealTimeProcessor:
    """Main real-time processing coordinator."""
    
    def __init__(self):
        self.pipeline = ProcessingPipeline()
        self.running = False
        self.worker_threads = []
        
    def start_monitoring(self, watch_directories: List[Path]):
        """Start monitoring directories for new files."""
        self.running = True
        
        # Start file system monitoring
        for directory in watch_directories:
            observer = Observer()
            handler = TiltSeriesHandler(self.pipeline)
            observer.schedule(handler, str(directory), recursive=True)
            observer.start()
            self.pipeline.observers.append(observer)
            
        # Start processing workers
        for i in range(4):  # 4 worker threads
            worker = threading.Thread(target=self._processing_worker, daemon=True)
            worker.start()
            self.worker_threads.append(worker)
            
        print("✅ Real-time monitoring started")
        
    def _processing_worker(self):
        """Worker thread for processing files."""
        while self.running:
            try:
                job = self.pipeline.processing_queue.get(timeout=1.0)
                self._process_job(job)
                self.pipeline.processing_queue.task_done()
            except queue.Empty:
                continue
            except Exception as e:
                logging.error(f"Processing worker error: {e}")
                
    def _process_job(self, job: Dict):
        """Process a single job."""
        job_type = job['type']
        file_path = job['path']
        
        if job_type == 'tilt_series':
            self._process_tilt_series(file_path)
            
    def _process_tilt_series(self, file_path: Path):
        """Process a tilt series file."""
        try:
            self.pipeline.notify_status(f"Processing {file_path.name}", 0.1)
            
            # Simulate processing steps
            steps = [
                ("Motion correction", 0.3),
                ("CTF estimation", 0.5), 
                ("Alignment", 0.7),
                ("Reconstruction", 0.9)
            ]
            
            for step_name, progress in steps:
                self.pipeline.notify_status(f"{step_name}: {file_path.name}", progress)
                time.sleep(1)  # Simulate processing time
                
            self.pipeline.notify_status(f"Completed: {file_path.name}", 1.0)
            
        except Exception as e:
            self.pipeline.notify_status(f"Error processing {file_path.name}: {e}", -1)
            
    def stop_monitoring(self):
        """Stop monitoring and processing."""
        self.running = False
        
        for observer in self.pipeline.observers:
            observer.stop()
            observer.join()
            
        print("✅ Real-time monitoring stopped")


# Global instance
realtime_processor = RealTimeProcessor()

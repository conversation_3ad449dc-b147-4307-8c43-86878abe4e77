"""
External Software Integration
Seamless integration with RELION, IMOD, Warp, EMAN2, Scipion
"""

import subprocess
import json
import tempfile
from pathlib import Path
from typing import Dict, List, Any, Optional, Union
from abc import ABC, abstractmethod
import shutil


class ExternalSoftware(ABC):
    """Abstract base class for external software integration."""
    
    @abstractmethod
    def is_available(self) -> bool:
        """Check if software is available."""
        pass
        
    @abstractmethod
    def get_version(self) -> str:
        """Get software version."""
        pass
        
    @abstractmethod
    def run_command(self, command: List[str], **kwargs) -> Dict[str, Any]:
        """Run software command."""
        pass


class RELIONIntegration(ExternalSoftware):
    """RELION software integration."""
    
    def __init__(self):
        self.executable_path = self._find_executable()
        
    def _find_executable(self) -> Optional[str]:
        """Find RELION executable."""
        possible_paths = [
            'relion',
            '/usr/local/bin/relion',
            '/opt/relion/bin/relion'
        ]
        
        for path in possible_paths:
            if shutil.which(path):
                return path
        return None
        
    def is_available(self) -> bool:
        """Check if RELION is available."""
        return self.executable_path is not None
        
    def get_version(self) -> str:
        """Get RELION version."""
        if not self.is_available():
            return "Not available"
            
        try:
            result = subprocess.run([self.executable_path, '--version'], 
                                  capture_output=True, text=True, timeout=10)
            return result.stdout.strip()
        except Exception:
            return "Unknown"
            
    def run_command(self, command: List[str], **kwargs) -> Dict[str, Any]:
        """Run RELION command."""
        if not self.is_available():
            raise RuntimeError("RELION not available")
            
        try:
            result = subprocess.run(command, capture_output=True, text=True, **kwargs)
            return {
                'returncode': result.returncode,
                'stdout': result.stdout,
                'stderr': result.stderr,
                'success': result.returncode == 0
            }
        except Exception as e:
            return {
                'returncode': -1,
                'stdout': '',
                'stderr': str(e),
                'success': False
            }
            
    def import_particles(self, star_file: str) -> Dict[str, Any]:
        """Import particles from RELION STAR file."""
        # Parse STAR file (simplified)
        particles = []
        try:
            with open(star_file, 'r') as f:
                lines = f.readlines()
                
            # Find data section
            data_start = -1
            for i, line in enumerate(lines):
                if line.startswith('data_'):
                    data_start = i
                    break
                    
            if data_start >= 0:
                # Parse particle data (simplified)
                for line in lines[data_start+1:]:
                    if line.strip() and not line.startswith('#'):
                        parts = line.strip().split()
                        if len(parts) >= 3:
                            particles.append({
                                'x': float(parts[0]),
                                'y': float(parts[1]),
                                'z': float(parts[2]) if len(parts) > 2 else 0.0
                            })
                            
        except Exception as e:
            return {'success': False, 'error': str(e)}
            
        return {'success': True, 'particles': particles}


class IMODIntegration(ExternalSoftware):
    """IMOD software integration."""
    
    def __init__(self):
        self.imod_dir = self._find_imod_dir()
        
    def _find_imod_dir(self) -> Optional[str]:
        """Find IMOD installation directory."""
        possible_paths = [
            '/usr/local/IMOD',
            '/opt/IMOD',
            '/usr/local/imod'
        ]
        
        for path in possible_paths:
            if Path(path).exists():
                return path
        return None
        
    def is_available(self) -> bool:
        """Check if IMOD is available."""
        return self.imod_dir is not None
        
    def get_version(self) -> str:
        """Get IMOD version."""
        if not self.is_available():
            return "Not available"
            
        version_file = Path(self.imod_dir) / "VERSION"
        if version_file.exists():
            return version_file.read_text().strip()
        return "Unknown"
        
    def run_command(self, command: List[str], **kwargs) -> Dict[str, Any]:
        """Run IMOD command."""
        if not self.is_available():
            raise RuntimeError("IMOD not available")
            
        # Set IMOD environment
        env = kwargs.get('env', {}).copy()
        env['IMOD_DIR'] = self.imod_dir
        env['PATH'] = f"{self.imod_dir}/bin:{env.get('PATH', '')}"
        kwargs['env'] = env
        
        try:
            result = subprocess.run(command, capture_output=True, text=True, **kwargs)
            return {
                'returncode': result.returncode,
                'stdout': result.stdout,
                'stderr': result.stderr,
                'success': result.returncode == 0
            }
        except Exception as e:
            return {
                'returncode': -1,
                'stdout': '',
                'stderr': str(e),
                'success': False
            }
            
    def run_tilt_alignment(self, input_stack: str, output_file: str) -> Dict[str, Any]:
        """Run IMOD tilt alignment."""
        command = ['tiltalign', '-input', input_stack, '-output', output_file]
        return self.run_command(command)


class WarpIntegration(ExternalSoftware):
    """Warp software integration."""
    
    def __init__(self):
        self.executable_path = self._find_executable()
        
    def _find_executable(self) -> Optional[str]:
        """Find Warp executable."""
        possible_names = ['warp', 'Warp', 'WarpTools']
        
        for name in possible_names:
            if shutil.which(name):
                return name
        return None
        
    def is_available(self) -> bool:
        """Check if Warp is available."""
        return self.executable_path is not None
        
    def get_version(self) -> str:
        """Get Warp version."""
        if not self.is_available():
            return "Not available"
        return "Unknown"  # Warp doesn't have standard version command
        
    def run_command(self, command: List[str], **kwargs) -> Dict[str, Any]:
        """Run Warp command."""
        if not self.is_available():
            raise RuntimeError("Warp not available")
            
        try:
            result = subprocess.run(command, capture_output=True, text=True, **kwargs)
            return {
                'returncode': result.returncode,
                'stdout': result.stdout,
                'stderr': result.stderr,
                'success': result.returncode == 0
            }
        except Exception as e:
            return {
                'returncode': -1,
                'stdout': '',
                'stderr': str(e),
                'success': False
            }


class SoftwareManager:
    """Manage all external software integrations."""
    
    def __init__(self):
        self.software = {
            'relion': RELIONIntegration(),
            'imod': IMODIntegration(),
            'warp': WarpIntegration()
        }
        
    def get_available_software(self) -> Dict[str, bool]:
        """Get availability status of all software."""
        return {name: sw.is_available() for name, sw in self.software.items()}
        
    def get_software_versions(self) -> Dict[str, str]:
        """Get versions of all software."""
        return {name: sw.get_version() for name, sw in self.software.items()}
        
    def run_external_command(self, software_name: str, command: List[str], **kwargs) -> Dict[str, Any]:
        """Run command in external software."""
        if software_name not in self.software:
            raise ValueError(f"Unknown software: {software_name}")
            
        return self.software[software_name].run_command(command, **kwargs)
        
    def import_from_relion(self, star_file: str) -> Dict[str, Any]:
        """Import data from RELION."""
        if 'relion' not in self.software:
            raise ValueError("RELION integration not available")
            
        return self.software['relion'].import_particles(star_file)
        
    def export_to_relion(self, particles: List[Dict[str, Any]], output_file: str) -> bool:
        """Export data to RELION format."""
        try:
            with open(output_file, 'w') as f:
                f.write("# RELION STAR file generated by AreTomo3 GUI\n")
                f.write("\ndata_particles\n\n")
                f.write("loop_\n")
                f.write("_rlnCoordinateX #1\n")
                f.write("_rlnCoordinateY #2\n")
                f.write("_rlnCoordinateZ #3\n")
                
                for particle in particles:
                    f.write(f"{particle['x']:.2f} {particle['y']:.2f} {particle['z']:.2f}\n")
                    
            return True
        except Exception:
            return False


# Global software manager
software_manager = SoftwareManager()

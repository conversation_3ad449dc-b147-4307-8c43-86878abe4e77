"""
Comprehensive Data Management System
Professional data organization with FAIR principles
"""

import sqlite3
import json
import hashlib
from datetime import datetime
from typing import Dict, List, Any, Optional
from dataclasses import dataclass, asdict
from pathlib import Path
import uuid


@dataclass
class DatasetMetadata:
    """Dataset metadata following FAIR principles."""
    id: str
    name: str
    description: str
    creation_date: datetime
    modification_date: datetime
    file_paths: List[str]
    file_sizes: List[int]
    checksums: List[str]
    processing_history: List[Dict[str, Any]]
    tags: List[str]
    provenance: Dict[str, Any]
    quality_metrics: Dict[str, float]


class DataManager:
    """Professional data management system."""
    
    def __init__(self, db_path: str = "aretomo3_data.db"):
        self.db_path = db_path
        self.init_database()
        
    def init_database(self):
        """Initialize SQLite database."""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        # Datasets table
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS datasets (
                id TEXT PRIMARY KEY,
                name TEXT NOT NULL,
                description TEXT,
                creation_date TEXT,
                modification_date TEXT,
                metadata TEXT
            )
        """)
        
        # Files table
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS files (
                id TEXT PRIMARY KEY,
                dataset_id TEXT,
                file_path TEXT,
                file_size INTEGER,
                checksum TEXT,
                file_type TEXT,
                FOREIGN KEY (dataset_id) REFERENCES datasets (id)
            )
        """)
        
        # Processing history table
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS processing_history (
                id TEXT PRIMARY KEY,
                dataset_id TEXT,
                step_name TEXT,
                parameters TEXT,
                timestamp TEXT,
                status TEXT,
                FOREIGN KEY (dataset_id) REFERENCES datasets (id)
            )
        """)
        
        conn.commit()
        conn.close()
        
    def create_dataset(self, name: str, description: str = "", file_paths: List[str] = None) -> str:
        """Create new dataset."""
        dataset_id = str(uuid.uuid4())
        now = datetime.now().isoformat()
        
        file_paths = file_paths or []
        file_sizes = []
        checksums = []
        
        # Calculate file metadata
        for file_path in file_paths:
            path = Path(file_path)
            if path.exists():
                file_sizes.append(path.stat().st_size)
                checksums.append(self._calculate_checksum(file_path))
            else:
                file_sizes.append(0)
                checksums.append("")
        
        metadata = DatasetMetadata(
            id=dataset_id,
            name=name,
            description=description,
            creation_date=datetime.fromisoformat(now),
            modification_date=datetime.fromisoformat(now),
            file_paths=file_paths,
            file_sizes=file_sizes,
            checksums=checksums,
            processing_history=[],
            tags=[],
            provenance={},
            quality_metrics={}
        )
        
        # Store in database
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        cursor.execute("""
            INSERT INTO datasets (id, name, description, creation_date, modification_date, metadata)
            VALUES (?, ?, ?, ?, ?, ?)
        """, (dataset_id, name, description, now, now, json.dumps(asdict(metadata), default=str)))
        
        # Store file information
        for i, file_path in enumerate(file_paths):
            cursor.execute("""
                INSERT INTO files (id, dataset_id, file_path, file_size, checksum, file_type)
                VALUES (?, ?, ?, ?, ?, ?)
            """, (str(uuid.uuid4()), dataset_id, file_path, file_sizes[i], checksums[i], 
                  Path(file_path).suffix))
        
        conn.commit()
        conn.close()
        
        return dataset_id
        
    def _calculate_checksum(self, file_path: str) -> str:
        """Calculate MD5 checksum of file."""
        hash_md5 = hashlib.md5()
        try:
            with open(file_path, "rb") as f:
                for chunk in iter(lambda: f.read(4096), b""):
                    hash_md5.update(chunk)
            return hash_md5.hexdigest()
        except Exception:
            return ""
            
    def add_processing_step(self, dataset_id: str, step_name: str, parameters: Dict[str, Any]):
        """Add processing step to dataset history."""
        step_id = str(uuid.uuid4())
        timestamp = datetime.now().isoformat()
        
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        cursor.execute("""
            INSERT INTO processing_history (id, dataset_id, step_name, parameters, timestamp, status)
            VALUES (?, ?, ?, ?, ?, ?)
        """, (step_id, dataset_id, step_name, json.dumps(parameters), timestamp, "completed"))
        
        conn.commit()
        conn.close()
        
    def search_datasets(self, query: str = "", tags: List[str] = None) -> List[Dict[str, Any]]:
        """Search datasets by name, description, or tags."""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        sql = "SELECT * FROM datasets WHERE 1=1"
        params = []
        
        if query:
            sql += " AND (name LIKE ? OR description LIKE ?)"
            params.extend([f"%{query}%", f"%{query}%"])
            
        cursor.execute(sql, params)
        results = cursor.fetchall()
        
        datasets = []
        for row in results:
            dataset = {
                "id": row[0],
                "name": row[1], 
                "description": row[2],
                "creation_date": row[3],
                "modification_date": row[4],
                "metadata": json.loads(row[5]) if row[5] else {}
            }
            datasets.append(dataset)
            
        conn.close()
        return datasets


# Global data manager
data_manager = DataManager()

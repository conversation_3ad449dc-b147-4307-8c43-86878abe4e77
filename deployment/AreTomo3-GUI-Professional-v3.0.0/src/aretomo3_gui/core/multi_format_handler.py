#!/usr/bin/env python3
"""
Multi-format Input Handler for AreTomo3 GUI
Supports EER, TIFF, MRC, SerialEM formats with flexible path handling.
"""

import logging
import re
from dataclasses import dataclass
from enum import Enum
from pathlib import Path
from typing import Dict, List, Optional, Set, Tuple

logger = logging.getLogger(__name__)


class InputFormat(Enum):
    """Supported input formats."""

    EER = "eer"
    TIFF = "tiff"
    TIF = "tif"
    MRC = "mrc"
    MRCS = "mrcs"
    ST = "st"
    MDOC = "mdoc"


@dataclass
class TiltSeriesInfo:
    """Information about a tilt series."""

    name: str
    format: InputFormat
    files: List[Path]
    mdoc_file: Optional[Path]
    gain_file: Optional[Path]
    dark_file: Optional[Path]
    directory: Path
    organization_type: str  # 'subdirectory' or 'single_directory'
    tilt_angles: List[float]
    total_dose: Optional[float]


class MultiFormatHandler:
    """
    Handles multiple input formats for AreTomo3 processing.
    Supports both subdirectory and single directory organizations.
    """

    def __init__(self):
        """Initialize the instance."""
        self.supported_formats = {
            ".eer": InputFormat.EER,
            ".tiff": InputFormat.TIFF,
            ".tif": InputFormat.TIF,
            ".mrc": InputFormat.MRC,
            ".mrcs": InputFormat.MRCS,
            ".st": InputFormat.ST,
            ".mdoc": InputFormat.MDOC,
        }

        # Format-specific parameters
        self.format_defaults = {
            InputFormat.EER: {"fm_int": 15, "eer_sampling": 1, "group_frames": 4},
            InputFormat.TIFF: {"fm_int": 1, "group_frames": 1},
            InputFormat.MRC: {"fm_int": 1, "group_frames": 1},
        }

        logger.info("Multi-format handler initialized")

    def scan_input_directory(
        self, input_path: Path, file_patterns: Optional[List[str]] = None
    ) -> List[TiltSeriesInfo]:
        """
        Scan input directory for tilt series in various formats.

        Args:
            input_path: Path to input directory
            file_patterns: Optional list of file patterns to match

        Returns:
            List of detected tilt series
        """
        tilt_series = []

        if not input_path.exists():
            logger.error(f"Input path does not exist: {input_path}")
            return tilt_series

        # Check organization type
        if self._has_subdirectories_with_data(input_path):
            # Subdirectory organization
            tilt_series.extend(
                self._scan_subdirectory_organization(input_path, file_patterns)
            )
        else:
            # Single directory organization
            tilt_series.extend(
                self._scan_single_directory_organization(input_path, file_patterns)
            )

        logger.info(f"Found {len(tilt_series)} tilt series in {input_path}")
        return tilt_series

    def _has_subdirectories_with_data(self, input_path: Path) -> bool:
        """Check if input uses subdirectory organization."""
        subdirs_with_data = 0
        for item in input_path.iterdir():
            if item.is_dir():
                # Check if subdirectory contains data files
                for ext in self.supported_formats.keys():
                    if list(item.glob(f"*{ext}")):
                        subdirs_with_data += 1
                        break

        # If we have subdirectories with data, assume subdirectory organization
        return subdirs_with_data > 0

    def _scan_subdirectory_organization(
        self, input_path: Path, file_patterns: Optional[List[str]]
    ) -> List[TiltSeriesInfo]:
        """Scan subdirectory organization (each tilt series in its own folder)."""
        tilt_series = []

        for subdir in input_path.iterdir():
            if not subdir.is_dir():
                continue

            series_info = self._analyze_directory_for_series(subdir, file_patterns)
            if series_info:
                series_info.organization_type = "subdirectory"
                tilt_series.append(series_info)

        return tilt_series

    def _scan_single_directory_organization(
        self, input_path: Path, file_patterns: Optional[List[str]]
    ) -> List[TiltSeriesInfo]:
        """Scan single directory organization (all files in one directory)."""
        tilt_series = []

        # Group files by series name
        series_groups = self._group_files_by_series(input_path, file_patterns)

        for series_name, files in series_groups.items():
            series_info = self._create_series_info_from_files(
                series_name, files, input_path, "single_directory"
            )
            if series_info:
                tilt_series.append(series_info)

        return tilt_series

    # TODO: Refactor function - Function '_analyze_directory_for_series' too
    # long (54 lines)
    def _analyze_directory_for_series(
        self, directory: Path, file_patterns: Optional[List[str]]
    ) -> Optional[TiltSeriesInfo]:
        """Analyze a directory for a single tilt series."""
        # Find all supported files
        all_files = []
        for ext in self.supported_formats.keys():
            all_files.extend(directory.glob(f"*{ext}"))

        if not all_files:
            return None

        # Determine series name (usually directory name)
        series_name = directory.name

        # Group files by type
        data_files = []
        mdoc_file = None
        gain_file = None
        dark_file = None

        for file_path in all_files:
            ext = file_path.suffix.lower()

            if ext == ".mdoc":
                mdoc_file = file_path
            elif "gain" in file_path.name.lower():
                gain_file = file_path
            elif "dark" in file_path.name.lower():
                dark_file = file_path
            elif ext in self.supported_formats:
                data_files.append(file_path)

        if not data_files:
            return None

        # Determine format
        format_type = self.supported_formats[data_files[0].suffix.lower()]

        # Extract tilt angles
        tilt_angles = self._extract_tilt_angles(data_files, mdoc_file)

        return TiltSeriesInfo(
            name=series_name,
            format=format_type,
            files=sorted(data_files),
            mdoc_file=mdoc_file,
            gain_file=gain_file,
            dark_file=dark_file,
            directory=directory,
            organization_type="subdirectory",
            tilt_angles=tilt_angles,
            total_dose=None,
        )

    def _group_files_by_series(
        self, directory: Path, file_patterns: Optional[List[str]]
    ) -> Dict[str, List[Path]]:
        """Group files by series name in single directory organization."""
        series_groups = {}

        # Find all supported files
        all_files = []
        for ext in self.supported_formats.keys():
            all_files.extend(directory.glob(f"*{ext}"))

        for file_path in all_files:
            # Extract series name from filename
            series_name = self._extract_series_name(file_path)

            if series_name not in series_groups:
                series_groups[series_name] = []
            series_groups[series_name].append(file_path)

        return series_groups

    def _extract_series_name(self, file_path: Path) -> str:
        """Extract series name from filename."""
        filename = file_path.stem

        # Common patterns for series names
        patterns = [
            r"^(.+?)[-_]\d+",  # name_001, name-001
            r"^(.+?)[-_]?\[.*?\]",  # name[angle], name_[angle]
            r"^(.+?)[-_]?\d+\.\d+",  # name_1.0, name1.0
            r"^(.+?)_\d{3}_",  # SerialEM format: name_001_angle
        ]

        for pattern in patterns:
            match = re.match(pattern, filename)
            if match:
                return match.group(1)

        # Fallback: use filename without extension
        return filename

    def _create_series_info_from_files(
        self, series_name: str, files: List[Path], directory: Path, org_type: str
    ) -> Optional[TiltSeriesInfo]:
        """Create TiltSeriesInfo from grouped files."""
        # Separate file types
        data_files = []
        mdoc_file = None
        gain_file = None
        dark_file = None

        for file_path in files:
            ext = file_path.suffix.lower()

            if ext == ".mdoc":
                mdoc_file = file_path
            elif "gain" in file_path.name.lower():
                gain_file = file_path
            elif "dark" in file_path.name.lower():
                dark_file = file_path
            elif ext in self.supported_formats:
                data_files.append(file_path)

        if not data_files:
            return None

        # Determine format
        format_type = self.supported_formats[data_files[0].suffix.lower()]

        # Extract tilt angles
        tilt_angles = self._extract_tilt_angles(data_files, mdoc_file)

        return TiltSeriesInfo(
            name=series_name,
            format=format_type,
            files=sorted(data_files),
            mdoc_file=mdoc_file,
            gain_file=gain_file,
            dark_file=dark_file,
            directory=directory,
            organization_type=org_type,
            tilt_angles=tilt_angles,
            total_dose=None,
        )

    def _extract_tilt_angles(
        self, data_files: List[Path], mdoc_file: Optional[Path]
    ) -> List[float]:
        """Extract tilt angles from filenames or MDOC file."""
        tilt_angles = []

        if mdoc_file and mdoc_file.exists():
            # Parse MDOC file for tilt angles
            tilt_angles = self._parse_mdoc_tilt_angles(mdoc_file)

        if not tilt_angles:
            # Extract from filenames
            for file_path in data_files:
                angle = self._extract_angle_from_filename(file_path.name)
                if angle is not None:
                    tilt_angles.append(angle)

        return sorted(tilt_angles)

    def _parse_mdoc_tilt_angles(self, mdoc_file: Path) -> List[float]:
        """Parse tilt angles from MDOC file."""
        tilt_angles = []

        try:
            with open(mdoc_file) as f:
                content = f.read()

            # Find TiltAngle entries
            angle_matches = re.findall(r"TiltAngle\s*=\s*([-+]?\d*\.?\d+)", content)
            tilt_angles = [float(angle) for angle in angle_matches]

        except Exception as e:
            logger.warning(f"Could not parse MDOC file {mdoc_file}: {e}")

        return tilt_angles

    def _extract_angle_from_filename(self, filename: str) -> Optional[float]:
        """Extract tilt angle from filename."""
        # Common angle patterns in filenames
        patterns = [
            r"\[([+-]?\d*\.?\d+)\]",  # [angle]
            r"_([+-]?\d*\.?\d+)_",  # _angle_
            r"_([+-]?\d*\.?\d+)\.",  # _angle.ext
            r"([+-]?\d+\.\d+)",  # decimal angle
        ]

        for pattern in patterns:
            match = re.search(pattern, filename)
            if match:
                try:
                    return float(match.group(1))
                except ValueError:
                    continue

        return None

    def get_format_parameters(self, format_type: InputFormat) -> Dict:
        """Get recommended parameters for a specific format."""
        return self.format_defaults.get(format_type, {})

    def validate_series_completeness(
        self, series_info: TiltSeriesInfo
    ) -> Dict[str, bool]:
        """Validate if a tilt series is complete and ready for processing."""
        validation = {
            "has_data_files": len(series_info.files) > 0,
            "has_mdoc": series_info.mdoc_file is not None,
            "has_gain_ref": series_info.gain_file is not None,
            "has_tilt_angles": len(series_info.tilt_angles) > 0,
            "angle_count_matches": len(series_info.tilt_angles)
            == len(series_info.files),
            "format_supported": series_info.format in self.supported_formats.values(),
        }

        # Format-specific validation
        if series_info.format == InputFormat.EER:
            validation["eer_ready"] = validation["has_gain_ref"]
        elif series_info.format in [InputFormat.TIFF, InputFormat.TIF]:
            # TIFF usually doesn't need gain ref
            validation["tiff_ready"] = True

        return validation

    def generate_aretomo3_command(
        self,
        series_info: TiltSeriesInfo,
        output_dir: Path,
        additional_params: Dict = None,
    ) -> str:
        """Generate AreTomo3 command for the tilt series."""
        params = []

        # Input parameters
        if series_info.organization_type == "subdirectory":
            params.append(f"-InPrefix {series_info.directory}")
        else:
            params.append(f"-InPrefix {series_info.directory}/{series_info.name}")

        params.append(f"-Insuffix .{series_info.format.value}")
        params.append(f"-OutDir {output_dir}")

        # Gain reference
        if series_info.gain_file:
            params.append(f"-Gain {series_info.gain_file}")

        # Dark reference
        if series_info.dark_file:
            params.append(f"-Dark {series_info.dark_file}")

        # Format-specific parameters
        format_params = self.get_format_parameters(series_info.format)
        for key, value in format_params.items():
            params.append(f"-{key.title()} {value}")

        # Additional parameters
        if additional_params:
            for key, value in additional_params.items():
                if value is not None:
                    params.append(f"-{key} {value}")

        return "AreTomo3 \\\n    " + " \\\n    ".join(params)

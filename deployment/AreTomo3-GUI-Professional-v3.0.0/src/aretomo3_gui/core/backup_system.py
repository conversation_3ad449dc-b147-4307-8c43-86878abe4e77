#!/usr/bin/env python3
"""
AreTomo3 GUI Backup and Recovery System
Automated backup and recovery for configurations, sessions, and processing data.
"""

import gzip
import hashlib
import json
import logging
import shutil
import tarfile
import threading
from dataclasses import asdict, dataclass, field
from datetime import datetime, timedelta
from pathlib import Path
from typing import Any, Dict, List, Optional, Union

import schedule

logger = logging.getLogger(__name__)


@dataclass
class BackupMetadata:
    """Backup metadata information."""

    backup_id: str
    timestamp: datetime
    backup_type: str  # full, incremental, config, session
    source_paths: List[str]
    backup_path: str
    size_bytes: int
    checksum: str
    compression_ratio: float = 0.0
    description: str = ""
    tags: List[str] = field(default_factory=list)


class BackupSystem:
    """
    Comprehensive backup and recovery system for AreTomo3 GUI.
    Handles automated backups, compression, and recovery operations.
    """

    def __init__(self, backup_dir: Union[str, Path] = None):
        """Initialize the backup system."""
        self.backup_dir = (
            Path(backup_dir)
            if backup_dir
            else Path.home() / ".aretomo3_gui" / "backups"
        )
        self.backup_dir.mkdir(parents=True, exist_ok=True)

        # Backup configuration
        self.config = {
            "enabled": True,
            "auto_backup_interval": 3600,  # 1 hour in seconds
            "max_backups": 50,
            "compression_enabled": True,
            "compression_level": 6,
            "backup_types": {
                "config": True,
                "sessions": True,
                "logs": True,
                "results": False,  # Large files, disabled by default
                "cache": False,
            },
        }

        # Backup metadata
        self.metadata_file = self.backup_dir / "backup_metadata.json"
        self.backups: Dict[str, BackupMetadata] = {}

        # Scheduler
        self.scheduler_thread = None
        self.scheduler_running = False

        # Load existing metadata
        self._load_metadata()

        # Start automatic backup scheduler
        self._start_scheduler()

        logger.info(f"Backup System initialized - Backup dir: {self.backup_dir}")

    def _load_metadata(self):
        """Load backup metadata from file."""
        try:
            if self.metadata_file.exists():
                with open(self.metadata_file) as f:
                    data = json.load(f)

                for backup_id, metadata in data.items():
                    self.backups[backup_id] = BackupMetadata(
                        backup_id=metadata["backup_id"],
                        timestamp=datetime.fromisoformat(metadata["timestamp"]),
                        backup_type=metadata["backup_type"],
                        source_paths=metadata["source_paths"],
                        backup_path=metadata["backup_path"],
                        size_bytes=metadata["size_bytes"],
                        checksum=metadata["checksum"],
                        compression_ratio=metadata.get("compression_ratio", 0.0),
                        description=metadata.get("description", ""),
                        tags=metadata.get("tags", []),
                    )

                logger.info(f"Loaded {len(self.backups)} backup records")

        except Exception as e:
            logger.error(f"Error loading backup metadata: {e}")

    def _save_metadata(self):
        """Save backup metadata to file."""
        try:
            data = {}
            for backup_id, metadata in self.backups.items():
                data[backup_id] = {
                    "backup_id": metadata.backup_id,
                    "timestamp": metadata.timestamp.isoformat(),
                    "backup_type": metadata.backup_type,
                    "source_paths": metadata.source_paths,
                    "backup_path": metadata.backup_path,
                    "size_bytes": metadata.size_bytes,
                    "checksum": metadata.checksum,
                    "compression_ratio": metadata.compression_ratio,
                    "description": metadata.description,
                    "tags": metadata.tags,
                }

            with open(self.metadata_file, "w") as f:
                json.dump(data, f, indent=2)

        except Exception as e:
            logger.error(f"Error saving backup metadata: {e}")

    def create_backup(
        self, backup_type: str = "full", description: str = "", tags: List[str] = None
    ) -> Optional[str]:
        """
        Create a backup of specified type.

        Args:
            backup_type: Type of backup (full, config, session, logs)
            description: Optional description
            tags: Optional tags for categorization

        Returns:
            Backup ID if successful, None otherwise
        """
        if not self.config["enabled"]:
            logger.warning("Backup system is disabled")
            return None

        try:
            # Generate backup ID
            backup_id = self._generate_backup_id(backup_type)

            # Determine source paths
            source_paths = self._get_source_paths(backup_type)
            if not source_paths:
                logger.warning(f"No source paths found for backup type: {backup_type}")
                return None

            # Create backup
            backup_path = self._create_backup_archive(
                backup_id, source_paths, backup_type
            )
            if not backup_path:
                return None

            # Calculate metadata
            size_bytes = backup_path.stat().st_size
            checksum = self._calculate_checksum(backup_path)
            compression_ratio = self._calculate_compression_ratio(
                source_paths, backup_path
            )

            # Create metadata
            metadata = BackupMetadata(
                backup_id=backup_id,
                timestamp=datetime.now(),
                backup_type=backup_type,
                source_paths=[str(p) for p in source_paths],
                backup_path=str(backup_path),
                size_bytes=size_bytes,
                checksum=checksum,
                compression_ratio=compression_ratio,
                description=description,
                tags=tags or [],
            )

            # Store metadata
            self.backups[backup_id] = metadata
            self._save_metadata()

            # Clean up old backups
            self._cleanup_old_backups()

            logger.info(
                f"Backup created successfully: {backup_id} ({ size_bytes / 1024 / 1024:.1f} MB)"
            )
            return backup_id

        except Exception as e:
            logger.error(f"Error creating backup: {e}")
            return None

    def _generate_backup_id(self, backup_type: str) -> str:
        """Generate unique backup ID."""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        return f"{backup_type}_{timestamp}"

    def _get_source_paths(self, backup_type: str) -> List[Path]:
        """Get source paths for backup type."""
        base_dir = Path.home() / ".aretomo3_gui"
        source_paths = []

        if backup_type == "full":
            # Full backup includes everything
            if base_dir.exists():
                source_paths.append(base_dir)

        elif backup_type == "config":
            # Configuration files
            config_files = [
                base_dir / "config.json",
                base_dir / "profiles.json",
                base_dir / "user_settings.json",
            ]
            source_paths.extend([f for f in config_files if f.exists()])

        elif backup_type == "sessions":
            # Session data
            sessions_dir = base_dir / "sessions"
            if sessions_dir.exists():
                source_paths.append(sessions_dir)

        elif backup_type == "logs":
            # Log files
            logs_dir = base_dir / "logs"
            if logs_dir.exists():
                source_paths.append(logs_dir)

        elif backup_type == "results":
            # Processing results (if enabled)
            if self.config["backup_types"].get("results", False):
                results_dir = base_dir / "results"
                if results_dir.exists():
                    source_paths.append(results_dir)

        return source_paths

    def _create_backup_archive(
        self, backup_id: str, source_paths: List[Path], backup_type: str
    ) -> Optional[Path]:
        """Create compressed backup archive."""
        try:
            # Determine archive format
            if self.config["compression_enabled"]:
                archive_path = self.backup_dir / f"{backup_id}.tar.gz"
                mode = "w:gz"
            else:
                archive_path = self.backup_dir / f"{backup_id}.tar"
                mode = "w"

            # Create archive
            with tarfile.open(archive_path, mode) as tar:
                for source_path in source_paths:
                    if source_path.exists():
                        # Add to archive with relative path
                        arcname = source_path.name
                        tar.add(source_path, arcname=arcname)

            return archive_path

        except Exception as e:
            logger.error(f"Error creating backup archive: {e}")
            return None

    def _calculate_checksum(self, file_path: Path) -> str:
        """Calculate SHA256 checksum of file."""
        try:
            sha256_hash = hashlib.sha256()
            with open(file_path, "rb") as f:
                for chunk in iter(lambda: f.read(4096), b""):
                    sha256_hash.update(chunk)
            return sha256_hash.hexdigest()
        except Exception as e:
            logger.error(f"Error calculating checksum: {e}")
            return ""

    def _calculate_compression_ratio(
        self, source_paths: List[Path], backup_path: Path
    ) -> float:
        """Calculate compression ratio."""
        try:
            total_source_size = 0
            for source_path in source_paths:
                if source_path.is_file():
                    total_source_size += source_path.stat().st_size
                elif source_path.is_dir():
                    for file_path in source_path.rglob("*"):
                        if file_path.is_file():
                            total_source_size += file_path.stat().st_size

            if total_source_size > 0:
                backup_size = backup_path.stat().st_size
                return (total_source_size - backup_size) / total_source_size * 100

            return 0.0

        except Exception as e:
            logger.error(f"Error calculating compression ratio: {e}")
            return 0.0

    def restore_backup(self, backup_id: str, restore_path: Path = None) -> bool:
        """
        Restore a backup.

        Args:
            backup_id: ID of backup to restore
            restore_path: Optional custom restore path

        Returns:
            True if successful, False otherwise
        """
        if backup_id not in self.backups:
            logger.error(f"Backup not found: {backup_id}")
            return False

        try:
            metadata = self.backups[backup_id]
            backup_path = Path(metadata.backup_path)

            if not backup_path.exists():
                logger.error(f"Backup file not found: {backup_path}")
                return False

            # Verify checksum
            if not self._verify_backup_integrity(backup_path, metadata.checksum):
                logger.error(f"Backup integrity check failed: {backup_id}")
                return False

            # Determine restore location
            if restore_path is None:
                restore_path = Path.home() / ".aretomo3_gui"

            # Create restore directory
            restore_path.mkdir(parents=True, exist_ok=True)

            # Extract archive
            with tarfile.open(backup_path, "r:*") as tar:
                tar.extractall(restore_path)

            logger.info(f"Backup restored successfully: {backup_id} -> {restore_path}")
            return True

        except Exception as e:
            logger.error(f"Error restoring backup {backup_id}: {e}")
            return False

    def _verify_backup_integrity(
        self, backup_path: Path, expected_checksum: str
    ) -> bool:
        """Verify backup file integrity."""
        if not expected_checksum:
            logger.warning("No checksum available for verification")
            return True

        actual_checksum = self._calculate_checksum(backup_path)
        return actual_checksum == expected_checksum

    def delete_backup(self, backup_id: str) -> bool:
        """Delete a backup."""
        if backup_id not in self.backups:
            logger.error(f"Backup not found: {backup_id}")
            return False

        try:
            metadata = self.backups[backup_id]
            backup_path = Path(metadata.backup_path)

            # Delete backup file
            if backup_path.exists():
                backup_path.unlink()

            # Remove from metadata
            del self.backups[backup_id]
            self._save_metadata()

            logger.info(f"Backup deleted: {backup_id}")
            return True

        except Exception as e:
            logger.error(f"Error deleting backup {backup_id}: {e}")
            return False

    def _cleanup_old_backups(self):
        """Clean up old backups based on retention policy."""
        try:
            # Sort backups by timestamp
            sorted_backups = sorted(
                self.backups.items(), key=lambda x: x[1].timestamp, reverse=True
            )

            # Keep only the most recent backups
            max_backups = self.config["max_backups"]
            if len(sorted_backups) > max_backups:
                backups_to_delete = sorted_backups[max_backups:]

                for backup_id, metadata in backups_to_delete:
                    self.delete_backup(backup_id)
                    logger.info(f"Cleaned up old backup: {backup_id}")

        except Exception as e:
            logger.error(f"Error cleaning up old backups: {e}")

    def get_backup_list(self, backup_type: str = None) -> List[BackupMetadata]:
        """Get list of backups, optionally filtered by type."""
        backups = list(self.backups.values())

        if backup_type:
            backups = [b for b in backups if b.backup_type == backup_type]

        # Sort by timestamp (newest first)
        backups.sort(key=lambda x: x.timestamp, reverse=True)

        return backups

    def get_backup_statistics(self) -> Dict[str, Any]:
        """Get backup system statistics."""
        total_size = sum(b.size_bytes for b in self.backups.values())
        avg_compression = (
            sum(b.compression_ratio for b in self.backups.values()) / len(self.backups)
            if self.backups
            else 0
        )

        backup_types = {}
        for backup in self.backups.values():
            backup_type = backup.backup_type
            if backup_type not in backup_types:
                backup_types[backup_type] = {"count": 0, "size": 0}
            backup_types[backup_type]["count"] += 1
            backup_types[backup_type]["size"] += backup.size_bytes

        return {
            "total_backups": len(self.backups),
            "total_size_mb": total_size / 1024 / 1024,
            "average_compression_ratio": avg_compression,
            "backup_types": backup_types,
            "backup_directory": str(self.backup_dir),
            "auto_backup_enabled": self.config["enabled"],
        }

    def _start_scheduler(self):
        """Start automatic backup scheduler."""
        if not self.config["enabled"]:
            return

        def run_scheduler():
            """Execute run_scheduler operation."""
            schedule.every(self.config["auto_backup_interval"]).seconds.do(
                lambda: self.create_backup("config", "Automatic backup")
            )

            while self.scheduler_running:
                schedule.run_pending()
                threading.Event().wait(60)  # Check every minute

        self.scheduler_running = True
        self.scheduler_thread = threading.Thread(target=run_scheduler, daemon=True)
        self.scheduler_thread.start()

        logger.info("Automatic backup scheduler started")

    def stop_scheduler(self):
        """Stop automatic backup scheduler."""
        self.scheduler_running = False
        if self.scheduler_thread:
            self.scheduler_thread.join(timeout=5)

        logger.info("Automatic backup scheduler stopped")

    def update_config(self, config_updates: Dict[str, Any]):
        """Update backup configuration."""
        self.config.update(config_updates)

        # Restart scheduler if needed
        if "enabled" in config_updates or "auto_backup_interval" in config_updates:
            self.stop_scheduler()
            if self.config["enabled"]:
                self._start_scheduler()

        logger.info("Backup configuration updated")


# Global backup system instance
backup_system = BackupSystem()


def create_backup(backup_type: str = "config", description: str = "") -> Optional[str]:
    """Convenience function to create a backup."""
    return backup_system.create_backup(backup_type, description)


def restore_backup(backup_id: str, restore_path: Path = None) -> bool:
    """Convenience function to restore a backup."""
    return backup_system.restore_backup(backup_id, restore_path)

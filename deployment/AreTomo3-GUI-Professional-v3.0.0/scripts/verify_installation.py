#!/usr/bin/env python3
"""
Installation Verification Script
Verifies that AreTomo3 GUI is properly installed and functional.
"""

import sys
import importlib

def verify_installation():
    print("🔍 VERIFYING INSTALLATION")
    print("=" * 40)
    
    # Test core imports
    try:
        import aretomo3_gui
        print("✅ Core package import successful")
    except ImportError as e:
        print(f"❌ Core package import failed: {e}")
        return False
    
    # Test key components
    components = [
        "aretomo3_gui.utils.file_utils",
        "aretomo3_gui.analytics.advanced_analytics",
        "aretomo3_gui.data_management.data_manager",
        "aretomo3_gui.core.config_manager",
        "aretomo3_gui.web.server"
    ]
    
    for component in components:
        try:
            importlib.import_module(component)
            print(f"✅ {component}")
        except ImportError as e:
            print(f"❌ {component}: {e}")
            return False
    
    print("\n🎉 Installation verification successful!")
    return True

if __name__ == "__main__":
    success = verify_installation()
    sys.exit(0 if success else 1)
